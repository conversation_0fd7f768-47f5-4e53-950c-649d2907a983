// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGPortal.h"
#include "Engine/HitResult.h"
#include "Engine/TimerHandle.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGPortal() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGPortal();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGPortal_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONMapPhase();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONPortalType();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPortalSettings();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_ACharacter_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UAudioComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UPointLightComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UPrimitiveComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USoundBase_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USphereComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FHitResult();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FTimerHandle();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAURACRONPortalType *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONPortalType;
static UEnum* EAURACRONPortalType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONPortalType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONPortalType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONPortalType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONPortalType"));
	}
	return Z_Registration_Info_UEnum_EAURACRONPortalType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONPortalType>()
{
	return EAURACRONPortalType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONPortalType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enumera\xc3\xa7\xc3\xa3o para os tipos de portais de posicionamento t\xc3\xa1tico\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
		{ "PurgatoryRealm.Comment", "// Energia prateada - Firmamento Zephyr\n" },
		{ "PurgatoryRealm.DisplayName", "Portal Umbral" },
		{ "PurgatoryRealm.Name", "EAURACRONPortalType::PurgatoryRealm" },
		{ "PurgatoryRealm.ToolTip", "Energia prateada - Firmamento Zephyr" },
		{ "RadiantPlains.DisplayName", "Portal Radiante" },
		{ "RadiantPlains.Name", "EAURACRONPortalType::RadiantPlains" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enumera\xc3\xa7\xc3\xa3o para os tipos de portais de posicionamento t\xc3\xa1tico" },
#endif
		{ "ZephyrFirmament.Comment", "// Energia dourada - Plan\xc3\xad""cie Radiante\n" },
		{ "ZephyrFirmament.DisplayName", "Portal Zephyr" },
		{ "ZephyrFirmament.Name", "EAURACRONPortalType::ZephyrFirmament" },
		{ "ZephyrFirmament.ToolTip", "Energia dourada - Plan\xc3\xad""cie Radiante" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONPortalType::RadiantPlains", (int64)EAURACRONPortalType::RadiantPlains },
		{ "EAURACRONPortalType::ZephyrFirmament", (int64)EAURACRONPortalType::ZephyrFirmament },
		{ "EAURACRONPortalType::PurgatoryRealm", (int64)EAURACRONPortalType::PurgatoryRealm },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONPortalType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONPortalType",
	"EAURACRONPortalType",
	Z_Construct_UEnum_AURACRON_EAURACRONPortalType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONPortalType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONPortalType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONPortalType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONPortalType()
{
	if (!Z_Registration_Info_UEnum_EAURACRONPortalType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONPortalType.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONPortalType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONPortalType.InnerSingleton;
}
// ********** End Enum EAURACRONPortalType *********************************************************

// ********** Begin ScriptStruct FAURACRONPortalSettings *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONPortalSettings;
class UScriptStruct* FAURACRONPortalSettings::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONPortalSettings.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONPortalSettings.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONPortalSettings, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONPortalSettings"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONPortalSettings.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para configura\xc3\xa7\xc3\xb5""es de portal de posicionamento t\xc3\xa1tico\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para configura\xc3\xa7\xc3\xb5""es de portal de posicionamento t\xc3\xa1tico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PortalType_MetaData[] = {
		{ "Category", "AURACRONPortalSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo do portal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo do portal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentEnvironment_MetaData[] = {
		{ "Category", "AURACRONPortalSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo do ambiente atual */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo do ambiente atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestinationLocation_MetaData[] = {
		{ "Category", "AURACRONPortalSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Localiza\xc3\xa7\xc3\xa3o de destino para teletransporte */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Localiza\xc3\xa7\xc3\xa3o de destino para teletransporte" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestinationRotation_MetaData[] = {
		{ "Category", "AURACRONPortalSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Rota\xc3\xa7\xc3\xa3o de destino para teletransporte */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rota\xc3\xa7\xc3\xa3o de destino para teletransporte" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PortalScale_MetaData[] = {
		{ "Category", "AURACRONPortalSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Escala do portal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escala do portal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PortalColor_MetaData[] = {
		{ "Category", "AURACRONPortalSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor do portal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor do portal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectIntensity_MetaData[] = {
		{ "Category", "AURACRONPortalSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade do efeito */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade do efeito" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivationRadius_MetaData[] = {
		{ "Category", "AURACRONPortalSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio de ativa\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio de ativa\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "AURACRONPortalSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se o portal est\xc3\xa1 ativo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se o portal est\xc3\xa1 ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsVisible_MetaData[] = {
		{ "Category", "AURACRONPortalSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se o portal est\xc3\xa1 vis\xc3\xadvel */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se o portal est\xc3\xa1 vis\xc3\xadvel" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredPhase_MetaData[] = {
		{ "Category", "AURACRONPortalSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Fase necess\xc3\xa1ria para ativar o portal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fase necess\xc3\xa1ria para ativar o portal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PortalID_MetaData[] = {
		{ "Category", "AURACRONPortalSettings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID \xc3\xbanico do portal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID \xc3\xbanico do portal" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_PortalType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PortalType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentEnvironment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentEnvironment;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DestinationLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DestinationRotation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PortalScale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PortalColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EffectIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActivationRadius;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static void NewProp_bIsVisible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsVisible;
	static const UECodeGen_Private::FBytePropertyParams NewProp_RequiredPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_RequiredPhase;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PortalID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONPortalSettings>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_PortalType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_PortalType = { "PortalType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPortalSettings, PortalType), Z_Construct_UEnum_AURACRON_EAURACRONPortalType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PortalType_MetaData), NewProp_PortalType_MetaData) }; // 1562177233
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_CurrentEnvironment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_CurrentEnvironment = { "CurrentEnvironment", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPortalSettings, CurrentEnvironment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentEnvironment_MetaData), NewProp_CurrentEnvironment_MetaData) }; // 2509470107
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_DestinationLocation = { "DestinationLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPortalSettings, DestinationLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestinationLocation_MetaData), NewProp_DestinationLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_DestinationRotation = { "DestinationRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPortalSettings, DestinationRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestinationRotation_MetaData), NewProp_DestinationRotation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_PortalScale = { "PortalScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPortalSettings, PortalScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PortalScale_MetaData), NewProp_PortalScale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_PortalColor = { "PortalColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPortalSettings, PortalColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PortalColor_MetaData), NewProp_PortalColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_EffectIntensity = { "EffectIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPortalSettings, EffectIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectIntensity_MetaData), NewProp_EffectIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_ActivationRadius = { "ActivationRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPortalSettings, ActivationRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivationRadius_MetaData), NewProp_ActivationRadius_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FAURACRONPortalSettings*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONPortalSettings), &Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_bIsVisible_SetBit(void* Obj)
{
	((FAURACRONPortalSettings*)Obj)->bIsVisible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_bIsVisible = { "bIsVisible", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONPortalSettings), &Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_bIsVisible_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsVisible_MetaData), NewProp_bIsVisible_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_RequiredPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_RequiredPhase = { "RequiredPhase", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPortalSettings, RequiredPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredPhase_MetaData), NewProp_RequiredPhase_MetaData) }; // 2541365769
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_PortalID = { "PortalID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPortalSettings, PortalID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PortalID_MetaData), NewProp_PortalID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_PortalType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_PortalType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_CurrentEnvironment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_CurrentEnvironment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_DestinationLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_DestinationRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_PortalScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_PortalColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_EffectIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_ActivationRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_bIsVisible,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_RequiredPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_RequiredPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewProp_PortalID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONPortalSettings",
	Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::PropPointers),
	sizeof(FAURACRONPortalSettings),
	alignof(FAURACRONPortalSettings),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPortalSettings()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONPortalSettings.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONPortalSettings.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONPortalSettings.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONPortalSettings *********************************************

// ********** Begin Class AAURACRONPCGPortal Function ActivatePortal *******************************
struct Z_Construct_UFunction_AAURACRONPCGPortal_ActivatePortal_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ativar portal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativar portal" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_ActivatePortal_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "ActivatePortal", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_ActivatePortal_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_ActivatePortal_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_ActivatePortal()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_ActivatePortal_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execActivatePortal)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ActivatePortal();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function ActivatePortal *********************************

// ********** Begin Class AAURACRONPCGPortal Function ApplyTeleportEffect **************************
struct Z_Construct_UFunction_AAURACRONPCGPortal_ApplyTeleportEffect_Statics
{
	struct AURACRONPCGPortal_eventApplyTeleportEffect_Parms
	{
		float EffectAlpha;
		bool bFadeIn;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aplicar efeito de teletransporte */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar efeito de teletransporte" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EffectAlpha;
	static void NewProp_bFadeIn_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFadeIn;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_ApplyTeleportEffect_Statics::NewProp_EffectAlpha = { "EffectAlpha", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventApplyTeleportEffect_Parms, EffectAlpha), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAURACRONPCGPortal_ApplyTeleportEffect_Statics::NewProp_bFadeIn_SetBit(void* Obj)
{
	((AURACRONPCGPortal_eventApplyTeleportEffect_Parms*)Obj)->bFadeIn = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_ApplyTeleportEffect_Statics::NewProp_bFadeIn = { "bFadeIn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPortal_eventApplyTeleportEffect_Parms), &Z_Construct_UFunction_AAURACRONPCGPortal_ApplyTeleportEffect_Statics::NewProp_bFadeIn_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_ApplyTeleportEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_ApplyTeleportEffect_Statics::NewProp_EffectAlpha,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_ApplyTeleportEffect_Statics::NewProp_bFadeIn,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_ApplyTeleportEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_ApplyTeleportEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "ApplyTeleportEffect", Z_Construct_UFunction_AAURACRONPCGPortal_ApplyTeleportEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_ApplyTeleportEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_ApplyTeleportEffect_Statics::AURACRONPCGPortal_eventApplyTeleportEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_ApplyTeleportEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_ApplyTeleportEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_ApplyTeleportEffect_Statics::AURACRONPCGPortal_eventApplyTeleportEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_ApplyTeleportEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_ApplyTeleportEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execApplyTeleportEffect)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_EffectAlpha);
	P_GET_UBOOL(Z_Param_bFadeIn);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyTeleportEffect(Z_Param_EffectAlpha,Z_Param_bFadeIn);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function ApplyTeleportEffect ****************************

// ********** Begin Class AAURACRONPCGPortal Function ClientNotifyPortalActivation *****************
struct AURACRONPCGPortal_eventClientNotifyPortalActivation_Parms
{
	ACharacter* PlayerCharacter;
};
static FName NAME_AAURACRONPCGPortal_ClientNotifyPortalActivation = FName(TEXT("ClientNotifyPortalActivation"));
void AAURACRONPCGPortal::ClientNotifyPortalActivation(ACharacter* PlayerCharacter)
{
	AURACRONPCGPortal_eventClientNotifyPortalActivation_Parms Parms;
	Parms.PlayerCharacter=PlayerCharacter;
	UFunction* Func = FindFunctionChecked(NAME_AAURACRONPCGPortal_ClientNotifyPortalActivation);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AAURACRONPCGPortal_ClientNotifyPortalActivation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** RPC multicast para notificar ativa\xc3\xa7\xc3\xa3o do portal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "RPC multicast para notificar ativa\xc3\xa7\xc3\xa3o do portal" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerCharacter;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_ClientNotifyPortalActivation_Statics::NewProp_PlayerCharacter = { "PlayerCharacter", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventClientNotifyPortalActivation_Parms, PlayerCharacter), Z_Construct_UClass_ACharacter_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_ClientNotifyPortalActivation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_ClientNotifyPortalActivation_Statics::NewProp_PlayerCharacter,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_ClientNotifyPortalActivation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_ClientNotifyPortalActivation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "ClientNotifyPortalActivation", Z_Construct_UFunction_AAURACRONPCGPortal_ClientNotifyPortalActivation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_ClientNotifyPortalActivation_Statics::PropPointers), sizeof(AURACRONPCGPortal_eventClientNotifyPortalActivation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00044CC1, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_ClientNotifyPortalActivation_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_ClientNotifyPortalActivation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONPCGPortal_eventClientNotifyPortalActivation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_ClientNotifyPortalActivation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_ClientNotifyPortalActivation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execClientNotifyPortalActivation)
{
	P_GET_OBJECT(ACharacter,Z_Param_PlayerCharacter);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClientNotifyPortalActivation_Implementation(Z_Param_PlayerCharacter);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function ClientNotifyPortalActivation *******************

// ********** Begin Class AAURACRONPCGPortal Function CreateRadiantPortal **************************
struct Z_Construct_UFunction_AAURACRONPCGPortal_CreateRadiantPortal_Statics
{
	struct AURACRONPCGPortal_eventCreateRadiantPortal_Parms
	{
		FVector DestLocation;
		FRotator DestRotation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Criar portal radiante (energia dourada) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar portal radiante (energia dourada)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_DestLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DestRotation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_CreateRadiantPortal_Statics::NewProp_DestLocation = { "DestLocation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventCreateRadiantPortal_Parms, DestLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_CreateRadiantPortal_Statics::NewProp_DestRotation = { "DestRotation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventCreateRadiantPortal_Parms, DestRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_CreateRadiantPortal_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_CreateRadiantPortal_Statics::NewProp_DestLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_CreateRadiantPortal_Statics::NewProp_DestRotation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_CreateRadiantPortal_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_CreateRadiantPortal_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "CreateRadiantPortal", Z_Construct_UFunction_AAURACRONPCGPortal_CreateRadiantPortal_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_CreateRadiantPortal_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_CreateRadiantPortal_Statics::AURACRONPCGPortal_eventCreateRadiantPortal_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_CreateRadiantPortal_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_CreateRadiantPortal_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_CreateRadiantPortal_Statics::AURACRONPCGPortal_eventCreateRadiantPortal_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_CreateRadiantPortal()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_CreateRadiantPortal_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execCreateRadiantPortal)
{
	P_GET_STRUCT(FVector,Z_Param_DestLocation);
	P_GET_STRUCT(FRotator,Z_Param_DestRotation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateRadiantPortal(Z_Param_DestLocation,Z_Param_DestRotation);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function CreateRadiantPortal ****************************

// ********** Begin Class AAURACRONPCGPortal Function CreateUmbralPortal ***************************
struct Z_Construct_UFunction_AAURACRONPCGPortal_CreateUmbralPortal_Statics
{
	struct AURACRONPCGPortal_eventCreateUmbralPortal_Parms
	{
		FVector DestLocation;
		FRotator DestRotation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Criar portal umbral (energia violeta) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar portal umbral (energia violeta)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_DestLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DestRotation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_CreateUmbralPortal_Statics::NewProp_DestLocation = { "DestLocation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventCreateUmbralPortal_Parms, DestLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_CreateUmbralPortal_Statics::NewProp_DestRotation = { "DestRotation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventCreateUmbralPortal_Parms, DestRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_CreateUmbralPortal_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_CreateUmbralPortal_Statics::NewProp_DestLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_CreateUmbralPortal_Statics::NewProp_DestRotation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_CreateUmbralPortal_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_CreateUmbralPortal_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "CreateUmbralPortal", Z_Construct_UFunction_AAURACRONPCGPortal_CreateUmbralPortal_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_CreateUmbralPortal_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_CreateUmbralPortal_Statics::AURACRONPCGPortal_eventCreateUmbralPortal_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_CreateUmbralPortal_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_CreateUmbralPortal_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_CreateUmbralPortal_Statics::AURACRONPCGPortal_eventCreateUmbralPortal_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_CreateUmbralPortal()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_CreateUmbralPortal_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execCreateUmbralPortal)
{
	P_GET_STRUCT(FVector,Z_Param_DestLocation);
	P_GET_STRUCT(FRotator,Z_Param_DestRotation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateUmbralPortal(Z_Param_DestLocation,Z_Param_DestRotation);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function CreateUmbralPortal *****************************

// ********** Begin Class AAURACRONPCGPortal Function CreateZephyrPortal ***************************
struct Z_Construct_UFunction_AAURACRONPCGPortal_CreateZephyrPortal_Statics
{
	struct AURACRONPCGPortal_eventCreateZephyrPortal_Parms
	{
		FVector DestLocation;
		FRotator DestRotation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Criar portal zephyr (energia prateada) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar portal zephyr (energia prateada)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_DestLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DestRotation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_CreateZephyrPortal_Statics::NewProp_DestLocation = { "DestLocation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventCreateZephyrPortal_Parms, DestLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_CreateZephyrPortal_Statics::NewProp_DestRotation = { "DestRotation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventCreateZephyrPortal_Parms, DestRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_CreateZephyrPortal_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_CreateZephyrPortal_Statics::NewProp_DestLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_CreateZephyrPortal_Statics::NewProp_DestRotation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_CreateZephyrPortal_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_CreateZephyrPortal_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "CreateZephyrPortal", Z_Construct_UFunction_AAURACRONPCGPortal_CreateZephyrPortal_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_CreateZephyrPortal_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_CreateZephyrPortal_Statics::AURACRONPCGPortal_eventCreateZephyrPortal_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_CreateZephyrPortal_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_CreateZephyrPortal_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_CreateZephyrPortal_Statics::AURACRONPCGPortal_eventCreateZephyrPortal_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_CreateZephyrPortal()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_CreateZephyrPortal_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execCreateZephyrPortal)
{
	P_GET_STRUCT(FVector,Z_Param_DestLocation);
	P_GET_STRUCT(FRotator,Z_Param_DestRotation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateZephyrPortal(Z_Param_DestLocation,Z_Param_DestRotation);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function CreateZephyrPortal *****************************

// ********** Begin Class AAURACRONPCGPortal Function DeactivatePortal *****************************
struct Z_Construct_UFunction_AAURACRONPCGPortal_DeactivatePortal_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Desativar portal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Desativar portal" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_DeactivatePortal_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "DeactivatePortal", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_DeactivatePortal_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_DeactivatePortal_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_DeactivatePortal()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_DeactivatePortal_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execDeactivatePortal)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DeactivatePortal();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function DeactivatePortal *******************************

// ********** Begin Class AAURACRONPCGPortal Function GetActivationRadius **************************
struct Z_Construct_UFunction_AAURACRONPCGPortal_GetActivationRadius_Statics
{
	struct AURACRONPCGPortal_eventGetActivationRadius_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter raio de ativa\xc3\xa7\xc3\xa3o (alias) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter raio de ativa\xc3\xa7\xc3\xa3o (alias)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_GetActivationRadius_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventGetActivationRadius_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_GetActivationRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_GetActivationRadius_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_GetActivationRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_GetActivationRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "GetActivationRadius", Z_Construct_UFunction_AAURACRONPCGPortal_GetActivationRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_GetActivationRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_GetActivationRadius_Statics::AURACRONPCGPortal_eventGetActivationRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_GetActivationRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_GetActivationRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_GetActivationRadius_Statics::AURACRONPCGPortal_eventGetActivationRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_GetActivationRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_GetActivationRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execGetActivationRadius)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetActivationRadius();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function GetActivationRadius ****************************

// ********** Begin Class AAURACRONPCGPortal Function GetActivationRange ***************************
struct Z_Construct_UFunction_AAURACRONPCGPortal_GetActivationRange_Statics
{
	struct AURACRONPCGPortal_eventGetActivationRange_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter raio de ativa\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter raio de ativa\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_GetActivationRange_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventGetActivationRange_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_GetActivationRange_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_GetActivationRange_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_GetActivationRange_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_GetActivationRange_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "GetActivationRange", Z_Construct_UFunction_AAURACRONPCGPortal_GetActivationRange_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_GetActivationRange_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_GetActivationRange_Statics::AURACRONPCGPortal_eventGetActivationRange_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_GetActivationRange_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_GetActivationRange_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_GetActivationRange_Statics::AURACRONPCGPortal_eventGetActivationRange_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_GetActivationRange()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_GetActivationRange_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execGetActivationRange)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetActivationRange();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function GetActivationRange *****************************

// ********** Begin Class AAURACRONPCGPortal Function GetCurrentEnvironment ************************
struct Z_Construct_UFunction_AAURACRONPCGPortal_GetCurrentEnvironment_Statics
{
	struct AURACRONPCGPortal_eventGetCurrentEnvironment_Parms
	{
		EAURACRONEnvironmentType ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter ambiente atual */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter ambiente atual" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_GetCurrentEnvironment_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_GetCurrentEnvironment_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventGetCurrentEnvironment_Parms, ReturnValue), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2509470107
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_GetCurrentEnvironment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_GetCurrentEnvironment_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_GetCurrentEnvironment_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_GetCurrentEnvironment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_GetCurrentEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "GetCurrentEnvironment", Z_Construct_UFunction_AAURACRONPCGPortal_GetCurrentEnvironment_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_GetCurrentEnvironment_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_GetCurrentEnvironment_Statics::AURACRONPCGPortal_eventGetCurrentEnvironment_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_GetCurrentEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_GetCurrentEnvironment_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_GetCurrentEnvironment_Statics::AURACRONPCGPortal_eventGetCurrentEnvironment_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_GetCurrentEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_GetCurrentEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execGetCurrentEnvironment)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAURACRONEnvironmentType*)Z_Param__Result=P_THIS->GetCurrentEnvironment();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function GetCurrentEnvironment **************************

// ********** Begin Class AAURACRONPCGPortal Function GetDestinationLocation ***********************
struct Z_Construct_UFunction_AAURACRONPCGPortal_GetDestinationLocation_Statics
{
	struct AURACRONPCGPortal_eventGetDestinationLocation_Parms
	{
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter localiza\xc3\xa7\xc3\xa3o de destino */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter localiza\xc3\xa7\xc3\xa3o de destino" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_GetDestinationLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventGetDestinationLocation_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_GetDestinationLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_GetDestinationLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_GetDestinationLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_GetDestinationLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "GetDestinationLocation", Z_Construct_UFunction_AAURACRONPCGPortal_GetDestinationLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_GetDestinationLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_GetDestinationLocation_Statics::AURACRONPCGPortal_eventGetDestinationLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_GetDestinationLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_GetDestinationLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_GetDestinationLocation_Statics::AURACRONPCGPortal_eventGetDestinationLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_GetDestinationLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_GetDestinationLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execGetDestinationLocation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetDestinationLocation();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function GetDestinationLocation *************************

// ********** Begin Class AAURACRONPCGPortal Function GetDestinationRotation ***********************
struct Z_Construct_UFunction_AAURACRONPCGPortal_GetDestinationRotation_Statics
{
	struct AURACRONPCGPortal_eventGetDestinationRotation_Parms
	{
		FRotator ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter rota\xc3\xa7\xc3\xa3o de destino */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter rota\xc3\xa7\xc3\xa3o de destino" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_GetDestinationRotation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventGetDestinationRotation_Parms, ReturnValue), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_GetDestinationRotation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_GetDestinationRotation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_GetDestinationRotation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_GetDestinationRotation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "GetDestinationRotation", Z_Construct_UFunction_AAURACRONPCGPortal_GetDestinationRotation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_GetDestinationRotation_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_GetDestinationRotation_Statics::AURACRONPCGPortal_eventGetDestinationRotation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_GetDestinationRotation_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_GetDestinationRotation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_GetDestinationRotation_Statics::AURACRONPCGPortal_eventGetDestinationRotation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_GetDestinationRotation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_GetDestinationRotation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execGetDestinationRotation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FRotator*)Z_Param__Result=P_THIS->GetDestinationRotation();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function GetDestinationRotation *************************

// ********** Begin Class AAURACRONPCGPortal Function GetPortalColor *******************************
struct Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalColor_Statics
{
	struct AURACRONPCGPortal_eventGetPortalColor_Parms
	{
		FLinearColor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter cor do portal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter cor do portal" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalColor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventGetPortalColor_Parms, ReturnValue), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalColor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalColor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalColor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalColor_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "GetPortalColor", Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalColor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalColor_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalColor_Statics::AURACRONPCGPortal_eventGetPortalColor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalColor_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalColor_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalColor_Statics::AURACRONPCGPortal_eventGetPortalColor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalColor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalColor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execGetPortalColor)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FLinearColor*)Z_Param__Result=P_THIS->GetPortalColor();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function GetPortalColor *********************************

// ********** Begin Class AAURACRONPCGPortal Function GetPortalID **********************************
struct Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalID_Statics
{
	struct AURACRONPCGPortal_eventGetPortalID_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter ID do portal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter ID do portal" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalID_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventGetPortalID_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalID_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalID_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalID_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalID_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "GetPortalID", Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalID_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalID_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalID_Statics::AURACRONPCGPortal_eventGetPortalID_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalID_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalID_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalID_Statics::AURACRONPCGPortal_eventGetPortalID_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalID()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalID_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execGetPortalID)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetPortalID();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function GetPortalID ************************************

// ********** Begin Class AAURACRONPCGPortal Function GetPortalPhase *******************************
struct Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalPhase_Statics
{
	struct AURACRONPCGPortal_eventGetPortalPhase_Parms
	{
		EAURACRONMapPhase ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter fase do portal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter fase do portal" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalPhase_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalPhase_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventGetPortalPhase_Parms, ReturnValue), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalPhase_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalPhase_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "GetPortalPhase", Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalPhase_Statics::AURACRONPCGPortal_eventGetPortalPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalPhase_Statics::AURACRONPCGPortal_eventGetPortalPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execGetPortalPhase)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAURACRONMapPhase*)Z_Param__Result=P_THIS->GetPortalPhase();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function GetPortalPhase *********************************

// ********** Begin Class AAURACRONPCGPortal Function GetPortalType ********************************
struct Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalType_Statics
{
	struct AURACRONPCGPortal_eventGetPortalType_Parms
	{
		EAURACRONPortalType ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter tipo do portal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter tipo do portal" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalType_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventGetPortalType_Parms, ReturnValue), Z_Construct_UEnum_AURACRON_EAURACRONPortalType, METADATA_PARAMS(0, nullptr) }; // 1562177233
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalType_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "GetPortalType", Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalType_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalType_Statics::AURACRONPCGPortal_eventGetPortalType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalType_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalType_Statics::AURACRONPCGPortal_eventGetPortalType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execGetPortalType)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAURACRONPortalType*)Z_Param__Result=P_THIS->GetPortalType();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function GetPortalType **********************************

// ********** Begin Class AAURACRONPCGPortal Function InitializePortal *****************************
struct Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortal_Statics
{
	struct AURACRONPCGPortal_eventInitializePortal_Parms
	{
		FAURACRONPortalSettings Settings;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Inicializar portal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inicializar portal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Settings_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Settings;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortal_Statics::NewProp_Settings = { "Settings", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventInitializePortal_Parms, Settings), Z_Construct_UScriptStruct_FAURACRONPortalSettings, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Settings_MetaData), NewProp_Settings_MetaData) }; // 1501520788
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortal_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortal_Statics::NewProp_Settings,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortal_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortal_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "InitializePortal", Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortal_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortal_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortal_Statics::AURACRONPCGPortal_eventInitializePortal_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortal_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortal_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortal_Statics::AURACRONPCGPortal_eventInitializePortal_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortal()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortal_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execInitializePortal)
{
	P_GET_STRUCT_REF(FAURACRONPortalSettings,Z_Param_Out_Settings);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializePortal(Z_Param_Out_Settings);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function InitializePortal *******************************

// ********** Begin Class AAURACRONPCGPortal Function InitializePortalWithType *********************
struct Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortalWithType_Statics
{
	struct AURACRONPCGPortal_eventInitializePortalWithType_Parms
	{
		EAURACRONPortalType PortalType;
		FVector DestLocation;
		FRotator DestRotation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Inicializar portal com tipo espec\xc3\xad""fico */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inicializar portal com tipo espec\xc3\xad""fico" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_PortalType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PortalType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DestLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DestRotation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortalWithType_Statics::NewProp_PortalType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortalWithType_Statics::NewProp_PortalType = { "PortalType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventInitializePortalWithType_Parms, PortalType), Z_Construct_UEnum_AURACRON_EAURACRONPortalType, METADATA_PARAMS(0, nullptr) }; // 1562177233
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortalWithType_Statics::NewProp_DestLocation = { "DestLocation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventInitializePortalWithType_Parms, DestLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortalWithType_Statics::NewProp_DestRotation = { "DestRotation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventInitializePortalWithType_Parms, DestRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortalWithType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortalWithType_Statics::NewProp_PortalType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortalWithType_Statics::NewProp_PortalType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortalWithType_Statics::NewProp_DestLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortalWithType_Statics::NewProp_DestRotation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortalWithType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortalWithType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "InitializePortalWithType", Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortalWithType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortalWithType_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortalWithType_Statics::AURACRONPCGPortal_eventInitializePortalWithType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortalWithType_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortalWithType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortalWithType_Statics::AURACRONPCGPortal_eventInitializePortalWithType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortalWithType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortalWithType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execInitializePortalWithType)
{
	P_GET_ENUM(EAURACRONPortalType,Z_Param_PortalType);
	P_GET_STRUCT(FVector,Z_Param_DestLocation);
	P_GET_STRUCT(FRotator,Z_Param_DestRotation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializePortalWithType(EAURACRONPortalType(Z_Param_PortalType),Z_Param_DestLocation,Z_Param_DestRotation);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function InitializePortalWithType ***********************

// ********** Begin Class AAURACRONPCGPortal Function IsActive *************************************
struct Z_Construct_UFunction_AAURACRONPCGPortal_IsActive_Statics
{
	struct AURACRONPCGPortal_eventIsActive_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se est\xc3\xa1 ativo (alias) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se est\xc3\xa1 ativo (alias)" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGPortal_IsActive_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGPortal_eventIsActive_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_IsActive_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPortal_eventIsActive_Parms), &Z_Construct_UFunction_AAURACRONPCGPortal_IsActive_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_IsActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_IsActive_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_IsActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_IsActive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "IsActive", Z_Construct_UFunction_AAURACRONPCGPortal_IsActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_IsActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_IsActive_Statics::AURACRONPCGPortal_eventIsActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_IsActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_IsActive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_IsActive_Statics::AURACRONPCGPortal_eventIsActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_IsActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_IsActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execIsActive)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsActive();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function IsActive ***************************************

// ********** Begin Class AAURACRONPCGPortal Function IsPortalActive *******************************
struct Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalActive_Statics
{
	struct AURACRONPCGPortal_eventIsPortalActive_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se o portal est\xc3\xa1 ativo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se o portal est\xc3\xa1 ativo" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalActive_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGPortal_eventIsPortalActive_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalActive_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPortal_eventIsPortalActive_Parms), &Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalActive_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalActive_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalActive_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "IsPortalActive", Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalActive_Statics::AURACRONPCGPortal_eventIsPortalActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalActive_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalActive_Statics::AURACRONPCGPortal_eventIsPortalActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execIsPortalActive)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPortalActive();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function IsPortalActive *********************************

// ********** Begin Class AAURACRONPCGPortal Function IsPortalVisible ******************************
struct Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalVisible_Statics
{
	struct AURACRONPCGPortal_eventIsPortalVisible_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se o portal est\xc3\xa1 vis\xc3\xadvel */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se o portal est\xc3\xa1 vis\xc3\xadvel" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalVisible_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGPortal_eventIsPortalVisible_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalVisible_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPortal_eventIsPortalVisible_Parms), &Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalVisible_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalVisible_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalVisible_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalVisible_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalVisible_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "IsPortalVisible", Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalVisible_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalVisible_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalVisible_Statics::AURACRONPCGPortal_eventIsPortalVisible_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalVisible_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalVisible_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalVisible_Statics::AURACRONPCGPortal_eventIsPortalVisible_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalVisible()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalVisible_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execIsPortalVisible)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPortalVisible();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function IsPortalVisible ********************************

// ********** Begin Class AAURACRONPCGPortal Function IsRadiantPortal ******************************
struct Z_Construct_UFunction_AAURACRONPCGPortal_IsRadiantPortal_Statics
{
	struct AURACRONPCGPortal_eventIsRadiantPortal_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se \xc3\xa9 um portal radiante */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se \xc3\xa9 um portal radiante" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGPortal_IsRadiantPortal_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGPortal_eventIsRadiantPortal_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_IsRadiantPortal_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPortal_eventIsRadiantPortal_Parms), &Z_Construct_UFunction_AAURACRONPCGPortal_IsRadiantPortal_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_IsRadiantPortal_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_IsRadiantPortal_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_IsRadiantPortal_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_IsRadiantPortal_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "IsRadiantPortal", Z_Construct_UFunction_AAURACRONPCGPortal_IsRadiantPortal_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_IsRadiantPortal_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_IsRadiantPortal_Statics::AURACRONPCGPortal_eventIsRadiantPortal_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_IsRadiantPortal_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_IsRadiantPortal_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_IsRadiantPortal_Statics::AURACRONPCGPortal_eventIsRadiantPortal_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_IsRadiantPortal()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_IsRadiantPortal_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execIsRadiantPortal)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsRadiantPortal();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function IsRadiantPortal ********************************

// ********** Begin Class AAURACRONPCGPortal Function IsUmbralPortal *******************************
struct Z_Construct_UFunction_AAURACRONPCGPortal_IsUmbralPortal_Statics
{
	struct AURACRONPCGPortal_eventIsUmbralPortal_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se \xc3\xa9 um portal umbral */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se \xc3\xa9 um portal umbral" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGPortal_IsUmbralPortal_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGPortal_eventIsUmbralPortal_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_IsUmbralPortal_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPortal_eventIsUmbralPortal_Parms), &Z_Construct_UFunction_AAURACRONPCGPortal_IsUmbralPortal_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_IsUmbralPortal_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_IsUmbralPortal_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_IsUmbralPortal_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_IsUmbralPortal_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "IsUmbralPortal", Z_Construct_UFunction_AAURACRONPCGPortal_IsUmbralPortal_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_IsUmbralPortal_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_IsUmbralPortal_Statics::AURACRONPCGPortal_eventIsUmbralPortal_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_IsUmbralPortal_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_IsUmbralPortal_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_IsUmbralPortal_Statics::AURACRONPCGPortal_eventIsUmbralPortal_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_IsUmbralPortal()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_IsUmbralPortal_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execIsUmbralPortal)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsUmbralPortal();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function IsUmbralPortal *********************************

// ********** Begin Class AAURACRONPCGPortal Function IsVisible ************************************
struct Z_Construct_UFunction_AAURACRONPCGPortal_IsVisible_Statics
{
	struct AURACRONPCGPortal_eventIsVisible_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se est\xc3\xa1 vis\xc3\xadvel (alias) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se est\xc3\xa1 vis\xc3\xadvel (alias)" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGPortal_IsVisible_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGPortal_eventIsVisible_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_IsVisible_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPortal_eventIsVisible_Parms), &Z_Construct_UFunction_AAURACRONPCGPortal_IsVisible_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_IsVisible_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_IsVisible_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_IsVisible_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_IsVisible_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "IsVisible", Z_Construct_UFunction_AAURACRONPCGPortal_IsVisible_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_IsVisible_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_IsVisible_Statics::AURACRONPCGPortal_eventIsVisible_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_IsVisible_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_IsVisible_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_IsVisible_Statics::AURACRONPCGPortal_eventIsVisible_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_IsVisible()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_IsVisible_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execIsVisible)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsVisible();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function IsVisible **************************************

// ********** Begin Class AAURACRONPCGPortal Function IsZephyrPortal *******************************
struct Z_Construct_UFunction_AAURACRONPCGPortal_IsZephyrPortal_Statics
{
	struct AURACRONPCGPortal_eventIsZephyrPortal_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se \xc3\xa9 um portal zephyr */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se \xc3\xa9 um portal zephyr" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGPortal_IsZephyrPortal_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGPortal_eventIsZephyrPortal_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_IsZephyrPortal_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPortal_eventIsZephyrPortal_Parms), &Z_Construct_UFunction_AAURACRONPCGPortal_IsZephyrPortal_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_IsZephyrPortal_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_IsZephyrPortal_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_IsZephyrPortal_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_IsZephyrPortal_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "IsZephyrPortal", Z_Construct_UFunction_AAURACRONPCGPortal_IsZephyrPortal_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_IsZephyrPortal_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_IsZephyrPortal_Statics::AURACRONPCGPortal_eventIsZephyrPortal_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_IsZephyrPortal_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_IsZephyrPortal_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_IsZephyrPortal_Statics::AURACRONPCGPortal_eventIsZephyrPortal_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_IsZephyrPortal()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_IsZephyrPortal_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execIsZephyrPortal)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsZephyrPortal();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function IsZephyrPortal *********************************

// ********** Begin Class AAURACRONPCGPortal Function OnOverlapBegin *******************************
struct Z_Construct_UFunction_AAURACRONPCGPortal_OnOverlapBegin_Statics
{
	struct AURACRONPCGPortal_eventOnOverlapBegin_Parms
	{
		UPrimitiveComponent* OverlappedComponent;
		AActor* OtherActor;
		UPrimitiveComponent* OtherComp;
		int32 OtherBodyIndex;
		bool bFromSweep;
		FHitResult SweepResult;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Callback para sobreposi\xc3\xa7\xc3\xa3o com ator */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Callback para sobreposi\xc3\xa7\xc3\xa3o com ator" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverlappedComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OtherComp_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SweepResult_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OverlappedComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherComp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OtherBodyIndex;
	static void NewProp_bFromSweep_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFromSweep;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SweepResult;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_OnOverlapBegin_Statics::NewProp_OverlappedComponent = { "OverlappedComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventOnOverlapBegin_Parms, OverlappedComponent), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverlappedComponent_MetaData), NewProp_OverlappedComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_OnOverlapBegin_Statics::NewProp_OtherActor = { "OtherActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventOnOverlapBegin_Parms, OtherActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_OnOverlapBegin_Statics::NewProp_OtherComp = { "OtherComp", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventOnOverlapBegin_Parms, OtherComp), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OtherComp_MetaData), NewProp_OtherComp_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_OnOverlapBegin_Statics::NewProp_OtherBodyIndex = { "OtherBodyIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventOnOverlapBegin_Parms, OtherBodyIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAURACRONPCGPortal_OnOverlapBegin_Statics::NewProp_bFromSweep_SetBit(void* Obj)
{
	((AURACRONPCGPortal_eventOnOverlapBegin_Parms*)Obj)->bFromSweep = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_OnOverlapBegin_Statics::NewProp_bFromSweep = { "bFromSweep", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPortal_eventOnOverlapBegin_Parms), &Z_Construct_UFunction_AAURACRONPCGPortal_OnOverlapBegin_Statics::NewProp_bFromSweep_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_OnOverlapBegin_Statics::NewProp_SweepResult = { "SweepResult", nullptr, (EPropertyFlags)0x0010008008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventOnOverlapBegin_Parms, SweepResult), Z_Construct_UScriptStruct_FHitResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SweepResult_MetaData), NewProp_SweepResult_MetaData) }; // 267591329
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_OnOverlapBegin_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_OnOverlapBegin_Statics::NewProp_OverlappedComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_OnOverlapBegin_Statics::NewProp_OtherActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_OnOverlapBegin_Statics::NewProp_OtherComp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_OnOverlapBegin_Statics::NewProp_OtherBodyIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_OnOverlapBegin_Statics::NewProp_bFromSweep,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_OnOverlapBegin_Statics::NewProp_SweepResult,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_OnOverlapBegin_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_OnOverlapBegin_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "OnOverlapBegin", Z_Construct_UFunction_AAURACRONPCGPortal_OnOverlapBegin_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_OnOverlapBegin_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_OnOverlapBegin_Statics::AURACRONPCGPortal_eventOnOverlapBegin_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00440401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_OnOverlapBegin_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_OnOverlapBegin_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_OnOverlapBegin_Statics::AURACRONPCGPortal_eventOnOverlapBegin_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_OnOverlapBegin()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_OnOverlapBegin_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execOnOverlapBegin)
{
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OverlappedComponent);
	P_GET_OBJECT(AActor,Z_Param_OtherActor);
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OtherComp);
	P_GET_PROPERTY(FIntProperty,Z_Param_OtherBodyIndex);
	P_GET_UBOOL(Z_Param_bFromSweep);
	P_GET_STRUCT_REF(FHitResult,Z_Param_Out_SweepResult);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnOverlapBegin(Z_Param_OverlappedComponent,Z_Param_OtherActor,Z_Param_OtherComp,Z_Param_OtherBodyIndex,Z_Param_bFromSweep,Z_Param_Out_SweepResult);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function OnOverlapBegin *********************************

// ********** Begin Class AAURACRONPCGPortal Function OnRep_PortalLifetime *************************
struct Z_Construct_UFunction_AAURACRONPCGPortal_OnRep_PortalLifetime_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Replica\xc3\xa7\xc3\xa3o do tempo de vida do portal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Replica\xc3\xa7\xc3\xa3o do tempo de vida do portal" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_OnRep_PortalLifetime_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "OnRep_PortalLifetime", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_OnRep_PortalLifetime_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_OnRep_PortalLifetime_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_OnRep_PortalLifetime()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_OnRep_PortalLifetime_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execOnRep_PortalLifetime)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnRep_PortalLifetime();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function OnRep_PortalLifetime ***************************

// ********** Begin Class AAURACRONPCGPortal Function ServerRequestTeleport ************************
struct AURACRONPCGPortal_eventServerRequestTeleport_Parms
{
	ACharacter* PlayerCharacter;
};
static FName NAME_AAURACRONPCGPortal_ServerRequestTeleport = FName(TEXT("ServerRequestTeleport"));
void AAURACRONPCGPortal::ServerRequestTeleport(ACharacter* PlayerCharacter)
{
	AURACRONPCGPortal_eventServerRequestTeleport_Parms Parms;
	Parms.PlayerCharacter=PlayerCharacter;
	UFunction* Func = FindFunctionChecked(NAME_AAURACRONPCGPortal_ServerRequestTeleport);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AAURACRONPCGPortal_ServerRequestTeleport_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** RPC do servidor para solicitar teletransporte */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "RPC do servidor para solicitar teletransporte" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerCharacter;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_ServerRequestTeleport_Statics::NewProp_PlayerCharacter = { "PlayerCharacter", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventServerRequestTeleport_Parms, PlayerCharacter), Z_Construct_UClass_ACharacter_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_ServerRequestTeleport_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_ServerRequestTeleport_Statics::NewProp_PlayerCharacter,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_ServerRequestTeleport_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_ServerRequestTeleport_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "ServerRequestTeleport", Z_Construct_UFunction_AAURACRONPCGPortal_ServerRequestTeleport_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_ServerRequestTeleport_Statics::PropPointers), sizeof(AURACRONPCGPortal_eventServerRequestTeleport_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00240CC1, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_ServerRequestTeleport_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_ServerRequestTeleport_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONPCGPortal_eventServerRequestTeleport_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_ServerRequestTeleport()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_ServerRequestTeleport_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execServerRequestTeleport)
{
	P_GET_OBJECT(ACharacter,Z_Param_PlayerCharacter);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ServerRequestTeleport_Implementation(Z_Param_PlayerCharacter);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function ServerRequestTeleport **************************

// ********** Begin Class AAURACRONPCGPortal Function SetActivationRange ***************************
struct Z_Construct_UFunction_AAURACRONPCGPortal_SetActivationRange_Statics
{
	struct AURACRONPCGPortal_eventSetActivationRange_Parms
	{
		float NewRange;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Definir raio de ativa\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir raio de ativa\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewRange;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_SetActivationRange_Statics::NewProp_NewRange = { "NewRange", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventSetActivationRange_Parms, NewRange), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_SetActivationRange_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_SetActivationRange_Statics::NewProp_NewRange,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_SetActivationRange_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_SetActivationRange_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "SetActivationRange", Z_Construct_UFunction_AAURACRONPCGPortal_SetActivationRange_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_SetActivationRange_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_SetActivationRange_Statics::AURACRONPCGPortal_eventSetActivationRange_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_SetActivationRange_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_SetActivationRange_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_SetActivationRange_Statics::AURACRONPCGPortal_eventSetActivationRange_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_SetActivationRange()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_SetActivationRange_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execSetActivationRange)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewRange);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetActivationRange(Z_Param_NewRange);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function SetActivationRange *****************************

// ********** Begin Class AAURACRONPCGPortal Function SetPortalVisibility **************************
struct Z_Construct_UFunction_AAURACRONPCGPortal_SetPortalVisibility_Statics
{
	struct AURACRONPCGPortal_eventSetPortalVisibility_Parms
	{
		bool bVisible;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Definir visibilidade do portal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir visibilidade do portal" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bVisible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bVisible;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGPortal_SetPortalVisibility_Statics::NewProp_bVisible_SetBit(void* Obj)
{
	((AURACRONPCGPortal_eventSetPortalVisibility_Parms*)Obj)->bVisible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_SetPortalVisibility_Statics::NewProp_bVisible = { "bVisible", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPortal_eventSetPortalVisibility_Parms), &Z_Construct_UFunction_AAURACRONPCGPortal_SetPortalVisibility_Statics::NewProp_bVisible_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_SetPortalVisibility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_SetPortalVisibility_Statics::NewProp_bVisible,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_SetPortalVisibility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_SetPortalVisibility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "SetPortalVisibility", Z_Construct_UFunction_AAURACRONPCGPortal_SetPortalVisibility_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_SetPortalVisibility_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_SetPortalVisibility_Statics::AURACRONPCGPortal_eventSetPortalVisibility_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_SetPortalVisibility_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_SetPortalVisibility_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_SetPortalVisibility_Statics::AURACRONPCGPortal_eventSetPortalVisibility_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_SetPortalVisibility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_SetPortalVisibility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execSetPortalVisibility)
{
	P_GET_UBOOL(Z_Param_bVisible);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPortalVisibility(Z_Param_bVisible);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function SetPortalVisibility ****************************

// ********** Begin Class AAURACRONPCGPortal Function StartFadeInEffect ****************************
struct Z_Construct_UFunction_AAURACRONPCGPortal_StartFadeInEffect_Statics
{
	struct AURACRONPCGPortal_eventStartFadeInEffect_Parms
	{
		ACharacter* PlayerCharacter;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Iniciar efeito de Fade In ap\xc3\xb3s o teletransporte */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Iniciar efeito de Fade In ap\xc3\xb3s o teletransporte" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerCharacter;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_StartFadeInEffect_Statics::NewProp_PlayerCharacter = { "PlayerCharacter", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventStartFadeInEffect_Parms, PlayerCharacter), Z_Construct_UClass_ACharacter_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_StartFadeInEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_StartFadeInEffect_Statics::NewProp_PlayerCharacter,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_StartFadeInEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_StartFadeInEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "StartFadeInEffect", Z_Construct_UFunction_AAURACRONPCGPortal_StartFadeInEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_StartFadeInEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_StartFadeInEffect_Statics::AURACRONPCGPortal_eventStartFadeInEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_StartFadeInEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_StartFadeInEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_StartFadeInEffect_Statics::AURACRONPCGPortal_eventStartFadeInEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_StartFadeInEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_StartFadeInEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execStartFadeInEffect)
{
	P_GET_OBJECT(ACharacter,Z_Param_PlayerCharacter);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartFadeInEffect(Z_Param_PlayerCharacter);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function StartFadeInEffect ******************************

// ********** Begin Class AAURACRONPCGPortal Function StartFadeOutEffect ***************************
struct Z_Construct_UFunction_AAURACRONPCGPortal_StartFadeOutEffect_Statics
{
	struct AURACRONPCGPortal_eventStartFadeOutEffect_Parms
	{
		ACharacter* PlayerCharacter;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Iniciar efeito de Fade Out antes do teletransporte */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Iniciar efeito de Fade Out antes do teletransporte" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerCharacter;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_StartFadeOutEffect_Statics::NewProp_PlayerCharacter = { "PlayerCharacter", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventStartFadeOutEffect_Parms, PlayerCharacter), Z_Construct_UClass_ACharacter_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_StartFadeOutEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_StartFadeOutEffect_Statics::NewProp_PlayerCharacter,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_StartFadeOutEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_StartFadeOutEffect_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "StartFadeOutEffect", Z_Construct_UFunction_AAURACRONPCGPortal_StartFadeOutEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_StartFadeOutEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_StartFadeOutEffect_Statics::AURACRONPCGPortal_eventStartFadeOutEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_StartFadeOutEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_StartFadeOutEffect_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_StartFadeOutEffect_Statics::AURACRONPCGPortal_eventStartFadeOutEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_StartFadeOutEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_StartFadeOutEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execStartFadeOutEffect)
{
	P_GET_OBJECT(ACharacter,Z_Param_PlayerCharacter);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartFadeOutEffect(Z_Param_PlayerCharacter);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function StartFadeOutEffect *****************************

// ********** Begin Class AAURACRONPCGPortal Function TeleportPlayerToDestination ******************
struct Z_Construct_UFunction_AAURACRONPCGPortal_TeleportPlayerToDestination_Statics
{
	struct AURACRONPCGPortal_eventTeleportPlayerToDestination_Parms
	{
		ACharacter* PlayerCharacter;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Teletransportar jogador para destino com efeito de Fade Out/In */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Teletransportar jogador para destino com efeito de Fade Out/In" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerCharacter;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_TeleportPlayerToDestination_Statics::NewProp_PlayerCharacter = { "PlayerCharacter", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventTeleportPlayerToDestination_Parms, PlayerCharacter), Z_Construct_UClass_ACharacter_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_TeleportPlayerToDestination_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_TeleportPlayerToDestination_Statics::NewProp_PlayerCharacter,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_TeleportPlayerToDestination_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_TeleportPlayerToDestination_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "TeleportPlayerToDestination", Z_Construct_UFunction_AAURACRONPCGPortal_TeleportPlayerToDestination_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_TeleportPlayerToDestination_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_TeleportPlayerToDestination_Statics::AURACRONPCGPortal_eventTeleportPlayerToDestination_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_TeleportPlayerToDestination_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_TeleportPlayerToDestination_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_TeleportPlayerToDestination_Statics::AURACRONPCGPortal_eventTeleportPlayerToDestination_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_TeleportPlayerToDestination()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_TeleportPlayerToDestination_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execTeleportPlayerToDestination)
{
	P_GET_OBJECT(ACharacter,Z_Param_PlayerCharacter);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->TeleportPlayerToDestination(Z_Param_PlayerCharacter);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function TeleportPlayerToDestination ********************

// ********** Begin Class AAURACRONPCGPortal Function UpdateForMapPhase ****************************
struct Z_Construct_UFunction_AAURACRONPCGPortal_UpdateForMapPhase_Statics
{
	struct AURACRONPCGPortal_eventUpdateForMapPhase_Parms
	{
		EAURACRONMapPhase MapPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualizar para fase do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar para fase do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MapPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGPortal_UpdateForMapPhase_Statics::NewProp_MapPhase = { "MapPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPortal_eventUpdateForMapPhase_Parms, MapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPortal_UpdateForMapPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPortal_UpdateForMapPhase_Statics::NewProp_MapPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_UpdateForMapPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPortal_UpdateForMapPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPortal, nullptr, "UpdateForMapPhase", Z_Construct_UFunction_AAURACRONPCGPortal_UpdateForMapPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_UpdateForMapPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_UpdateForMapPhase_Statics::AURACRONPCGPortal_eventUpdateForMapPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPortal_UpdateForMapPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPortal_UpdateForMapPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPortal_UpdateForMapPhase_Statics::AURACRONPCGPortal_eventUpdateForMapPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPortal_UpdateForMapPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPortal_UpdateForMapPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPortal::execUpdateForMapPhase)
{
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_MapPhase);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateForMapPhase(EAURACRONMapPhase(Z_Param_MapPhase));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPortal Function UpdateForMapPhase ******************************

// ********** Begin Class AAURACRONPCGPortal *******************************************************
void AAURACRONPCGPortal::StaticRegisterNativesAAURACRONPCGPortal()
{
	UClass* Class = AAURACRONPCGPortal::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ActivatePortal", &AAURACRONPCGPortal::execActivatePortal },
		{ "ApplyTeleportEffect", &AAURACRONPCGPortal::execApplyTeleportEffect },
		{ "ClientNotifyPortalActivation", &AAURACRONPCGPortal::execClientNotifyPortalActivation },
		{ "CreateRadiantPortal", &AAURACRONPCGPortal::execCreateRadiantPortal },
		{ "CreateUmbralPortal", &AAURACRONPCGPortal::execCreateUmbralPortal },
		{ "CreateZephyrPortal", &AAURACRONPCGPortal::execCreateZephyrPortal },
		{ "DeactivatePortal", &AAURACRONPCGPortal::execDeactivatePortal },
		{ "GetActivationRadius", &AAURACRONPCGPortal::execGetActivationRadius },
		{ "GetActivationRange", &AAURACRONPCGPortal::execGetActivationRange },
		{ "GetCurrentEnvironment", &AAURACRONPCGPortal::execGetCurrentEnvironment },
		{ "GetDestinationLocation", &AAURACRONPCGPortal::execGetDestinationLocation },
		{ "GetDestinationRotation", &AAURACRONPCGPortal::execGetDestinationRotation },
		{ "GetPortalColor", &AAURACRONPCGPortal::execGetPortalColor },
		{ "GetPortalID", &AAURACRONPCGPortal::execGetPortalID },
		{ "GetPortalPhase", &AAURACRONPCGPortal::execGetPortalPhase },
		{ "GetPortalType", &AAURACRONPCGPortal::execGetPortalType },
		{ "InitializePortal", &AAURACRONPCGPortal::execInitializePortal },
		{ "InitializePortalWithType", &AAURACRONPCGPortal::execInitializePortalWithType },
		{ "IsActive", &AAURACRONPCGPortal::execIsActive },
		{ "IsPortalActive", &AAURACRONPCGPortal::execIsPortalActive },
		{ "IsPortalVisible", &AAURACRONPCGPortal::execIsPortalVisible },
		{ "IsRadiantPortal", &AAURACRONPCGPortal::execIsRadiantPortal },
		{ "IsUmbralPortal", &AAURACRONPCGPortal::execIsUmbralPortal },
		{ "IsVisible", &AAURACRONPCGPortal::execIsVisible },
		{ "IsZephyrPortal", &AAURACRONPCGPortal::execIsZephyrPortal },
		{ "OnOverlapBegin", &AAURACRONPCGPortal::execOnOverlapBegin },
		{ "OnRep_PortalLifetime", &AAURACRONPCGPortal::execOnRep_PortalLifetime },
		{ "ServerRequestTeleport", &AAURACRONPCGPortal::execServerRequestTeleport },
		{ "SetActivationRange", &AAURACRONPCGPortal::execSetActivationRange },
		{ "SetPortalVisibility", &AAURACRONPCGPortal::execSetPortalVisibility },
		{ "StartFadeInEffect", &AAURACRONPCGPortal::execStartFadeInEffect },
		{ "StartFadeOutEffect", &AAURACRONPCGPortal::execStartFadeOutEffect },
		{ "TeleportPlayerToDestination", &AAURACRONPCGPortal::execTeleportPlayerToDestination },
		{ "UpdateForMapPhase", &AAURACRONPCGPortal::execUpdateForMapPhase },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAURACRONPCGPortal;
UClass* AAURACRONPCGPortal::GetPrivateStaticClass()
{
	using TClass = AAURACRONPCGPortal;
	if (!Z_Registration_Info_UClass_AAURACRONPCGPortal.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONPCGPortal"),
			Z_Registration_Info_UClass_AAURACRONPCGPortal.InnerSingleton,
			StaticRegisterNativesAAURACRONPCGPortal,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGPortal.InnerSingleton;
}
UClass* Z_Construct_UClass_AAURACRONPCGPortal_NoRegister()
{
	return AAURACRONPCGPortal::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAURACRONPCGPortal_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Classe para portais de posicionamento t\xc3\xa1tico\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGPortal.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Classe para portais de posicionamento t\xc3\xa1tico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PortalMesh_MetaData[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de malha est\xc3\xa1tica */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de malha est\xc3\xa1tica" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivationSphere_MetaData[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de colis\xc3\xa3o */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de colis\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PortalEffect_MetaData[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de efeito de part\xc3\xad""culas */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de efeito de part\xc3\xad""culas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PortalParticleSystem_MetaData[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sistema de part\xc3\xad""culas para o portal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de part\xc3\xad""culas para o portal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeleportEffect_MetaData[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeito Niagara para teletransporte */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito Niagara para teletransporte" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeleportSound_MetaData[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Som de teletransporte */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Som de teletransporte" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PortalSettings_MetaData[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xb5""es do portal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es do portal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentMapPhase_MetaData[] = {
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Fase atual do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fase atual do mapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PortalDynamicMaterial_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Material din\xc3\xa2mico para o portal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material din\xc3\xa2mico para o portal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PortalLifetime_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo de vida do portal */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo de vida do portal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RotationSpeed_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Velocidade de rota\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade de rota\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PulsateIntensity_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade de pulsa\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade de pulsa\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PulsateFrequency_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Frequ\xc3\xaancia de pulsa\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Frequ\xc3\xaancia de pulsa\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FadeTransitionDuration_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\xa7\xc3\xa3o do efeito de Fade Out/In em segundos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o do efeito de Fade Out/In em segundos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FadeTimerHandle_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Temporizador para controlar o efeito de Fade */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Temporizador para controlar o efeito de Fade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeleportingCharacter_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Personagem atualmente em processo de teletransporte */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Personagem atualmente em processo de teletransporte" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentFadeValue_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Valor atual do efeito de Fade (0.0 a 1.0) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valor atual do efeito de Fade (0.0 a 1.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AudioComponent_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de \xc3\xa1udio para sons do portal */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de \xc3\xa1udio para sons do portal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PortalLight_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "AURACRON|Portal" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de luz do portal */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de luz do portal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsLoadingAssets_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Flag indicando se assets est\xc3\xa3o sendo carregados */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Flag indicando se assets est\xc3\xa3o sendo carregados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisualEffectsTimerHandle_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timer para atualiza\xc3\xa7\xc3\xa3o de efeitos visuais */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPortal.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timer para atualiza\xc3\xa7\xc3\xa3o de efeitos visuais" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PortalMesh;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActivationSphere;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PortalEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PortalParticleSystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TeleportEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TeleportSound;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PortalSettings;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentMapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentMapPhase;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PortalDynamicMaterial;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PortalLifetime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RotationSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PulsateIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PulsateFrequency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FadeTransitionDuration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FadeTimerHandle;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TeleportingCharacter;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentFadeValue;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AudioComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PortalLight;
	static void NewProp_bIsLoadingAssets_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsLoadingAssets;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VisualEffectsTimerHandle;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_ActivatePortal, "ActivatePortal" }, // 3600507024
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_ApplyTeleportEffect, "ApplyTeleportEffect" }, // 946628536
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_ClientNotifyPortalActivation, "ClientNotifyPortalActivation" }, // 2109876670
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_CreateRadiantPortal, "CreateRadiantPortal" }, // 551373581
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_CreateUmbralPortal, "CreateUmbralPortal" }, // 1704594187
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_CreateZephyrPortal, "CreateZephyrPortal" }, // 1358351696
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_DeactivatePortal, "DeactivatePortal" }, // 948712632
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_GetActivationRadius, "GetActivationRadius" }, // 609318640
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_GetActivationRange, "GetActivationRange" }, // 2231237400
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_GetCurrentEnvironment, "GetCurrentEnvironment" }, // 1194437593
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_GetDestinationLocation, "GetDestinationLocation" }, // 3577679154
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_GetDestinationRotation, "GetDestinationRotation" }, // 1262596873
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalColor, "GetPortalColor" }, // 1762798949
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalID, "GetPortalID" }, // 2183684448
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalPhase, "GetPortalPhase" }, // 2895333843
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_GetPortalType, "GetPortalType" }, // 1527233184
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortal, "InitializePortal" }, // 3354012572
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_InitializePortalWithType, "InitializePortalWithType" }, // 3852218074
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_IsActive, "IsActive" }, // 3570446294
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalActive, "IsPortalActive" }, // 122771813
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_IsPortalVisible, "IsPortalVisible" }, // 3460001112
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_IsRadiantPortal, "IsRadiantPortal" }, // 2977776791
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_IsUmbralPortal, "IsUmbralPortal" }, // 3817202258
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_IsVisible, "IsVisible" }, // 1480422621
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_IsZephyrPortal, "IsZephyrPortal" }, // 283511880
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_OnOverlapBegin, "OnOverlapBegin" }, // 4093248157
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_OnRep_PortalLifetime, "OnRep_PortalLifetime" }, // 276326068
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_ServerRequestTeleport, "ServerRequestTeleport" }, // 1948672704
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_SetActivationRange, "SetActivationRange" }, // 1331746655
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_SetPortalVisibility, "SetPortalVisibility" }, // 1413950504
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_StartFadeInEffect, "StartFadeInEffect" }, // 1672724228
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_StartFadeOutEffect, "StartFadeOutEffect" }, // 2680972056
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_TeleportPlayerToDestination, "TeleportPlayerToDestination" }, // 3947571116
		{ &Z_Construct_UFunction_AAURACRONPCGPortal_UpdateForMapPhase, "UpdateForMapPhase" }, // 3408403480
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAURACRONPCGPortal>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_PortalMesh = { "PortalMesh", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPortal, PortalMesh), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PortalMesh_MetaData), NewProp_PortalMesh_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_ActivationSphere = { "ActivationSphere", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPortal, ActivationSphere), Z_Construct_UClass_USphereComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivationSphere_MetaData), NewProp_ActivationSphere_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_PortalEffect = { "PortalEffect", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPortal, PortalEffect), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PortalEffect_MetaData), NewProp_PortalEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_PortalParticleSystem = { "PortalParticleSystem", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPortal, PortalParticleSystem), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PortalParticleSystem_MetaData), NewProp_PortalParticleSystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_TeleportEffect = { "TeleportEffect", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPortal, TeleportEffect), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeleportEffect_MetaData), NewProp_TeleportEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_TeleportSound = { "TeleportSound", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPortal, TeleportSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeleportSound_MetaData), NewProp_TeleportSound_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_PortalSettings = { "PortalSettings", nullptr, (EPropertyFlags)0x0020080000000025, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPortal, PortalSettings), Z_Construct_UScriptStruct_FAURACRONPortalSettings, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PortalSettings_MetaData), NewProp_PortalSettings_MetaData) }; // 1501520788
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_CurrentMapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_CurrentMapPhase = { "CurrentMapPhase", nullptr, (EPropertyFlags)0x0020080000000034, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPortal, CurrentMapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentMapPhase_MetaData), NewProp_CurrentMapPhase_MetaData) }; // 2541365769
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_PortalDynamicMaterial = { "PortalDynamicMaterial", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPortal, PortalDynamicMaterial), Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PortalDynamicMaterial_MetaData), NewProp_PortalDynamicMaterial_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_PortalLifetime = { "PortalLifetime", "OnRep_PortalLifetime", (EPropertyFlags)0x0040000100000020, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPortal, PortalLifetime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PortalLifetime_MetaData), NewProp_PortalLifetime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_RotationSpeed = { "RotationSpeed", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPortal, RotationSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RotationSpeed_MetaData), NewProp_RotationSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_PulsateIntensity = { "PulsateIntensity", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPortal, PulsateIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PulsateIntensity_MetaData), NewProp_PulsateIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_PulsateFrequency = { "PulsateFrequency", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPortal, PulsateFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PulsateFrequency_MetaData), NewProp_PulsateFrequency_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_FadeTransitionDuration = { "FadeTransitionDuration", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPortal, FadeTransitionDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FadeTransitionDuration_MetaData), NewProp_FadeTransitionDuration_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_FadeTimerHandle = { "FadeTimerHandle", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPortal, FadeTimerHandle), Z_Construct_UScriptStruct_FTimerHandle, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FadeTimerHandle_MetaData), NewProp_FadeTimerHandle_MetaData) }; // 3834150579
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_TeleportingCharacter = { "TeleportingCharacter", nullptr, (EPropertyFlags)0x0040000000000020, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPortal, TeleportingCharacter), Z_Construct_UClass_ACharacter_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeleportingCharacter_MetaData), NewProp_TeleportingCharacter_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_CurrentFadeValue = { "CurrentFadeValue", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPortal, CurrentFadeValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentFadeValue_MetaData), NewProp_CurrentFadeValue_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_AudioComponent = { "AudioComponent", nullptr, (EPropertyFlags)0x00400000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPortal, AudioComponent), Z_Construct_UClass_UAudioComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AudioComponent_MetaData), NewProp_AudioComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_PortalLight = { "PortalLight", nullptr, (EPropertyFlags)0x00400000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPortal, PortalLight), Z_Construct_UClass_UPointLightComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PortalLight_MetaData), NewProp_PortalLight_MetaData) };
void Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_bIsLoadingAssets_SetBit(void* Obj)
{
	((AAURACRONPCGPortal*)Obj)->bIsLoadingAssets = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_bIsLoadingAssets = { "bIsLoadingAssets", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGPortal), &Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_bIsLoadingAssets_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsLoadingAssets_MetaData), NewProp_bIsLoadingAssets_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_VisualEffectsTimerHandle = { "VisualEffectsTimerHandle", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPortal, VisualEffectsTimerHandle), Z_Construct_UScriptStruct_FTimerHandle, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisualEffectsTimerHandle_MetaData), NewProp_VisualEffectsTimerHandle_MetaData) }; // 3834150579
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAURACRONPCGPortal_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_PortalMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_ActivationSphere,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_PortalEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_PortalParticleSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_TeleportEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_TeleportSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_PortalSettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_CurrentMapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_CurrentMapPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_PortalDynamicMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_PortalLifetime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_RotationSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_PulsateIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_PulsateFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_FadeTransitionDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_FadeTimerHandle,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_TeleportingCharacter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_CurrentFadeValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_AudioComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_PortalLight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_bIsLoadingAssets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPortal_Statics::NewProp_VisualEffectsTimerHandle,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGPortal_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAURACRONPCGPortal_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGPortal_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAURACRONPCGPortal_Statics::ClassParams = {
	&AAURACRONPCGPortal::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAURACRONPCGPortal_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGPortal_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGPortal_Statics::Class_MetaDataParams), Z_Construct_UClass_AAURACRONPCGPortal_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAURACRONPCGPortal()
{
	if (!Z_Registration_Info_UClass_AAURACRONPCGPortal.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAURACRONPCGPortal.OuterSingleton, Z_Construct_UClass_AAURACRONPCGPortal_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGPortal.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void AAURACRONPCGPortal::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_PortalSettings(TEXT("PortalSettings"));
	static FName Name_CurrentMapPhase(TEXT("CurrentMapPhase"));
	static FName Name_PortalLifetime(TEXT("PortalLifetime"));
	static FName Name_TeleportingCharacter(TEXT("TeleportingCharacter"));
	const bool bIsValid = true
		&& Name_PortalSettings == ClassReps[(int32)ENetFields_Private::PortalSettings].Property->GetFName()
		&& Name_CurrentMapPhase == ClassReps[(int32)ENetFields_Private::CurrentMapPhase].Property->GetFName()
		&& Name_PortalLifetime == ClassReps[(int32)ENetFields_Private::PortalLifetime].Property->GetFName()
		&& Name_TeleportingCharacter == ClassReps[(int32)ENetFields_Private::TeleportingCharacter].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in AAURACRONPCGPortal"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(AAURACRONPCGPortal);
AAURACRONPCGPortal::~AAURACRONPCGPortal() {}
// ********** End Class AAURACRONPCGPortal *********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPortal_h__Script_AURACRON_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAURACRONPortalType_StaticEnum, TEXT("EAURACRONPortalType"), &Z_Registration_Info_UEnum_EAURACRONPortalType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1562177233U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAURACRONPortalSettings::StaticStruct, Z_Construct_UScriptStruct_FAURACRONPortalSettings_Statics::NewStructOps, TEXT("AURACRONPortalSettings"), &Z_Registration_Info_UScriptStruct_FAURACRONPortalSettings, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONPortalSettings), 1501520788U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAURACRONPCGPortal, AAURACRONPCGPortal::StaticClass, TEXT("AAURACRONPCGPortal"), &Z_Registration_Info_UClass_AAURACRONPCGPortal, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAURACRONPCGPortal), 2009176297U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPortal_h__Script_AURACRON_1783253458(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPortal_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPortal_h__Script_AURACRON_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPortal_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPortal_h__Script_AURACRON_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPortal_h__Script_AURACRON_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPortal_h__Script_AURACRON_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
