// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PCG/AURACRONPCGPrismalFlow.h"

#ifdef AURACRON_AURACRONPCGPrismalFlow_generated_h
#error "AURACRONPCGPrismalFlow.generated.h already included, missing '#pragma once' in AURACRONPCGPrismalFlow.h"
#endif
#define AURACRON_AURACRONPCGPrismalFlow_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class APrismalFlowIsland;
class UPCGComponent;
class UPrimitiveComponent;
class UStaticMeshComponent;
enum class EAURACRONEnvironmentType : uint8;
enum class EAURACRONMapPhase : uint8;
enum class EPrismalFlowIslandType : uint8;
struct FAURACRONPCGStreamingConfig;
struct FHitResult;
struct FLinearColor;

// ********** Begin Class APrismalFlowIsland *******************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h_56_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetFlowPosition); \
	DECLARE_FUNCTION(execSetFlowPosition); \
	DECLARE_FUNCTION(execGetIslandType); \
	DECLARE_FUNCTION(execSetIslandType); \
	DECLARE_FUNCTION(execApplyIslandEffect); \
	DECLARE_FUNCTION(execSetIslandActive);


AURACRON_API UClass* Z_Construct_UClass_APrismalFlowIsland_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h_56_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAPrismalFlowIsland(); \
	friend struct Z_Construct_UClass_APrismalFlowIsland_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_APrismalFlowIsland_NoRegister(); \
public: \
	DECLARE_CLASS2(APrismalFlowIsland, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_APrismalFlowIsland_NoRegister) \
	DECLARE_SERIALIZER(APrismalFlowIsland)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h_56_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	APrismalFlowIsland(APrismalFlowIsland&&) = delete; \
	APrismalFlowIsland(const APrismalFlowIsland&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, APrismalFlowIsland); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(APrismalFlowIsland); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(APrismalFlowIsland) \
	NO_API virtual ~APrismalFlowIsland();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h_53_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h_56_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h_56_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h_56_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h_56_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class APrismalFlowIsland;

// ********** End Class APrismalFlowIsland *********************************************************

// ********** Begin Class AAURACRONPCGPrismalFlow **************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h_133_RPC_WRAPPERS_NO_PURE_DECLS \
	virtual void MulticastUpdateControllingTeam_Implementation(int32 NewTeam, FLinearColor const& NewColor); \
	virtual bool ServerUpdateControllingTeam_Validate(int32 ); \
	virtual void ServerUpdateControllingTeam_Implementation(int32 NewTeam); \
	virtual void MulticastUpdateFlowIntensity_Implementation(float NewIntensity); \
	virtual bool ServerUpdateFlowIntensity_Validate(float ); \
	virtual void ServerUpdateFlowIntensity_Implementation(float NewIntensity); \
	DECLARE_FUNCTION(execMulticastUpdateControllingTeam); \
	DECLARE_FUNCTION(execServerUpdateControllingTeam); \
	DECLARE_FUNCTION(execMulticastUpdateFlowIntensity); \
	DECLARE_FUNCTION(execServerUpdateFlowIntensity); \
	DECLARE_FUNCTION(execApplyEnvironmentAssetsToComponents); \
	DECLARE_FUNCTION(execOnTransitionMaterialLoaded); \
	DECLARE_FUNCTION(execOnEnvironmentAssetsLoaded); \
	DECLARE_FUNCTION(execApplyLunarPhaseShift); \
	DECLARE_FUNCTION(execApplyAxisGravitationalStabilization); \
	DECLARE_FUNCTION(execApplySolarEnergyBoost); \
	DECLARE_FUNCTION(execCreateLunarIntersectionEffect); \
	DECLARE_FUNCTION(execCreateAxisIntersectionEffect); \
	DECLARE_FUNCTION(execCreateSolarIntersectionEffect); \
	DECLARE_FUNCTION(execFindLineIntersection); \
	DECLARE_FUNCTION(execFindIntersectionPoints); \
	DECLARE_FUNCTION(execIntegrateWithLunarRail); \
	DECLARE_FUNCTION(execIntegrateWithAxisRail); \
	DECLARE_FUNCTION(execIntegrateWithSolarRail); \
	DECLARE_FUNCTION(execIntegrateWithDynamicRails); \
	DECLARE_FUNCTION(execApplyParticlesBudgetToComponents); \
	DECLARE_FUNCTION(execUpdateParticlesBudgetForHardware); \
	DECLARE_FUNCTION(execValidateFlowIntegrityServerSide); \
	DECLARE_FUNCTION(execUnloadUnusedAssets); \
	DECLARE_FUNCTION(execPreloadAssetsForPosition); \
	DECLARE_FUNCTION(execUpdateIntelligentStreaming); \
	DECLARE_FUNCTION(execUpdateTimeBasedParametersTimer); \
	DECLARE_FUNCTION(execUpdateDynamicEffectsTimer); \
	DECLARE_FUNCTION(execOnMapContraction); \
	DECLARE_FUNCTION(execGetHardwareVolatilityMultiplier); \
	DECLARE_FUNCTION(execUpdateVolatilityForHardware); \
	DECLARE_FUNCTION(execUnregisterStrategicIsland); \
	DECLARE_FUNCTION(execRegisterStrategicIsland); \
	DECLARE_FUNCTION(execUpdateVisualEffectsForPhase); \
	DECLARE_FUNCTION(execSetVisualEffectsEnabled); \
	DECLARE_FUNCTION(execSetStreamingEnabled); \
	DECLARE_FUNCTION(execConfigureStreamingSettings); \
	DECLARE_FUNCTION(execGetCurrentMapPhase); \
	DECLARE_FUNCTION(execGetAssociatedDataLayer); \
	DECLARE_FUNCTION(execGetStreamingConfiguration); \
	DECLARE_FUNCTION(execApplyPhaseVisualEffects); \
	DECLARE_FUNCTION(execGenerateFlowPath); \
	DECLARE_FUNCTION(execGetControllingTeam); \
	DECLARE_FUNCTION(execSetControllingTeam); \
	DECLARE_FUNCTION(execGetFlowColorForTeam); \
	DECLARE_FUNCTION(execGetFlowColorForPhase); \
	DECLARE_FUNCTION(execUpdateFlowPhase); \
	DECLARE_FUNCTION(execOnFlowDeactivated); \
	DECLARE_FUNCTION(execOnFlowActivated); \
	DECLARE_FUNCTION(execSetActivityScale); \
	DECLARE_FUNCTION(execGetPCGComponent); \
	DECLARE_FUNCTION(execGetFlowControlPoints); \
	DECLARE_FUNCTION(execAssociateWithDataLayer); \
	DECLARE_FUNCTION(execConfigureWorldPartitionStreaming); \
	DECLARE_FUNCTION(execGetIslandsByType); \
	DECLARE_FUNCTION(execGetAllIslands); \
	DECLARE_FUNCTION(execRemoveIsland); \
	DECLARE_FUNCTION(execAddIslandAtPosition); \
	DECLARE_FUNCTION(execSetEffectQuality); \
	DECLARE_FUNCTION(execSetPredeterminedPattern); \
	DECLARE_FUNCTION(execConfigureForConvergencePhase); \
	DECLARE_FUNCTION(execConfigureForAwakeningPhase); \
	DECLARE_FUNCTION(execInitializePrismalFlow); \
	DECLARE_FUNCTION(execIsPositionInFlow); \
	DECLARE_FUNCTION(execGetFlowWidthAtT); \
	DECLARE_FUNCTION(execGetFlowPositionAtT); \
	DECLARE_FUNCTION(execSetFlowIntensity); \
	DECLARE_FUNCTION(execUpdateForMapPhase); \
	DECLARE_FUNCTION(execGeneratePrismalFlow);


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h_133_CALLBACK_WRAPPERS
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGPrismalFlow_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h_133_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAURACRONPCGPrismalFlow(); \
	friend struct Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGPrismalFlow_NoRegister(); \
public: \
	DECLARE_CLASS2(AAURACRONPCGPrismalFlow, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_AAURACRONPCGPrismalFlow_NoRegister) \
	DECLARE_SERIALIZER(AAURACRONPCGPrismalFlow) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		ControllingTeam=NETFIELD_REP_START, \
		NETFIELD_REP_END=ControllingTeam	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h_133_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAURACRONPCGPrismalFlow(AAURACRONPCGPrismalFlow&&) = delete; \
	AAURACRONPCGPrismalFlow(const AAURACRONPCGPrismalFlow&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAURACRONPCGPrismalFlow); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAURACRONPCGPrismalFlow); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAURACRONPCGPrismalFlow)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h_130_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h_133_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h_133_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h_133_CALLBACK_WRAPPERS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h_133_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h_133_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAURACRONPCGPrismalFlow;

// ********** End Class AAURACRONPCGPrismalFlow ****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h

// ********** Begin Enum EPrismalFlowIslandType ****************************************************
#define FOREACH_ENUM_EPRISMALFLOWISLANDTYPE(op) \
	op(EPrismalFlowIslandType::None) \
	op(EPrismalFlowIslandType::Nexus) \
	op(EPrismalFlowIslandType::Sanctuary) \
	op(EPrismalFlowIslandType::Arsenal) \
	op(EPrismalFlowIslandType::Chaos) \
	op(EPrismalFlowIslandType::Battlefield) \
	op(EPrismalFlowIslandType::Amplifier) \
	op(EPrismalFlowIslandType::Gateway) \
	op(EPrismalFlowIslandType::Corrupted) 

enum class EPrismalFlowIslandType : uint8;
template<> struct TIsUEnumClass<EPrismalFlowIslandType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EPrismalFlowIslandType>();
// ********** End Enum EPrismalFlowIslandType ******************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
