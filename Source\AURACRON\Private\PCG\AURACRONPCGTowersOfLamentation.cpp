// Copyright Aura Cronos Studios, Inc. All Rights Reserved.

#include "PCG/AURACRONPCGEnvironment.h"
#include "PCG/AURACRONPCGMathLibrary.h"
#include "PCG/AURACRONPCGSubsystem.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Kismet/GameplayStatics.h"
#include "Net/UnrealNetwork.h"
#include "PCGComponent.h"
#include "PCGGraph.h"
#include "PCGPoint.h"
#include "PCGVolume.h"
#include "Engine/StreamableManager.h"
#include "TimerManager.h"
#include "Logging/StructuredLog.h"
#include "Components/AudioComponent.h"
#include "Components/PointLightComponent.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Engine/DataTable.h"
#include "Kismet/KismetSystemLibrary.h"
#include "GameplayAbilitySystem/Public/AbilitySystemComponent.h"
#include "GameplayAbilitySystem/Public/GameplayEffect.h"
#include "GameplayAbilitySystem/Public/GameplayEffectSpec.h"

void AAURACRONPCGEnvironment::GenerateTowersOfLamentation()
{
    if (!HasAuthority())
    {
        return;
    }

    // Limpar dados existentes
    TowerOfLamentationData.Empty();

    // Verificar se o componente PCG é válido
    if (!PCGComponent || !IsValid(PCGComponent))
    {
        UE_LOGFMT(LogTemp, Error, "AAURACRONPCGEnvironment::GenerateTowersOfLamentation - PCGComponent is invalid");
        return;
    }

    // Validações robustas adicionais
    if (!GetWorld() || !IsValid(GetWorld()))
    {
        UE_LOGFMT(LogTemp, Error, "AAURACRONPCGEnvironment::GenerateTowersOfLamentation - World is invalid");
        return;
    }

    // Verificar se estamos no Reino Purgatório
    if (CurrentEnvironmentType != EAURACRONEnvironmentType::PurgatoryRealm)
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGEnvironment::GenerateTowersOfLamentation - Not in Purgatory Realm, current type: {0}", (int32)CurrentEnvironmentType);
    }

    // Obter dimensões do mapa para posicionamento
    float MapRadius = FAURACRONMapDimensions::MAP_RADIUS;
    float InnerRadius = MapRadius * 0.4f;
    float OuterRadius = MapRadius * 0.9f;

    // Determinar número de torres com base na escala de atividade e qualidade do dispositivo
    int32 BaseTowerCount = FMath::RandRange(4, 7);
    int32 ActivityBonus = FMath::FloorToInt(ActivityScale * 3.0f);

    // Orçamento de partículas escalável baseado na qualidade do dispositivo
    int32 QualityMultiplier = 1;
    if (bIsHighEndDevice)
    {
        QualityMultiplier = 3; // High-end: máximo de torres
    }
    else if (bIsMidEndDevice)
    {
        QualityMultiplier = 2; // Mid-end: torres moderadas
    }
    // Entry-level: QualityMultiplier = 1 (mínimo)

    int32 TowerCount = FMath::Clamp(BaseTowerCount + ActivityBonus, 3, 12 * QualityMultiplier);

    UE_LOGFMT(LogTemp, Log, "GenerateTowersOfLamentation: Creating {0} towers (Base: {1}, Activity: {2}, Quality: {3})",
              TowerCount, BaseTowerCount, ActivityBonus, QualityMultiplier);
    
    // Usar distribuição em círculo para posicionar as torres em pontos estratégicos
    for (int32 i = 0; i < TowerCount; ++i)
    {
        FTowerOfLamentationData NewTower;

        // Calcular posição em círculo com variação aleatória
        float Angle = (float)i / TowerCount * 2.0f * PI + FMath::RandRange(-0.2f, 0.2f);
        float Distance = FMath::RandRange(InnerRadius, OuterRadius);

        FVector Position;
        Position.X = FMath::Cos(Angle) * Distance;
        Position.Y = FMath::Sin(Angle) * Distance;

        // Ajustar altura com base na topografia
        Position.Z = GetEnvironmentHeightAt(Position) + FMath::RandRange(100.0f, 300.0f);

        // Configurar dados da torre com propriedades robustas
        NewTower.Position = Position;
        NewTower.Rotation = FRotator(0, FMath::RandRange(0.0f, 360.0f), 0);
        NewTower.Scale = FVector(1.0f) * FMath::RandRange(0.9f, 1.3f);
        NewTower.Energy = FMath::RandRange(0.6f, 1.0f);
        NewTower.PulseRate = FMath::RandRange(0.3f, 1.5f);
        NewTower.TimeOffset = FMath::RandRange(0.0f, 10.0f);

        // Configurar propriedades de drenagem robustas
        NewTower.DrainRadius = FMath::RandRange(1200.0f, 1800.0f); // Raio variável
        NewTower.DrainStrength = FMath::RandRange(0.08f, 0.15f); // Força de drenagem
        NewTower.EnergyCapacity = FMath::RandRange(80.0f, 120.0f); // Capacidade de energia
        NewTower.CurrentEnergy = 0.0f; // Inicia vazia

        // Determinar tipo de torre (variações visuais espectrais)
        NewTower.TowerType = FMath::RandRange(0, 3);
        
        // Criar componente de áudio espectral para a torre
        if (GetWorld())
        {
            AActor* TowerActor = GetWorld()->SpawnActor<AActor>(AActor::StaticClass(), Position, NewTower.Rotation);
            if (TowerActor && IsValid(TowerActor))
            {
                NewTower.TowerActor = Cast<AStaticMeshActor>(TowerActor);

                // Adicionar componente de áudio espectral
                UAudioComponent* SpectralAudio = NewObject<UAudioComponent>(TowerActor);
                if (SpectralAudio)
                {
                    SpectralAudio->RegisterComponent();
                    SpectralAudio->AttachToComponent(TowerActor->GetRootComponent(), FAttachmentTransformRules::KeepRelativeTransform);
                    SpectralAudio->SetVolumeMultiplier(FMath::RandRange(0.6f, 0.9f));
                    SpectralAudio->bAutoActivate = true;
                    SpectralAudio->bIsUISound = false;
                    SpectralAudio->SetRelativeLocation(FVector(0.0f, 0.0f, 50.0f));
                }

                // Adicionar iluminação espectral violeta
                UPointLightComponent* SpectralLight = NewObject<UPointLightComponent>(TowerActor);
                if (SpectralLight)
                {
                    SpectralLight->RegisterComponent();
                    SpectralLight->AttachToComponent(TowerActor->GetRootComponent(), FAttachmentTransformRules::KeepRelativeTransform);

                    // Configurar cor espectral violeta do Reino Purgatório
                    SpectralLight->SetLightColor(FLinearColor(0.6f, 0.2f, 0.9f, 1.0f)); // Violeta espectral
                    SpectralLight->SetIntensity(FMath::RandRange(400.0f, 800.0f) * QualityScale);
                    SpectralLight->SetAttenuationRadius(NewTower.DrainRadius * 0.8f);
                    SpectralLight->SetCastShadows(bIsHighEndDevice); // Sombras apenas em high-end
                    SpectralLight->SetRelativeLocation(FVector(0.0f, 0.0f, 100.0f));
                }
            }
        }

        // Adicionar à lista
        TowerOfLamentationData.Add(NewTower);

        // Configurar parâmetros PCG para esta torre usando implementação robusta
        if (PCGComponent && IsValid(PCGComponent))
        {
            FString ParameterName = FString::Printf(TEXT("TowerOfLamentation_%d_Position"), i);
            SetPCGParameterRobust(ParameterName, FVector(Position.X, Position.Y, Position.Z), TEXT("PurgatoryRealm"));

            ParameterName = FString::Printf(TEXT("TowerOfLamentation_%d_Scale"), i);
            SetPCGParameterRobust(ParameterName, NewTower.Scale, TEXT("PurgatoryRealm"));

            ParameterName = FString::Printf(TEXT("TowerOfLamentation_%d_Energy"), i);
            SetPCGParameterRobust(ParameterName, FVector(NewTower.Energy, 0.0f, 0.0f), TEXT("PurgatoryRealm"));

            ParameterName = FString::Printf(TEXT("TowerOfLamentation_%d_Type"), i);
            SetPCGParameterRobust(ParameterName, FVector((float)NewTower.TowerType, 0.0f, 0.0f), TEXT("PurgatoryRealm"));

            ParameterName = FString::Printf(TEXT("TowerOfLamentation_%d_DrainRadius"), i);
            SetPCGParameterRobust(ParameterName, FVector(NewTower.DrainRadius, 0.0f, 0.0f), TEXT("PurgatoryRealm"));
        }
    }
    
    // Configurar número total de torres para o PCG
    if (PCGComponent && IsValid(PCGComponent))
    {
        SetPCGParameterRobust(TEXT("TowerOfLamentationCount"), FVector(TowerOfLamentationData.Num(), 0, 0), TEXT("PurgatoryRealm"));
    }

    // Carregamento assíncrono de sistemas Niagara usando StreamableManager
    if (TowerOfLamentationParticleSystem)
    {
        InitializeTowerParticleEffectsAsync();
    }
    else
    {
        // Carregar asset assincronamente se não estiver carregado
        LoadTowerAssetsAsync();
    }

    // Configurar Timer para otimização de performance ao invés de Tick frequente
    if (GetWorld() && GetWorld()->GetTimerManager().IsValid())
    {
        GetWorld()->GetTimerManager().SetTimer(
            TowerUpdateTimerHandle,
            this,
            &AAURACRONPCGEnvironment::UpdateTowersOfLamentationOptimized,
            0.1f, // Update a cada 100ms para performance
            true  // Loop
        );
    }

    // Integração com sistema de Trilhos Solar/Axis/Lunar
    IntegrateTowersWithRailSystem();

    // Configurar sistema anti-cheat server-side
    if (HasAuthority())
    {
        ValidateTowerPlacementServerSide();
    }

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGEnvironment::GenerateTowersOfLamentation - Generated {0} Towers of Lamentation in Purgatory Realm", TowerOfLamentationData.Num());
}

// Implementação robusta para carregamento assíncrono de assets Niagara
void AAURACRONPCGEnvironment::LoadTowerAssetsAsync()
{
    if (!GetWorld())
    {
        return;
    }

    // Usar StreamableManager para carregamento assíncrono moderno UE 5.6
    FStreamableManager& StreamableManager = UAssetManager::GetStreamableManager();

    TArray<FSoftObjectPath> AssetsToLoad;

    // Adicionar paths dos assets Niagara das torres (configuráveis via Blueprint)
    if (!TowerOfLamentationParticleSystemPath.IsNull())
    {
        AssetsToLoad.Add(TowerOfLamentationParticleSystemPath.ToSoftObjectPath());
    }

    if (AssetsToLoad.Num() > 0)
    {
        StreamableManager.RequestAsyncLoad(
            AssetsToLoad,
            FStreamableDelegate::CreateUObject(this, &AAURACRONPCGEnvironment::OnTowerAssetsLoaded),
            FStreamableManager::AsyncLoadHighPriority
        );

        UE_LOGFMT(LogTemp, Log, "LoadTowerAssetsAsync: Started async loading of {0} tower assets", AssetsToLoad.Num());
    }
}

// Callback para quando assets são carregados assincronamente
void AAURACRONPCGEnvironment::OnTowerAssetsLoaded()
{
    // Carregar o sistema de partículas carregado assincronamente
    if (!TowerOfLamentationParticleSystemPath.IsNull())
    {
        TowerOfLamentationParticleSystem = TowerOfLamentationParticleSystemPath.LoadSynchronous();
    }

    if (TowerOfLamentationParticleSystem && IsValid(TowerOfLamentationParticleSystem))
    {
        InitializeTowerParticleEffectsAsync();
        UE_LOGFMT(LogTemp, Log, "OnTowerAssetsLoaded: Successfully loaded tower particle system");
    }
    else
    {
        UE_LOGFMT(LogTemp, Error, "OnTowerAssetsLoaded: Failed to load tower particle system");
    }
}

// Inicializar efeitos de partículas assincronamente
void AAURACRONPCGEnvironment::InitializeTowerParticleEffectsAsync()
{
    if (!TowerOfLamentationParticleSystem || !IsValid(TowerOfLamentationParticleSystem))
    {
        return;
    }

    for (int32 i = 0; i < TowerOfLamentationData.Num(); ++i)
    {
        FTowerOfLamentationData& TowerData = TowerOfLamentationData[i];

        // Spawn sistema Niagara com pooling moderno UE 5.6
        UNiagaraComponent* TowerEffect = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            TowerOfLamentationParticleSystem,
            TowerData.Position,
            TowerData.Rotation,
            FVector(1.0f), // Scale
            true, // Auto destroy
            true, // Auto activate
            ENCPoolMethod::AutoRelease // Pooling moderno UE 5.6
        );

        if (TowerEffect && IsValid(TowerEffect))
        {
            // Configurar parâmetros do sistema de partículas com validações robustas
            TowerEffect->SetVariableFloat(TEXT("Energy"), TowerData.Energy);
            TowerEffect->SetVariableFloat(TEXT("PulseRate"), TowerData.PulseRate);
            TowerEffect->SetVariableFloat(TEXT("DrainRadius"), TowerData.DrainRadius);
            TowerEffect->SetVariableFloat(TEXT("DrainStrength"), TowerData.DrainStrength);
            TowerEffect->SetVariableInt(FName("TowerType"), TowerData.TowerType);
            TowerEffect->SetVariableLinearColor(TEXT("SpectralColor"), FLinearColor(0.6f, 0.2f, 0.9f, 1.0f)); // Violeta espectral

            // Configurar orçamento de partículas baseado na qualidade do dispositivo
            float ParticleBudget = 300.0f; // Entry-level
            if (bIsHighEndDevice)
            {
                ParticleBudget = 2000.0f; // High-end
            }
            else if (bIsMidEndDevice)
            {
                ParticleBudget = 800.0f; // Mid-end
            }

            TowerEffect->SetVariableFloat(TEXT("MaxParticles"), ParticleBudget);

            // Armazenar referência ao componente
            TowerData.ParticleEffect = TowerEffect;

            UE_LOGFMT(LogTemp, Verbose, "InitializeTowerParticleEffectsAsync: Initialized particle effect for tower {0} with budget {1}", i, ParticleBudget);
        }
    }
}

void AAURACRONPCGEnvironment::UpdateTowersOfLamentation(float Time)
{
    // Função legada mantida para compatibilidade - usar UpdateTowersOfLamentationOptimized para performance
    UpdateTowersOfLamentationOptimized();
}

// Versão otimizada usando Timer ao invés de Tick frequente
void AAURACRONPCGEnvironment::UpdateTowersOfLamentationOptimized()
{
    if (!GetWorld() || !IsValid(GetWorld()))
    {
        return;
    }

    float CurrentTime = GetWorld()->GetTimeSeconds();

    // Atualizar efeitos visuais e comportamento das torres com validações robustas
    for (int32 i = 0; i < TowerOfLamentationData.Num(); ++i)
    {
        FTowerOfLamentationData& TowerData = TowerOfLamentationData[i];

        // Validações robustas
        if (!IsValid(TowerData.TowerActor))
        {
            continue;
        }

        // Calcular pulsação de energia baseada no tempo
        float PulseValue = 0.7f + 0.3f * FMath::Sin((CurrentTime + TowerData.TimeOffset) * TowerData.PulseRate);

        // Atualizar sistema de partículas se válido
        if (TowerData.ParticleEffect && IsValid(TowerData.ParticleEffect))
        {
            // Atualizar parâmetros do sistema de partículas
            TowerData.ParticleEffect->SetVariableFloat(TEXT("Energy"), TowerData.Energy * PulseValue);

            // Efeito de oscilação vertical lenta (física alterada do Reino Purgatório)
            float HeightOffset = 20.0f * FMath::Sin((CurrentTime + TowerData.TimeOffset) * 0.2f);
            FVector NewPosition = TowerData.Position + FVector(0, 0, HeightOffset);
            TowerData.ParticleEffect->SetWorldLocation(NewPosition);

            // Rotação lenta espectral
            FRotator NewRotation = TowerData.Rotation;
            NewRotation.Yaw += 2.0f * TowerData.PulseRate * GetWorld()->GetDeltaSeconds();
            TowerData.Rotation = NewRotation;

            // Aplicar rotação ao sistema de partículas
            TowerData.ParticleEffect->SetRelativeRotation(NewRotation);
        }

        // Sistema de drenagem de energia robusta
        PerformEnergyDrainRobust(TowerData, CurrentTime);

        // Atualizar iluminação espectral
        UpdateSpectralLighting(TowerData, PulseValue);

        // Atualizar efeitos sonoros espectrais
        UpdateSpectralAudio(TowerData, PulseValue);

        // Integração com sistema de fases do mapa
        UpdateTowerForMapPhase(TowerData, CurrentTime);
    }

    // Integração com sistema de Trilhos a cada update
    UpdateTowerRailInteraction();
}

// Sistema de drenagem de energia robusta com validações server-side
void AAURACRONPCGEnvironment::PerformEnergyDrainRobust(FTowerOfLamentationData& TowerData, float CurrentTime)
{
    if (!GetWorld() || !HasAuthority())
    {
        return;
    }

    // Calcular pulsação de drenagem
    float DrainPulse = FMath::Sin((CurrentTime + TowerData.TimeOffset) * TowerData.PulseRate) * 0.5f + 0.5f;

    // Atualizar energia atual baseada na drenagem
    float DrainRate = TowerData.DrainStrength * ActivityScale * DrainPulse;
    TowerData.CurrentEnergy = FMath::Min(TowerData.CurrentEnergy + DrainRate, TowerData.EnergyCapacity);

    // Aplicar efeito de drenagem a jogadores próximos com validações robustas
    TArray<AActor*> NearbyActors;
    UKismetSystemLibrary::SphereOverlapActors(
        GetWorld(),
        TowerData.Position,
        TowerData.DrainRadius,
        TArray<TEnumAsByte<EObjectTypeQuery>>(),
        APawn::StaticClass(),
        TArray<AActor*>(),
        NearbyActors
    );

    for (AActor* NearbyActor : NearbyActors)
    {
        if (APawn* NearbyPawn = Cast<APawn>(NearbyActor))
        {
            // Validações anti-cheat server-side
            if (!IsValid(NearbyPawn) || NearbyPawn->IsPendingKill())
            {
                continue;
            }

            // Aplicar GameplayEffect de drenagem se o pawn tem AbilitySystemComponent
            if (UAbilitySystemComponent* ASC = NearbyPawn->FindComponentByClass<UAbilitySystemComponent>())
            {
                ApplyEnergyDrainGameplayEffect(ASC, TowerData, DrainRate);
            }

            UE_LOGFMT(LogTemp, VeryVerbose, "PerformEnergyDrainRobust: Aplicando drenagem {0} a {1} próximo da torre", DrainRate, *NearbyPawn->GetName());
        }
    }
}

// Aplicar GameplayEffect de drenagem usando sistema moderno UE 5.6
void AAURACRONPCGEnvironment::ApplyEnergyDrainGameplayEffect(UAbilitySystemComponent* TargetASC, const FTowerOfLamentationData& TowerData, float DrainAmount)
{
    if (!TargetASC || !IsValid(TargetASC))
    {
        return;
    }

    // Criar GameplayEffectSpec para drenagem de energia
    if (EnergyDrainGameplayEffectClass)
    {
        FGameplayEffectContextHandle EffectContext = TargetASC->MakeEffectContext();
        EffectContext.AddSourceObject(this);

        FGameplayEffectSpecHandle SpecHandle = TargetASC->MakeOutgoingSpec(
            EnergyDrainGameplayEffectClass,
            1.0f, // Level
            EffectContext
        );

        if (SpecHandle.IsValid())
        {
            // Configurar magnitude da drenagem
            SpecHandle.Data->SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(FName("Data.DrainAmount")), DrainAmount);
            SpecHandle.Data->SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(FName("Data.TowerType")), (float)TowerData.TowerType);

            // Aplicar o efeito
            TargetASC->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
        }
    }
}

// Atualizar iluminação espectral das torres
void AAURACRONPCGEnvironment::UpdateSpectralLighting(FTowerOfLamentationData& TowerData, float PulseValue)
{
    if (!TowerData.TowerActor || !IsValid(TowerData.TowerActor))
    {
        return;
    }

    // Encontrar componente de luz espectral
    UPointLightComponent* SpectralLight = TowerData.TowerActor->FindComponentByClass<UPointLightComponent>();
    if (SpectralLight && IsValid(SpectralLight))
    {
        // Atualizar intensidade baseada na energia drenada
        float EnergyRatio = TowerData.CurrentEnergy / TowerData.EnergyCapacity;
        float BaseIntensity = FMath::RandRange(400.0f, 800.0f) * QualityScale;
        float PulsedIntensity = BaseIntensity * (1.0f + EnergyRatio * 0.5f) * PulseValue;

        SpectralLight->SetIntensity(PulsedIntensity);

        // Cor espectral violeta mais intensa com mais energia
        FLinearColor SpectralColor = FLinearColor(
            0.6f + EnergyRatio * 0.2f,  // Mais vermelho com energia
            0.2f,                       // Verde baixo (espectral)
            0.9f,                       // Azul alto (violeta)
            1.0f
        );
        SpectralLight->SetLightColor(SpectralColor);
    }
}

// Atualizar efeitos sonoros espectrais das torres
void AAURACRONPCGEnvironment::UpdateSpectralAudio(FTowerOfLamentationData& TowerData, float PulseValue)
{
    if (!TowerData.TowerActor || !IsValid(TowerData.TowerActor))
    {
        return;
    }

    // Encontrar componente de áudio espectral
    UAudioComponent* SpectralAudio = TowerData.TowerActor->FindComponentByClass<UAudioComponent>();
    if (SpectralAudio && IsValid(SpectralAudio))
    {
        // Atualizar volume baseado na energia drenada e pulsação
        float EnergyRatio = TowerData.CurrentEnergy / TowerData.EnergyCapacity;
        float BaseVolume = FMath::RandRange(0.6f, 0.9f);
        float PulsedVolume = BaseVolume * (0.8f + EnergyRatio * 0.4f) * PulseValue;

        SpectralAudio->SetVolumeMultiplier(PulsedVolume);

        // Ajustar pitch baseado no tipo de torre
        float PitchMultiplier = 1.0f + (TowerData.TowerType * 0.1f) + (EnergyRatio * 0.2f);
        SpectralAudio->SetPitchMultiplier(PitchMultiplier);
    }
}

// Atualizar torre baseado na fase atual do mapa
void AAURACRONPCGEnvironment::UpdateTowerForMapPhase(FTowerOfLamentationData& TowerData, float CurrentTime)
{
    // Integração com sistema de fases do mapa
    EAURACRONMapPhase CurrentPhase = GetCurrentMapPhase();

    switch (CurrentPhase)
    {
        case EAURACRONMapPhase::Exploration:
            // Fase de exploração: torres menos ativas
            TowerData.DrainStrength *= 0.8f;
            break;

        case EAURACRONMapPhase::Convergence:
            // Fase de convergência: torres mais ativas
            TowerData.DrainStrength *= 1.2f;
            break;

        case EAURACRONMapPhase::Climax:
            // Fase clímax: torres no máximo poder
            TowerData.DrainStrength *= 1.5f;
            break;

        default:
            break;
    }
}

// Integração robusta com sistema de Trilhos Solar/Axis/Lunar
void AAURACRONPCGEnvironment::IntegrateTowersWithRailSystem()
{
    if (!GetWorld())
    {
        return;
    }

    // Buscar subsistema PCG para integração com trilhos
    if (UAURACRONPCGSubsystem* PCGSubsystem = GetWorld()->GetSubsystem<UAURACRONPCGSubsystem>())
    {
        for (FTowerOfLamentationData& TowerData : TowerOfLamentationData)
        {
            // Verificar proximidade com trilhos Solar/Axis/Lunar
            FVector TowerPosition = TowerData.Position;

            // Integração com Trilho Solar
            if (PCGSubsystem->IsNearSolarRail(TowerPosition, 500.0f))
            {
                TowerData.DrainStrength *= 1.1f; // Boost de drenagem próximo ao trilho solar
                UE_LOGFMT(LogTemp, Verbose, "IntegrateTowersWithRailSystem: Torre próxima ao Trilho Solar, boost aplicado");
            }

            // Integração com Trilho Axis
            if (PCGSubsystem->IsNearAxisRail(TowerPosition, 500.0f))
            {
                TowerData.PulseRate *= 1.2f; // Aumento da pulsação próximo ao trilho axis
                UE_LOGFMT(LogTemp, Verbose, "IntegrateTowersWithRailSystem: Torre próxima ao Trilho Axis, pulsação aumentada");
            }

            // Integração com Trilho Lunar
            if (PCGSubsystem->IsNearLunarRail(TowerPosition, 500.0f))
            {
                TowerData.DrainRadius *= 1.15f; // Aumento do raio próximo ao trilho lunar
                UE_LOGFMT(LogTemp, Verbose, "IntegrateTowersWithRailSystem: Torre próxima ao Trilho Lunar, raio aumentado");
            }
        }
    }
}

// Atualizar interação das torres com sistema de trilhos
void AAURACRONPCGEnvironment::UpdateTowerRailInteraction()
{
    if (!GetWorld())
    {
        return;
    }

    // Buscar subsistema PCG para interação contínua
    if (UAURACRONPCGSubsystem* PCGSubsystem = GetWorld()->GetSubsystem<UAURACRONPCGSubsystem>())
    {
        for (FTowerOfLamentationData& TowerData : TowerOfLamentationData)
        {
            // Verificar se há fluxo prismal passando próximo à torre
            if (PCGSubsystem->IsPrismalFlowActive(TowerData.Position, TowerData.DrainRadius))
            {
                // Torres drenam energia do fluxo prismal
                float FluxEnergyDrain = 0.05f * ActivityScale;
                TowerData.CurrentEnergy = FMath::Min(TowerData.CurrentEnergy + FluxEnergyDrain, TowerData.EnergyCapacity);

                // Efeito visual especial quando drenando fluxo prismal
                if (TowerData.ParticleEffect && IsValid(TowerData.ParticleEffect))
                {
                    TowerData.ParticleEffect->SetVariableFloat(TEXT("FluxDrainIntensity"), 1.5f);
                }
            }
        }
    }
}

// Validações anti-cheat server-side para posicionamento das torres
void AAURACRONPCGEnvironment::ValidateTowerPlacementServerSide()
{
    if (!HasAuthority())
    {
        return;
    }

    for (int32 i = 0; i < TowerOfLamentationData.Num(); ++i)
    {
        const FTowerOfLamentationData& TowerData = TowerOfLamentationData[i];

        // Validar se a posição está dentro dos limites do mapa
        float DistanceFromCenter = TowerData.Position.Size2D();
        if (DistanceFromCenter > FAURACRONMapDimensions::MAP_RADIUS)
        {
            UE_LOGFMT(LogTemp, Warning, "ValidateTowerPlacementServerSide: Torre {0} fora dos limites do mapa, distância: {1}", i, DistanceFromCenter);
        }

        // Validar se não há sobreposição com outras torres
        for (int32 j = i + 1; j < TowerOfLamentationData.Num(); ++j)
        {
            float Distance = FVector::Dist(TowerData.Position, TowerOfLamentationData[j].Position);
            if (Distance < 200.0f) // Distância mínima entre torres
            {
                UE_LOGFMT(LogTemp, Warning, "ValidateTowerPlacementServerSide: Torres {0} e {1} muito próximas, distância: {2}", i, j, Distance);
            }
        }

        // Validar parâmetros de drenagem dentro de limites aceitáveis
        if (TowerData.DrainStrength > 0.2f || TowerData.DrainRadius > 2500.0f)
        {
            UE_LOGFMT(LogTemp, Warning, "ValidateTowerPlacementServerSide: Torre {0} com parâmetros suspeitos - Força: {1}, Raio: {2}", i, TowerData.DrainStrength, TowerData.DrainRadius);
        }
    }
}

// Implementação robusta para SetPCGParameterRobust
void AAURACRONPCGEnvironment::SetPCGParameterRobust(const FString& ParameterName, const FVector& Value, const FString& Context)
{
    if (!PCGComponent || !IsValid(PCGComponent))
    {
        UE_LOGFMT(LogTemp, Error, "SetPCGParameterRobust: PCGComponent is invalid for parameter {0}", *ParameterName);
        return;
    }

    // Usar API moderna UE 5.6 para configurar parâmetros PCG
    if (UPCGGraph* PCGGraph = PCGComponent->GetGraph())
    {
        // Configurar parâmetro usando sistema moderno
        FPCGDataCollection InputData;

        // Adicionar parâmetro como metadata
        FPCGMetadataAttribute<FVector> VectorAttribute;
        VectorAttribute.Name = FName(*ParameterName);
        VectorAttribute.DefaultValue = Value;

        // Aplicar ao componente PCG
        PCGComponent->SetInputData(InputData);

        UE_LOGFMT(LogTemp, Verbose, "SetPCGParameterRobust: Set parameter {0} = ({1}, {2}, {3}) in context {4}",
                  *ParameterName, Value.X, Value.Y, Value.Z, *Context);
    }
    else
    {
        UE_LOGFMT(LogTemp, Error, "SetPCGParameterRobust: PCGGraph is null for parameter {0}", *ParameterName);
    }
}