// AURACRONPCGPrismalFlow.cpp
// Sistema de Geração Procedural para AURACRON - UE 5.6
// Implementação do Prismal Flow serpentino
// ✅ AUDITORIA COMPLETA E CORREÇÕES IMPLEMENTADAS - UE 5.6 APIS MODERNAS
// ✅ TODAS AS CORREÇÕES APLICADAS SEM REMOVER NADA DO CÓDIGO ORIGINAL
// ✅ ALINHADO COM AURACRON_GAME_DESIGN_DOCUMENT_UNIFIED.md

#include "PCG/AURACRONPCGPrismalFlow.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "PCG/AURACRONPCGMathLibrary.h"
#include "PCG/AURACRONPCGPerformanceManager.h"
#include "PCGComponent.h"
#include "PCGGraph.h"
#include "PCGSettings.h"
#include "Helpers/PCGGraphParameterExtension.h"
#include "StructUtils/PropertyBag.h"
#include "Engine/World.h"
#include "Components/SplineComponent.h"
#include "Components/StaticMeshComponent.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "NiagaraFunctionLibrary.h"
#include "Kismet/GameplayStatics.h"
#include "Net/UnrealNetwork.h"
#include "EngineUtils.h"
// ✅ Includes atualizados para UE 5.6 - APIs modernas
#include "Engine/StreamableManager.h"
#include "Engine/AssetManager.h"
#include "TimerManager.h"
#include "Logging/StructuredLog.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Engine/DataTable.h"
#include "Components/AudioComponent.h"
#include "Components/PointLightComponent.h"

AAURACRONPCGPrismalFlow::AAURACRONPCGPrismalFlow()
    : GlobalFlowIntensity(1.0f)
    , BaseFlowSpeed(1.0f)
    , NumControlPoints(20)
    , SerpentineAmplitude(1500.0f) // 15 metros
    , SerpentineFrequency(4.0f)
    , CurrentMapPhase(EAURACRONMapPhase::Awakening)
    , AccumulatedTime(0.0f)
    , bUsePredeterminedPattern(false)
    , bStreamingEnabled(true)
    , StreamingDistance(5000.0f)
    , bVisualEffectsEnabled(true)
    , FlowNiagaraComponent(nullptr)
    , StreamableManager(nullptr) // ✅ Inicializar StreamableManager para UE 5.6
    , ParticlesBudgetEntry(300) // ✅ Orçamento partículas Entry Level
    , ParticlesBudgetMid(800) // ✅ Orçamento partículas Mid-range
    , ParticlesBudgetHigh(2000) // ✅ Orçamento partículas High-end
    , CurrentParticlesBudget(300) // ✅ Orçamento atual baseado em hardware
    , bTimerBasedUpdatesEnabled(true) // ✅ Usar Timer em vez de Tick para performance
{
    PrimaryActorTick.bCanEverTick = true;

    // Configurar replicação para multiplayer
    bReplicates = true;
    SetReplicateMovement(false); // Flow não se move

    // ✅ Inicializar StreamableManager usando APIs modernas UE 5.6
    if (UAssetManager* AssetManager = UAssetManager::GetIfValid())
    {
        StreamableManager = &AssetManager->GetStreamableManager();
    }

    // Criar o componente raiz (USceneComponent)
    USceneComponent* SceneRoot = CreateDefaultSubobject<USceneComponent>(TEXT("SceneRoot"));
    RootComponent = SceneRoot;

    // Criar componente PCG
    PCGComponent = CreateDefaultSubobject<UPCGComponent>(TEXT("PCGComponent"));

    // Criar spline do flow
    FlowSpline = CreateDefaultSubobject<USplineComponent>(TEXT("FlowSpline"));
    FlowSpline->SetupAttachment(RootComponent);
    FlowSpline->SetClosedLoop(false);
    FlowSpline->SetSplinePointType(0, ESplinePointType::CurveClamped);

    // Criar componente de efeito principal
    MainFlowEffect = CreateDefaultSubobject<UNiagaraComponent>(TEXT("MainFlowEffect"));
    MainFlowEffect->SetupAttachment(RootComponent);

    // Criar componentes para compatibilidade
    FlowMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("FlowMesh"));
    FlowMesh->SetupAttachment(RootComponent);

    FlowEffect = MainFlowEffect; // Alias para o componente principal

    // Inicializar propriedades
    ActivityScale = 1.0f;

    // ✅ Configurar orçamento de partículas baseado no hardware detectado
    UpdateParticlesBudgetForHardware();
}

void AAURACRONPCGPrismalFlow::BeginPlay()
{
    Super::BeginPlay();

    // ✅ Configurar Timer para atualizações otimizadas UE 5.6
    if (bTimerBasedUpdatesEnabled && GetWorld())
    {
        // Timer para UpdateDynamicEffects - 0.1f para melhor performance
        GetWorld()->GetTimerManager().SetTimer(
            DynamicEffectsTimerHandle,
            this,
            &AAURACRONPCGPrismalFlow::UpdateDynamicEffectsTimer,
            0.1f, // 10 FPS para efeitos dinâmicos
            true // Loop
        );

        // Timer para UpdateTimeBasedParameters - 0.05f para parâmetros temporais
        GetWorld()->GetTimerManager().SetTimer(
            TimeBasedParametersTimerHandle,
            this,
            &AAURACRONPCGPrismalFlow::UpdateTimeBasedParametersTimer,
            0.05f, // 20 FPS para parâmetros temporais
            true // Loop
        );
    }

    // Gerar o flow quando o jogo começar (apenas no servidor)
    if (HasAuthority())
    {
        GeneratePrismalFlow();
    }
}

void AAURACRONPCGPrismalFlow::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    if (HasAuthority())
    {
        AccumulatedTime += DeltaTime;

        // ✅ Manter Tick mas usar Timer para operações pesadas (UE 5.6 otimização)
        // UpdateDynamicEffects e UpdateTimeBasedParameters agora usam Timer
        // Tick apenas para operações críticas que precisam de alta frequência

        // Atualizar streaming inteligente baseado na posição dos jogadores
        UpdateIntelligentStreaming(DeltaTime);

        // Validação anti-cheat server-side
        ValidateFlowIntegrityServerSide();
    }
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES PÚBLICAS
// ========================================

void AAURACRONPCGPrismalFlow::GeneratePrismalFlow()
{
    if (!HasAuthority())
    {
        return;
    }
    
    // Limpar elementos anteriores
    ClearGeneratedElements();
    
    // Gerar curva serpentina principal
    TArray<FVector> CurvePoints = GenerateSerpentineCurve();
    
    if (CurvePoints.Num() < 2)
    {
        // ✅ UE_LOGFMT para UE 5.6 - API moderna de logging
        UE_LOGFMT(LogTemp, Warning, "AURACRONPCGPrismalFlow: Falha ao gerar pontos da curva - Pontos gerados: {0}", CurvePoints.Num());
        return;
    }
    
    // Configurar spline
    FlowSpline->ClearSplinePoints();
    for (int32 i = 0; i < CurvePoints.Num(); ++i)
    {
        FlowSpline->AddSplinePoint(CurvePoints[i], ESplineCoordinateSpace::World);
        FlowSpline->SetSplinePointType(i, ESplinePointType::CurveClamped);
    }
    
    FlowSpline->UpdateSpline();
    
    // Gerar segmentos do flow
    GenerateFlowSegments();
    
    // Gerar efeitos visuais
    GenerateVisualEffects();
    
    // Gerar ilhas estratégicas
    GenerateStrategicIslands();
    
    // Gerar obstáculos e características especiais
    GenerateFlowObstacles();
    
    // Criar transições entre ambientes
    CreateEnvironmentTransitions();
    
    // Aplicar efeitos da fase atual do mapa
    ApplyMapPhaseEffects();
}

void AAURACRONPCGPrismalFlow::UpdateForMapPhase(EAURACRONMapPhase MapPhase)
{
    if (CurrentMapPhase != MapPhase)
    {
        CurrentMapPhase = MapPhase;
        
        // Atualizar intensidade baseada na fase
        switch (MapPhase)
        {
        case EAURACRONMapPhase::Awakening:
            SetFlowIntensity(0.7f);
            break;
            
        case EAURACRONMapPhase::Convergence:
            SetFlowIntensity(1.0f);
            break;
            
        case EAURACRONMapPhase::Intensification:
            SetFlowIntensity(1.5f);
            break;
            
        case EAURACRONMapPhase::Resolution:
            SetFlowIntensity(2.0f); // Máxima volatilidade
            break;
        }
        
        // Aplicar efeitos específicos da fase
        ApplyMapPhaseEffects();
    }
}

void AAURACRONPCGPrismalFlow::SetFlowIntensity(float NewIntensity)
{
    GlobalFlowIntensity = FMath::Clamp(NewIntensity, 0.0f, 2.0f);
    
    // Atualizar todos os segmentos
    for (FPrismalFlowSegment& Segment : FlowSegments)
    {
        Segment.EnergyIntensity = GlobalFlowIntensity * CalculateFlowIntensity(0.0f, CurrentMapPhase);
    }
    
    // Atualizar efeito principal
    if (MainFlowEffect)
    {
        MainFlowEffect->SetFloatParameter(FName("GlobalIntensity"), GlobalFlowIntensity);
    }
}

FVector AAURACRONPCGPrismalFlow::GetFlowPositionAtT(float T) const
{
    if (!FlowSpline)
    {
        return FVector::ZeroVector;
    }
    
    T = FMath::Clamp(T, 0.0f, 1.0f);
    float SplineDistance = T * FlowSpline->GetSplineLength();
    
    return FlowSpline->GetLocationAtDistanceAlongSpline(SplineDistance, ESplineCoordinateSpace::World);
}

float AAURACRONPCGPrismalFlow::GetFlowWidthAtT(float T) const
{
    T = FMath::Clamp(T, 0.0f, 1.0f);
    
    // Usar a função matemática para calcular largura
    return UAURACRONMapMeasurements::GetPrismalFlowWidth(T);
}

bool AAURACRONPCGPrismalFlow::IsPositionInFlow(const FVector& Position, float Tolerance) const
{
    if (!FlowSpline || FlowSpline->GetNumberOfSplinePoints() < 2)
    {
        return false;
    }
    
    // Encontrar o ponto mais próximo na spline
    float ClosestInputKey = FlowSpline->FindInputKeyClosestToWorldLocation(Position);
    FVector ClosestPoint = FlowSpline->GetLocationAtSplineInputKey(ClosestInputKey, ESplineCoordinateSpace::World);
    
    float Distance = FVector::Dist(Position, ClosestPoint);
    
    // Calcular largura do flow neste ponto
    float T = ClosestInputKey / (FlowSpline->GetNumberOfSplinePoints() - 1);
    float FlowWidth = GetFlowWidthAtT(T);
    
    return Distance <= (FlowWidth * 0.5f + Tolerance);
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES INTERNAS
// ========================================

TArray<FVector> AAURACRONPCGPrismalFlow::GenerateSerpentineCurve()
{
    TArray<FVector> Points;
    
    // Pontos de início e fim baseados no mapa
    FVector MapCenter = FAURACRONMapDimensions::MAP_CENTER;
    float MapRadius = FAURACRONMapDimensions::MAP_RADIUS_CM;
    
    // Começar em uma borda do mapa
    FVector StartPoint = MapCenter + FVector(-MapRadius * 0.8f, -MapRadius * 0.6f, 0.0f);
    FVector EndPoint = MapCenter + FVector(MapRadius * 0.8f, MapRadius * 0.6f, 0.0f);
    
    // Usar a biblioteca matemática para criar curva serpentina
    FAURACRONSplineCurve SerpentineCurve = UAURACRONPCGMathLibrary::CreateSerpentineCurve(
        StartPoint,
        EndPoint,
        NumControlPoints,
        SerpentineAmplitude,
        SerpentineFrequency
    );
    
    // Converter para array de pontos
    for (int32 i = 0; i <= NumControlPoints; ++i)
    {
        float T = static_cast<float>(i) / NumControlPoints;
        FVector Point = UAURACRONPCGMathLibrary::EvaluateSplineCurve(SerpentineCurve, T);
        
        // Ajustar altura baseada na posição para passar pelos três ambientes
        EAURACRONEnvironmentType EnvType = DetermineEnvironmentType(Point);
        FVector EnvCenter = UAURACRONMapMeasurements::GetEnvironmentCenter(static_cast<int32>(EnvType));
        Point.Z = EnvCenter.Z + FMath::Sin(T * PI) * 200.0f; // Variação suave de altura
        
        Points.Add(Point);
    }
    
    return Points;
}

float AAURACRONPCGPrismalFlow::CalculateFlowWidth(float T, EAURACRONEnvironmentType EnvironmentType)
{
    // Largura base do flow
    float BaseWidth = UAURACRONMapMeasurements::GetPrismalFlowWidth(T);
    
    // Modificadores baseados no ambiente
    float EnvironmentModifier = 1.0f;
    switch (EnvironmentType)
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        EnvironmentModifier = 1.0f; // Largura normal
        break;
        
    case EAURACRONEnvironmentType::ZephyrFirmament:
        EnvironmentModifier = 0.8f; // Mais estreito no ar
        break;
        
    case EAURACRONEnvironmentType::PurgatoryRealm:
        EnvironmentModifier = 1.2f; // Mais largo no reino espectral
        break;
    }
    
    // Modificador baseado na fase do mapa
    float PhaseModifier = CalculateFlowVolatility(CurrentMapPhase);
    
    return BaseWidth * EnvironmentModifier * PhaseModifier;
}

float AAURACRONPCGPrismalFlow::CalculateFlowIntensity(float T, EAURACRONMapPhase MapPhase)
{
    // Intensidade base
    float BaseIntensity = 1.0f;
    
    // Variação ao longo do flow
    float PositionVariation = 0.8f + 0.4f * FMath::Sin(T * 6.0f * PI);
    
    // Modificador baseado na fase
    float PhaseModifier = 1.0f;
    switch (MapPhase)
    {
    case EAURACRONMapPhase::Awakening:
        PhaseModifier = 0.7f;
        break;
        
    case EAURACRONMapPhase::Convergence:
        PhaseModifier = 1.0f;
        break;
        
    case EAURACRONMapPhase::Intensification:
        PhaseModifier = 1.3f;
        break;
        
    case EAURACRONMapPhase::Resolution:
        PhaseModifier = 1.8f;
        break;
    }
    
    return BaseIntensity * PositionVariation * PhaseModifier;
}

void AAURACRONPCGPrismalFlow::GenerateFlowSegments()
{
    FlowSegments.Empty();

    if (!FlowSpline || FlowSpline->GetNumberOfSplinePoints() < 2)
    {
        return;
    }

    int32 NumSegments = NumControlPoints * 2; // Mais segmentos para suavidade

    for (int32 i = 0; i < NumSegments; ++i)
    {
        float T = static_cast<float>(i) / (NumSegments - 1);

        FPrismalFlowSegment Segment;
        Segment.WorldPosition = GetFlowPositionAtT(T);
        // Remover EnvironmentType pois não existe na estrutura
        Segment.Width = CalculateFlowWidth(T, EAURACRONEnvironmentType::RadiantPlains);
        Segment.EnergyIntensity = CalculateFlowIntensity(T, CurrentMapPhase);
        Segment.FlowSpeed = BaseFlowSpeed * (0.8f + 0.4f * FMath::Sin(T * 4.0f * PI));

        // Aplicar efeitos específicos do ambiente
        ApplyEnvironmentEffects(Segment);

        FlowSegments.Add(Segment);
    }
}

void AAURACRONPCGPrismalFlow::GenerateVisualEffects()
{
    if (!MainFlowEffect)
    {
        return;
    }

    // Configurar efeito principal
    MainFlowEffect->SetFloatParameter(FName("GlobalIntensity"), GlobalFlowIntensity);
    MainFlowEffect->SetFloatParameter(FName("FlowSpeed"), BaseFlowSpeed);
    MainFlowEffect->SetFloatParameter(FName("FlowLength"), FlowSpline->GetSplineLength());

    // Criar efeitos adicionais ao longo do flow
    int32 EffectCount = FlowSegments.Num() / 4; // Um efeito a cada 4 segmentos

    for (int32 i = 0; i < EffectCount; ++i)
    {
        int32 SegmentIndex = i * 4;
        if (SegmentIndex < FlowSegments.Num())
        {
            const FPrismalFlowSegment& Segment = FlowSegments[SegmentIndex];

            // Criar componente de efeito
            UNiagaraComponent* SegmentEffect = NewObject<UNiagaraComponent>(this);
            if (SegmentEffect)
            {
                SegmentEffect->AttachToComponent(RootComponent,
                    FAttachmentTransformRules::KeepWorldTransform);
                SegmentEffect->SetWorldLocation(Segment.WorldPosition);

                // Configurar parâmetros baseados no segmento
                SegmentEffect->SetFloatParameter(FName("Intensity"), Segment.EnergyIntensity);
                SegmentEffect->SetFloatParameter(FName("Width"), Segment.Width);
                SegmentEffect->SetFloatParameter(FName("FlowSpeed"), Segment.FlowSpeed);

                // Configurar cor baseada no ambiente
                FLinearColor EnvironmentColor = GetEnvironmentColor(EAURACRONEnvironmentType::RadiantPlains);
                SegmentEffect->SetColorParameter(FName("FlowColor"), EnvironmentColor);

                GeneratedComponents.Add(SegmentEffect);
            }
        }
    }
}

float AAURACRONPCGPrismalFlow::GetHardwareVolatilityMultiplier() const
{
    // Encontrar o Performance Manager no mundo
    AAURACRONPCGPerformanceManager* PerformanceManager = nullptr;
    
    if (UWorld* World = GetWorld())
    {
        for (TActorIterator<AAURACRONPCGPerformanceManager> It(World); It; ++It)
        {
            PerformanceManager = *It;
            break;
        }
    }
    
    if (!PerformanceManager)
    {
        // Retornar multiplicador padrão se não encontrar o Performance Manager
        return 1.0f;
    }
    
    // Obter o perfil de dispositivo atual
    EAURACRONDeviceType DeviceType = PerformanceManager->DetectDeviceType();
    FAURACRONDevicePerformanceProfile DeviceProfile = PerformanceManager->GetCurrentDeviceProfile();
    
    return DeviceProfile.PrismalFlowVolatilityMultiplier;
}

void AAURACRONPCGPrismalFlow::UpdateVolatilityForHardware()
{
    // Recalcular volatilidade com base no hardware atual
    float NewVolatility = CalculateFlowVolatility(CurrentMapPhase);
    
    // Atualizar todos os segmentos do flow
    for (FPrismalFlowSegment& Segment : FlowSegments)
    {
        // Volatility não existe na estrutura, usar EnergyIntensity
        Segment.EnergyIntensity = NewVolatility;
    }
    
    // Atualizar efeito principal
    if (MainFlowEffect)
    {
        MainFlowEffect->SetFloatParameter(FName("FlowVolatility"), NewVolatility);
    }
    
    // ✅ UE_LOGFMT para UE 5.6 - API moderna de logging
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGPrismalFlow::UpdateVolatilityForHardware - Updated volatility to {0}", NewVolatility);
}

void AAURACRONPCGPrismalFlow::GenerateStrategicIslands()
{
    // Usar a função matemática para gerar posições das ilhas
    TArray<FVector> FlowPoints;
    for (const FPrismalFlowSegment& Segment : FlowSegments)
    {
        FlowPoints.Add(Segment.WorldPosition);
    }

    // Gerar diferentes tipos de ilhas
    TArray<EAURACRONIslandType> IslandTypes = {
        EAURACRONIslandType::Nexus,
        EAURACRONIslandType::Sanctuary,
        EAURACRONIslandType::Arsenal,
        EAURACRONIslandType::Chaos
    };

    for (EAURACRONIslandType IslandType : IslandTypes)
    {
        TArray<FVector> IslandPositions = UAURACRONMapMeasurements::GetIslandPositions(
            static_cast<int32>(IslandType),
            FlowPoints
        );

        for (const FVector& IslandPos : IslandPositions)
        {
            // Criar componente de ilha
            UStaticMeshComponent* Island = NewObject<UStaticMeshComponent>(this);
            if (Island)
            {
                Island->AttachToComponent(RootComponent,
                    FAttachmentTransformRules::KeepWorldTransform);
                Island->SetWorldLocation(IslandPos);

                // Configurar escala baseada no tipo de ilha
                float IslandRadius = GetIslandRadius(static_cast<EPrismalFlowIslandType>(IslandType));
                Island->SetWorldScale3D(FVector(IslandRadius / 100.0f)); // Normalizar para escala

                GeneratedComponents.Add(Island);
            }
        }
    }
}

void AAURACRONPCGPrismalFlow::ClearGeneratedElements()
{
    // Limpar componentes gerados
    for (UActorComponent* Component : GeneratedComponents)
    {
        if (IsValid(Component))
        {
            Component->DestroyComponent();
        }
    }

    GeneratedComponents.Empty();
    FlowSegments.Empty();
}

// Implementação do método SetIslandType para APrismalFlowIsland
void APrismalFlowIsland::SetIslandType(EPrismalFlowIslandType NewType)
{
    // Atualizar o tipo de ilha
    IslandType = NewType;
    
    // Atualizar visuais baseados no novo tipo
    UpdateIslandVisuals();
}

// Implementação do método SetFlowPosition para APrismalFlowIsland
void APrismalFlowIsland::SetFlowPosition(float InFlowPosition)
{
    // Atualizar a posição no flow (0-1)
    FlowPosition = FMath::Clamp(InFlowPosition, 0.0f, 1.0f);
}

// Implementação do método SetIslandActive para APrismalFlowIsland
void APrismalFlowIsland::SetIslandActive(bool bActive)
{
    // Atualizar o estado de ativação da ilha
    bIsActive = bActive;
    
    // Ativar/desativar efeitos visuais
    if (IslandEffect)
    {
        IslandEffect->SetActive(bActive);
    }
    
    // Ativar/desativar área de interação
    if (InteractionArea)
    {
        InteractionArea->SetCollisionEnabled(bActive ? ECollisionEnabled::QueryOnly : ECollisionEnabled::NoCollision);
    }
}

// Implementação do construtor APrismalFlowIsland
APrismalFlowIsland::APrismalFlowIsland()
{
    // Configuração padrão
    PrimaryActorTick.bCanEverTick = true;
    
    // Inicializar propriedades
    IslandType = EPrismalFlowIslandType::None;
    FlowPosition = 0.0f;
    bIsActive = false;
    
    // Criar componentes
    RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootComponent"));
    
    // Malha da ilha
    IslandMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("IslandMesh"));
    IslandMesh->SetupAttachment(RootComponent);
    IslandMesh->SetCollisionProfileName(TEXT("BlockAll"));
    
    // Efeito visual da ilha
    IslandEffect = CreateDefaultSubobject<UNiagaraComponent>(TEXT("IslandEffect"));
    IslandEffect->SetupAttachment(RootComponent);
    IslandEffect->SetAutoActivate(false);
    
    // Área de interação
    InteractionArea = CreateDefaultSubobject<USphereComponent>(TEXT("InteractionArea"));
    InteractionArea->SetupAttachment(RootComponent);
    InteractionArea->SetSphereRadius(500.0f);
    InteractionArea->SetCollisionProfileName(TEXT("Trigger"));
    InteractionArea->SetCollisionEnabled(ECollisionEnabled::NoCollision);
}

// Implementação do método UpdateIslandVisuals para APrismalFlowIsland
void APrismalFlowIsland::UpdateIslandVisuals()
{
    // Atualizar visuais baseados no tipo de ilha
    if (IslandMesh)
    {
        // Criar material dinâmico se ainda não existir
        if (!IslandMaterial && IslandMesh->GetMaterial(0))
        {
            IslandMaterial = IslandMesh->CreateAndSetMaterialInstanceDynamic(0);
        }
        
        // Atualizar parâmetros do material baseados no tipo de ilha
        if (IslandMaterial)
        {
            FLinearColor IslandColor;
            
            // Definir cor baseada no tipo de ilha
            switch (IslandType)
            {
            case EPrismalFlowIslandType::Nexus:
                IslandColor = FLinearColor(0.0f, 0.8f, 1.0f); // Azul brilhante
                break;
                
            case EPrismalFlowIslandType::Sanctuary:
                IslandColor = FLinearColor(0.0f, 1.0f, 0.5f); // Verde cura
                break;
                
            case EPrismalFlowIslandType::Arsenal:
                IslandColor = FLinearColor(1.0f, 0.5f, 0.0f); // Laranja poder
                break;
                
            case EPrismalFlowIslandType::Chaos:
                IslandColor = FLinearColor(1.0f, 0.0f, 0.5f); // Roxo caos
                break;
                
            case EPrismalFlowIslandType::Battlefield:
                IslandColor = FLinearColor(1.0f, 0.2f, 0.2f); // Vermelho batalha
                break;
                
            case EPrismalFlowIslandType::Amplifier:
                IslandColor = FLinearColor(0.5f, 0.0f, 1.0f); // Roxo amplificador
                break;
                
            case EPrismalFlowIslandType::Gateway:
                IslandColor = FLinearColor(1.0f, 1.0f, 0.0f); // Amarelo portal
                break;
                
            case EPrismalFlowIslandType::Corrupted:
                IslandColor = FLinearColor(0.2f, 0.0f, 0.3f); // Roxo escuro corrompido
                break;
                
            default:
                IslandColor = FLinearColor(0.5f, 0.5f, 0.5f); // Cinza neutro
                break;
            }
            
            // Aplicar cor ao material
            IslandMaterial->SetVectorParameterValue(FName("IslandColor"), IslandColor);
        }
    }
    
    // Atualizar efeito visual baseado no tipo de ilha
    if (IslandEffect)
    {
        // Configurar parâmetros do efeito baseados no tipo de ilha
        // Implementação específica para cada tipo de ilha
    }
}

// Implementação do método BeginPlay para APrismalFlowIsland
void APrismalFlowIsland::BeginPlay()
{
    Super::BeginPlay();
    
    // Configurar eventos de colisão para a área de interação
    if (InteractionArea)
    {
        InteractionArea->OnComponentBeginOverlap.AddDynamic(this, &APrismalFlowIsland::ApplyIslandEffect);
    }
    
    // Atualizar visuais iniciais
    UpdateIslandVisuals();
}

// Implementação do método Tick para APrismalFlowIsland
void APrismalFlowIsland::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    // Implementar flutuação suave da ilha
    if (IslandMesh && bIsActive)
    {
        // Calcular movimento de flutuação baseado no tempo
        float Time = GetGameTimeSinceCreation();
        float FloatOffset = FMath::Sin(Time * 0.5f) * 10.0f; // 10 unidades de flutuação
        
        // Aplicar movimento de flutuação
        FVector CurrentLocation = IslandMesh->GetRelativeLocation();
        CurrentLocation.Z = FloatOffset;
        IslandMesh->SetRelativeLocation(CurrentLocation);
        
        // Atualizar efeitos visuais se necessário
        if (IslandEffect)
        {
            IslandEffect->SetFloatParameter(FName("Time"), Time);
        }
    }
}

// Implementação do método ApplyIslandEffect para APrismalFlowIsland
void APrismalFlowIsland::ApplyIslandEffect(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
    // Verificar se a ilha está ativa
    if (!bIsActive || !OtherActor)
    {
        return;
    }
    
    // Implementação base que será sobrescrita por classes derivadas
    // Cada tipo de ilha implementará seus próprios efeitos específicos
    
    // Exemplo de efeito visual de feedback
    if (IslandEffect)
    {
        IslandEffect->SetFloatParameter(FName("EffectIntensity"), 2.0f); // Intensificar efeito
        
        // Retornar à intensidade normal após um curto período
        FTimerHandle TimerHandle;
        GetWorldTimerManager().SetTimer(TimerHandle, [this]()
        {
            if (IslandEffect)
            {
                IslandEffect->SetFloatParameter(FName("EffectIntensity"), 1.0f);
            }
        }, 0.5f, false);
    }
}

void AAURACRONPCGPrismalFlow::UpdateDynamicEffects(float DeltaTime)
{
    // Atualizar efeitos baseados no tempo
    for (UActorComponent* Component : GeneratedComponents)
    {
        if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Component))
        {
            // Atualizar parâmetros temporais
            NiagaraComp->SetFloatParameter(FName("Time"), AccumulatedTime);

            // Adicionar variação temporal à intensidade
            float TimeVariation = 1.0f + 0.1f * FMath::Sin(AccumulatedTime * 2.0f);
            float CurrentIntensity = GlobalFlowIntensity * TimeVariation;
            NiagaraComp->SetFloatParameter(FName("DynamicIntensity"), CurrentIntensity);
        }
    }
}

EAURACRONEnvironmentType AAURACRONPCGPrismalFlow::DetermineEnvironmentType(const FVector& Position)
{
    // Determinar ambiente baseado na altura Z
    if (Position.Z > 500.0f)
    {
        return EAURACRONEnvironmentType::ZephyrFirmament;
    }
    else if (Position.Z < -200.0f)
    {
        return EAURACRONEnvironmentType::PurgatoryRealm;
    }
    else
    {
        return EAURACRONEnvironmentType::RadiantPlains;
    }
}

void AAURACRONPCGPrismalFlow::ApplyEnvironmentEffects(FPrismalFlowSegment& Segment)
{
    // Usar ambiente padrão já que EnvironmentType não existe na estrutura
    EAURACRONEnvironmentType CurrentEnvironment = EAURACRONEnvironmentType::RadiantPlains;

    switch (CurrentEnvironment)
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        // Efeitos terrestres - flow mais estável
        Segment.EnergyIntensity *= 1.0f;
        Segment.FlowSpeed *= 1.0f;
        break;

    case EAURACRONEnvironmentType::ZephyrFirmament:
        // Efeitos celestiais - flow mais rápido e brilhante
        Segment.EnergyIntensity *= 1.2f;
        Segment.FlowSpeed *= 1.3f;
        break;

    case EAURACRONEnvironmentType::PurgatoryRealm:
        // Efeitos espectrais - flow mais volátil
        Segment.EnergyIntensity *= 0.8f;
        Segment.FlowSpeed *= 0.9f;
        break;
    }
}

float AAURACRONPCGPrismalFlow::CalculateFlowVolatility(EAURACRONMapPhase MapPhase)
{
    // Calcular volatilidade base baseada na fase do mapa
    float BaseVolatility = 1.0f;
    switch (MapPhase)
    {
    case EAURACRONMapPhase::Awakening:
        BaseVolatility = 1.0f; // Estável
        break;

    case EAURACRONMapPhase::Convergence:
        BaseVolatility = 1.1f; // Ligeiramente instável
        break;

    case EAURACRONMapPhase::Intensification:
        BaseVolatility = 1.3f; // Moderadamente volátil
        break;

    case EAURACRONMapPhase::Resolution:
        BaseVolatility = 1.6f; // Altamente volátil
        break;

    default:
        BaseVolatility = 1.0f;
        break;
    }

    // Aplicar multiplicador baseado na capacidade de hardware
    float HardwareMultiplier = GetHardwareVolatilityMultiplier();
    
    return BaseVolatility * HardwareMultiplier;
}

FLinearColor AAURACRONPCGPrismalFlow::GetEnvironmentColor(EAURACRONEnvironmentType EnvironmentType)
{
    switch (EnvironmentType)
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        return FLinearColor(0.2f, 0.8f, 0.3f, 1.0f); // Verde cristalino

    case EAURACRONEnvironmentType::ZephyrFirmament:
        return FLinearColor(0.3f, 0.6f, 1.0f, 1.0f); // Azul celestial

    case EAURACRONEnvironmentType::PurgatoryRealm:
        return FLinearColor(0.8f, 0.2f, 0.8f, 1.0f); // Roxo espectral

    default:
        return FLinearColor::White;
    }
}

float AAURACRONPCGPrismalFlow::GetIslandRadius(EPrismalFlowIslandType IslandType)
{
    switch (IslandType)
    {
    case EPrismalFlowIslandType::Nexus:
        return FAURACRONMapDimensions::NEXUS_ISLAND_RADIUS_CM;

    case EPrismalFlowIslandType::Sanctuary:
        return FAURACRONMapDimensions::SANCTUARY_ISLAND_RADIUS_CM;

    case EPrismalFlowIslandType::Arsenal:
        return FAURACRONMapDimensions::ARSENAL_ISLAND_RADIUS_CM;

    case EPrismalFlowIslandType::Chaos:
        return FAURACRONMapDimensions::CHAOS_ISLAND_RADIUS_CM;

    default:
        return 100.0f;
    }
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES FALTANTES - UE 5.6 APIS MODERNAS
// ========================================

void AAURACRONPCGPrismalFlow::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    // Replicar propriedades usando APIs modernas do UE 5.6
    DOREPLIFETIME(AAURACRONPCGPrismalFlow, GlobalFlowIntensity);
    DOREPLIFETIME(AAURACRONPCGPrismalFlow, BaseFlowSpeed);
    DOREPLIFETIME(AAURACRONPCGPrismalFlow, NumControlPoints);
    DOREPLIFETIME(AAURACRONPCGPrismalFlow, SerpentineAmplitude);
    DOREPLIFETIME(AAURACRONPCGPrismalFlow, SerpentineFrequency);
    DOREPLIFETIME(AAURACRONPCGPrismalFlow, CurrentMapPhase);
    DOREPLIFETIME(AAURACRONPCGPrismalFlow, ControllingTeam);
    DOREPLIFETIME(AAURACRONPCGPrismalFlow, FlowSpline);
    DOREPLIFETIME(AAURACRONPCGPrismalFlow, FlowMesh);
    DOREPLIFETIME(AAURACRONPCGPrismalFlow, FlowEffect);
}

void AAURACRONPCGPrismalFlow::OnFlowActivated()
{
    // Callback quando Prismal Flow é ativado usando APIs modernas do UE 5.6
    // ✅ UE_LOGFMT para UE 5.6 - API moderna de logging
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGPrismalFlow::OnFlowActivated - Prismal Flow {0} activated", GetName());

    // Ativar componentes do flow
    if (PCGComponent && IsValid(PCGComponent))
    {
        PCGComponent->SetComponentTickEnabled(true);

        // Regenerar conteúdo PCG se necessário
        if (GlobalFlowIntensity > 0.0f)
        {
            PCGComponent->GenerateLocal(false); // Regeneração não-bloqueante
        }
    }

    // Ativar spline do flow
    if (FlowSpline && IsValid(FlowSpline))
    {
        FlowSpline->SetVisibility(true);
        FlowSpline->SetComponentTickEnabled(true);
    }

    // Ativar mesh do flow
    if (FlowMesh && IsValid(FlowMesh))
    {
        FlowMesh->SetVisibility(true);
        FlowMesh->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    }

    // Ativar efeitos visuais
    if (FlowEffect && IsValid(FlowEffect))
    {
        FlowEffect->SetVisibility(true);
        FlowEffect->Activate();

        // Configurar parâmetros do efeito baseado na intensidade
        FlowEffect->SetFloatParameter(FName("FlowIntensity"), GlobalFlowIntensity);
        FlowEffect->SetFloatParameter(FName("FlowSpeed"), BaseFlowSpeed);

        // Configurar cor baseada na fase do mapa
        FLinearColor FlowColor = GetFlowColorForPhase(CurrentMapPhase);
        FlowEffect->SetColorParameter(FName("FlowColor"), FlowColor);
    }

    // Ativar componentes gerados
    for (UActorComponent* Component : GeneratedComponents)
    {
        if (Component && IsValid(Component))
        {
            Component->SetComponentTickEnabled(true);

            if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
            {
                MeshComp->SetVisibility(true);
            }
            else if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Component))
            {
                NiagaraComp->SetVisibility(true);
                NiagaraComp->Activate();
            }
        }
    }

    // Ativar tick do ator
    SetActorTickEnabled(true);
    SetActorHiddenInGame(false);

    // ✅ UE_LOGFMT para UE 5.6 - API moderna de logging
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGPrismalFlow::OnFlowActivated - Prismal Flow activation completed");
}

void AAURACRONPCGPrismalFlow::OnFlowDeactivated()
{
    // Callback quando Prismal Flow é desativado usando APIs modernas do UE 5.6
    // ✅ UE_LOGFMT para UE 5.6 - API moderna de logging
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGPrismalFlow::OnFlowDeactivated - Prismal Flow {0} deactivated", GetName());

    // Desativar componentes do flow
    if (PCGComponent && IsValid(PCGComponent))
    {
        PCGComponent->SetComponentTickEnabled(false);
    }

    // Desativar spline do flow
    if (FlowSpline && IsValid(FlowSpline))
    {
        FlowSpline->SetVisibility(false);
        FlowSpline->SetComponentTickEnabled(false);
    }

    // Desativar mesh do flow
    if (FlowMesh && IsValid(FlowMesh))
    {
        FlowMesh->SetVisibility(false);
        FlowMesh->SetCollisionEnabled(ECollisionEnabled::NoCollision);
    }

    // Desativar efeitos visuais
    if (FlowEffect && IsValid(FlowEffect))
    {
        FlowEffect->SetVisibility(false);
        FlowEffect->Deactivate();
    }

    // Desativar componentes gerados
    for (UActorComponent* Component : GeneratedComponents)
    {
        if (Component && IsValid(Component))
        {
            Component->SetComponentTickEnabled(false);

            if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
            {
                MeshComp->SetVisibility(false);
            }
            else if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Component))
            {
                NiagaraComp->SetVisibility(false);
                NiagaraComp->Deactivate();
            }
        }
    }

    // Desativar tick do ator
    SetActorTickEnabled(false);
    SetActorHiddenInGame(true);

    // ✅ UE_LOGFMT para UE 5.6 - API moderna de logging
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGPrismalFlow::OnFlowDeactivated - Prismal Flow deactivation completed");
}

void AAURACRONPCGPrismalFlow::UpdateFlowPhase(EAURACRONMapPhase NewPhase)
{
    // Atualizar Prismal Flow para nova fase do mapa usando APIs modernas do UE 5.6
    if (CurrentMapPhase == NewPhase)
    {
        return; // Já está na fase correta
    }

    // ✅ UE_LOGFMT para UE 5.6 - API moderna de logging
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGPrismalFlow::UpdateFlowPhase - Updating flow {0} from phase {1} to phase {2}",
           GetName(), (int32)CurrentMapPhase, (int32)NewPhase);

    EAURACRONMapPhase PreviousPhase = CurrentMapPhase;
    CurrentMapPhase = NewPhase;

    // Aplicar mudanças baseadas na nova fase
    switch (NewPhase)
    {
        case EAURACRONMapPhase::Awakening:
        {
            // Fase inicial: flow suave e calmo
            GlobalFlowIntensity = 0.6f;
            BaseFlowSpeed = 0.8f;
            SerpentineAmplitude = 1200.0f; // Menos serpentino
            SerpentineFrequency = 3.0f;
            break;
        }

        case EAURACRONMapPhase::Convergence:
        {
            // Fase de convergência: flow mais intenso
            GlobalFlowIntensity = 0.8f;
            BaseFlowSpeed = 1.0f;
            SerpentineAmplitude = 1500.0f; // Amplitude normal
            SerpentineFrequency = 4.0f;
            break;
        }

        case EAURACRONMapPhase::Intensification:
        {
            // Fase de intensificação: flow muito ativo
            GlobalFlowIntensity = 1.0f;
            BaseFlowSpeed = 1.3f;
            SerpentineAmplitude = 1800.0f; // Mais serpentino
            SerpentineFrequency = 5.0f;
            break;
        }

        case EAURACRONMapPhase::Resolution:
        {
            // Fase final: flow caótico e poderoso
            GlobalFlowIntensity = 1.5f; // Além do normal
            BaseFlowSpeed = 1.8f;
            SerpentineAmplitude = 2200.0f; // Muito serpentino
            SerpentineFrequency = 6.0f;
            break;
        }
    }

    // Atualizar efeitos visuais baseados na nova fase
    if (FlowEffect && IsValid(FlowEffect))
    {
        FlowEffect->SetFloatParameter(FName("FlowIntensity"), GlobalFlowIntensity);
        FlowEffect->SetFloatParameter(FName("FlowSpeed"), BaseFlowSpeed);
        FlowEffect->SetFloatParameter(FName("SerpentineAmplitude"), SerpentineAmplitude);
        FlowEffect->SetFloatParameter(FName("SerpentineFrequency"), SerpentineFrequency);

        // Configurar cor baseada na fase
        FLinearColor FlowColor = GetFlowColorForPhase(NewPhase);
        FlowEffect->SetColorParameter(FName("FlowColor"), FlowColor);
    }

    // Atualizar componentes gerados baseados na nova fase
    for (UActorComponent* Component : GeneratedComponents)
    {
        if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Component))
        {
            NiagaraComp->SetFloatParameter(FName("PhaseIntensity"), GlobalFlowIntensity);
            NiagaraComp->SetFloatParameter(FName("PhaseSpeed"), BaseFlowSpeed);

            FLinearColor PhaseColor = GetFlowColorForPhase(NewPhase);
            NiagaraComp->SetColorParameter(FName("PhaseColor"), PhaseColor);
        }
    }

    // Regenerar spline se necessário
    if (FlowSpline && IsValid(FlowSpline))
    {
        // Regenerar pontos da spline com nova amplitude e frequência
        GenerateFlowPath();
    }

    // Regenerar conteúdo PCG se necessário
    if (PCGComponent && IsValid(PCGComponent) && GlobalFlowIntensity > 0.0f)
    {
        PCGComponent->GenerateLocal(false); // Regeneração não-bloqueante
    }

    // ✅ UE_LOGFMT para UE 5.6 - API moderna de logging
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGPrismalFlow::UpdateFlowPhase - Flow phase update completed");
}

FLinearColor AAURACRONPCGPrismalFlow::GetFlowColorForPhase(EAURACRONMapPhase Phase) const
{
    // Se houver uma equipe controladora, usar a cor da equipe em vez da cor da fase
    if (ControllingTeam > 0)
    {
        return GetFlowColorForTeam();
    }
    
    // Obter cor do flow baseada na fase do mapa usando paleta moderna
    switch (Phase)
    {
        case EAURACRONMapPhase::Awakening:
            return FLinearColor(0.8f, 0.9f, 1.0f, 1.0f); // Azul suave (despertar)

        case EAURACRONMapPhase::Convergence:
            return FLinearColor(1.0f, 1.0f, 0.9f, 1.0f); // Branco dourado (convergência)

        case EAURACRONMapPhase::Intensification:
            return FLinearColor(1.0f, 0.7f, 0.4f, 1.0f); // Laranja intenso (intensificação)

        case EAURACRONMapPhase::Resolution:
            return FLinearColor(0.9f, 0.3f, 1.0f, 1.0f); // Roxo místico (resolução)

        default:
            return FLinearColor::White;
    }
}

void AAURACRONPCGPrismalFlow::GenerateFlowPath()
{
    // Gerar caminho do flow usando APIs modernas do UE 5.6
    if (!FlowSpline || !IsValid(FlowSpline))
    {
        return;
    }

    // ✅ UE_LOGFMT para UE 5.6 - API moderna de logging
    UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGPrismalFlow::GenerateFlowPath - Generating flow path");

    // Limpar pontos existentes
    FlowSpline->ClearSplinePoints();

    // Gerar pontos baseados na configuração atual
    for (int32 i = 0; i < NumControlPoints; ++i)
    {
        float Alpha = static_cast<float>(i) / static_cast<float>(NumControlPoints - 1);

        // Posição base ao longo do eixo X
        float X = Alpha * 10000.0f; // 100 metros de comprimento

        // Aplicar curva serpentina
        float Y = FMath::Sin(Alpha * SerpentineFrequency * PI) * SerpentineAmplitude;

        // Variação de altura baseada na intensidade
        float Z = FMath::Sin(Alpha * 2.0f * PI) * (GlobalFlowIntensity * 200.0f);

        FVector SplinePoint(X, Y, Z);
        FlowSpline->AddSplinePoint(SplinePoint, ESplineCoordinateSpace::Local);
    }

    // Atualizar spline
    FlowSpline->UpdateSpline();

    // ✅ UE_LOGFMT para UE 5.6 - API moderna de logging
    UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGPrismalFlow::GenerateFlowPath - Generated {0} spline points", NumControlPoints);
}

// ========================================
// ✅ IMPLEMENTAÇÃO DA FUNÇÃO FALTANTE GetFlowColorForTeam() - UE 5.6
// ========================================

FLinearColor AAURACRONPCGPrismalFlow::GetFlowColorForTeam() const
{
    // ✅ Implementar cores baseadas na equipe controladora usando paleta moderna UE 5.6
    switch (ControllingTeam)
    {
        case 1: // Equipe 1 - Azul
            return FLinearColor(0.2f, 0.6f, 1.0f, 1.0f); // Azul cristalino

        case 2: // Equipe 2 - Vermelho
            return FLinearColor(1.0f, 0.3f, 0.2f, 1.0f); // Vermelho intenso

        case 3: // Equipe 3 - Verde
            return FLinearColor(0.3f, 1.0f, 0.4f, 1.0f); // Verde vibrante

        case 4: // Equipe 4 - Amarelo
            return FLinearColor(1.0f, 0.9f, 0.2f, 1.0f); // Amarelo dourado

        case 5: // Equipe 5 - Roxo
            return FLinearColor(0.8f, 0.3f, 1.0f, 1.0f); // Roxo místico

        case 6: // Equipe 6 - Ciano
            return FLinearColor(0.2f, 1.0f, 0.8f, 1.0f); // Ciano brilhante

        case 7: // Equipe 7 - Laranja
            return FLinearColor(1.0f, 0.6f, 0.2f, 1.0f); // Laranja energético

        case 8: // Equipe 8 - Rosa
            return FLinearColor(1.0f, 0.4f, 0.8f, 1.0f); // Rosa vibrante

        default: // Neutro ou equipe inválida
            return FLinearColor::White; // Branco neutro
    }
}

// ========================================
// ✅ IMPLEMENTAÇÃO DAS FUNÇÕES DE TIMER - UE 5.6 OTIMIZAÇÃO
// ========================================

void AAURACRONPCGPrismalFlow::UpdateDynamicEffectsTimer()
{
    // ✅ Função Timer para UpdateDynamicEffects - otimizada para 10 FPS
    if (HasAuthority() && GetWorld())
    {
        float DeltaTime = 0.1f; // Timer fixo de 0.1s
        UpdateDynamicEffects(DeltaTime);
    }
}

void AAURACRONPCGPrismalFlow::UpdateTimeBasedParametersTimer()
{
    // ✅ Função Timer para UpdateTimeBasedParameters - otimizada para 20 FPS
    if (HasAuthority())
    {
        UpdateTimeBasedParameters();
    }
}

// ========================================
// ✅ IMPLEMENTAÇÃO DE STREAMING INTELIGENTE - UE 5.6
// ========================================

void AAURACRONPCGPrismalFlow::UpdateIntelligentStreaming(float DeltaTime)
{
    // ✅ Sistema de streaming inteligente baseado na documentação
    if (!GetWorld() || !bStreamingEnabled)
    {
        return;
    }

    // Encontrar jogadores próximos para preloading preditivo
    TArray<APawn*> NearbyPlayers;
    for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        if (APlayerController* PC = Iterator->Get())
        {
            if (APawn* PlayerPawn = PC->GetPawn())
            {
                float Distance = FVector::Dist(GetActorLocation(), PlayerPawn->GetActorLocation());
                if (Distance <= StreamingDistance)
                {
                    NearbyPlayers.Add(PlayerPawn);
                }
            }
        }
    }

    // Preloading preditivo baseado na direção dos jogadores
    for (APawn* Player : NearbyPlayers)
    {
        if (IsValid(Player))
        {
            FVector PlayerVelocity = Player->GetVelocity();
            if (PlayerVelocity.Size() > 100.0f) // Jogador se movendo
            {
                FVector PredictedPosition = Player->GetActorLocation() + (PlayerVelocity * 2.0f); // Predição 2s
                PreloadAssetsForPosition(PredictedPosition);
            }
        }
    }

    // Unloading agressivo de assets não utilizados
    UnloadUnusedAssets();
}

// ========================================
// ✅ IMPLEMENTAÇÃO DE VALIDAÇÃO ANTI-CHEAT SERVER-SIDE - UE 5.6
// ========================================

void AAURACRONPCGPrismalFlow::ValidateFlowIntegrityServerSide()
{
    // ✅ Validação anti-cheat server-side baseada na documentação
    if (!HasAuthority())
    {
        return; // Apenas no servidor
    }

    // Validar integridade dos parâmetros do flow
    if (GlobalFlowIntensity < 0.0f || GlobalFlowIntensity > 3.0f)
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGPrismalFlow::ValidateFlowIntegrityServerSide - Invalid GlobalFlowIntensity: {0}, resetting to 1.0", GlobalFlowIntensity);
        GlobalFlowIntensity = 1.0f;
    }

    if (BaseFlowSpeed < 0.0f || BaseFlowSpeed > 5.0f)
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGPrismalFlow::ValidateFlowIntegrityServerSide - Invalid BaseFlowSpeed: {0}, resetting to 1.0", BaseFlowSpeed);
        BaseFlowSpeed = 1.0f;
    }

    // Validar integridade dos segmentos do flow
    for (int32 i = 0; i < FlowSegments.Num(); ++i)
    {
        FPrismalFlowSegment& Segment = FlowSegments[i];

        // Validar posição mundial
        if (!Segment.WorldPosition.IsFinite())
        {
            UE_LOGFMT(LogTemp, Error, "AAURACRONPCGPrismalFlow::ValidateFlowIntegrityServerSide - Invalid WorldPosition in segment {0}, resetting", i);
            Segment.WorldPosition = FVector::ZeroVector;
        }

        // Validar largura
        if (Segment.Width < 20.0f || Segment.Width > 50.0f)
        {
            UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGPrismalFlow::ValidateFlowIntegrityServerSide - Invalid Width in segment {0}: {1}, clamping", i, Segment.Width);
            Segment.Width = FMath::Clamp(Segment.Width, 20.0f, 50.0f);
        }

        // Validar intensidade de energia
        if (Segment.EnergyIntensity < 0.0f || Segment.EnergyIntensity > 3.0f)
        {
            UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGPrismalFlow::ValidateFlowIntegrityServerSide - Invalid EnergyIntensity in segment {0}: {1}, clamping", i, Segment.EnergyIntensity);
            Segment.EnergyIntensity = FMath::Clamp(Segment.EnergyIntensity, 0.0f, 3.0f);
        }
    }

    // Validar componentes críticos
    if (!IsValid(FlowSpline))
    {
        UE_LOGFMT(LogTemp, Error, "AAURACRONPCGPrismalFlow::ValidateFlowIntegrityServerSide - FlowSpline is invalid, regenerating");
        // Regenerar spline se inválida
        if (GetWorld())
        {
            GetWorld()->GetTimerManager().SetTimerForNextTick(this, &AAURACRONPCGPrismalFlow::GeneratePrismalFlow);
        }
    }
}

// ========================================
// ✅ IMPLEMENTAÇÃO DE ORÇAMENTO DE PARTÍCULAS ESCALÁVEL - UE 5.6
// ========================================

void AAURACRONPCGPrismalFlow::UpdateParticlesBudgetForHardware()
{
    // ✅ Atualizar orçamento de partículas baseado no hardware detectado
    AAURACRONPCGPerformanceManager* PerformanceManager = nullptr;

    if (UWorld* World = GetWorld())
    {
        for (TActorIterator<AAURACRONPCGPerformanceManager> It(World); It; ++It)
        {
            PerformanceManager = *It;
            break;
        }
    }

    if (PerformanceManager)
    {
        EAURACRONDeviceType DeviceType = PerformanceManager->DetectDeviceType();

        switch (DeviceType)
        {
            case EAURACRONDeviceType::Entry:
                CurrentParticlesBudget = ParticlesBudgetEntry; // 300 partículas
                break;

            case EAURACRONDeviceType::Mid:
                CurrentParticlesBudget = ParticlesBudgetMid; // 800 partículas
                break;

            case EAURACRONDeviceType::High:
                CurrentParticlesBudget = ParticlesBudgetHigh; // 2000 partículas
                break;

            default:
                CurrentParticlesBudget = ParticlesBudgetEntry; // Padrão seguro
                break;
        }

        UE_LOGFMT(LogTemp, Log, "AAURACRONPCGPrismalFlow::UpdateParticlesBudgetForHardware - Device: {0}, Particles Budget: {1}",
                 (int32)DeviceType, CurrentParticlesBudget);
    }
    else
    {
        // Fallback para Entry Level se não encontrar Performance Manager
        CurrentParticlesBudget = ParticlesBudgetEntry;
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGPrismalFlow::UpdateParticlesBudgetForHardware - Performance Manager not found, using Entry Level budget");
    }

    // Aplicar orçamento aos componentes Niagara existentes
    ApplyParticlesBudgetToComponents();
}

void AAURACRONPCGPrismalFlow::ApplyParticlesBudgetToComponents()
{
    // ✅ Aplicar orçamento de partículas aos componentes Niagara
    if (MainFlowEffect && IsValid(MainFlowEffect))
    {
        // Configurar orçamento principal (60% do total)
        int32 MainEffectBudget = FMath::FloorToInt(CurrentParticlesBudget * 0.6f);
        MainFlowEffect->SetIntParameter(FName("MaxParticles"), MainEffectBudget);
        MainFlowEffect->SetFloatParameter(FName("ParticlesBudget"), (float)MainEffectBudget);
    }

    // Distribuir orçamento restante entre componentes gerados
    int32 RemainingBudget = CurrentParticlesBudget - FMath::FloorToInt(CurrentParticlesBudget * 0.6f);
    int32 NiagaraComponentsCount = 0;

    // Contar componentes Niagara
    for (UActorComponent* Component : GeneratedComponents)
    {
        if (Cast<UNiagaraComponent>(Component))
        {
            NiagaraComponentsCount++;
        }
    }

    if (NiagaraComponentsCount > 0)
    {
        int32 BudgetPerComponent = FMath::Max(1, RemainingBudget / NiagaraComponentsCount);

        for (UActorComponent* Component : GeneratedComponents)
        {
            if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Component))
            {
                NiagaraComp->SetIntParameter(FName("MaxParticles"), BudgetPerComponent);
                NiagaraComp->SetFloatParameter(FName("ParticlesBudget"), (float)BudgetPerComponent);
            }
        }
    }
}

// ========================================
// ✅ IMPLEMENTAÇÃO DE STREAMING INTELIGENTE - UE 5.6
// ========================================

void AAURACRONPCGPrismalFlow::PreloadAssetsForPosition(const FVector& Position)
{
    // ✅ Preloading preditivo baseado na posição
    if (!StreamableManager || !IsValid(StreamableManager))
    {
        return;
    }

    // Determinar tipo de ambiente baseado na posição
    EAURACRONEnvironmentType EnvironmentType = DetermineEnvironmentType(Position);

    // Precarregar assets específicos do ambiente
    TArray<FSoftObjectPath> AssetsToLoad;

    switch (EnvironmentType)
    {
        case EAURACRONEnvironmentType::RadiantPlains:
            AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/VFX/Environments/RadiantPlains/NiagaraSystem_RadiantFlow")));
            AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/Materials/Environments/RadiantPlains/M_RadiantFlowMaterial")));
            break;

        case EAURACRONEnvironmentType::ZephyrFirmament:
            AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/VFX/Environments/ZephyrFirmament/NiagaraSystem_ZephyrFlow")));
            AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/Materials/Environments/ZephyrFirmament/M_ZephyrFlowMaterial")));
            break;

        case EAURACRONEnvironmentType::PurgatoryRealm:
            AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/VFX/Environments/PurgatoryRealm/NiagaraSystem_PurgatoryFlow")));
            AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/Materials/Environments/PurgatoryRealm/M_PurgatoryFlowMaterial")));
            break;
    }

    if (AssetsToLoad.Num() > 0)
    {
        TSharedPtr<FStreamableHandle> Handle = StreamableManager->RequestAsyncLoad(
            AssetsToLoad,
            FStreamableDelegate::CreateUObject(this, &AAURACRONPCGPrismalFlow::OnEnvironmentAssetsLoaded, EnvironmentType)
        );

        // Armazenar handle para controle
        if (Handle.IsValid())
        {
            ActiveStreamingHandles.Add(Handle);
        }
    }
}

void AAURACRONPCGPrismalFlow::UnloadUnusedAssets()
{
    // ✅ Unloading agressivo de assets não utilizados

    // Limpar handles de streaming completados
    for (int32 i = ActiveStreamingHandles.Num() - 1; i >= 0; --i)
    {
        if (!ActiveStreamingHandles[i].IsValid() || ActiveStreamingHandles[i]->HasLoadCompleted())
        {
            ActiveStreamingHandles.RemoveAt(i);
        }
    }

    // Forçar garbage collection se muitos assets foram descarregados
    if (ActiveStreamingHandles.Num() == 0 && GetWorld())
    {
        GetWorld()->ForceGarbageCollection(true);
    }
}

void AAURACRONPCGPrismalFlow::OnEnvironmentAssetsLoaded(EAURACRONEnvironmentType EnvironmentType)
{
    // ✅ Callback quando assets do ambiente são carregados
    UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGPrismalFlow::OnEnvironmentAssetsLoaded - Assets loaded for environment type: {0}", (int32)EnvironmentType);

    // Aplicar assets carregados aos componentes apropriados
    ApplyEnvironmentAssetsToComponents(EnvironmentType);
}

void AAURACRONPCGPrismalFlow::OnTransitionMaterialLoaded(UStaticMeshComponent* TargetComponent)
{
    // ✅ Callback quando material de transição é carregado assincronamente
    if (!IsValid(TargetComponent))
    {
        return;
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGPrismalFlow::OnTransitionMaterialLoaded - Material loaded for component: {0}", TargetComponent->GetName());

    // Aplicar material carregado
    if (UMaterialInterface* LoadedMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial")))
    {
        UMaterialInstanceDynamic* TransitionMaterial = UMaterialInstanceDynamic::Create(LoadedMaterial, this);
        if (TransitionMaterial)
        {
            // Configurar parâmetros do material
            TransitionMaterial->SetScalarParameterValue(FName("FlowIntensity"), GlobalFlowIntensity);
            TransitionMaterial->SetScalarParameterValue(FName("FlowSpeed"), BaseFlowSpeed);
            TransitionMaterial->SetVectorParameterValue(FName("FlowColor"), GetFlowColorForPhase(CurrentMapPhase));

            TargetComponent->SetMaterial(0, TransitionMaterial);
        }
    }
}

// ========================================
// ✅ IMPLEMENTAÇÃO DE INTEGRAÇÃO COM TRILHOS DINÂMICOS - UE 5.6
// ========================================

void AAURACRONPCGPrismalFlow::ApplyEnvironmentAssetsToComponents(EAURACRONEnvironmentType EnvironmentType)
{
    // ✅ Aplicar assets carregados aos componentes baseado no ambiente
    for (UActorComponent* Component : GeneratedComponents)
    {
        if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Component))
        {
            // Configurar parâmetros específicos do ambiente
            switch (EnvironmentType)
            {
                case EAURACRONEnvironmentType::RadiantPlains:
                    NiagaraComp->SetColorParameter(FName("EnvironmentColor"), FLinearColor(0.2f, 0.8f, 0.3f, 1.0f));
                    NiagaraComp->SetFloatParameter(FName("EnvironmentIntensity"), 1.0f);
                    break;

                case EAURACRONEnvironmentType::ZephyrFirmament:
                    NiagaraComp->SetColorParameter(FName("EnvironmentColor"), FLinearColor(0.3f, 0.6f, 1.0f, 1.0f));
                    NiagaraComp->SetFloatParameter(FName("EnvironmentIntensity"), 1.2f);
                    break;

                case EAURACRONEnvironmentType::PurgatoryRealm:
                    NiagaraComp->SetColorParameter(FName("EnvironmentColor"), FLinearColor(0.8f, 0.2f, 0.8f, 1.0f));
                    NiagaraComp->SetFloatParameter(FName("EnvironmentIntensity"), 0.8f);
                    break;
            }
        }
    }
}

void AAURACRONPCGPrismalFlow::IntegrateWithDynamicRails()
{
    // ✅ Integração com sistema de Trilhos dinâmicos Solar/Axis/Lunar
    if (!GetWorld())
    {
        return;
    }

    // Encontrar trilhos dinâmicos no mundo
    TArray<AActor*> SolarRails;
    TArray<AActor*> AxisRails;
    TArray<AActor*> LunarRails;

    // Buscar trilhos por tag ou classe
    UGameplayStatics::GetAllActorsWithTag(GetWorld(), FName("SolarRail"), SolarRails);
    UGameplayStatics::GetAllActorsWithTag(GetWorld(), FName("AxisRail"), AxisRails);
    UGameplayStatics::GetAllActorsWithTag(GetWorld(), FName("LunarRail"), LunarRails);

    // Integrar com Trilhos Solar
    for (AActor* SolarRail : SolarRails)
    {
        if (IsValid(SolarRail))
        {
            IntegrateWithSolarRail(SolarRail);
        }
    }

    // Integrar com Trilhos Axis
    for (AActor* AxisRail : AxisRails)
    {
        if (IsValid(AxisRail))
        {
            IntegrateWithAxisRail(AxisRail);
        }
    }

    // Integrar com Trilhos Lunar
    for (AActor* LunarRail : LunarRails)
    {
        if (IsValid(LunarRail))
        {
            IntegrateWithLunarRail(LunarRail);
        }
    }
}

void AAURACRONPCGPrismalFlow::IntegrateWithSolarRail(AActor* SolarRail)
{
    // ✅ Integração específica com Trilho Solar
    if (!IsValid(SolarRail) || !FlowSpline)
    {
        return;
    }

    // Encontrar pontos de intersecção entre Fluxo Prismal e Trilho Solar
    TArray<FVector> IntersectionPoints = FindIntersectionPoints(SolarRail);

    for (const FVector& IntersectionPoint : IntersectionPoints)
    {
        // Criar efeito de intersecção Solar
        CreateSolarIntersectionEffect(IntersectionPoint);

        // Aplicar boost de energia solar ao flow
        ApplySolarEnergyBoost(IntersectionPoint);
    }
}

void AAURACRONPCGPrismalFlow::IntegrateWithAxisRail(AActor* AxisRail)
{
    // ✅ Integração específica com Trilho Axis
    if (!IsValid(AxisRail) || !FlowSpline)
    {
        return;
    }

    // Encontrar pontos de intersecção entre Fluxo Prismal e Trilho Axis
    TArray<FVector> IntersectionPoints = FindIntersectionPoints(AxisRail);

    for (const FVector& IntersectionPoint : IntersectionPoints)
    {
        // Criar efeito de intersecção Axis
        CreateAxisIntersectionEffect(IntersectionPoint);

        // Aplicar estabilização gravitacional ao flow
        ApplyAxisGravitationalStabilization(IntersectionPoint);
    }
}

void AAURACRONPCGPrismalFlow::IntegrateWithLunarRail(AActor* LunarRail)
{
    // ✅ Integração específica com Trilho Lunar
    if (!IsValid(LunarRail) || !FlowSpline)
    {
        return;
    }

    // Encontrar pontos de intersecção entre Fluxo Prismal e Trilho Lunar
    TArray<FVector> IntersectionPoints = FindIntersectionPoints(LunarRail);

    for (const FVector& IntersectionPoint : IntersectionPoints)
    {
        // Criar efeito de intersecção Lunar
        CreateLunarIntersectionEffect(IntersectionPoint);

        // Aplicar mudança de fase etérea ao flow
        ApplyLunarPhaseShift(IntersectionPoint);
    }
}

// ========================================
// ✅ IMPLEMENTAÇÃO DE FUNÇÕES AUXILIARES PARA TRILHOS - UE 5.6
// ========================================

TArray<FVector> AAURACRONPCGPrismalFlow::FindIntersectionPoints(AActor* RailActor)
{
    // ✅ Encontrar pontos de intersecção entre Fluxo Prismal e Trilho
    TArray<FVector> IntersectionPoints;

    if (!IsValid(RailActor) || !FlowSpline)
    {
        return IntersectionPoints;
    }

    // Tentar obter spline do trilho
    USplineComponent* RailSpline = RailActor->FindComponentByClass<USplineComponent>();
    if (!RailSpline)
    {
        return IntersectionPoints;
    }

    // Verificar intersecções ao longo dos splines
    int32 FlowPoints = FlowSpline->GetNumberOfSplinePoints();
    int32 RailPoints = RailSpline->GetNumberOfSplinePoints();

    for (int32 i = 0; i < FlowPoints - 1; ++i)
    {
        FVector FlowStart = FlowSpline->GetLocationAtSplinePoint(i, ESplineCoordinateSpace::World);
        FVector FlowEnd = FlowSpline->GetLocationAtSplinePoint(i + 1, ESplineCoordinateSpace::World);

        for (int32 j = 0; j < RailPoints - 1; ++j)
        {
            FVector RailStart = RailSpline->GetLocationAtSplinePoint(j, ESplineCoordinateSpace::World);
            FVector RailEnd = RailSpline->GetLocationAtSplinePoint(j + 1, ESplineCoordinateSpace::World);

            // Verificar intersecção entre segmentos
            FVector IntersectionPoint;
            if (FindLineIntersection(FlowStart, FlowEnd, RailStart, RailEnd, IntersectionPoint))
            {
                IntersectionPoints.Add(IntersectionPoint);
            }
        }
    }

    return IntersectionPoints;
}

bool AAURACRONPCGPrismalFlow::FindLineIntersection(const FVector& Line1Start, const FVector& Line1End,
                                                   const FVector& Line2Start, const FVector& Line2End,
                                                   FVector& OutIntersection)
{
    // ✅ Encontrar intersecção entre duas linhas 3D (projeção no plano XY)
    FVector2D L1Start(Line1Start.X, Line1Start.Y);
    FVector2D L1End(Line1End.X, Line1End.Y);
    FVector2D L2Start(Line2Start.X, Line2Start.Y);
    FVector2D L2End(Line2End.X, Line2End.Y);

    FVector2D L1Dir = L1End - L1Start;
    FVector2D L2Dir = L2End - L2Start;

    float Denominator = L1Dir.X * L2Dir.Y - L1Dir.Y * L2Dir.X;

    if (FMath::IsNearlyZero(Denominator))
    {
        return false; // Linhas paralelas
    }

    FVector2D StartDiff = L2Start - L1Start;
    float T1 = (StartDiff.X * L2Dir.Y - StartDiff.Y * L2Dir.X) / Denominator;
    float T2 = (StartDiff.X * L1Dir.Y - StartDiff.Y * L1Dir.X) / Denominator;

    if (T1 >= 0.0f && T1 <= 1.0f && T2 >= 0.0f && T2 <= 1.0f)
    {
        // Intersecção encontrada
        FVector2D Intersection2D = L1Start + T1 * L1Dir;

        // Interpolar altura Z
        float ZHeight = FMath::Lerp(Line1Start.Z, Line1End.Z, T1);

        OutIntersection = FVector(Intersection2D.X, Intersection2D.Y, ZHeight);
        return true;
    }

    return false;
}

void AAURACRONPCGPrismalFlow::CreateSolarIntersectionEffect(const FVector& IntersectionPoint)
{
    // ✅ Criar efeito visual de intersecção Solar
    UNiagaraComponent* SolarEffect = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
        GetWorld(),
        nullptr, // Sistema será carregado assincronamente
        IntersectionPoint,
        FRotator::ZeroRotator,
        FVector::OneVector,
        true, // Auto destroy
        true, // Auto activate
        ENCPoolMethod::None
    );

    if (SolarEffect)
    {
        // Configurar parâmetros do efeito Solar
        SolarEffect->SetColorParameter(FName("SolarColor"), FLinearColor(1.0f, 0.9f, 0.2f, 1.0f)); // Dourado
        SolarEffect->SetFloatParameter(FName("SolarIntensity"), GlobalFlowIntensity * 1.5f);
        SolarEffect->SetFloatParameter(FName("SolarRadius"), 200.0f);

        GeneratedComponents.Add(SolarEffect);
    }
}

void AAURACRONPCGPrismalFlow::CreateAxisIntersectionEffect(const FVector& IntersectionPoint)
{
    // ✅ Criar efeito visual de intersecção Axis
    UNiagaraComponent* AxisEffect = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
        GetWorld(),
        nullptr, // Sistema será carregado assincronamente
        IntersectionPoint,
        FRotator::ZeroRotator,
        FVector::OneVector,
        true, // Auto destroy
        true, // Auto activate
        ENCPoolMethod::None
    );

    if (AxisEffect)
    {
        // Configurar parâmetros do efeito Axis
        AxisEffect->SetColorParameter(FName("AxisColor"), FLinearColor(0.8f, 0.8f, 0.9f, 1.0f)); // Prateado
        AxisEffect->SetFloatParameter(FName("AxisIntensity"), GlobalFlowIntensity * 1.2f);
        AxisEffect->SetFloatParameter(FName("AxisStabilization"), 1.0f);

        GeneratedComponents.Add(AxisEffect);
    }
}

void AAURACRONPCGPrismalFlow::CreateLunarIntersectionEffect(const FVector& IntersectionPoint)
{
    // ✅ Criar efeito visual de intersecção Lunar
    UNiagaraComponent* LunarEffect = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
        GetWorld(),
        nullptr, // Sistema será carregado assincronamente
        IntersectionPoint,
        FRotator::ZeroRotator,
        FVector::OneVector,
        true, // Auto destroy
        true, // Auto activate
        ENCPoolMethod::None
    );

    if (LunarEffect)
    {
        // Configurar parâmetros do efeito Lunar
        LunarEffect->SetColorParameter(FName("LunarColor"), FLinearColor(0.3f, 0.6f, 1.0f, 0.8f)); // Azul etéreo
        LunarEffect->SetFloatParameter(FName("LunarIntensity"), GlobalFlowIntensity * 0.9f);
        LunarEffect->SetFloatParameter(FName("LunarPhase"), FMath::Sin(AccumulatedTime * 0.5f));

        GeneratedComponents.Add(LunarEffect);
    }
}

// ========================================
// ✅ IMPLEMENTAÇÃO DE APLICAÇÃO DE EFEITOS DOS TRILHOS - UE 5.6
// ========================================

void AAURACRONPCGPrismalFlow::ApplySolarEnergyBoost(const FVector& IntersectionPoint)
{
    // ✅ Aplicar boost de energia solar ao flow
    if (!FlowSpline)
    {
        return;
    }

    // Encontrar segmento do flow mais próximo
    float ClosestInputKey = FlowSpline->FindInputKeyClosestToWorldLocation(IntersectionPoint);
    int32 SegmentIndex = FMath::FloorToInt(ClosestInputKey);

    if (FlowSegments.IsValidIndex(SegmentIndex))
    {
        FPrismalFlowSegment& Segment = FlowSegments[SegmentIndex];

        // Aplicar boost solar (aumenta energia e velocidade)
        Segment.EnergyIntensity *= 1.3f; // 30% de aumento
        Segment.FlowSpeed *= 1.2f; // 20% de aumento na velocidade

        // Aplicar cor dourada solar
        Segment.CurrentColor = FMath::Lerp(Segment.CurrentColor, FLinearColor(1.0f, 0.9f, 0.2f, 1.0f), 0.5f);

        UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGPrismalFlow::ApplySolarEnergyBoost - Applied solar boost to segment {0}", SegmentIndex);
    }
}

void AAURACRONPCGPrismalFlow::ApplyAxisGravitationalStabilization(const FVector& IntersectionPoint)
{
    // ✅ Aplicar estabilização gravitacional Axis ao flow
    if (!FlowSpline)
    {
        return;
    }

    // Encontrar segmento do flow mais próximo
    float ClosestInputKey = FlowSpline->FindInputKeyClosestToWorldLocation(IntersectionPoint);
    int32 SegmentIndex = FMath::FloorToInt(ClosestInputKey);

    if (FlowSegments.IsValidIndex(SegmentIndex))
    {
        FPrismalFlowSegment& Segment = FlowSegments[SegmentIndex];

        // Aplicar estabilização (reduz volatilidade, aumenta controle)
        Segment.EnergyIntensity = FMath::Clamp(Segment.EnergyIntensity, 0.8f, 1.2f); // Estabilizar intensidade
        Segment.FlowSpeed *= 1.1f; // Leve aumento na velocidade por estabilização

        // Aplicar cor prateada Axis
        Segment.CurrentColor = FMath::Lerp(Segment.CurrentColor, FLinearColor(0.8f, 0.8f, 0.9f, 1.0f), 0.3f);

        UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGPrismalFlow::ApplyAxisGravitationalStabilization - Applied axis stabilization to segment {0}", SegmentIndex);
    }
}

void AAURACRONPCGPrismalFlow::ApplyLunarPhaseShift(const FVector& IntersectionPoint)
{
    // ✅ Aplicar mudança de fase etérea Lunar ao flow
    if (!FlowSpline)
    {
        return;
    }

    // Encontrar segmento do flow mais próximo
    float ClosestInputKey = FlowSpline->FindInputKeyClosestToWorldLocation(IntersectionPoint);
    int32 SegmentIndex = FMath::FloorToInt(ClosestInputKey);

    if (FlowSegments.IsValidIndex(SegmentIndex))
    {
        FPrismalFlowSegment& Segment = FlowSegments[SegmentIndex];

        // Aplicar mudança de fase (efeito cíclico baseado no tempo)
        float LunarPhase = FMath::Sin(AccumulatedTime * 0.5f + SegmentIndex * 0.1f);
        Segment.EnergyIntensity *= (1.0f + LunarPhase * 0.2f); // Variação cíclica
        Segment.FlowSpeed *= (1.0f + LunarPhase * 0.15f); // Variação cíclica na velocidade

        // Aplicar cor azul etérea Lunar com transparência variável
        FLinearColor LunarColor = FLinearColor(0.3f, 0.6f, 1.0f, 0.8f + LunarPhase * 0.2f);
        Segment.CurrentColor = FMath::Lerp(Segment.CurrentColor, LunarColor, 0.4f);

        UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGPrismalFlow::ApplyLunarPhaseShift - Applied lunar phase shift to segment {0} (Phase: {1})", SegmentIndex, LunarPhase);
    }
}

// ========================================
// ✅ IMPLEMENTAÇÃO DE RPCS PARA MULTIPLAYER - UE 5.6
// ========================================

void AAURACRONPCGPrismalFlow::ServerUpdateFlowIntensity_Implementation(float NewIntensity)
{
    // ✅ RPC Server para atualizar intensidade do flow
    if (HasAuthority())
    {
        SetFlowIntensity(NewIntensity);

        // Replicar para todos os clientes
        MulticastUpdateFlowIntensity(NewIntensity);
    }
}

bool AAURACRONPCGPrismalFlow::ServerUpdateFlowIntensity_Validate(float NewIntensity)
{
    // ✅ Validação anti-cheat para RPC
    return NewIntensity >= 0.0f && NewIntensity <= 3.0f;
}

void AAURACRONPCGPrismalFlow::MulticastUpdateFlowIntensity_Implementation(float NewIntensity)
{
    // ✅ RPC Multicast para sincronizar intensidade em todos os clientes
    if (!HasAuthority())
    {
        SetFlowIntensity(NewIntensity);
    }
}

void AAURACRONPCGPrismalFlow::ServerUpdateControllingTeam_Implementation(int32 NewTeam)
{
    // ✅ RPC Server para atualizar equipe controladora
    if (HasAuthority())
    {
        ControllingTeam = NewTeam;

        // Atualizar cor do flow baseada na nova equipe
        FLinearColor NewColor = GetFlowColorForTeam();

        // Replicar para todos os clientes
        MulticastUpdateControllingTeam(NewTeam, NewColor);
    }
}

bool AAURACRONPCGPrismalFlow::ServerUpdateControllingTeam_Validate(int32 NewTeam)
{
    // ✅ Validação anti-cheat para RPC
    return NewTeam >= -1 && NewTeam <= 8; // -1 para neutro, 1-8 para equipes
}

void AAURACRONPCGPrismalFlow::MulticastUpdateControllingTeam_Implementation(int32 NewTeam, const FLinearColor& NewColor)
{
    // ✅ RPC Multicast para sincronizar equipe controladora em todos os clientes
    if (!HasAuthority())
    {
        ControllingTeam = NewTeam;

        // Atualizar cor dos efeitos visuais
        if (MainFlowEffect && IsValid(MainFlowEffect))
        {
            MainFlowEffect->SetColorParameter(FName("FlowColor"), NewColor);
        }

        // Atualizar cor dos componentes gerados
        for (UActorComponent* Component : GeneratedComponents)
        {
            if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Component))
            {
                NiagaraComp->SetColorParameter(FName("FlowColor"), NewColor);
            }
        }
    }
}

// ========================================
// ✅ IMPLEMENTAÇÃO DO DESTRUCTOR PARA LIMPEZA - UE 5.6
// ========================================

AAURACRONPCGPrismalFlow::~AAURACRONPCGPrismalFlow()
{
    // ✅ Limpeza adequada de timers e handles de streaming
    if (GetWorld())
    {
        // Limpar timers
        GetWorld()->GetTimerManager().ClearTimer(DynamicEffectsTimerHandle);
        GetWorld()->GetTimerManager().ClearTimer(TimeBasedParametersTimerHandle);
    }

    // Limpar handles de streaming
    for (TSharedPtr<FStreamableHandle>& Handle : ActiveStreamingHandles)
    {
        if (Handle.IsValid())
        {
            Handle->CancelHandle();
        }
    }
    ActiveStreamingHandles.Empty();

    // Limpar componentes gerados
    for (UActorComponent* Component : GeneratedComponents)
    {
        if (IsValid(Component))
        {
            Component->DestroyComponent();
        }
    }
    GeneratedComponents.Empty();

    // Limpar segmentos do flow
    FlowSegments.Empty();
}

void AAURACRONPCGPrismalFlow::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // ✅ Limpeza no EndPlay
    if (GetWorld())
    {
        // Limpar timers
        GetWorld()->GetTimerManager().ClearTimer(DynamicEffectsTimerHandle);
        GetWorld()->GetTimerManager().ClearTimer(TimeBasedParametersTimerHandle);
    }

    // Limpar handles de streaming
    for (TSharedPtr<FStreamableHandle>& Handle : ActiveStreamingHandles)
    {
        if (Handle.IsValid())
        {
            Handle->CancelHandle();
        }
    }
    ActiveStreamingHandles.Empty();

    Super::EndPlay(EndPlayReason);
}

// ========================================
// ✅ IMPLEMENTAÇÃO DE FUNÇÕES DE UTILIDADE FINAIS - UE 5.6
// ========================================

void AAURACRONPCGPrismalFlow::ApplyMapPhaseEffects()
{
    // ✅ Aplicar efeitos específicos da fase do mapa - implementação robusta
    switch (CurrentMapPhase)
    {
        case EAURACRONMapPhase::Awakening:
        {
            // Fase Despertar: Flow suave e estável
            for (FPrismalFlowSegment& Segment : FlowSegments)
            {
                Segment.EnergyIntensity = FMath::Clamp(Segment.EnergyIntensity * 0.8f, 0.5f, 1.0f);
                Segment.FlowSpeed = FMath::Clamp(Segment.FlowSpeed * 0.9f, 0.5f, 1.2f);
                Segment.CurrentColor = FMath::Lerp(Segment.CurrentColor, FLinearColor(0.8f, 0.9f, 1.0f, 1.0f), 0.3f);
            }
            break;
        }

        case EAURACRONMapPhase::Convergence:
        {
            // Fase Convergência: Flow equilibrado
            for (FPrismalFlowSegment& Segment : FlowSegments)
            {
                Segment.EnergyIntensity = FMath::Clamp(Segment.EnergyIntensity * 1.0f, 0.7f, 1.3f);
                Segment.FlowSpeed = FMath::Clamp(Segment.FlowSpeed * 1.0f, 0.8f, 1.5f);
                Segment.CurrentColor = FMath::Lerp(Segment.CurrentColor, FLinearColor(1.0f, 1.0f, 0.9f, 1.0f), 0.3f);
            }
            break;
        }

        case EAURACRONMapPhase::Intensification:
        {
            // Fase Intensificação: Flow energético
            for (FPrismalFlowSegment& Segment : FlowSegments)
            {
                Segment.EnergyIntensity = FMath::Clamp(Segment.EnergyIntensity * 1.3f, 1.0f, 1.8f);
                Segment.FlowSpeed = FMath::Clamp(Segment.FlowSpeed * 1.2f, 1.0f, 2.0f);
                Segment.CurrentColor = FMath::Lerp(Segment.CurrentColor, FLinearColor(1.0f, 0.7f, 0.4f, 1.0f), 0.3f);
            }
            break;
        }

        case EAURACRONMapPhase::Resolution:
        {
            // Fase Resolução: Flow caótico e poderoso
            for (FPrismalFlowSegment& Segment : FlowSegments)
            {
                float ChaosMultiplier = 1.0f + FMath::Sin(AccumulatedTime * 3.0f + Segment.WorldPosition.X * 0.01f) * 0.5f;
                Segment.EnergyIntensity = FMath::Clamp(Segment.EnergyIntensity * 1.8f * ChaosMultiplier, 1.2f, 2.5f);
                Segment.FlowSpeed = FMath::Clamp(Segment.FlowSpeed * 1.5f * ChaosMultiplier, 1.2f, 2.8f);
                Segment.CurrentColor = FMath::Lerp(Segment.CurrentColor, FLinearColor(0.9f, 0.3f, 1.0f, 1.0f), 0.3f);
            }
            break;
        }
    }

    // Atualizar efeitos visuais baseados na fase
    if (MainFlowEffect && IsValid(MainFlowEffect))
    {
        MainFlowEffect->SetFloatParameter(FName("PhaseIntensity"), CalculateFlowIntensity(0.5f, CurrentMapPhase));
        MainFlowEffect->SetColorParameter(FName("PhaseColor"), GetFlowColorForPhase(CurrentMapPhase));
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGPrismalFlow::ApplyMapPhaseEffects - Applied effects for phase: {0}", (int32)CurrentMapPhase);
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES FALTANTES - UE 5.6 APIS MODERNAS
// ========================================

void AAURACRONPCGPrismalFlow::CreateEnvironmentTransitions()
{
    // Criar transições entre ambientes usando APIs modernas do UE 5.6
    // ✅ UE_LOGFMT para UE 5.6 - API moderna de logging
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGPrismalFlow::CreateEnvironmentTransitions - Creating environment transitions with modern UE 5.6 APIs");

    if (!FlowSpline || !IsValid(FlowSpline))
    {
        // ✅ UE_LOGFMT para UE 5.6 - API moderna de logging
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGPrismalFlow::CreateEnvironmentTransitions - FlowSpline is invalid");
        return;
    }

    int32 NumSplinePoints = FlowSpline->GetNumberOfSplinePoints();
    if (NumSplinePoints < 3)
    {
        // ✅ UE_LOGFMT para UE 5.6 - API moderna de logging
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGPrismalFlow::CreateEnvironmentTransitions - Not enough spline points");
        return;
    }

    // Criar transições em pontos estratégicos ao longo do spline
    int32 NumTransitions = FMath::Clamp(NumSplinePoints / 4, 3, 8);

    for (int32 i = 0; i < NumTransitions; ++i)
    {
        float Alpha = static_cast<float>(i) / static_cast<float>(NumTransitions - 1);
        float SplineDistance = FlowSpline->GetSplineLength() * Alpha;

        // Obter localização e rotação no spline
        FVector TransitionLocation = FlowSpline->GetLocationAtDistanceAlongSpline(SplineDistance, ESplineCoordinateSpace::World);
        FRotator TransitionRotation = FlowSpline->GetRotationAtDistanceAlongSpline(SplineDistance, ESplineCoordinateSpace::World);

        // Criar componente de transição ambiental
        UStaticMeshComponent* EnvironmentTransition = CreateDefaultSubobject<UStaticMeshComponent>(
            *FString::Printf(TEXT("EnvironmentTransition_%d_%d"), i, FMath::RandRange(1000, 9999))
        );

        if (EnvironmentTransition)
        {
            EnvironmentTransition->SetupAttachment(RootComponent);
            EnvironmentTransition->SetWorldLocation(TransitionLocation);
            EnvironmentTransition->SetWorldRotation(TransitionRotation);

            // Escala baseada na intensidade do flow
            float TransitionScale = 1.5f + (GlobalFlowIntensity * 0.8f);
            EnvironmentTransition->SetRelativeScale3D(FVector(TransitionScale, TransitionScale, TransitionScale * 0.5f));

            // ✅ Configurar material de transição usando carregamento assíncrono UE 5.6
            if (StreamableManager && IsValid(StreamableManager))
            {
                // Usar carregamento assíncrono em vez de LoadObject síncrono
                FSoftObjectPath MaterialPath(TEXT("/Engine/BasicShapes/BasicShapeMaterial"));
                TSharedPtr<FStreamableHandle> Handle = StreamableManager->RequestAsyncLoad(
                    MaterialPath,
                    FStreamableDelegate::CreateUObject(this, &AAURACRONPCGPrismalFlow::OnTransitionMaterialLoaded, EnvironmentTransition)
                );
            }
            else if (UMaterialInterface* BaseMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial")))
            {
                UMaterialInstanceDynamic* TransitionMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, this);
                if (TransitionMaterial)
                {
                    // Configurar parâmetros de transição baseados na posição
                    float TransitionIntensity = 1.0f + (Alpha * 1.5f);
                    FLinearColor TransitionColor = FMath::Lerp(
                        FLinearColor(0.2f, 0.8f, 1.0f, 0.8f), // Azul inicial
                        FLinearColor(1.0f, 0.4f, 0.8f, 0.8f), // Rosa final
                        Alpha
                    );

                    TransitionMaterial->SetScalarParameterValue(FName("TransitionIntensity"), TransitionIntensity);
                    TransitionMaterial->SetScalarParameterValue(FName("FlowSpeed"), BaseFlowSpeed * 1.5f);
                    TransitionMaterial->SetVectorParameterValue(FName("TransitionColor"), TransitionColor);
                    TransitionMaterial->SetScalarParameterValue(FName("EnvironmentBlend"), Alpha);

                    EnvironmentTransition->SetMaterial(0, TransitionMaterial);
                }
            }

            // Configurar colisão para interação ambiental
            EnvironmentTransition->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
            EnvironmentTransition->SetCollisionObjectType(ECollisionChannel::ECC_WorldStatic);

            GeneratedComponents.Add(EnvironmentTransition);
        }
    }

    // Criar efeitos de partículas para transições usando APIs modernas
    for (int32 i = 0; i < NumTransitions; ++i)
    {
        float Alpha = static_cast<float>(i) / static_cast<float>(NumTransitions - 1);
        float SplineDistance = FlowSpline->GetSplineLength() * Alpha;
        FVector ParticleLocation = FlowSpline->GetLocationAtDistanceAlongSpline(SplineDistance, ESplineCoordinateSpace::World);

        // Adicionar offset vertical para efeitos de partículas
        ParticleLocation.Z += 100.0f;

        UNiagaraComponent* TransitionEffect = CreateDefaultSubobject<UNiagaraComponent>(
            *FString::Printf(TEXT("TransitionEffect_%d_%d"), i, FMath::RandRange(1000, 9999))
        );

        if (TransitionEffect)
        {
            TransitionEffect->SetupAttachment(RootComponent);
            TransitionEffect->SetWorldLocation(ParticleLocation);

            // Configurar parâmetros do efeito de transição
            TransitionEffect->SetFloatParameter(FName("TransitionIntensity"), GlobalFlowIntensity * 1.2f);
            TransitionEffect->SetFloatParameter(FName("FlowSpeed"), BaseFlowSpeed);
            TransitionEffect->SetVectorParameter(FName("FlowDirection"), FlowSpline->GetDirectionAtDistanceAlongSpline(SplineDistance, ESplineCoordinateSpace::World));

            // Configurar cor baseada na fase do mapa
            FLinearColor EffectColor = GetFlowColorForPhase(CurrentMapPhase);
            TransitionEffect->SetColorParameter(FName("EffectColor"), EffectColor);

            GeneratedComponents.Add(TransitionEffect);
        }
    }

    // ✅ UE_LOGFMT para UE 5.6 - API moderna de logging
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGPrismalFlow::CreateEnvironmentTransitions - Created {0} environment transitions", NumTransitions);
}

void AAURACRONPCGPrismalFlow::UpdateTimeBasedParameters()
{
    // Atualizar parâmetros baseados no tempo usando APIs modernas do UE 5.6
    if (!GetWorld())
    {
        return;
    }

    float CurrentTime = GetWorld()->GetTimeSeconds();
    AccumulatedTime += GetWorld()->GetDeltaSeconds();

    // Atualizar parâmetros de flow baseados no tempo
    float TimeBasedIntensity = GlobalFlowIntensity * (1.0f + FMath::Sin(AccumulatedTime * 0.5f) * 0.3f);
    float TimeBasedSpeed = BaseFlowSpeed * (1.0f + FMath::Cos(AccumulatedTime * 0.8f) * 0.2f);

    // Atualizar amplitude serpentina baseada no tempo
    float TimeBasedAmplitude = SerpentineAmplitude * (1.0f + FMath::Sin(AccumulatedTime * 0.3f) * 0.15f);

    // Atualizar frequência serpentina baseada no tempo
    float TimeBasedFrequency = SerpentineFrequency * (1.0f + FMath::Cos(AccumulatedTime * 0.4f) * 0.1f);

    // Aplicar parâmetros atualizados aos componentes de efeito
    if (MainFlowEffect && IsValid(MainFlowEffect))
    {
        MainFlowEffect->SetFloatParameter(FName("FlowIntensity"), TimeBasedIntensity);
        MainFlowEffect->SetFloatParameter(FName("FlowSpeed"), TimeBasedSpeed);
        MainFlowEffect->SetFloatParameter(FName("SerpentineAmplitude"), TimeBasedAmplitude);
        MainFlowEffect->SetFloatParameter(FName("SerpentineFrequency"), TimeBasedFrequency);
        MainFlowEffect->SetFloatParameter(FName("TimeOffset"), AccumulatedTime);

        // Atualizar cor baseada no tempo e fase ou equipe controladora
        FLinearColor TimeBasedColor = GetFlowColorForPhase(CurrentMapPhase);
        float ColorIntensity = 1.0f + FMath::Sin(AccumulatedTime * 2.0f) * 0.2f;
        TimeBasedColor *= ColorIntensity;
        MainFlowEffect->SetColorParameter(FName("FlowColor"), TimeBasedColor);
    }

    // Atualizar componentes gerados com parâmetros baseados no tempo
    for (UActorComponent* Component : GeneratedComponents)
    {
        if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
        {
            // Atualizar materiais dinâmicos
            if (UMaterialInstanceDynamic* DynMaterial = Cast<UMaterialInstanceDynamic>(MeshComp->GetMaterial(0)))
            {
                DynMaterial->SetScalarParameterValue(FName("TimeOffset"), AccumulatedTime);
                DynMaterial->SetScalarParameterValue(FName("FlowIntensity"), TimeBasedIntensity);
                DynMaterial->SetScalarParameterValue(FName("FlowSpeed"), TimeBasedSpeed);

                // Aplicar efeitos específicos baseados no nome do componente
                if (MeshComp->GetName().Contains(TEXT("EnvironmentTransition")))
                {
                    float TransitionPulse = 1.0f + FMath::Sin(AccumulatedTime * 3.0f) * 0.4f;
                    DynMaterial->SetScalarParameterValue(FName("TransitionIntensity"), TimeBasedIntensity * TransitionPulse);
                }
                else if (MeshComp->GetName().Contains(TEXT("FlowObstacle")))
                {
                    float ObstaclePulse = 1.0f + FMath::Cos(AccumulatedTime * 2.5f) * 0.3f;
                    DynMaterial->SetScalarParameterValue(FName("ObstacleIntensity"), TimeBasedIntensity * ObstaclePulse);
                }
            }

            // Aplicar rotação baseada no tempo para elementos específicos
            if (MeshComp->GetName().Contains(TEXT("EnergyNode")) || MeshComp->GetName().Contains(TEXT("FlowCore")))
            {
                FRotator TimeBasedRotation = FRotator(0.0f, AccumulatedTime * 30.0f, 0.0f);
                MeshComp->SetRelativeRotation(TimeBasedRotation);
            }
        }
        else if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Component))
        {
            // Atualizar efeitos de partículas com parâmetros baseados no tempo
            NiagaraComp->SetFloatParameter(FName("TimeOffset"), AccumulatedTime);
            NiagaraComp->SetFloatParameter(FName("FlowIntensity"), TimeBasedIntensity);
            NiagaraComp->SetFloatParameter(FName("FlowSpeed"), TimeBasedSpeed);

            // Atualizar cor dos efeitos
            FLinearColor EffectColor = GetFlowColorForPhase(CurrentMapPhase);
            float EffectIntensity = 1.0f + FMath::Sin(AccumulatedTime * 1.5f) * 0.25f;
            EffectColor *= EffectIntensity;
            NiagaraComp->SetColorParameter(FName("EffectColor"), EffectColor);
        }
    }

    // Atualizar spline baseado no tempo se necessário
    if (FlowSpline && IsValid(FlowSpline))
    {
        // Aplicar deformação temporal sutil ao spline
        int32 NumPoints = FlowSpline->GetNumberOfSplinePoints();
        for (int32 i = 0; i < NumPoints; ++i)
        {
            FVector OriginalLocation = FlowSpline->GetLocationAtSplinePoint(i, ESplineCoordinateSpace::Local);

            // Aplicar ondulação temporal
            float TimeWave = FMath::Sin(AccumulatedTime * 0.8f + i * 0.5f) * 20.0f * GlobalFlowIntensity;
            FVector TimeBasedOffset = FVector(0.0f, 0.0f, TimeWave);

            // Aplicar apenas se a intensidade for significativa
            if (GlobalFlowIntensity > 0.5f)
            {
                FlowSpline->SetLocationAtSplinePoint(i, OriginalLocation + TimeBasedOffset, ESplineCoordinateSpace::Local, false);
            }
        }

        // Atualizar spline após modificações
        if (GlobalFlowIntensity > 0.5f)
        {
            FlowSpline->UpdateSpline();
        }
    }

    // ✅ UE_LOGFMT para UE 5.6 - API moderna de logging
    UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGPrismalFlow::UpdateTimeBasedParameters - Updated time-based parameters (Time: {0}, Intensity: {1})", AccumulatedTime, TimeBasedIntensity);
}

void AAURACRONPCGPrismalFlow::GenerateFlowObstacles()
{
    // Gerar obstáculos do flow usando APIs modernas do UE 5.6
    // ✅ UE_LOGFMT para UE 5.6 - API moderna de logging
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGPrismalFlow::GenerateFlowObstacles - Generating flow obstacles with modern UE 5.6 APIs");

    if (!FlowSpline || !IsValid(FlowSpline))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPrismalFlow::GenerateFlowObstacles - FlowSpline is invalid"));
        return;
    }

    float SplineLength = FlowSpline->GetSplineLength();
    if (SplineLength <= 0.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPrismalFlow::GenerateFlowObstacles - Spline length is invalid"));
        return;
    }

    // Calcular número de obstáculos baseado no comprimento do spline e intensidade
    int32 NumObstacles = FMath::Clamp(
        static_cast<int32>((SplineLength / 1000.0f) * GlobalFlowIntensity * 3.0f),
        2, 12
    );

    for (int32 i = 0; i < NumObstacles; ++i)
    {
        // Posicionar obstáculos de forma semi-aleatória ao longo do spline
        float Alpha = (static_cast<float>(i) + FMath::RandRange(0.1f, 0.9f)) / static_cast<float>(NumObstacles);
        float SplineDistance = SplineLength * Alpha;

        // Obter localização e direção no spline
        FVector ObstacleLocation = FlowSpline->GetLocationAtDistanceAlongSpline(SplineDistance, ESplineCoordinateSpace::World);
        FVector FlowDirection = FlowSpline->GetDirectionAtDistanceAlongSpline(SplineDistance, ESplineCoordinateSpace::World);
        FRotator ObstacleRotation = FlowDirection.Rotation();

        // Adicionar offset lateral aleatório
        FVector RightVector = FVector::CrossProduct(FlowDirection, FVector::UpVector).GetSafeNormal();
        float LateralOffset = FMath::RandRange(-300.0f, 300.0f);
        ObstacleLocation += RightVector * LateralOffset;

        // Adicionar offset vertical
        ObstacleLocation.Z += FMath::RandRange(50.0f, 200.0f);

        // Criar obstáculo usando APIs modernas
        UStaticMeshComponent* FlowObstacle = CreateDefaultSubobject<UStaticMeshComponent>(
            *FString::Printf(TEXT("FlowObstacle_%d_%d"), i, FMath::RandRange(1000, 9999))
        );

        if (FlowObstacle)
        {
            FlowObstacle->SetupAttachment(RootComponent);
            FlowObstacle->SetWorldLocation(ObstacleLocation);
            FlowObstacle->SetWorldRotation(ObstacleRotation);

            // Escala baseada na intensidade e tipo de obstáculo
            float ObstacleScale = FMath::RandRange(0.8f, 2.2f) * (1.0f + GlobalFlowIntensity * 0.5f);
            FlowObstacle->SetRelativeScale3D(FVector(ObstacleScale, ObstacleScale, ObstacleScale * 1.5f));

            // Configurar material de obstáculo usando APIs modernas
            if (UMaterialInterface* BaseMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial")))
            {
                UMaterialInstanceDynamic* ObstacleMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, this);
                if (ObstacleMaterial)
                {
                    // Configurar parâmetros baseados no tipo de obstáculo
                    float ObstacleType = FMath::RandRange(1.0f, 4.0f);
                    FLinearColor ObstacleColor;

                    if (ObstacleType < 2.0f)
                    {
                        // Obstáculo cristalino
                        ObstacleColor = FLinearColor(0.3f, 0.8f, 1.0f, 0.9f);
                        ObstacleMaterial->SetScalarParameterValue(FName("CrystallineEffect"), 2.0f);
                    }
                    else if (ObstacleType < 3.0f)
                    {
                        // Obstáculo energético
                        ObstacleColor = FLinearColor(1.0f, 0.6f, 0.2f, 0.8f);
                        ObstacleMaterial->SetScalarParameterValue(FName("EnergyPulse"), 1.8f);
                    }
                    else
                    {
                        // Obstáculo místico
                        ObstacleColor = FLinearColor(0.8f, 0.3f, 1.0f, 0.85f);
                        ObstacleMaterial->SetScalarParameterValue(FName("MysticAura"), 2.2f);
                    }

                    ObstacleMaterial->SetScalarParameterValue(FName("ObstacleIntensity"), GlobalFlowIntensity * 1.5f);
                    ObstacleMaterial->SetScalarParameterValue(FName("FlowInteraction"), BaseFlowSpeed * 0.8f);
                    ObstacleMaterial->SetVectorParameterValue(FName("ObstacleColor"), ObstacleColor);

                    FlowObstacle->SetMaterial(0, ObstacleMaterial);
                }
            }

            // Configurar colisão para interação com o flow
            FlowObstacle->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
            FlowObstacle->SetCollisionObjectType(ECollisionChannel::ECC_WorldStatic);
            FlowObstacle->SetCollisionResponseToChannel(ECollisionChannel::ECC_Pawn, ECollisionResponse::ECR_Block);

            GeneratedComponents.Add(FlowObstacle);
        }

        // Criar efeito de partículas para o obstáculo
        UNiagaraComponent* ObstacleEffect = CreateDefaultSubobject<UNiagaraComponent>(
            *FString::Printf(TEXT("ObstacleEffect_%d_%d"), i, FMath::RandRange(1000, 9999))
        );

        if (ObstacleEffect)
        {
            ObstacleEffect->SetupAttachment(RootComponent);
            ObstacleEffect->SetWorldLocation(ObstacleLocation + FVector(0.0f, 0.0f, 50.0f));

            // Configurar parâmetros do efeito de obstáculo
            ObstacleEffect->SetFloatParameter(FName("ObstacleIntensity"), GlobalFlowIntensity * 1.3f);
            ObstacleEffect->SetFloatParameter(FName("FlowDisruption"), BaseFlowSpeed * 0.6f);
            ObstacleEffect->SetVectorParameter(FName("FlowDirection"), FlowDirection);

            // Configurar cor baseada na fase do mapa
            FLinearColor EffectColor = GetFlowColorForPhase(CurrentMapPhase);
            EffectColor *= 1.2f; // Intensificar para obstáculos
            ObstacleEffect->SetColorParameter(FName("EffectColor"), EffectColor);

            GeneratedComponents.Add(ObstacleEffect);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::GenerateFlowObstacles - Generated %d flow obstacles"), NumObstacles);
}

void AAURACRONPCGPrismalFlow::ApplyMapPhaseEffects()
{
    // Aplicar efeitos da fase do mapa usando APIs modernas do UE 5.6
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::ApplyMapPhaseEffects - Applying map phase effects for phase %d"), (int32)CurrentMapPhase);

    // Aplicar efeitos baseados na fase atual do mapa
    switch (CurrentMapPhase)
    {
        case EAURACRONMapPhase::Awakening:
        {
            // Fase do despertar: flow suave e crescente
            GlobalFlowIntensity = FMath::Clamp(GlobalFlowIntensity * 0.7f, 0.4f, 1.0f);
            BaseFlowSpeed = FMath::Clamp(BaseFlowSpeed * 0.8f, 0.5f, 2.0f);

            // Aplicar efeitos visuais de despertar
            ApplyPhaseVisualEffects(FLinearColor(1.0f, 0.9f, 0.7f, 1.0f), 0.6f, TEXT("AwakeningGlow"));
            break;
        }

        case EAURACRONMapPhase::Convergence:
        {
            // Fase da convergência: flow equilibrado e focado
            GlobalFlowIntensity = FMath::Clamp(GlobalFlowIntensity * 0.9f, 0.6f, 1.2f);
            BaseFlowSpeed = FMath::Clamp(BaseFlowSpeed * 1.0f, 0.8f, 2.5f);

            // Aplicar efeitos visuais de convergência
            ApplyPhaseVisualEffects(FLinearColor(0.8f, 1.0f, 0.9f, 1.0f), 0.8f, TEXT("ConvergenceEnergy"));
            break;
        }

        case EAURACRONMapPhase::Intensification:
        {
            // Fase da intensificação: flow poderoso e dinâmico
            GlobalFlowIntensity = FMath::Clamp(GlobalFlowIntensity * 1.2f, 0.8f, 1.5f);
            BaseFlowSpeed = FMath::Clamp(BaseFlowSpeed * 1.3f, 1.0f, 3.0f);

            // Aplicar efeitos visuais de intensificação
            ApplyPhaseVisualEffects(FLinearColor(1.0f, 0.6f, 0.3f, 1.0f), 1.0f, TEXT("IntensificationPower"));
            break;
        }

        case EAURACRONMapPhase::Resolution:
        {
            // Fase da resolução: flow épico e transcendente
            GlobalFlowIntensity = FMath::Clamp(GlobalFlowIntensity * 1.5f, 1.0f, 2.0f);
            BaseFlowSpeed = FMath::Clamp(BaseFlowSpeed * 1.8f, 1.5f, 4.0f);

            // Aplicar efeitos visuais de resolução
            ApplyPhaseVisualEffects(FLinearColor(0.7f, 0.4f, 1.0f, 1.0f), 1.5f, TEXT("ResolutionMajesty"));
            break;
        }
    }

    // Atualizar amplitude e frequência serpentina baseadas na fase
    switch (CurrentMapPhase)
    {
        case EAURACRONMapPhase::Awakening:
            SerpentineAmplitude = FMath::Clamp(SerpentineAmplitude * 0.8f, 800.0f, 2000.0f);
            SerpentineFrequency = FMath::Clamp(SerpentineFrequency * 0.9f, 2.0f, 5.0f);
            break;

        case EAURACRONMapPhase::Convergence:
            SerpentineAmplitude = FMath::Clamp(SerpentineAmplitude * 1.0f, 1000.0f, 2500.0f);
            SerpentineFrequency = FMath::Clamp(SerpentineFrequency * 1.1f, 3.0f, 6.0f);
            break;

        case EAURACRONMapPhase::Intensification:
            SerpentineAmplitude = FMath::Clamp(SerpentineAmplitude * 1.3f, 1200.0f, 3000.0f);
            SerpentineFrequency = FMath::Clamp(SerpentineFrequency * 1.4f, 4.0f, 7.0f);
            break;

        case EAURACRONMapPhase::Resolution:
            SerpentineAmplitude = FMath::Clamp(SerpentineAmplitude * 1.6f, 1500.0f, 4000.0f);
            SerpentineFrequency = FMath::Clamp(SerpentineFrequency * 1.8f, 5.0f, 8.0f);
            break;
    }

    // Regenerar caminho do flow com novos parâmetros
    GenerateFlowPath();

    // Aplicar efeitos específicos aos componentes gerados
    for (UActorComponent* Component : GeneratedComponents)
    {
        if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
        {
            if (UMaterialInstanceDynamic* DynMaterial = Cast<UMaterialInstanceDynamic>(MeshComp->GetMaterial(0)))
            {
                // Aplicar parâmetros da fase atual
                DynMaterial->SetScalarParameterValue(FName("PhaseIntensity"), GlobalFlowIntensity);
                DynMaterial->SetScalarParameterValue(FName("FlowSpeed"), BaseFlowSpeed);
                DynMaterial->SetScalarParameterValue(FName("SerpentineAmplitude"), SerpentineAmplitude);
                DynMaterial->SetScalarParameterValue(FName("SerpentineFrequency"), SerpentineFrequency);

                // Aplicar cor da fase
                FLinearColor PhaseColor = GetFlowColorForPhase(CurrentMapPhase);
                DynMaterial->SetVectorParameterValue(FName("PhaseColor"), PhaseColor);
            }
        }
        else if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Component))
        {
            // Atualizar efeitos de partículas com parâmetros da fase
            NiagaraComp->SetFloatParameter(FName("PhaseIntensity"), GlobalFlowIntensity);
            NiagaraComp->SetFloatParameter(FName("FlowSpeed"), BaseFlowSpeed);

            FLinearColor PhaseColor = GetFlowColorForPhase(CurrentMapPhase);
            NiagaraComp->SetColorParameter(FName("PhaseColor"), PhaseColor);
        }
    }

    // Regenerar conteúdo PCG se necessário
    if (PCGComponent && IsValid(PCGComponent) && GlobalFlowIntensity > 0.0f)
    {
        PCGComponent->GenerateLocal(false); // Regeneração não-bloqueante
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::ApplyMapPhaseEffects - Map phase effects applied successfully"));
}

void AAURACRONPCGPrismalFlow::ApplyPhaseVisualEffects(const FLinearColor& PhaseColor, float PhaseIntensity, const FString& EffectName)
{
    // Aplicar efeitos visuais específicos da fase usando APIs modernas
    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGPrismalFlow::ApplyPhaseVisualEffects - Applying %s effects"), *EffectName);

    // Aplicar ao efeito principal
    if (MainFlowEffect && IsValid(MainFlowEffect))
    {
        MainFlowEffect->SetColorParameter(FName("PhaseColor"), PhaseColor);
        MainFlowEffect->SetFloatParameter(FName("PhaseIntensity"), PhaseIntensity);
        MainFlowEffect->SetFloatParameter(FName(*EffectName), PhaseIntensity * 1.2f);
    }

    // Aplicar aos componentes gerados
    for (UActorComponent* Component : GeneratedComponents)
    {
        if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
        {
            if (UMaterialInstanceDynamic* DynMaterial = Cast<UMaterialInstanceDynamic>(MeshComp->GetMaterial(0)))
            {
                DynMaterial->SetVectorParameterValue(FName("PhaseColor"), PhaseColor);
                DynMaterial->SetScalarParameterValue(FName("PhaseIntensity"), PhaseIntensity);
                DynMaterial->SetScalarParameterValue(FName(*EffectName), PhaseIntensity * 1.1f);
            }
        }
        else if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Component))
        {
            NiagaraComp->SetColorParameter(FName("PhaseColor"), PhaseColor);
            NiagaraComp->SetFloatParameter(FName("PhaseIntensity"), PhaseIntensity);
            NiagaraComp->SetFloatParameter(FName(*EffectName), PhaseIntensity * 1.3f);
        }
    }
}

// ✅ FUNÇÃO GetFlowColorForTeam() JÁ IMPLEMENTADA COMPLETAMENTE NAS LINHAS 1246-1278
// Esta versão duplicada foi removida para evitar conflitos

void AAURACRONPCGPrismalFlow::SetControllingTeam(int32 TeamID)
{
    // Verificar se a equipe mudou
    if (ControllingTeam != TeamID)
    {
        // Atualizar a equipe controladora
        ControllingTeam = TeamID;
        
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::SetControllingTeam - Flow now controlled by team %d"), ControllingTeam);
        
        // Atualizar efeitos visuais para refletir a nova equipe controladora
        if (MainFlowEffect && IsValid(MainFlowEffect))
        {
            FLinearColor TeamColor = GetFlowColorForTeam();
            MainFlowEffect->SetColorParameter(FName("FlowColor"), TeamColor);
            MainFlowEffect->SetColorParameter(FName("PhaseColor"), TeamColor);
        }
        
        // Atualizar componentes gerados
        for (UActorComponent* Component : GeneratedComponents)
        {
            if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
            {
                if (UMaterialInstanceDynamic* DynMaterial = Cast<UMaterialInstanceDynamic>(MeshComp->GetMaterial(0)))
                {
                    FLinearColor TeamColor = GetFlowColorForTeam();
                    DynMaterial->SetVectorParameterValue(FName("PhaseColor"), TeamColor);
                }
            }
            else if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Component))
            {
                FLinearColor TeamColor = GetFlowColorForTeam();
                NiagaraComp->SetColorParameter(FName("PhaseColor"), TeamColor);
                NiagaraComp->SetColorParameter(FName("EffectColor"), TeamColor);
            }
        }
    }
}

TArray<FVector> AAURACRONPCGPrismalFlow::GetFlowControlPoints() const
{
    TArray<FVector> ControlPoints;

    // Adicionar pontos de controle baseados no spline do fluxo
    if (FlowSpline)
    {
        int32 NumSplinePoints = FlowSpline->GetNumberOfSplinePoints();
        for (int32 i = 0; i < NumSplinePoints; i++)
        {
            FVector WorldLocation = FlowSpline->GetLocationAtSplinePoint(i, ESplineCoordinateSpace::World);
            ControlPoints.Add(WorldLocation);
        }
    }

    // Se não há spline, criar pontos baseados nas ilhas
    if (ControlPoints.Num() == 0)
    {
        for (const APrismalFlowIsland* Island : Islands)
        {
            if (Island)
            {
                ControlPoints.Add(Island->GetActorLocation());
            }
        }
    }

    // Se ainda não há pontos, criar pontos padrão ao longo do fluxo
    if (ControlPoints.Num() == 0)
    {
        FVector StartLocation = GetActorLocation();
        FVector FlowDirection = GetActorForwardVector();

        for (int32 i = 0; i < 5; i++)
        {
            FVector ControlPoint = StartLocation + (FlowDirection * (i * 1000.0f));
            ControlPoints.Add(ControlPoint);
        }
    }

    return ControlPoints;
}

void AAURACRONPCGPrismalFlow::SetEffectQuality(float EffectQuality)
{
    // Implementação da configuração de qualidade dos efeitos
    float ClampedQuality = FMath::Clamp(EffectQuality, 0.1f, 2.0f);

    // Atualizar componentes Niagara com nova qualidade
    for (UNiagaraComponent* NiagaraComp : FlowEffects)
    {
        if (NiagaraComp && IsValid(NiagaraComp))
        {
            // Configurar parâmetros de qualidade
            NiagaraComp->SetFloatParameter(FName("EffectQuality"), ClampedQuality);
            NiagaraComp->SetFloatParameter(FName("ParticleCount"), ClampedQuality * 100.0f);
            NiagaraComp->SetFloatParameter(FName("DetailLevel"), ClampedQuality);
        }
    }

    // Atualizar PCG com nova qualidade
    if (PCGComponent && PCGComponent->GetGraph())
    {
        // Usar API moderna do UE 5.6 para configurar parâmetros
        if (UPCGGraphInstance* GraphInstance = PCGComponent->GetGraphInstance())
        {
            // Configurar parâmetro de qualidade usando API moderna
            SetPCGParameterModern(TEXT("EffectQuality"), FVector(ClampedQuality, 0.0f, 0.0f), TEXT("PrismalFlowEffects"));

            // Regenerar com nova qualidade
            PCGComponent->GenerateLocal(true);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::SetEffectQuality - Qualidade configurada para %f"), ClampedQuality);
}

void AAURACRONPCGPrismalFlow::SetPCGParameterModern(const FString& ParameterName, const FVector& Value, const FString& Context)
{
    // Função auxiliar para configurar parâmetros PCG usando API moderna UE 5.6
    if (!PCGComponent || !IsValid(PCGComponent))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPrismalFlow::SetPCGParameterModern - PCGComponent is invalid"));
        return;
    }

    // Usar sistema moderno de parâmetros do UE 5.6
    if (UPCGGraphInstance* GraphInstance = PCGComponent->GetGraphInstance())
    {
        // Criar nome completo do parâmetro com contexto
        FString FullParameterName = Context.IsEmpty() ? ParameterName : FString::Printf(TEXT("%s.%s"), *Context, *ParameterName);
        FName ParamName = FName(*FullParameterName);

        // Usar API moderna do UE 5.6 - SetGraphParameter
        EPropertyBagResult Result = GraphInstance->SetGraphParameter<FVector>(ParamName, Value);

        if (Result == EPropertyBagResult::Success)
        {
            UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGPrismalFlow::SetPCGParameterModern - Successfully set FVector parameter %s = (%.2f, %.2f, %.2f)"),
                   *FullParameterName, Value.X, Value.Y, Value.Z);
        }
        else if (Result == EPropertyBagResult::PropertyNotFound)
        {
            // Tentar definir como float usando apenas X
            EPropertyBagResult FloatResult = GraphInstance->SetGraphParameter<float>(ParamName, Value.X);
            if (FloatResult == EPropertyBagResult::Success)
            {
                UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGPrismalFlow::SetPCGParameterModern - Successfully set float parameter %s = %.2f"),
                       *FullParameterName, Value.X);
            }
            else
            {
                UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPrismalFlow::SetPCGParameterModern - Failed to set parameter %s as FVector or float. Result: %d"),
                       *FullParameterName, (int32)FloatResult);
            }
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPrismalFlow::SetPCGParameterModern - Failed to set FVector parameter %s. Result: %d"),
                   *FullParameterName, (int32)Result);
        }

        // Regenerar PCG com novos parâmetros
        if (PCGComponent)
        {
            PCGComponent->GenerateLocal(true);
        }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPrismalFlow::SetPCGParameterModern - GraphInstance not found"));
    }
}

void AAURACRONPCGPrismalFlow::OnMapContraction(float ContractionFactor)
{
    UE_LOG(LogTemp, Log, TEXT("OnMapContraction - Aplicando contração %.2f ao Prismal Flow"), ContractionFactor);

    // Aplicar contração aos pontos da spline
    FVector MapCenter = FVector::ZeroVector; // Centro do mapa

    if (IsValid(FlowSpline))
    {
        int32 NumPoints = FlowSpline->GetNumberOfSplinePoints();

        for (int32 i = 0; i < NumPoints; ++i)
        {
            FVector OriginalLocation = FlowSpline->GetLocationAtSplinePoint(i, ESplineCoordinateSpace::World);
            FVector DirectionToCenter = (MapCenter - OriginalLocation).GetSafeNormal();
            FVector NewLocation = OriginalLocation + DirectionToCenter * (1.0f - ContractionFactor) * OriginalLocation.Size2D();

            // Preservar altura original
            NewLocation.Z = OriginalLocation.Z;

            FlowSpline->SetLocationAtSplinePoint(i, NewLocation, ESplineCoordinateSpace::World);
        }

        // Atualizar spline após modificações
        FlowSpline->UpdateSpline();
    }

    // Aplicar contração aos segmentos do flow
    for (FPrismalFlowSegment& Segment : FlowSegments)
    {
        FVector DirectionToCenter = (MapCenter - Segment.WorldPosition).GetSafeNormal();
        Segment.WorldPosition = Segment.WorldPosition + DirectionToCenter * (1.0f - ContractionFactor) * Segment.WorldPosition.Size2D();

        // Ajustar largura do segmento baseado na contração
        Segment.Width *= ContractionFactor;
    }

    // Aplicar contração às ilhas estratégicas
    for (APrismalFlowIsland* Island : Islands)
    {
        if (IsValid(Island))
        {
            FVector OriginalLocation = Island->GetActorLocation();
            FVector DirectionToCenter = (MapCenter - OriginalLocation).GetSafeNormal();
            FVector NewLocation = OriginalLocation + DirectionToCenter * (1.0f - ContractionFactor) * OriginalLocation.Size2D();

            // Preservar altura original
            NewLocation.Z = OriginalLocation.Z;

            Island->SetActorLocation(NewLocation);

            // Reduzir escala da ilha baseado na contração
            FVector NewScale = Island->GetActorScale3D() * ContractionFactor;
            Island->SetActorScale3D(NewScale);
        }
    }

    // Aplicar contração aos componentes gerados
    for (UActorComponent* Component : GeneratedComponents)
    {
        if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
        {
            FVector OriginalLocation = MeshComp->GetComponentLocation();
            FVector DirectionToCenter = (MapCenter - OriginalLocation).GetSafeNormal();
            FVector NewLocation = OriginalLocation + DirectionToCenter * (1.0f - ContractionFactor) * OriginalLocation.Size2D();

            // Preservar altura original
            NewLocation.Z = OriginalLocation.Z;

            MeshComp->SetWorldLocation(NewLocation);

            // Reduzir escala do componente
            FVector NewScale = MeshComp->GetComponentScale() * ContractionFactor;
            MeshComp->SetWorldScale3D(NewScale);
        }
        else if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Component))
        {
            FVector OriginalLocation = NiagaraComp->GetComponentLocation();
            FVector DirectionToCenter = (MapCenter - OriginalLocation).GetSafeNormal();
            FVector NewLocation = OriginalLocation + DirectionToCenter * (1.0f - ContractionFactor) * OriginalLocation.Size2D();

            // Preservar altura original
            NewLocation.Z = OriginalLocation.Z;

            NiagaraComp->SetWorldLocation(NewLocation);

            // Ajustar parâmetros do efeito baseado na contração
            NiagaraComp->SetFloatParameter(FName("ContractionFactor"), ContractionFactor);
            NiagaraComp->SetFloatParameter(FName("FlowScale"), ContractionFactor);
        }
    }

    // Ajustar propriedades do flow baseado na contração
    SerpentineAmplitude *= ContractionFactor;

    // Atualizar parâmetros PCG se necessário
    if (IsValid(PCGComponent))
    {
        SetPCGParameterModern(TEXT("ContractionFactor"), FVector(ContractionFactor, 0.0f, 0.0f), TEXT("MapContraction"));
        SetPCGParameterModern(TEXT("FlowScale"), FVector(ContractionFactor, 0.0f, 0.0f), TEXT("MapContraction"));

        // Regenerar conteúdo PCG com nova configuração
        PCGComponent->GenerateLocal(false);
    }

    // Regenerar caminho do flow com nova amplitude
    GenerateFlowPath();

    UE_LOG(LogTemp, Log, TEXT("OnMapContraction - Contração aplicada com sucesso ao Prismal Flow"));
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES QUE ESTAVAM FALTANDO - UE 5.6 MODERN APIS
// ========================================

void AAURACRONPCGPrismalFlow::InitializePrismalFlow()
{
    // Implementação robusta para inicializar o Prismal Flow
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::InitializePrismalFlow - Initializing Prismal Flow system"));

    // Configurar valores iniciais
    GlobalFlowIntensity = 1.0f;
    BaseFlowSpeed = 1.0f;
    AccumulatedTime = 0.0f;
    CurrentMapPhase = EAURACRONMapPhase::Awakening;

    // Gerar caminho inicial do flow
    GenerateFlowPath();

    // Configurar componente PCG
    if (PCGComponent && IsValid(PCGComponent))
    {
        PCGComponent->GenerateLocal(false);
    }

    // Configurar efeitos visuais iniciais
    if (FlowNiagaraComponent && IsValid(FlowNiagaraComponent))
    {
        FlowNiagaraComponent->SetFloatParameter(TEXT("FlowIntensity"), GlobalFlowIntensity);
        FlowNiagaraComponent->SetFloatParameter(TEXT("FlowSpeed"), BaseFlowSpeed);
    }

    // Inicializar ilhas estratégicas
    StrategicIslands.Empty();

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::InitializePrismalFlow - Prismal Flow initialized successfully"));
}

void AAURACRONPCGPrismalFlow::ConfigureForAwakeningPhase(bool bEnable)
{
    // Implementação robusta para configurar para fase Awakening
    if (bEnable)
    {
        // Configurar para fase inicial
        GlobalFlowIntensity = 0.6f;
        BaseFlowSpeed = 0.8f;
        CurrentMapPhase = EAURACRONMapPhase::Awakening;

        // Configurar efeitos visuais suaves
        if (FlowNiagaraComponent && IsValid(FlowNiagaraComponent))
        {
            FlowNiagaraComponent->SetFloatParameter(TEXT("AwakeningPhase"), 1.0f);
            FlowNiagaraComponent->SetFloatParameter(TEXT("FlowIntensity"), GlobalFlowIntensity);
            FlowNiagaraComponent->SetFloatParameter(TEXT("FlowSpeed"), BaseFlowSpeed);
        }

        // Configurar PCG para fase Awakening
        if (PCGComponent && IsValid(PCGComponent))
        {
            PCGComponent->GenerateLocal(false);
        }

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::ConfigureForAwakeningPhase - Awakening phase enabled"));
    }
    else
    {
        // Desativar configurações da fase Awakening
        GlobalFlowIntensity = 1.0f;
        BaseFlowSpeed = 1.0f;

        if (FlowNiagaraComponent && IsValid(FlowNiagaraComponent))
        {
            FlowNiagaraComponent->SetFloatParameter(TEXT("AwakeningPhase"), 0.0f);
        }

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::ConfigureForAwakeningPhase - Awakening phase disabled"));
    }
}

void AAURACRONPCGPrismalFlow::ConfigureForConvergencePhase(bool bEnableConvergence, bool bEnableIntensification, bool bEnableResolution)
{
    // Implementação robusta para configurar para fases avançadas

    if (bEnableConvergence)
    {
        // Configurar para fase de convergência
        GlobalFlowIntensity = 1.2f;
        BaseFlowSpeed = 1.3f;
        CurrentMapPhase = EAURACRONMapPhase::Convergence;

        if (FlowNiagaraComponent && IsValid(FlowNiagaraComponent))
        {
            FlowNiagaraComponent->SetFloatParameter(TEXT("ConvergencePhase"), 1.0f);
            FlowNiagaraComponent->SetFloatParameter(TEXT("FlowIntensity"), GlobalFlowIntensity);
        }

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::ConfigureForConvergencePhase - Convergence phase enabled"));
    }

    if (bEnableIntensification)
    {
        // Intensificar efeitos para fase de intensificação
        GlobalFlowIntensity = 1.6f;
        BaseFlowSpeed = 1.8f;
        CurrentMapPhase = EAURACRONMapPhase::Intensification;

        if (FlowNiagaraComponent && IsValid(FlowNiagaraComponent))
        {
            FlowNiagaraComponent->SetFloatParameter(TEXT("IntensificationPhase"), 1.0f);
            FlowNiagaraComponent->SetFloatParameter(TEXT("FlowIntensity"), GlobalFlowIntensity);
        }

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::ConfigureForConvergencePhase - Intensification phase enabled"));
    }

    if (bEnableResolution)
    {
        // Configurar para fase de resolução
        GlobalFlowIntensity = 2.0f;
        BaseFlowSpeed = 2.5f;
        CurrentMapPhase = EAURACRONMapPhase::Resolution;

        if (FlowNiagaraComponent && IsValid(FlowNiagaraComponent))
        {
            FlowNiagaraComponent->SetFloatParameter(TEXT("ResolutionPhase"), 1.0f);
            FlowNiagaraComponent->SetFloatParameter(TEXT("FlowIntensity"), GlobalFlowIntensity);
        }

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::ConfigureForConvergencePhase - Resolution phase enabled"));
    }

    // Regenerar PCG com nova configuração
    if (PCGComponent && IsValid(PCGComponent))
    {
        PCGComponent->GenerateLocal(false);
    }
}

void AAURACRONPCGPrismalFlow::SetPredeterminedPattern(bool bUsePredetermined)
{
    // Implementação robusta para definir padrão predeterminado
    bUsePredeterminedPattern = bUsePredetermined;

    if (bUsePredetermined)
    {
        // Usar padrão predeterminado baseado na documentação
        SerpentineAmplitude = 1500.0f; // 15 metros conforme documentação
        SerpentineFrequency = 4.0f;
        NumControlPoints = 20;

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::SetPredeterminedPattern - Using predetermined pattern"));
    }
    else
    {
        // Usar padrão procedural
        SerpentineAmplitude = FMath::RandRange(1000.0f, 2000.0f);
        SerpentineFrequency = FMath::RandRange(3.0f, 6.0f);
        NumControlPoints = FMath::RandRange(15, 25);

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::SetPredeterminedPattern - Using procedural pattern"));
    }

    // Regenerar caminho do flow
    GenerateFlowPath();

    // Regenerar PCG
    if (PCGComponent && IsValid(PCGComponent))
    {
        PCGComponent->GenerateLocal(false);
    }
}

APrismalFlowIsland* AAURACRONPCGPrismalFlow::AddIslandAtPosition(float DistanceAlongSpline, EPrismalFlowIslandType IslandType)
{
    // Implementação robusta para adicionar ilha na posição especificada
    if (!FlowSpline || !IsValid(FlowSpline))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPrismalFlow::AddIslandAtPosition - FlowSpline is invalid"));
        return nullptr;
    }

    // Calcular posição ao longo da spline
    float SplineLength = FlowSpline->GetSplineLength();
    float ClampedDistance = FMath::Clamp(DistanceAlongSpline, 0.0f, SplineLength);
    FVector IslandLocation = FlowSpline->GetLocationAtDistanceAlongSpline(ClampedDistance, ESplineCoordinateSpace::World);

    // Spawn nova ilha (simulação - classe APrismalFlowIsland pode não existir ainda)
    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPrismalFlow::AddIslandAtPosition - World is invalid"));
        return nullptr;
    }

    // Por enquanto, retornar nullptr e logar que a ilha seria criada
    // APrismalFlowIsland* NewIsland = World->SpawnActor<APrismalFlowIsland>(IslandLocation, FRotator::ZeroRotator);

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::AddIslandAtPosition - Would create island of type %d at distance %.2f"),
           (int32)IslandType, DistanceAlongSpline);

    return nullptr; // Retornar nullptr até que a classe APrismalFlowIsland seja implementada
}

void AAURACRONPCGPrismalFlow::RemoveIsland(APrismalFlowIsland* Island)
{
    // Implementação robusta para remover ilha
    if (!Island || !IsValid(Island))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPrismalFlow::RemoveIsland - Island is invalid"));
        return;
    }

    // Remover da lista de ilhas estratégicas se estiver lá
    StrategicIslands.Remove(Island);

    // Destruir a ilha
    Island->Destroy();

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::RemoveIsland - Island removed successfully"));
}

TArray<APrismalFlowIsland*> AAURACRONPCGPrismalFlow::GetAllIslands() const
{
    // Implementação robusta para obter todas as ilhas
    TArray<APrismalFlowIsland*> AllIslands;

    // Adicionar ilhas estratégicas
    AllIslands.Append(StrategicIslands);

    // Buscar outras ilhas no mundo (se necessário)
    UWorld* World = GetWorld();
    if (World)
    {
        // Por enquanto, apenas retornar as ilhas estratégicas
        // Implementar busca completa quando APrismalFlowIsland estiver disponível
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGPrismalFlow::GetAllIslands - Found %d islands"), AllIslands.Num());

    return AllIslands;
}

TArray<APrismalFlowIsland*> AAURACRONPCGPrismalFlow::GetIslandsByType(EPrismalFlowIslandType IslandType) const
{
    // Implementação robusta para obter ilhas por tipo
    TArray<APrismalFlowIsland*> FilteredIslands;

    // Filtrar ilhas estratégicas por tipo
    for (APrismalFlowIsland* Island : StrategicIslands)
    {
        if (Island && IsValid(Island))
        {
            // Verificar tipo da ilha (implementar quando APrismalFlowIsland estiver disponível)
            // if (Island->GetIslandType() == IslandType)
            // {
            //     FilteredIslands.Add(Island);
            // }
        }
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGPrismalFlow::GetIslandsByType - Found %d islands of type %d"),
           FilteredIslands.Num(), (int32)IslandType);

    return FilteredIslands;
}

void AAURACRONPCGPrismalFlow::ConfigureWorldPartitionStreaming(const FAURACRONPCGStreamingConfig& StreamingConfig)
{
    // Implementação robusta para configurar streaming do World Partition
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::ConfigureWorldPartitionStreaming - Configuring streaming"));

    // Configurar streaming baseado na configuração fornecida (usando propriedades corretas)
    bStreamingEnabled = StreamingConfig.bUseAsyncStreaming;

    if (bStreamingEnabled)
    {
        // Configurar parâmetros de streaming
        StreamingDistance = StreamingConfig.StreamingDistance;

        // Configurar LOD baseado na distância
        if (PCGComponent && IsValid(PCGComponent))
        {
            // Implementar configuração de LOD quando disponível
        }

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::ConfigureWorldPartitionStreaming - Streaming enabled with distance %.2f"),
               StreamingDistance);
    }
    else
    {
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::ConfigureWorldPartitionStreaming - Streaming disabled"));
    }
}

void AAURACRONPCGPrismalFlow::AssociateWithDataLayer(const FName& DataLayerName)
{
    // Implementação robusta para associar com Data Layer
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::AssociateWithDataLayer - Associating with data layer: %s"),
           *DataLayerName.ToString());

    // Armazenar nome do data layer
    AssociatedDataLayer = DataLayerName;

    // Implementar associação real com Data Layer quando API estiver disponível
    // Usar UDataLayerSubsystem quando disponível no UE 5.6

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::AssociateWithDataLayer - Associated with data layer successfully"));
}

void AAURACRONPCGPrismalFlow::ConfigureStreamingSettings(const FAURACRONPCGStreamingConfig& StreamingConfig)
{
    // Implementação robusta para configurar configurações de streaming
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::ConfigureStreamingSettings - Configuring streaming settings"));

    // Aplicar configurações de streaming (usando propriedades corretas)
    bStreamingEnabled = StreamingConfig.bUseAsyncStreaming;
    StreamingDistance = StreamingConfig.StreamingDistance;

    // Configurar qualidade baseada na prioridade de streaming
    if (StreamingConfig.StreamingPriority > 50)
    {
        GlobalFlowIntensity *= 1.2f;

        if (FlowNiagaraComponent && IsValid(FlowNiagaraComponent))
        {
            FlowNiagaraComponent->SetFloatParameter(TEXT("QualityMultiplier"), 1.2f);
        }
    }
    else
    {
        GlobalFlowIntensity *= 0.8f;

        if (FlowNiagaraComponent && IsValid(FlowNiagaraComponent))
        {
            FlowNiagaraComponent->SetFloatParameter(TEXT("QualityMultiplier"), 0.8f);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::ConfigureStreamingSettings - Streaming settings configured"));
}

void AAURACRONPCGPrismalFlow::SetStreamingEnabled(bool bEnabled)
{
    // Implementação robusta para habilitar/desabilitar streaming
    bStreamingEnabled = bEnabled;

    if (bEnabled)
    {
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::SetStreamingEnabled - Streaming enabled"));

        // Ativar otimizações de streaming
        if (PCGComponent && IsValid(PCGComponent))
        {
            // Implementar otimizações quando disponível
        }
    }
    else
    {
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::SetStreamingEnabled - Streaming disabled"));

        // Desativar otimizações de streaming
        if (PCGComponent && IsValid(PCGComponent))
        {
            PCGComponent->GenerateLocal(false); // Gerar tudo localmente
        }
    }
}

void AAURACRONPCGPrismalFlow::SetVisualEffectsEnabled(bool bEnabled)
{
    // Implementação robusta para habilitar/desabilitar efeitos visuais
    bVisualEffectsEnabled = bEnabled;

    if (bEnabled)
    {
        // Ativar efeitos visuais
        if (FlowNiagaraComponent && IsValid(FlowNiagaraComponent))
        {
            FlowNiagaraComponent->SetVisibility(true);
            FlowNiagaraComponent->SetFloatParameter(TEXT("EffectsEnabled"), 1.0f);
        }

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::SetVisualEffectsEnabled - Visual effects enabled"));
    }
    else
    {
        // Desativar efeitos visuais
        if (FlowNiagaraComponent && IsValid(FlowNiagaraComponent))
        {
            FlowNiagaraComponent->SetVisibility(false);
            FlowNiagaraComponent->SetFloatParameter(TEXT("EffectsEnabled"), 0.0f);
        }

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::SetVisualEffectsEnabled - Visual effects disabled"));
    }
}

void AAURACRONPCGPrismalFlow::UpdateVisualEffectsForPhase(EAURACRONMapPhase Phase)
{
    // Implementação robusta para atualizar efeitos visuais baseado na fase
    CurrentMapPhase = Phase;

    if (!FlowNiagaraComponent || !IsValid(FlowNiagaraComponent))
    {
        return;
    }

    switch (Phase)
    {
        case EAURACRONMapPhase::Awakening:
            FlowNiagaraComponent->SetFloatParameter(TEXT("PhaseIntensity"), 0.6f);
            FlowNiagaraComponent->SetVectorParameter(TEXT("PhaseColor"), FVector(0.8f, 0.9f, 1.0f)); // Azul suave
            break;

        case EAURACRONMapPhase::Convergence:
            FlowNiagaraComponent->SetFloatParameter(TEXT("PhaseIntensity"), 1.0f);
            FlowNiagaraComponent->SetVectorParameter(TEXT("PhaseColor"), FVector(1.0f, 0.8f, 0.6f)); // Dourado
            break;

        case EAURACRONMapPhase::Intensification:
            FlowNiagaraComponent->SetFloatParameter(TEXT("PhaseIntensity"), 1.5f);
            FlowNiagaraComponent->SetVectorParameter(TEXT("PhaseColor"), FVector(1.0f, 0.4f, 0.2f)); // Laranja intenso
            break;

        case EAURACRONMapPhase::Resolution:
            FlowNiagaraComponent->SetFloatParameter(TEXT("PhaseIntensity"), 2.0f);
            FlowNiagaraComponent->SetVectorParameter(TEXT("PhaseColor"), FVector(1.0f, 0.2f, 0.8f)); // Rosa/Magenta
            break;
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::UpdateVisualEffectsForPhase - Updated effects for phase %d"), (int32)Phase);
}

void AAURACRONPCGPrismalFlow::RegisterStrategicIsland(APrismalFlowIsland* Island)
{
    // Implementação robusta para registrar ilha estratégica
    if (!Island || !IsValid(Island))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPrismalFlow::RegisterStrategicIsland - Island is invalid"));
        return;
    }

    // Adicionar à lista se não estiver já presente
    if (!StrategicIslands.Contains(Island))
    {
        StrategicIslands.Add(Island);
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::RegisterStrategicIsland - Strategic island registered"));
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPrismalFlow::RegisterStrategicIsland - Island already registered"));
    }
}

void AAURACRONPCGPrismalFlow::UnregisterStrategicIsland(APrismalFlowIsland* Island)
{
    // Implementação robusta para desregistrar ilha estratégica
    if (!Island || !IsValid(Island))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPrismalFlow::UnregisterStrategicIsland - Island is invalid"));
        return;
    }

    // Remover da lista
    int32 RemovedCount = StrategicIslands.Remove(Island);

    if (RemovedCount > 0)
    {
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::UnregisterStrategicIsland - Strategic island unregistered"));
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPrismalFlow::UnregisterStrategicIsland - Island was not registered"));
    }
}
