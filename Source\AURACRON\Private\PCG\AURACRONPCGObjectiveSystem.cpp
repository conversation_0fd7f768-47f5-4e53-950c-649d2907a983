// AURACRONPCGObjectiveSystem.cpp
// Implementação do sistema de objetivos estratégicos baseado em LoL Baron/Dragon
// Sistema robusto com APIs modernas UE 5.6, carregamento assíncrono, replicação e integração completa

#include "PCG/AURACRONPCGObjectiveSystem.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "PCG/AURACRONPCGEnvironmentManager.h"
#include "PCG/AURACRONPCGIsland.h"
#include "PCG/AURACRONPCGTrail.h"
#include "PCG/AURACRONPCGPrismalFlow.h"
#include "Components/StaticMeshComponent.h"
#include "Components/BoxComponent.h"
#include "Components/PointLightComponent.h"
#include "Components/AudioComponent.h"
#include "Engine/World.h"
#include "Engine/StaticMesh.h"
#include "Engine/StreamableManager.h"
#include "Engine/DataTable.h"
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Kismet/GameplayStatics.h"
#include "Net/UnrealNetwork.h"
#include "UObject/ConstructorHelpers.h"
#include "GameFramework/GameStateBase.h"
#include "EngineUtils.h"
#include "TimerManager.h"
#include "Logging/StructuredLog.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "NiagaraFunctionLibrary.h"

AAURACRONPCGObjectiveSystem::AAURACRONPCGObjectiveSystem()
    : bAutoGenerate(true)
    , CurrentEnvironment(EAURACRONEnvironmentType::RadiantPlains)
    , CurrentMapPhase(EAURACRONMapPhase::Awakening)
    , NextChaosIslandIndex(0)
    , StreamableManager(FStreamableManager())
    , ObjectiveUpdateTimer(0.1f)
    , bIsInitialized(false)
    , bStreamingInProgress(false)
{
    PrimaryActorTick.bCanEverTick = false; // Usar Timer ao invés de Tick para performance

    // Configurar replicação para multiplayer moderno UE 5.6
    bReplicates = true;
    SetReplicateMovement(false);
    bAlwaysRelevant = true; // Objetivos sempre relevantes para todos os jogadores
    NetUpdateFrequency = 10.0f; // Atualização de rede otimizada

    // Criar componente raiz
    RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootComponent"));

    // Inicializar componentes de áudio para feedback de objetivos
    ObjectiveAudioComponent = CreateDefaultSubobject<UAudioComponent>(TEXT("ObjectiveAudioComponent"));
    ObjectiveAudioComponent->SetupAttachment(RootComponent);
    ObjectiveAudioComponent->bAutoActivate = false;

    // Inicializar arrays de componentes
    ObjectiveEffectComponents.Reserve(20); // Reservar espaço para performance
    ObjectiveMeshesByType.Reserve(10);

    // Configurar valores padrão para integração com documentação
    PrismalFlowIntensity = 1.0f;
    SolarTrailPower = 0.5f;
    AxisTrailPower = 0.5f;
    LunarTrailPower = 0.5f;
    CentralAuracronIslandControlPercentage = 0.0f;
}

void AAURACRONPCGObjectiveSystem::BeginPlay()
{
    Super::BeginPlay();

    // Validações robustas de inicialização
    if (!IsValid(GetWorld()))
    {
        UE_LOGFMT(LogTemp, Error, "AURACRONPCGObjectiveSystem: World inválido durante BeginPlay");
        return;
    }

    // Inicializar sistema apenas no servidor
    if (HasAuthority() && bAutoGenerate)
    {
        // Configurar timer de atualização otimizado (0.1f para performance)
        GetWorld()->GetTimerManager().SetTimer(ObjectiveUpdateTimerHandle, this,
            &AAURACRONPCGObjectiveSystem::UpdateObjectivesTimer, ObjectiveUpdateTimer, true);

        // Delay pequeno para garantir que outros sistemas estejam prontos
        FTimerHandle GenerationTimer;
        GetWorld()->GetTimerManager().SetTimer(GenerationTimer, this,
            &AAURACRONPCGObjectiveSystem::InitializeObjectiveSystemAsync, 2.0f, false);

        UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Sistema inicializado no servidor com timer otimizado {Timer}s", ObjectiveUpdateTimer);
    }
    else if (!HasAuthority())
    {
        UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Cliente inicializado, aguardando replicação do servidor");
    }
}

void AAURACRONPCGObjectiveSystem::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Tick desabilitado - usando Timer otimizado para performance
    // Toda lógica de atualização foi movida para UpdateObjectivesTimer()
}

// Nova função de atualização otimizada usando Timer ao invés de Tick
void AAURACRONPCGObjectiveSystem::UpdateObjectivesTimer()
{
    // Validações robustas
    if (!HasAuthority() || !bIsInitialized)
    {
        return;
    }

    // Atualizar timers de respawn com validações
    for (int32 i = 0; i < Objectives.Num(); ++i)
    {
        if (!Objectives.IsValidIndex(i))
        {
            continue;
        }

        FAURACRONObjectiveInfo& Objective = Objectives[i];

        if (Objective.CurrentState == EAURACRONObjectiveState::Respawning &&
            Objective.TimeUntilRespawn > 0.0f)
        {
            Objective.TimeUntilRespawn -= ObjectiveUpdateTimer;

            if (Objective.TimeUntilRespawn <= 0.0f)
            {
                OnObjectiveRespawn(i);
            }
        }
    }

    // Atualizar integração com Trilhos e Fluxo Prismal
    UpdateTrailsIntegration();
    UpdatePrismalFlowIntegration();
    UpdateCentralAuracronIslandIntegration();
}

// ========================================
// REPLICAÇÃO MODERNA UE 5.6
// ========================================

void AAURACRONPCGObjectiveSystem::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    // Replicar propriedades essenciais para todos os clientes
    DOREPLIFETIME(AAURACRONPCGObjectiveSystem, Objectives);
    DOREPLIFETIME(AAURACRONPCGObjectiveSystem, ActiveProceduralObjectives);
    DOREPLIFETIME(AAURACRONPCGObjectiveSystem, CurrentEnvironment);
    DOREPLIFETIME(AAURACRONPCGObjectiveSystem, CurrentMapPhase);
    DOREPLIFETIME(AAURACRONPCGObjectiveSystem, NextChaosIslandIndex);
    DOREPLIFETIME(AAURACRONPCGObjectiveSystem, PrismalFlowIntensity);
    DOREPLIFETIME(AAURACRONPCGObjectiveSystem, SolarTrailPower);
    DOREPLIFETIME(AAURACRONPCGObjectiveSystem, AxisTrailPower);
    DOREPLIFETIME(AAURACRONPCGObjectiveSystem, LunarTrailPower);
    DOREPLIFETIME(AAURACRONPCGObjectiveSystem, CentralAuracronIslandControlPercentage);
    DOREPLIFETIME(AAURACRONPCGObjectiveSystem, bIsInitialized);
}

// ========================================
// INICIALIZAÇÃO ASSÍNCRONA ROBUSTA
// ========================================

void AAURACRONPCGObjectiveSystem::InitializeObjectiveSystemAsync()
{
    if (!HasAuthority())
    {
        UE_LOGFMT(LogTemp, Warning, "AURACRONPCGObjectiveSystem: InitializeObjectiveSystemAsync chamado em cliente - ignorando");
        return;
    }

    if (bStreamingInProgress)
    {
        UE_LOGFMT(LogTemp, Warning, "AURACRONPCGObjectiveSystem: Streaming já em progresso - aguardando conclusão");
        return;
    }

    bStreamingInProgress = true;
    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Iniciando carregamento assíncrono de assets");

    // Carregar assets assincronamente usando StreamableManager moderno UE 5.6
    TArray<FSoftObjectPath> AssetsToLoad;

    // Assets de mesh para objetivos
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/AURACRON/Meshes/Objectives/SM_PrismalNexus.SM_PrismalNexus")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/AURACRON/Meshes/Objectives/SM_RadiantAnchor.SM_RadiantAnchor")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/AURACRON/Meshes/Objectives/SM_ZephyrAnchor.SM_ZephyrAnchor")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/AURACRON/Meshes/Objectives/SM_PurgatoryAnchor.SM_PurgatoryAnchor")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/AURACRON/Meshes/Objectives/SM_StormCore.SM_StormCore")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/AURACRON/Meshes/Islands/SM_NexusIsland.SM_NexusIsland")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/AURACRON/Meshes/Islands/SM_SanctuaryIsland.SM_SanctuaryIsland")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/AURACRON/Meshes/Islands/SM_ArsenalIsland.SM_ArsenalIsland")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/AURACRON/Meshes/Islands/SM_ChaosIsland.SM_ChaosIsland")));

    // Assets de efeitos Niagara
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/AURACRON/VFX/Objectives/NS_PrismalNexusEffect.NS_PrismalNexusEffect")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/AURACRON/VFX/Objectives/NS_RadiantAnchorEffect.NS_RadiantAnchorEffect")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/AURACRON/VFX/Objectives/NS_ZephyrAnchorEffect.NS_ZephyrAnchorEffect")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/AURACRON/VFX/Objectives/NS_PurgatoryAnchorEffect.NS_PurgatoryAnchorEffect")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/AURACRON/VFX/Objectives/NS_StormCoreEffect.NS_StormCoreEffect")));

    // Assets de áudio
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/AURACRON/Audio/Objectives/SC_ObjectiveCapture.SC_ObjectiveCapture")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/AURACRON/Audio/Objectives/SC_ObjectiveRespawn.SC_ObjectiveRespawn")));

    // Iniciar carregamento assíncrono
    StreamableManager.RequestAsyncLoad(AssetsToLoad,
        FStreamableDelegate::CreateUObject(this, &AAURACRONPCGObjectiveSystem::OnAssetsLoadedComplete));
}

void AAURACRONPCGObjectiveSystem::OnAssetsLoadedComplete()
{
    bStreamingInProgress = false;

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Assets carregados com sucesso - iniciando geração de objetivos");

    // Agora que os assets estão carregados, gerar objetivos
    GenerateObjectives();

    bIsInitialized = true;
    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Sistema completamente inicializado");
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES PÚBLICAS ROBUSTAS
// ========================================

void AAURACRONPCGObjectiveSystem::GenerateObjectives()
{
    // Validações robustas
    if (!HasAuthority())
    {
        UE_LOGFMT(LogTemp, Warning, "AURACRONPCGObjectiveSystem: GenerateObjectives chamado em cliente - ignorando");
        return;
    }

    if (!IsValid(GetWorld()))
    {
        UE_LOGFMT(LogTemp, Error, "AURACRONPCGObjectiveSystem: World inválido durante GenerateObjectives");
        return;
    }

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Gerando objetivos estratégicos baseados em LoL Baron/Dragon com integração completa AURACRON");

    // Limpar objetivos anteriores com validações
    for (const auto& EnvPair : ObjectiveMeshesByEnvironment)
    {
        if (EnvPair.Value.Num() > 0)
        {
            ClearObjectivesForEnvironment(EnvPair.Key);
        }
    }

    // Inicializar informações dos objetivos
    InitializeObjectiveInfos();

    // Gerar objetivos para todos os 3 ambientes com validações
    for (int32 EnvIndex = 0; EnvIndex < 3; ++EnvIndex)
    {
        EAURACRONEnvironmentType Environment = static_cast<EAURACRONEnvironmentType>(EnvIndex);
        GenerateObjectivesForEnvironment(Environment);

        UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Objetivos gerados para ambiente {Environment}", static_cast<int32>(Environment));
    }

    // Inicializar integração com sistemas da documentação
    InitializeTrailsIntegration();
    InitializePrismalFlowIntegration();
    InitializeCentralAuracronIslandIntegration();

    // Iniciar com Radiant Plains ativo
    UpdateForEnvironment(EAURACRONEnvironmentType::RadiantPlains);

    // Iniciar rotação de Chaos Islands
    StartChaosIslandRotation();

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Gerados {ObjectiveCount} objetivos estratégicos para 3 ambientes com integração completa", Objectives.Num());
}

void AAURACRONPCGObjectiveSystem::GenerateObjectivesForEnvironment(EAURACRONEnvironmentType Environment)
{
    // Validações robustas
    if (!HasAuthority())
    {
        UE_LOGFMT(LogTemp, Warning, "AURACRONPCGObjectiveSystem: GenerateObjectivesForEnvironment chamado em cliente - ignorando");
        return;
    }

    if (!IsValid(GetWorld()))
    {
        UE_LOGFMT(LogTemp, Error, "AURACRONPCGObjectiveSystem: World inválido durante GenerateObjectivesForEnvironment");
        return;
    }

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Gerando objetivos para ambiente {Environment} com validações robustas", static_cast<int32>(Environment));

    // Validar se temos objetivos para processar
    if (Objectives.Num() == 0)
    {
        UE_LOGFMT(LogTemp, Warning, "AURACRONPCGObjectiveSystem: Nenhum objetivo inicializado para ambiente {Environment}", static_cast<int32>(Environment));
        return;
    }

    // Criar objetivos com validações
    for (int32 i = 0; i < Objectives.Num(); ++i)
    {
        if (!Objectives.IsValidIndex(i))
        {
            UE_LOGFMT(LogTemp, Error, "AURACRONPCGObjectiveSystem: Índice de objetivo inválido {Index} para ambiente {Environment}", i, static_cast<int32>(Environment));
            continue;
        }

        CreateObjective(i, Environment);
        ApplyEnvironmentCharacteristics(i, Environment);

        // Aplicar integração específica do ambiente com sistemas da documentação
        ApplyEnvironmentTrailsIntegration(i, Environment);
        ApplyEnvironmentPrismalFlowIntegration(i, Environment);
    }

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Objetivos criados com sucesso para ambiente {Environment}", static_cast<int32>(Environment));
}

TArray<FAURACRONObjectiveInfo> AAURACRONPCGObjectiveSystem::GetObjectivesByType(EAURACRONObjectiveType ObjectiveType) const
{
    TArray<FAURACRONObjectiveInfo> FilteredObjectives;
    
    for (const FAURACRONObjectiveInfo& Objective : Objectives)
    {
        if (Objective.ObjectiveType == ObjectiveType)
        {
            FilteredObjectives.Add(Objective);
        }
    }
    
    return FilteredObjectives;
}

TArray<FAURACRONObjectiveInfo> AAURACRONPCGObjectiveSystem::GetObjectivesByState(EAURACRONObjectiveState State) const
{
    TArray<FAURACRONObjectiveInfo> FilteredObjectives;
    
    for (const FAURACRONObjectiveInfo& Objective : Objectives)
    {
        if (Objective.CurrentState == State)
        {
            FilteredObjectives.Add(Objective);
        }
    }
    
    return FilteredObjectives;
}

bool AAURACRONPCGObjectiveSystem::AttackObjective(int32 ObjectiveIndex, float Damage, int32 AttackingTeam)
{
    // Validações robustas
    if (!HasAuthority())
    {
        UE_LOGFMT(LogTemp, Warning, "AURACRONPCGObjectiveSystem: AttackObjective chamado em cliente - ignorando");
        return false;
    }

    if (ObjectiveIndex < 0 || ObjectiveIndex >= Objectives.Num())
    {
        UE_LOGFMT(LogTemp, Error, "AURACRONPCGObjectiveSystem: Índice de objetivo inválido {Index} para ataque", ObjectiveIndex);
        return false;
    }

    if (Damage <= 0.0f)
    {
        UE_LOGFMT(LogTemp, Warning, "AURACRONPCGObjectiveSystem: Dano inválido {Damage} para objetivo {Index}", Damage, ObjectiveIndex);
        return false;
    }

    if (AttackingTeam < 0 || AttackingTeam > 1)
    {
        UE_LOGFMT(LogTemp, Warning, "AURACRONPCGObjectiveSystem: Team atacante inválido {Team} para objetivo {Index}", AttackingTeam, ObjectiveIndex);
        return false;
    }

    FAURACRONObjectiveInfo& Objective = Objectives[ObjectiveIndex];

    if (Objective.CurrentState != EAURACRONObjectiveState::Available &&
        Objective.CurrentState != EAURACRONObjectiveState::InCombat)
    {
        UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Objetivo {Index} não pode ser atacado no estado {State}",
            ObjectiveIndex, static_cast<int32>(Objective.CurrentState));
        return false;
    }

    // Aplicar dano com validações
    float PreviousHealth = Objective.CurrentHealth;
    Objective.CurrentHealth = FMath::Max(0.0f, Objective.CurrentHealth - Damage);
    Objective.CurrentState = EAURACRONObjectiveState::InCombat;

    // Tocar efeito sonoro de ataque se disponível
    if (IsValid(ObjectiveAudioComponent) && IsValid(ObjectiveAttackSound))
    {
        ObjectiveAudioComponent->SetSound(ObjectiveAttackSound);
        ObjectiveAudioComponent->Play();
    }

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Objetivo {Index} atacado por Team {Team}, HP: {CurrentHP}/{MaxHP} (Dano: {Damage})",
        ObjectiveIndex, AttackingTeam, Objective.CurrentHealth, Objective.MaxHealth, Damage);

    // Verificar se foi morto
    if (Objective.CurrentHealth <= 0.0f)
    {
        UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Objetivo {Index} destruído, iniciando captura por Team {Team}",
            ObjectiveIndex, AttackingTeam);
        return CaptureObjective(ObjectiveIndex, AttackingTeam);
    }

    return true;
}

bool AAURACRONPCGObjectiveSystem::CaptureObjective(int32 ObjectiveIndex, int32 CapturingTeam)
{
    // Validações robustas
    if (!HasAuthority())
    {
        UE_LOGFMT(LogTemp, Warning, "AURACRONPCGObjectiveSystem: CaptureObjective chamado em cliente - ignorando");
        return false;
    }

    if (ObjectiveIndex < 0 || ObjectiveIndex >= Objectives.Num())
    {
        UE_LOGFMT(LogTemp, Error, "AURACRONPCGObjectiveSystem: Índice de objetivo inválido {Index} para captura", ObjectiveIndex);
        return false;
    }

    if (CapturingTeam < 0 || CapturingTeam > 1)
    {
        UE_LOGFMT(LogTemp, Warning, "AURACRONPCGObjectiveSystem: Team capturador inválido {Team} para objetivo {Index}", CapturingTeam, ObjectiveIndex);
        return false;
    }

    FAURACRONObjectiveInfo& Objective = Objectives[ObjectiveIndex];

    if (Objective.CurrentState == EAURACRONObjectiveState::Captured)
    {
        UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Objetivo {Index} já capturado por Team {Team}",
            ObjectiveIndex, Objective.ControllingTeam);
        return false;
    }

    // Capturar objetivo com validações
    EAURACRONObjectiveState PreviousState = Objective.CurrentState;
    Objective.CurrentState = EAURACRONObjectiveState::Captured;
    Objective.ControllingTeam = CapturingTeam;
    Objective.TimeUntilRespawn = Objective.RespawnTime;

    // Tocar efeito sonoro de captura se disponível
    if (IsValid(ObjectiveAudioComponent) && IsValid(ObjectiveCaptureSound))
    {
        ObjectiveAudioComponent->SetSound(ObjectiveCaptureSound);
        ObjectiveAudioComponent->Play();
    }

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Objetivo {Index} capturado por Team {Team}, respawn em {RespawnTime}s",
        ObjectiveIndex, CapturingTeam, Objective.RespawnTime);

    // Aplicar buffs ao time com validações
    ApplyObjectiveBuffsToTeam(ObjectiveIndex, CapturingTeam);

    // Iniciar timer de respawn
    StartRespawnTimer(ObjectiveIndex);

    // Atualizar visibilidade
    UpdateObjectiveVisibility();

    // Atualizar integração com sistemas da documentação
    UpdateTrailsIntegration();
    UpdatePrismalFlowIntegration();
    UpdateCentralAuracronIslandIntegration();

    // Broadcast evento de captura
    OnObjectiveCaptured.Broadcast(ObjectiveIndex, CapturingTeam);

    return true;
}

bool AAURACRONPCGObjectiveSystem::IsObjectiveAvailable(int32 ObjectiveIndex) const
{
    if (ObjectiveIndex < 0 || ObjectiveIndex >= Objectives.Num())
    {
        return false;
    }
    
    const FAURACRONObjectiveInfo& Objective = Objectives[ObjectiveIndex];
    return Objective.CurrentState == EAURACRONObjectiveState::Available && 
           Objective.bIsActiveInCurrentPhase;
}

TMap<FString, float> AAURACRONPCGObjectiveSystem::GetObjectiveBuffs(int32 ObjectiveIndex) const
{
    if (ObjectiveIndex < 0 || ObjectiveIndex >= Objectives.Num())
    {
        return TMap<FString, float>();
    }
    
    return Objectives[ObjectiveIndex].ObjectiveBuffs;
}

void AAURACRONPCGObjectiveSystem::UpdateForEnvironment(EAURACRONEnvironmentType NewEnvironment)
{
    // Validações robustas
    if (!HasAuthority())
    {
        UE_LOGFMT(LogTemp, Warning, "AURACRONPCGObjectiveSystem: UpdateForEnvironment chamado em cliente - ignorando");
        return;
    }

    if (CurrentEnvironment == NewEnvironment)
    {
        UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Ambiente já é {Environment} - sem mudanças necessárias", static_cast<int32>(NewEnvironment));
        return;
    }

    EAURACRONEnvironmentType PreviousEnvironment = CurrentEnvironment;
    CurrentEnvironment = NewEnvironment;

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Mudando ambiente de {PrevEnv} para {NewEnv}",
        static_cast<int32>(PreviousEnvironment), static_cast<int32>(NewEnvironment));

    // Atualizar visibilidade dos objetivos
    UpdateObjectiveVisibility();

    // Atualizar objetivos específicos do ambiente
    UpdateEnvironmentSpecificObjectives(NewEnvironment);

    // Atualizar integração com trilhos baseado no novo ambiente
    UpdateTrailsForEnvironment(NewEnvironment);

    // Atualizar integração com Fluxo Prismal baseado no novo ambiente
    UpdatePrismalFlowForEnvironment(NewEnvironment);

    // Broadcast mudança de ambiente
    OnEnvironmentChanged.Broadcast(PreviousEnvironment, NewEnvironment);

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Atualizado com sucesso para ambiente {Environment}", static_cast<int32>(NewEnvironment));
}

void AAURACRONPCGObjectiveSystem::UpdateTrailsForEnvironment(EAURACRONEnvironmentType Environment)
{
    if (!HasAuthority())
    {
        return;
    }

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Atualizando trilhos para ambiente {Environment}", static_cast<int32>(Environment));

    // Aplicar modificadores específicos do ambiente nos trilhos
    float SolarModifier = 1.0f;
    float AxisModifier = 1.0f;
    float LunarModifier = 1.0f;

    switch (Environment)
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        SolarModifier = 1.5f; // Solar Trilhos mais poderosos
        AxisModifier = 1.0f;
        LunarModifier = 0.8f;
        break;

    case EAURACRONEnvironmentType::ZephyrFirmament:
        SolarModifier = 0.8f;
        AxisModifier = 1.5f; // Axis Trilhos mais poderosos
        LunarModifier = 1.2f;
        break;

    case EAURACRONEnvironmentType::PurgatoryRealm:
        SolarModifier = 0.7f;
        AxisModifier = 0.9f;
        LunarModifier = 1.8f; // Lunar Trilhos mais poderosos
        break;
    }

    // Aplicar modificadores aos trilhos
    for (AAURACRONPCGTrail* Trail : SolarTrails)
    {
        if (IsValid(Trail))
        {
            Trail->SetEnvironmentModifier(SolarModifier);
        }
    }

    for (AAURACRONPCGTrail* Trail : AxisTrails)
    {
        if (IsValid(Trail))
        {
            Trail->SetEnvironmentModifier(AxisModifier);
        }
    }

    for (AAURACRONPCGTrail* Trail : LunarTrails)
    {
        if (IsValid(Trail))
        {
            Trail->SetEnvironmentModifier(LunarModifier);
        }
    }

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Modificadores aplicados - Solar: {Solar}, Axis: {Axis}, Lunar: {Lunar}",
        SolarModifier, AxisModifier, LunarModifier);
}

void AAURACRONPCGObjectiveSystem::UpdatePrismalFlowForEnvironment(EAURACRONEnvironmentType Environment)
{
    if (!HasAuthority() || !IsValid(PrismalFlowReference))
    {
        return;
    }

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Atualizando Fluxo Prismal para ambiente {Environment}", static_cast<int32>(Environment));

    // Aplicar modificadores específicos do ambiente no Fluxo Prismal
    float EnvironmentModifier = 1.0f;

    switch (Environment)
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        EnvironmentModifier = 1.0f; // Fluxo normal
        break;

    case EAURACRONEnvironmentType::ZephyrFirmament:
        EnvironmentModifier = 1.3f; // Fluxo mais intenso no ar
        break;

    case EAURACRONEnvironmentType::PurgatoryRealm:
        EnvironmentModifier = 0.8f; // Fluxo reduzido no reino espectral
        break;
    }

    // Aplicar modificador ao Fluxo Prismal
    PrismalFlowReference->SetEnvironmentModifier(EnvironmentModifier);

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Modificador de ambiente {Modifier} aplicado ao Fluxo Prismal", EnvironmentModifier);
}

void AAURACRONPCGObjectiveSystem::UpdateEnvironmentSpecificObjectives(EAURACRONEnvironmentType EnvironmentType)
{
    // Limpar objetivos procedurais específicos do ambiente anterior
    TArray<FAURACRONProceduralObjective> RemainingObjectives;
    for (const FAURACRONProceduralObjective& Objective : ActiveProceduralObjectives)
    {
        // Manter apenas objetivos que não são específicos de ambiente
        if (Objective.EnvironmentType != EnvironmentType && 
            Objective.ObjectiveCategory != EAURACRONObjectiveCategory::Environment)
        {
            RemainingObjectives.Add(Objective);
        }
    }
    
    // Substituir a lista ativa com os objetivos restantes
    ActiveProceduralObjectives = RemainingObjectives;
    
    // Gerar novos objetivos específicos para o ambiente atual
    switch (EnvironmentType)
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        // Gerar objetivos específicos para Radiant Plains
        for (int32 i = 0; i < 2; ++i)
        {
            FAURACRONProceduralObjective NewObjective = GenerateNewProceduralObjective(EAURACRONObjectiveType::RadiantShrine);
            NewObjective.EnvironmentType = EnvironmentType;
            NewObjective.ObjectiveCategory = EAURACRONObjectiveCategory::Environment;
            ActiveProceduralObjectives.Add(NewObjective);
        }
        break;
        
    case EAURACRONEnvironmentType::ZephyrFirmament:
        // Gerar objetivos específicos para Firmamento Zephyr
        
        // Gerar Núcleo de Tempestade (objetivo principal)
        {
            FAURACRONProceduralObjective StormCore = GenerateNewProceduralObjective(EAURACRONObjectiveType::StormCore);
            StormCore.EnvironmentType = EnvironmentType;
            StormCore.ObjectiveCategory = EAURACRONObjectiveCategory::Environment;
            StormCore.StrategicValue = 0.9f; // Valor estratégico alto
            StormCore.RespawnTime = 300.0f; // 5 minutos para respawn
            
            // Posicionar no centro do mapa em uma posição elevada
            FVector CentralPosition;
            EAURACRONEnvironmentType TempEnv;
            CentralPosition = FindCentralPosition(TempEnv);
            CentralPosition.Z += 200.0f; // Posição elevada
            StormCore.WorldPosition = CentralPosition;
            
            ActiveProceduralObjectives.Add(StormCore);
            
            UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Núcleo de Tempestade gerado em {Position}",
                   *StormCore.WorldPosition.ToString());
        }
        
        // Gerar Santuários dos Ventos (objetivos secundários)
        for (int32 i = 0; i < 3; ++i)
        {
            FAURACRONProceduralObjective WindSanctuary = GenerateNewProceduralObjective(EAURACRONObjectiveType::WindSanctuary);
            WindSanctuary.EnvironmentType = EnvironmentType;
            WindSanctuary.ObjectiveCategory = EAURACRONObjectiveCategory::Environment;
            WindSanctuary.StrategicValue = 0.6f; // Valor estratégico médio
            WindSanctuary.RespawnTime = 180.0f; // 3 minutos para respawn
            
            // Posicionar em pontos estratégicos do mapa
            FVector SanctuaryPosition;
            EAURACRONEnvironmentType TempEnv;
            
            // Distribuir os santuários em diferentes áreas
            if (i == 0) {
                // Próximo à base da equipe 1
                SanctuaryPosition = FindPositionNearTeamBase(0, TempEnv);
                SanctuaryPosition.Z += 50.0f;
            } else if (i == 1) {
                // Próximo à base da equipe 2
                SanctuaryPosition = FindPositionNearTeamBase(1, TempEnv);
                SanctuaryPosition.Z += 50.0f;
            } else {
                // Em uma posição neutra
                SanctuaryPosition = FindCentralPosition(TempEnv);
                SanctuaryPosition += FVector(FMath::RandRange(-500.0f, 500.0f), 
                                           FMath::RandRange(-500.0f, 500.0f), 
                                           100.0f);
            }
            
            WindSanctuary.WorldPosition = SanctuaryPosition;
            ActiveProceduralObjectives.Add(WindSanctuary);
            
            UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Santuário dos Ventos {Index} gerado em {Position}",
                   i, *WindSanctuary.WorldPosition.ToString());
        }
        break;
        
    case EAURACRONEnvironmentType::PurgatoryRealm:
        // Gerar objetivos específicos para Purgatory Realm
        for (int32 i = 0; i < 2; ++i)
        {
            FAURACRONProceduralObjective NewObjective = GenerateNewProceduralObjective(EAURACRONObjectiveType::PurgatoryShrine);
            NewObjective.EnvironmentType = EnvironmentType;
            NewObjective.ObjectiveCategory = EAURACRONObjectiveCategory::Environment;
            ActiveProceduralObjectives.Add(NewObjective);
        }
        break;
        
    default:
        break;
    }
    
    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Objetivos específicos do ambiente {Environment} atualizados",
           static_cast<int32>(EnvironmentType));
}

void AAURACRONPCGObjectiveSystem::UpdateForMapPhase(EAURACRONMapPhase MapPhase)
{
    // Validações robustas
    if (!HasAuthority())
    {
        UE_LOGFMT(LogTemp, Warning, "AURACRONPCGObjectiveSystem: UpdateForMapPhase chamado em cliente - ignorando");
        return;
    }

    if (CurrentMapPhase == MapPhase)
    {
        UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Fase já é {Phase} - sem mudanças necessárias", static_cast<int32>(MapPhase));
        return;
    }

    EAURACRONMapPhase PreviousPhase = CurrentMapPhase;
    CurrentMapPhase = MapPhase;

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Mudando fase de {PrevPhase} para {NewPhase}",
        static_cast<int32>(PreviousPhase), static_cast<int32>(MapPhase));

    int32 ActivatedObjectives = 0;

    // Ativar objetivos baseado na fase com validações
    for (int32 i = 0; i < Objectives.Num(); ++i)
    {
        if (!Objectives.IsValidIndex(i))
        {
            continue;
        }

        FAURACRONObjectiveInfo& Objective = Objectives[i];
        bool bShouldBeActive = (MapPhase >= Objective.MinimumPhaseForActivation);

        if (bShouldBeActive && !Objective.bIsActiveInCurrentPhase)
        {
            Objective.bIsActiveInCurrentPhase = true;
            if (Objective.CurrentState == EAURACRONObjectiveState::Inactive)
            {
                Objective.CurrentState = EAURACRONObjectiveState::Available;
                Objective.CurrentHealth = Objective.MaxHealth;
                ActivatedObjectives++;

                UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Objetivo {Index} ativado na fase {Phase}",
                    i, static_cast<int32>(MapPhase));
            }
        }
    }

    // Gerar novos objetivos procedurais específicos da fase
    GeneratePhaseSpecificObjectives(MapPhase);

    // Aplicar efeitos da fase do mapa
    ApplyMapPhaseEffects();

    // Atualizar visibilidade dos objetivos
    UpdateObjectiveVisibility();

    // Atualizar integração com sistemas baseado na nova fase
    UpdateSystemsForMapPhase(MapPhase);

    // Notificar sobre a mudança de fase
    OnMapPhaseChanged(PreviousPhase, MapPhase);

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Atualizado para fase {Phase} - {ActivatedCount} objetivos ativados",
        static_cast<int32>(MapPhase), ActivatedObjectives);
}

void AAURACRONPCGObjectiveSystem::UpdateSystemsForMapPhase(EAURACRONMapPhase MapPhase)
{
    if (!HasAuthority())
    {
        return;
    }

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Atualizando sistemas para fase {Phase}", static_cast<int32>(MapPhase));

    // Atualizar poder dos trilhos baseado na fase
    float PhaseMultiplier = 1.0f;

    switch (MapPhase)
    {
    case EAURACRONMapPhase::Awakening:
        PhaseMultiplier = 0.8f; // 80% do poder na fase inicial
        break;
    case EAURACRONMapPhase::Convergence:
        PhaseMultiplier = 1.0f; // 100% do poder na fase média
        break;
    case EAURACRONMapPhase::Intensification:
        PhaseMultiplier = 1.3f; // 130% do poder na fase de intensificação
        break;
    case EAURACRONMapPhase::Resolution:
        PhaseMultiplier = 1.5f; // 150% do poder na fase final
        break;
    }

    // Aplicar multiplicador aos trilhos
    for (AAURACRONPCGTrail* Trail : SolarTrails)
    {
        if (IsValid(Trail))
        {
            Trail->SetPhaseMultiplier(PhaseMultiplier);
        }
    }

    for (AAURACRONPCGTrail* Trail : AxisTrails)
    {
        if (IsValid(Trail))
        {
            Trail->SetPhaseMultiplier(PhaseMultiplier);
        }
    }

    for (AAURACRONPCGTrail* Trail : LunarTrails)
    {
        if (IsValid(Trail))
        {
            Trail->SetPhaseMultiplier(PhaseMultiplier);
        }
    }

    // Atualizar Fluxo Prismal baseado na fase
    if (IsValid(PrismalFlowReference))
    {
        PrismalFlowReference->SetPhaseMultiplier(PhaseMultiplier);
    }

    // Atualizar Ilha Central baseado na fase
    if (IsValid(CentralAuracronIslandReference))
    {
        CentralAuracronIslandReference->SetPhaseMultiplier(PhaseMultiplier);
    }

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Multiplicador de fase {Multiplier} aplicado a todos os sistemas", PhaseMultiplier);
}

void AAURACRONPCGObjectiveSystem::TriggerChaosIslandEvent(int32 ChaosIslandIndex)
{
    // Validações robustas
    if (!HasAuthority())
    {
        UE_LOGFMT(LogTemp, Warning, "AURACRONPCGObjectiveSystem: TriggerChaosIslandEvent chamado em cliente - ignorando");
        return;
    }

    if (ChaosIslandIndex < 0)
    {
        UE_LOGFMT(LogTemp, Error, "AURACRONPCGObjectiveSystem: Índice de Chaos Island inválido {Index}", ChaosIslandIndex);
        return;
    }

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Ativando evento para Chaos Island {Index}", ChaosIslandIndex);

    // Buscar a ilha de caos correspondente com validações
    if (!PCGActorReferences.IslandActors.IsValidIndex(ChaosIslandIndex))
    {
        UE_LOGFMT(LogTemp, Warning, "AURACRONPCGObjectiveSystem: Chaos Island {Index} não encontrada na lista de ilhas", ChaosIslandIndex);
        return;
    }

    AAURACRONPCGIsland* ChaosIsland = PCGActorReferences.IslandActors[ChaosIslandIndex];
    if (!IsValid(ChaosIsland))
    {
        UE_LOGFMT(LogTemp, Error, "AURACRONPCGObjectiveSystem: Chaos Island {Index} é inválida", ChaosIslandIndex);
        return;
    }

    if (ChaosIsland->GetIslandType() != EAURACRONIslandType::Chaos)
    {
        UE_LOGFMT(LogTemp, Warning, "AURACRONPCGObjectiveSystem: Ilha {Index} não é do tipo Chaos", ChaosIslandIndex);
        return;
    }

    // Ativar a ilha
    ChaosIsland->OnIslandActivated();
    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Chaos Island {Index} ativada - {IslandName}",
        ChaosIslandIndex, *ChaosIsland->GetName());

    // Gerar objetivos procedurais na ilha com validações
    int32 ObjectivesGenerated = 0;
    for (int32 i = 0; i < 3; ++i) // Gerar 3 objetivos
    {
        FAURACRONProceduralObjective NewObjective = GenerateNewProceduralObjective(EAURACRONObjectiveType::ChaosRift);

        // Posicionar o objetivo na ilha
        FVector IslandLocation = ChaosIsland->GetActorLocation();
        float IslandRadius = ChaosIsland->GetIslandSize() / 2.0f;

        if (IslandRadius <= 0.0f)
        {
            UE_LOGFMT(LogTemp, Warning, "AURACRONPCGObjectiveSystem: Raio da ilha inválido {Radius} - usando valor padrão", IslandRadius);
            IslandRadius = 500.0f; // Valor padrão
        }

        // Distribuir objetivos em diferentes pontos da ilha
        float Angle = (float)i / 3.0f * 2.0f * PI;
        float Distance = IslandRadius * 0.7f; // 70% do raio da ilha

        FVector ObjectiveOffset;
        ObjectiveOffset.X = Distance * FMath::Cos(Angle);
        ObjectiveOffset.Y = Distance * FMath::Sin(Angle);
        ObjectiveOffset.Z = 100.0f; // Ligeiramente acima da superfície da ilha

        NewObjective.WorldPosition = IslandLocation + ObjectiveOffset;
        NewObjective.ChaosIslandIndex = ChaosIslandIndex; // Associar com a ilha

        // Adicionar o objetivo à lista de objetivos ativos
        ActiveProceduralObjectives.Add(NewObjective);
        ObjectivesGenerated++;

        UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Objetivo Chaos Rift {Index} gerado na posição {Position}",
            i, *NewObjective.WorldPosition.ToString());
    }

    // Disparar evento para notificar outros sistemas
    OnChaosIslandEvent.Broadcast(ChaosIslandIndex);

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Evento de Chaos Island {Index} ativado com {ObjectiveCount} objetivos gerados",
        ChaosIslandIndex, ObjectivesGenerated);
}

// ========================================
// INTEGRAÇÃO COM SISTEMAS DA DOCUMENTAÇÃO
// ========================================

void AAURACRONPCGObjectiveSystem::InitializeTrailsIntegration()
{
    if (!HasAuthority())
    {
        return;
    }

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Inicializando integração com Trilhos Solar/Axis/Lunar");

    // Configurar valores iniciais dos trilhos baseados na documentação
    SolarTrailPower = 0.5f; // 50% de poder inicial
    AxisTrailPower = 0.5f;  // 50% de poder inicial
    LunarTrailPower = 0.5f; // 50% de poder inicial

    // Buscar referências dos trilhos no mundo
    if (IsValid(GetWorld()))
    {
        for (TActorIterator<AAURACRONPCGTrail> ActorItr(GetWorld()); ActorItr; ++ActorItr)
        {
            AAURACRONPCGTrail* Trail = *ActorItr;
            if (IsValid(Trail))
            {
                // Registrar trilho baseado no tipo
                switch (Trail->GetTrailType())
                {
                case EAURACRONTrailType::Solar:
                    SolarTrails.AddUnique(Trail);
                    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Solar Trail registrado - {TrailName}", *Trail->GetName());
                    break;
                case EAURACRONTrailType::Axis:
                    AxisTrails.AddUnique(Trail);
                    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Axis Trail registrado - {TrailName}", *Trail->GetName());
                    break;
                case EAURACRONTrailType::Lunar:
                    LunarTrails.AddUnique(Trail);
                    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Lunar Trail registrado - {TrailName}", *Trail->GetName());
                    break;
                }
            }
        }
    }

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Trilhos integrados - Solar: {SolarCount}, Axis: {AxisCount}, Lunar: {LunarCount}",
        SolarTrails.Num(), AxisTrails.Num(), LunarTrails.Num());
}

void AAURACRONPCGObjectiveSystem::InitializePrismalFlowIntegration()
{
    if (!HasAuthority())
    {
        return;
    }

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Inicializando integração com Fluxo Prismal");

    // Configurar intensidade inicial do Fluxo Prismal
    PrismalFlowIntensity = 1.0f; // Intensidade padrão

    // Buscar referência do Fluxo Prismal no mundo
    if (IsValid(GetWorld()))
    {
        for (TActorIterator<AAURACRONPCGPrismalFlow> ActorItr(GetWorld()); ActorItr; ++ActorItr)
        {
            AAURACRONPCGPrismalFlow* PrismalFlow = *ActorItr;
            if (IsValid(PrismalFlow))
            {
                PrismalFlowReference = PrismalFlow;
                UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Fluxo Prismal registrado - {FlowName}", *PrismalFlow->GetName());
                break; // Apenas um Fluxo Prismal por mapa
            }
        }
    }

    if (!IsValid(PrismalFlowReference))
    {
        UE_LOGFMT(LogTemp, Warning, "AURACRONPCGObjectiveSystem: Fluxo Prismal não encontrado no mundo");
    }
}

void AAURACRONPCGObjectiveSystem::InitializeCentralAuracronIslandIntegration()
{
    if (!HasAuthority())
    {
        return;
    }

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Inicializando integração com Ilha Central Auracron");

    // Configurar controle inicial da ilha
    CentralAuracronIslandControlPercentage = 0.0f; // Neutro inicialmente

    // Buscar referência da Ilha Central Auracron no mundo
    if (IsValid(GetWorld()))
    {
        for (TActorIterator<AAURACRONPCGIsland> ActorItr(GetWorld()); ActorItr; ++ActorItr)
        {
            AAURACRONPCGIsland* Island = *ActorItr;
            if (IsValid(Island) && Island->GetIslandType() == EAURACRONIslandType::Nexus)
            {
                CentralAuracronIslandReference = Island;
                UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Ilha Central Auracron registrada - {IslandName}", *Island->GetName());

                // Inicializar setores da ilha baseados na documentação
                InitializeIslandSectors(Island);
                break; // Apenas uma Ilha Central por mapa
            }
        }
    }

    if (!IsValid(CentralAuracronIslandReference))
    {
        UE_LOGFMT(LogTemp, Warning, "AURACRONPCGObjectiveSystem: Ilha Central Auracron não encontrada no mundo");
    }
}

void AAURACRONPCGObjectiveSystem::InitializeIslandSectors(AAURACRONPCGIsland* Island)
{
    if (!IsValid(Island))
    {
        UE_LOGFMT(LogTemp, Error, "AURACRONPCGObjectiveSystem: Ilha inválida para inicialização de setores");
        return;
    }

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Inicializando setores da Ilha Central Auracron");

    // Inicializar setores baseados na documentação:
    // - Setor Nexus: Geradores de recursos e manipulação do Fluxo
    // - Setor Santuário: Fontes de cura e amplificadores de visão
    // - Setor Arsenal: Upgrades de armas e potencializadores de habilidades
    // - Setor Caos: Perigos ambientais com recompensas de alto risco

    IslandSectorControl.Empty();
    IslandSectorControl.Add(EAURACRONIslandSector::Nexus, -1);      // -1 = neutro
    IslandSectorControl.Add(EAURACRONIslandSector::Sanctuary, -1);  // -1 = neutro
    IslandSectorControl.Add(EAURACRONIslandSector::Arsenal, -1);    // -1 = neutro
    IslandSectorControl.Add(EAURACRONIslandSector::Chaos, -1);      // -1 = neutro

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: {SectorCount} setores inicializados na Ilha Central", IslandSectorControl.Num());
}

void AAURACRONPCGObjectiveSystem::UpdateTrailsIntegration()
{
    if (!HasAuthority() || !bIsInitialized)
    {
        return;
    }

    // Atualizar poder dos trilhos baseado no controle de objetivos
    float NewSolarPower = CalculateTrailPowerFromObjectives(EAURACRONTrailType::Solar);
    float NewAxisPower = CalculateTrailPowerFromObjectives(EAURACRONTrailType::Axis);
    float NewLunarPower = CalculateTrailPowerFromObjectives(EAURACRONTrailType::Lunar);

    // Aplicar mudanças se houver diferença significativa
    if (FMath::Abs(SolarTrailPower - NewSolarPower) > 0.01f)
    {
        SolarTrailPower = NewSolarPower;
        ApplyTrailPowerToActors(SolarTrails, SolarTrailPower);
        UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Solar Trail power atualizado para {Power}", SolarTrailPower);
    }

    if (FMath::Abs(AxisTrailPower - NewAxisPower) > 0.01f)
    {
        AxisTrailPower = NewAxisPower;
        ApplyTrailPowerToActors(AxisTrails, AxisTrailPower);
        UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Axis Trail power atualizado para {Power}", AxisTrailPower);
    }

    if (FMath::Abs(LunarTrailPower - NewLunarPower) > 0.01f)
    {
        LunarTrailPower = NewLunarPower;
        ApplyTrailPowerToActors(LunarTrails, LunarTrailPower);
        UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Lunar Trail power atualizado para {Power}", LunarTrailPower);
    }
}

void AAURACRONPCGObjectiveSystem::UpdatePrismalFlowIntegration()
{
    if (!HasAuthority() || !bIsInitialized || !IsValid(PrismalFlowReference))
    {
        return;
    }

    // Calcular nova intensidade do Fluxo Prismal baseado no controle de objetivos
    float NewIntensity = CalculatePrismalFlowIntensityFromObjectives();

    if (FMath::Abs(PrismalFlowIntensity - NewIntensity) > 0.01f)
    {
        PrismalFlowIntensity = NewIntensity;

        // Aplicar nova intensidade ao Fluxo Prismal
        PrismalFlowReference->SetFlowIntensity(PrismalFlowIntensity);

        UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Fluxo Prismal intensidade atualizada para {Intensity}", PrismalFlowIntensity);
    }
}

void AAURACRONPCGObjectiveSystem::UpdateCentralAuracronIslandIntegration()
{
    if (!HasAuthority() || !bIsInitialized || !IsValid(CentralAuracronIslandReference))
    {
        return;
    }

    // Calcular controle da ilha baseado nos setores
    float NewControlPercentage = CalculateIslandControlFromSectors();

    if (FMath::Abs(CentralAuracronIslandControlPercentage - NewControlPercentage) > 0.01f)
    {
        CentralAuracronIslandControlPercentage = NewControlPercentage;

        // Aplicar efeitos baseados no controle da ilha
        ApplyIslandControlEffects(CentralAuracronIslandControlPercentage);

        UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Controle da Ilha Central atualizado para {Control}%", CentralAuracronIslandControlPercentage * 100.0f);
    }
}

void AAURACRONPCGObjectiveSystem::ApplyEnvironmentTrailsIntegration(int32 ObjectiveIndex, EAURACRONEnvironmentType Environment)
{
    if (!Objectives.IsValidIndex(ObjectiveIndex))
    {
        return;
    }

    FAURACRONObjectiveInfo& Objective = Objectives[ObjectiveIndex];

    // Aplicar modificações específicas do ambiente nos trilhos
    switch (Environment)
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        // Solar Trilhos mais poderosos nas Planícies Radiantes
        Objective.TrailAffinityBonus.Add(EAURACRONTrailType::Solar, 1.5f);
        Objective.TrailAffinityBonus.Add(EAURACRONTrailType::Axis, 1.0f);
        Objective.TrailAffinityBonus.Add(EAURACRONTrailType::Lunar, 0.8f);
        break;

    case EAURACRONEnvironmentType::ZephyrFirmament:
        // Axis Trilhos mais poderosos no Firmamento Zephyr
        Objective.TrailAffinityBonus.Add(EAURACRONTrailType::Solar, 0.8f);
        Objective.TrailAffinityBonus.Add(EAURACRONTrailType::Axis, 1.5f);
        Objective.TrailAffinityBonus.Add(EAURACRONTrailType::Lunar, 1.2f);
        break;

    case EAURACRONEnvironmentType::PurgatoryRealm:
        // Lunar Trilhos mais poderosos no Reino Purgatório
        Objective.TrailAffinityBonus.Add(EAURACRONTrailType::Solar, 0.7f);
        Objective.TrailAffinityBonus.Add(EAURACRONTrailType::Axis, 0.9f);
        Objective.TrailAffinityBonus.Add(EAURACRONTrailType::Lunar, 1.8f);
        break;
    }

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Integração de trilhos aplicada ao objetivo {Index} no ambiente {Environment}",
        ObjectiveIndex, static_cast<int32>(Environment));
}

void AAURACRONPCGObjectiveSystem::ApplyEnvironmentPrismalFlowIntegration(int32 ObjectiveIndex, EAURACRONEnvironmentType Environment)
{
    if (!Objectives.IsValidIndex(ObjectiveIndex))
    {
        return;
    }

    FAURACRONObjectiveInfo& Objective = Objectives[ObjectiveIndex];

    // Aplicar modificações específicas do ambiente no Fluxo Prismal
    switch (Environment)
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        Objective.PrismalFlowAffinityMultiplier = 1.2f; // 20% mais efetivo
        break;

    case EAURACRONEnvironmentType::ZephyrFirmament:
        Objective.PrismalFlowAffinityMultiplier = 1.5f; // 50% mais efetivo (ambiente aéreo)
        break;

    case EAURACRONEnvironmentType::PurgatoryRealm:
        Objective.PrismalFlowAffinityMultiplier = 0.8f; // 20% menos efetivo (ambiente espectral)
        break;
    }

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Integração de Fluxo Prismal aplicada ao objetivo {Index} com multiplicador {Multiplier}",
        ObjectiveIndex, Objective.PrismalFlowAffinityMultiplier);
}

// ========================================
// FUNÇÕES AUXILIARES DE CÁLCULO
// ========================================

float AAURACRONPCGObjectiveSystem::CalculateTrailPowerFromObjectives(EAURACRONTrailType TrailType)
{
    if (!HasAuthority())
    {
        return 0.5f; // Valor padrão
    }

    float TotalPower = 0.5f; // Poder base
    int32 ControlledObjectives = 0;

    // Calcular poder baseado nos objetivos controlados
    for (const FAURACRONObjectiveInfo& Objective : Objectives)
    {
        if (Objective.CurrentState == EAURACRONObjectiveState::Captured &&
            Objective.ControllingTeam >= 0)
        {
            // Verificar afinidade do objetivo com o tipo de trilho
            if (Objective.TrailAffinityBonus.Contains(TrailType))
            {
                TotalPower += Objective.TrailAffinityBonus[TrailType] * 0.1f; // 10% por objetivo
                ControlledObjectives++;
            }
        }
    }

    // Aplicar bônus baseado na fase do mapa
    switch (CurrentMapPhase)
    {
    case EAURACRONMapPhase::Awakening:
        TotalPower *= 0.8f; // 80% do poder na fase inicial
        break;
    case EAURACRONMapPhase::Convergence:
        TotalPower *= 1.0f; // 100% do poder na fase média
        break;
    case EAURACRONMapPhase::Intensification:
        TotalPower *= 1.3f; // 130% do poder na fase de intensificação
        break;
    case EAURACRONMapPhase::Resolution:
        TotalPower *= 1.5f; // 150% do poder na fase final
        break;
    }

    // Limitar poder entre 0.1 e 2.0
    TotalPower = FMath::Clamp(TotalPower, 0.1f, 2.0f);

    return TotalPower;
}

float AAURACRONPCGObjectiveSystem::CalculatePrismalFlowIntensityFromObjectives()
{
    if (!HasAuthority())
    {
        return 1.0f; // Valor padrão
    }

    float TotalIntensity = 1.0f; // Intensidade base
    int32 ControlledObjectives = 0;

    // Calcular intensidade baseada nos objetivos controlados
    for (const FAURACRONObjectiveInfo& Objective : Objectives)
    {
        if (Objective.CurrentState == EAURACRONObjectiveState::Captured &&
            Objective.ControllingTeam >= 0)
        {
            TotalIntensity += Objective.PrismalFlowAffinityMultiplier * 0.15f; // 15% por objetivo
            ControlledObjectives++;
        }
    }

    // Bônus especial para controle da Ilha Central
    if (CentralAuracronIslandControlPercentage > 0.5f)
    {
        TotalIntensity *= (1.0f + CentralAuracronIslandControlPercentage * 0.5f); // Até 50% de bônus
    }

    // Aplicar modificador baseado no ambiente atual
    switch (CurrentEnvironment)
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        TotalIntensity *= 1.0f; // Intensidade normal
        break;
    case EAURACRONEnvironmentType::ZephyrFirmament:
        TotalIntensity *= 1.2f; // 20% mais intenso no ar
        break;
    case EAURACRONEnvironmentType::PurgatoryRealm:
        TotalIntensity *= 0.9f; // 10% menos intenso no reino espectral
        break;
    }

    // Limitar intensidade entre 0.5 e 3.0
    TotalIntensity = FMath::Clamp(TotalIntensity, 0.5f, 3.0f);

    return TotalIntensity;
}

float AAURACRONPCGObjectiveSystem::CalculateIslandControlFromSectors()
{
    if (!HasAuthority() || IslandSectorControl.Num() == 0)
    {
        return 0.0f;
    }

    int32 ControlledSectors = 0;
    int32 TotalSectors = IslandSectorControl.Num();

    // Contar setores controlados (não neutros)
    for (const auto& SectorPair : IslandSectorControl)
    {
        if (SectorPair.Value >= 0) // Controlado por alguma equipe
        {
            ControlledSectors++;
        }
    }

    // Calcular porcentagem de controle
    float ControlPercentage = static_cast<float>(ControlledSectors) / static_cast<float>(TotalSectors);

    return ControlPercentage;
}

void AAURACRONPCGObjectiveSystem::ApplyTrailPowerToActors(const TArray<AAURACRONPCGTrail*>& Trails, float Power)
{
    for (AAURACRONPCGTrail* Trail : Trails)
    {
        if (IsValid(Trail))
        {
            Trail->SetTrailPower(Power);
        }
    }
}

void AAURACRONPCGObjectiveSystem::ApplyIslandControlEffects(float ControlPercentage)
{
    if (!IsValid(CentralAuracronIslandReference))
    {
        return;
    }

    // Aplicar efeitos baseados no controle da ilha
    if (ControlPercentage >= 0.75f) // 75% ou mais de controle
    {
        // Ativar todos os benefícios da ilha
        CentralAuracronIslandReference->SetIslandControlLevel(EAURACRONIslandControlLevel::Full);
        UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Ilha Central com controle total - todos os benefícios ativos");
    }
    else if (ControlPercentage >= 0.5f) // 50% ou mais de controle
    {
        // Ativar benefícios parciais
        CentralAuracronIslandReference->SetIslandControlLevel(EAURACRONIslandControlLevel::Partial);
        UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Ilha Central com controle parcial - benefícios limitados");
    }
    else
    {
        // Sem benefícios especiais
        CentralAuracronIslandReference->SetIslandControlLevel(EAURACRONIslandControlLevel::None);
        UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Ilha Central sem controle - sem benefícios especiais");
    }
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES INTERNAS
// ========================================

void AAURACRONPCGObjectiveSystem::InitializeObjectiveInfos()
{
    Objectives.Empty();

    // OBJETIVO PRINCIPAL - Prismal Nexus (Baron equivalent)
    FAURACRONObjectiveInfo PrismalNexus = GetDefaultObjectiveConfig(EAURACRONObjectiveType::PrismalNexus);
    Objectives.Add(PrismalNexus);

    // OBJETIVOS SECUNDÁRIOS - Elemental Anchors (Dragon equivalents)
    FAURACRONObjectiveInfo RadiantAnchor = GetDefaultObjectiveConfig(EAURACRONObjectiveType::RadiantAnchor);
    Objectives.Add(RadiantAnchor);

    FAURACRONObjectiveInfo ZephyrAnchor = GetDefaultObjectiveConfig(EAURACRONObjectiveType::ZephyrAnchor);
    Objectives.Add(ZephyrAnchor);

    FAURACRONObjectiveInfo PurgatoryAnchor = GetDefaultObjectiveConfig(EAURACRONObjectiveType::PurgatoryAnchor);
    Objectives.Add(PurgatoryAnchor);

    // OBJETIVOS ÚNICOS DO AURACRON

    // Storm Core (novo objetivo)
    FAURACRONObjectiveInfo StormCore = GetDefaultObjectiveConfig(EAURACRONObjectiveType::StormCore);
    Objectives.Add(StormCore);

    // Nexus Island (centro do mapa)
    FAURACRONObjectiveInfo NexusIsland = GetDefaultObjectiveConfig(EAURACRONObjectiveType::NexusIsland);
    Objectives.Add(NexusIsland);

    // Sanctuary Islands (4 posições simétricas)
    for (int32 i = 0; i < 4; ++i)
    {
        FAURACRONObjectiveInfo SanctuaryIsland = GetDefaultObjectiveConfig(EAURACRONObjectiveType::SanctuaryIsland);
        Objectives.Add(SanctuaryIsland);
    }

    // Arsenal Islands (2 posições)
    for (int32 i = 0; i < 2; ++i)
    {
        FAURACRONObjectiveInfo ArsenalIsland = GetDefaultObjectiveConfig(EAURACRONObjectiveType::ArsenalIsland);
        Objectives.Add(ArsenalIsland);
    }

    // Chaos Islands (3 posições rotativas)
    for (int32 i = 0; i < 3; ++i)
    {
        FAURACRONObjectiveInfo ChaosIsland = GetDefaultObjectiveConfig(EAURACRONObjectiveType::ChaosIsland);
        Objectives.Add(ChaosIsland);
    }

    // Calcular posições para todos os ambientes
    for (int32 i = 0; i < Objectives.Num(); ++i)
    {
        for (int32 EnvIndex = 0; EnvIndex < 3; ++EnvIndex)
        {
            EAURACRONEnvironmentType Environment = static_cast<EAURACRONEnvironmentType>(EnvIndex);
            FVector Position = CalculateObjectivePosition(Objectives[i].ObjectiveType, Environment);
            Objectives[i].PositionsByEnvironment.Add(Environment, Position);
        }
    }
}

FAURACRONObjectiveInfo AAURACRONPCGObjectiveSystem::GetDefaultObjectiveConfig(EAURACRONObjectiveType ObjectiveType)
{
    FAURACRONObjectiveInfo Config;
    Config.ObjectiveType = ObjectiveType;

    switch (ObjectiveType)
    {
    case EAURACRONObjectiveType::PrismalNexus:
        Config.ObjectiveRadius = FAURACRONMapDimensions::PRISMAL_NEXUS_PIT_RADIUS_CM;
        Config.PitDepth = FAURACRONMapDimensions::PRISMAL_NEXUS_PIT_DEPTH_CM;
        Config.RespawnTime = FAURACRONMapDimensions::PRISMAL_NEXUS_RESPAWN_TIME;
        Config.MaxHealth = 15000.0f;
        Config.CurrentHealth = 15000.0f;
        Config.BuffDuration = 180.0f; // 3 minutos (como Baron)
        Config.MinimumPhaseForActivation = EAURACRONMapPhase::Convergence;
        Config.ObjectiveBuffs.Add(TEXT("PrismalEmpowerment"), 40.0f);
        Config.ObjectiveBuffs.Add(TEXT("AttackDamage"), 50.0f);
        Config.ObjectiveBuffs.Add(TEXT("AbilityPower"), 50.0f);
        Config.ObjectiveBuffs.Add(TEXT("MinionEmpowerment"), 100.0f);
        break;

    case EAURACRONObjectiveType::RadiantAnchor:
        Config.ObjectiveRadius = FAURACRONMapDimensions::ELEMENTAL_ANCHOR_PIT_RADIUS_CM;
        Config.PitDepth = FAURACRONMapDimensions::ELEMENTAL_ANCHOR_PIT_DEPTH_CM;
        Config.RespawnTime = FAURACRONMapDimensions::ELEMENTAL_ANCHOR_RESPAWN_TIME;
        Config.MaxHealth = 8000.0f;
        Config.CurrentHealth = 8000.0f;
        Config.BuffDuration = 150.0f; // 2.5 minutos
        Config.MinimumPhaseForActivation = EAURACRONMapPhase::Convergence;
        Config.ObjectiveBuffs.Add(TEXT("FireResistance"), 25.0f);
        Config.ObjectiveBuffs.Add(TEXT("AttackDamage"), 20.0f);
        Config.ObjectiveBuffs.Add(TEXT("HealthRegen"), 15.0f);
        break;

    case EAURACRONObjectiveType::ZephyrAnchor:
        Config.ObjectiveRadius = FAURACRONMapDimensions::ELEMENTAL_ANCHOR_PIT_RADIUS_CM;
        Config.PitDepth = FAURACRONMapDimensions::ELEMENTAL_ANCHOR_PIT_DEPTH_CM;
        Config.RespawnTime = FAURACRONMapDimensions::ELEMENTAL_ANCHOR_RESPAWN_TIME;
        Config.MaxHealth = 8000.0f;
        Config.CurrentHealth = 8000.0f;
        Config.BuffDuration = 150.0f;
        Config.MinimumPhaseForActivation = EAURACRONMapPhase::Convergence;
        Config.ObjectiveBuffs.Add(TEXT("MovementSpeed"), 30.0f);
        Config.ObjectiveBuffs.Add(TEXT("AttackSpeed"), 25.0f);
        Config.ObjectiveBuffs.Add(TEXT("CooldownReduction"), 15.0f);
        break;

    case EAURACRONObjectiveType::PurgatoryAnchor:
        Config.ObjectiveRadius = FAURACRONMapDimensions::ELEMENTAL_ANCHOR_PIT_RADIUS_CM;
        Config.PitDepth = FAURACRONMapDimensions::ELEMENTAL_ANCHOR_PIT_DEPTH_CM;
        Config.RespawnTime = FAURACRONMapDimensions::ELEMENTAL_ANCHOR_RESPAWN_TIME;
        Config.MaxHealth = 8000.0f;
        Config.CurrentHealth = 8000.0f;
        Config.BuffDuration = 150.0f;
        Config.MinimumPhaseForActivation = EAURACRONMapPhase::Convergence;
        Config.ObjectiveBuffs.Add(TEXT("AbilityPower"), 30.0f);
        Config.ObjectiveBuffs.Add(TEXT("ManaRegen"), 20.0f);
        Config.ObjectiveBuffs.Add(TEXT("SpellVamp"), 15.0f);
        break;
        
    case EAURACRONObjectiveType::SpectralGuardian:
        Config.ObjectiveRadius = 800.0f; // Maior que os outros objetivos
        Config.PitDepth = 0.0f; // Não fica em um buraco
        Config.RespawnTime = 420.0f; // 7 minutos
        Config.MaxHealth = 12000.0f; // Mais resistente que outros objetivos
        Config.CurrentHealth = 12000.0f;
        Config.BuffDuration = 180.0f; // 3 minutos
        Config.MinimumPhaseForActivation = EAURACRONMapPhase::Intensification; // Disponível apenas na fase de intensificação
        Config.ObjectiveBuffs.Add(TEXT("SpectralVision"), 1.0f); // Visão espectral (ver através de paredes)
        Config.ObjectiveBuffs.Add(TEXT("SpectralDamage"), 15.0f); // Dano espectral adicional
        Config.ObjectiveBuffs.Add(TEXT("SpectralResistance"), 20.0f); // Resistência a dano espectral
        Config.ObjectiveBuffs.Add(TEXT("UltimateHaste"), 10.0f); // Redução de cooldown da ultimate
        break;

    case EAURACRONObjectiveType::NexusIsland:
        Config.ObjectiveRadius = FAURACRONMapDimensions::NEXUS_ISLAND_RADIUS_CM;
        Config.PitDepth = 0.0f; // Ilha elevada
        Config.RespawnTime = 0.0f; // Permanente
        Config.MaxHealth = 0.0f; // Não atacável
        Config.CurrentHealth = 0.0f;
        Config.BuffDuration = 60.0f; // 1 minuto
        Config.MinimumPhaseForActivation = EAURACRONMapPhase::Awakening;
        Config.CurrentState = EAURACRONObjectiveState::Available;
        Config.ObjectiveBuffs.Add(TEXT("VisionRange"), 50.0f);
        Config.ObjectiveBuffs.Add(TEXT("ExperienceBonus"), 25.0f);
        break;

    case EAURACRONObjectiveType::SanctuaryIsland:
        Config.ObjectiveRadius = FAURACRONMapDimensions::SANCTUARY_ISLAND_RADIUS_CM;
        Config.PitDepth = 0.0f;
        Config.RespawnTime = 180.0f; // 3 minutos
        Config.MaxHealth = 0.0f; // Não atacável
        Config.CurrentHealth = 0.0f;
        Config.BuffDuration = 120.0f; // 2 minutos
        Config.MinimumPhaseForActivation = EAURACRONMapPhase::Convergence;
        Config.CurrentState = EAURACRONObjectiveState::Available;
        Config.ObjectiveBuffs.Add(TEXT("HealthRegen"), 30.0f);
        Config.ObjectiveBuffs.Add(TEXT("ManaRegen"), 30.0f);
        break;

    case EAURACRONObjectiveType::ArsenalIsland:
        Config.ObjectiveRadius = FAURACRONMapDimensions::ARSENAL_ISLAND_RADIUS_CM;
        Config.PitDepth = 0.0f;
        Config.RespawnTime = 300.0f; // 5 minutos
        Config.MaxHealth = 0.0f; // Não atacável
        Config.CurrentHealth = 0.0f;
        Config.BuffDuration = 0.0f; // Permanente até morte
        Config.MinimumPhaseForActivation = EAURACRONMapPhase::Intensification;
        Config.CurrentState = EAURACRONObjectiveState::Available;
        Config.ObjectiveBuffs.Add(TEXT("ItemUpgrade"), 1.0f);
        Config.ObjectiveBuffs.Add(TEXT("GoldBonus"), 500.0f);
        break;

    case EAURACRONObjectiveType::ChaosIsland:
        Config.ObjectiveRadius = FAURACRONMapDimensions::CHAOS_ISLAND_RADIUS_CM;
        Config.PitDepth = 0.0f;
        Config.RespawnTime = FAURACRONMapDimensions::CHAOS_ISLAND_ROTATION_TIME;
        Config.MaxHealth = 0.0f; // Não atacável
        Config.CurrentHealth = 0.0f;
        Config.BuffDuration = 90.0f; // 1.5 minutos
        Config.MinimumPhaseForActivation = EAURACRONMapPhase::Intensification;
        Config.CurrentState = EAURACRONObjectiveState::Inactive; // Ativa por rotação
        Config.ObjectiveBuffs.Add(TEXT("RandomEvent"), 1.0f);
        break;
        
    case EAURACRONObjectiveType::StormCore:
        Config.ObjectiveRadius = 750.0f;
        Config.PitDepth = 300.0f;
        Config.RespawnTime = 360.0f; // 6 minutos
        Config.MaxHealth = 10000.0f;
        Config.CurrentHealth = 10000.0f;
        Config.BuffDuration = 150.0f; // 2.5 minutos
        Config.MinimumPhaseForActivation = EAURACRONMapPhase::Intensification;
        Config.ObjectiveBuffs.Add(TEXT("StormEmpowerment"), 1.0f);
        Config.ObjectiveBuffs.Add(TEXT("AttackDamage"), 15.0f);
        Config.ObjectiveBuffs.Add(TEXT("MovementSpeed"), 10.0f);
        Config.ObjectiveBuffs.Add(TEXT("CrowdControlResist"), 25.0f);
        break;
    }

    return Config;
}

FVector AAURACRONPCGObjectiveSystem::CalculateObjectivePosition(EAURACRONObjectiveType ObjectiveType, EAURACRONEnvironmentType Environment)
{
    FVector BasePosition = FAURACRONMapDimensions::MAP_CENTER;
    float EnvironmentHeight = 0.0f;

    // Obter altura do ambiente
    switch (Environment)
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        EnvironmentHeight = FAURACRONMapDimensions::RADIANT_PLAINS_HEIGHT_CM;
        break;
    case EAURACRONEnvironmentType::ZephyrFirmament:
        EnvironmentHeight = FAURACRONMapDimensions::ZEPHYR_FIRMAMENT_HEIGHT_CM;
        break;
    case EAURACRONEnvironmentType::PurgatoryRealm:
        EnvironmentHeight = FAURACRONMapDimensions::PURGATORY_REALM_HEIGHT_CM;
        break;
    }

    FVector Position = BasePosition;
    Position.Z = EnvironmentHeight;

    // Calcular posição baseada no tipo de objetivo
    switch (ObjectiveType)
    {
    case EAURACRONObjectiveType::PrismalNexus:
        // Baron equivalent - lado superior do mapa
        Position += FVector(FAURACRONMapDimensions::PRISMAL_NEXUS_X, FAURACRONMapDimensions::PRISMAL_NEXUS_Y, 0.0f);
        break;

    case EAURACRONObjectiveType::RadiantAnchor:
        // Fire/Earth Dragon - inferior esquerdo
        Position += FVector(FAURACRONMapDimensions::RADIANT_ANCHOR_X, FAURACRONMapDimensions::RADIANT_ANCHOR_Y, 0.0f);
        break;

    case EAURACRONObjectiveType::ZephyrAnchor:
        // Air/Lightning Dragon - direita
        Position += FVector(FAURACRONMapDimensions::ZEPHYR_ANCHOR_X, FAURACRONMapDimensions::ZEPHYR_ANCHOR_Y, 0.0f);
        break;

    case EAURACRONObjectiveType::PurgatoryAnchor:
        // Shadow/Spectral Dragon - inferior direito
        Position += FVector(FAURACRONMapDimensions::PURGATORY_ANCHOR_X, FAURACRONMapDimensions::PURGATORY_ANCHOR_Y, 0.0f);
        break;

    case EAURACRONObjectiveType::NexusIsland:
        // Centro do mapa
        Position += FVector(FAURACRONMapDimensions::NEXUS_ISLAND_X, FAURACRONMapDimensions::NEXUS_ISLAND_Y, 0.0f);
        break;

    case EAURACRONObjectiveType::SanctuaryIsland:
        {
            // 4 posições simétricas ao redor do centro
            static int32 SanctuaryCounter = 0;
            float Angle = (SanctuaryCounter % 4) * 90.0f * PI / 180.0f; // 0°, 90°, 180°, 270°
            float Distance = FAURACRONMapDimensions::SANCTUARY_ISLAND_DISTANCE_CM;

            Position += FVector(
                FMath::Cos(Angle) * Distance,
                FMath::Sin(Angle) * Distance,
                0.0f
            );

            SanctuaryCounter++;
        }
        break;

    case EAURACRONObjectiveType::ArsenalIsland:
        {
            // 2 posições específicas
            static int32 ArsenalCounter = 0;
            if (ArsenalCounter % 2 == 0)
            {
                Position += FVector(FAURACRONMapDimensions::ARSENAL_ISLAND_1_X, FAURACRONMapDimensions::ARSENAL_ISLAND_1_Y, 0.0f);
            }
            else
            {
                Position += FVector(FAURACRONMapDimensions::ARSENAL_ISLAND_2_X, FAURACRONMapDimensions::ARSENAL_ISLAND_2_Y, 0.0f);
            }
            ArsenalCounter++;
        }
        break;

    case EAURACRONObjectiveType::ChaosIsland:
        {
            // 3 posições rotativas
            static int32 ChaosCounter = 0;
            float Angle = (ChaosCounter % 3) * 120.0f * PI / 180.0f; // 0°, 120°, 240°
            float Distance = 4000.0f; // 40 metros do centro

            Position += FVector(
                FMath::Cos(Angle) * Distance,
                FMath::Sin(Angle) * Distance,
                0.0f
            );

            ChaosCounter++;
        }
        break;
        
    case EAURACRONObjectiveType::StormCore:
        {
            // Posição entre o Prismal Nexus e o Nexus Island
            FVector PrismalPos = BasePosition + FVector(FAURACRONMapDimensions::PRISMAL_NEXUS_X, FAURACRONMapDimensions::PRISMAL_NEXUS_Y, 0.0f);
            FVector NexusIslandPos = BasePosition + FVector(FAURACRONMapDimensions::NEXUS_ISLAND_X, FAURACRONMapDimensions::NEXUS_ISLAND_Y, 0.0f);
            
            // Posição a 70% do caminho do Nexus Island para o Prismal Nexus
            Position = FMath::Lerp(NexusIslandPos, PrismalPos, 0.7f);
            Position.Z = EnvironmentHeight;
        }
        break;
    }

    // Aplicar variações específicas do ambiente
    if (Environment == EAURACRONEnvironmentType::ZephyrFirmament)
    {
        // Elevar ilhas no ambiente aéreo
        if (ObjectiveType == EAURACRONObjectiveType::NexusIsland ||
            ObjectiveType == EAURACRONObjectiveType::SanctuaryIsland ||
            ObjectiveType == EAURACRONObjectiveType::ArsenalIsland)
        {
            Position.Z += 300.0f; // +3m de elevação extra
        }
    }
    else if (Environment == EAURACRONEnvironmentType::PurgatoryRealm)
    {
        // Aprofundar pits no ambiente espectral
        if (ObjectiveType == EAURACRONObjectiveType::PrismalNexus ||
            ObjectiveType == EAURACRONObjectiveType::RadiantAnchor ||
            ObjectiveType == EAURACRONObjectiveType::ZephyrAnchor ||
            ObjectiveType == EAURACRONObjectiveType::PurgatoryAnchor)
        {
            Position.Z -= 200.0f; // -2m mais profundo
        }
    }

    return Position;
}

void AAURACRONPCGObjectiveSystem::CreateObjective(int32 ObjectiveIndex, EAURACRONEnvironmentType Environment)
{
    if (ObjectiveIndex < 0 || ObjectiveIndex >= Objectives.Num())
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGObjectiveSystem::CreateObjective - Índice inválido: {Index}", ObjectiveIndex);
        return;
    }
    
    FAURACRONObjectiveInfo& Objective = Objectives[ObjectiveIndex];
    
    // Verificar se o objetivo já foi criado
    if (Objective.CurrentState != EAURACRONObjectiveState::Inactive)
    {
        UE_LOGFMT(LogTemp, Log, "AAURACRONPCGObjectiveSystem::CreateObjective - Objetivo {Index} já está ativo", ObjectiveIndex);
        return;
    }
    
    // Obter mesh apropriado para o tipo de objetivo e ambiente
    UStaticMesh* ObjectiveMesh = GetObjectiveMesh(Objective.ObjectiveType, Environment);
    
    if (!ObjectiveMesh)
    {
        UE_LOGFMT(LogTemp, Error, "AAURACRONPCGObjectiveSystem::CreateObjective - Mesh não encontrado para objetivo tipo {Type}", (int32)Objective.ObjectiveType);
        return;
    }
    
    // Criar componente de mesh para o objetivo
    UStaticMeshComponent* MeshComponent = NewObject<UStaticMeshComponent>(this, UStaticMeshComponent::StaticClass(), *FString::Printf(TEXT("ObjectiveMesh_%d"), ObjectiveIndex));
    if (!MeshComponent)
    {
        UE_LOGFMT(LogTemp, Error, "AAURACRONPCGObjectiveSystem::CreateObjective - Falha ao criar MeshComponent");
        return;
    }
    
    MeshComponent->SetStaticMesh(ObjectiveMesh);
    MeshComponent->SetWorldLocation(Objective.WorldPosition);
    MeshComponent->SetCollisionProfileName(TEXT("Objective"));
    MeshComponent->SetVisibility(true);
    MeshComponent->SetCastShadow(true);
    MeshComponent->SetReceivesDecals(false);
    
    // Configurar propriedades de renderização
    MeshComponent->SetRenderCustomDepth(true);
    MeshComponent->SetCustomDepthStencilValue(GetStencilValueForObjectiveType(Objective.ObjectiveType));
    
    // Registrar componente
    MeshComponent->RegisterComponent();
    MeshComponent->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepWorldTransform);
    
    // Armazenar referência do componente
    Objective.MeshComponent = MeshComponent;
    
    // Adicionar efeitos visuais baseados no tipo de objetivo
    switch (Objective.ObjectiveType)
    {
        case EAURACRONObjectiveType::PrismalNexus:
            AddPrismalNexusEffects(ObjectiveIndex, MeshComponent);
            break;
            
        case EAURACRONObjectiveType::RadiantAnchor:
        case EAURACRONObjectiveType::ZephyrAnchor:
        case EAURACRONObjectiveType::PurgatoryAnchor:
            AddAnchorEffects(ObjectiveIndex, MeshComponent);
            break;
            
        case EAURACRONObjectiveType::PowerCore:
        case EAURACRONObjectiveType::StormCore:
            AddStormCoreEffects(ObjectiveIndex, MeshComponent);
            break;
            
        default:
            // Efeitos genéricos para outros tipos
            break;
    }
    
    // Aplicar características específicas do ambiente
    ApplyEnvironmentCharacteristics(ObjectiveIndex, Environment);
    
    // Atualizar estado do objetivo
    Objective.CurrentState = EAURACRONObjectiveState::Available;
    Objective.Health = Objective.MaxHealth;
    Objective.ControllingTeam = -1; // Neutro inicialmente
    
    // Notificar sistemas relacionados
    // Criar FAURACRONProceduralObjective para o broadcast
    FAURACRONProceduralObjective ProceduralObjective;
    ProceduralObjective.ObjectiveType = Objective.ObjectiveType;
    ProceduralObjective.Position = Objective.WorldPosition;
    ProceduralObjective.State = EAURACRONObjectiveState::Active;
    ProceduralObjective.TeamOwner = -1; // Neutro inicialmente

    OnObjectiveCreated.Broadcast(ProceduralObjective);
    
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGObjectiveSystem::CreateObjective - Objetivo {Index} criado com sucesso (Tipo: {Type}, Ambiente: {Environment})",
           ObjectiveIndex, (int32)Objective.ObjectiveType, (int32)Environment);

    // Adicionar efeitos visuais baseados no tipo de objetivo
    switch (Objective.ObjectiveType)
    {
        case EAURACRONObjectiveType::PrismalNexus:
            AddPrismalNexusEffects(ObjectiveIndex, MeshComponent);
            break;

        case EAURACRONObjectiveType::RadiantAnchor:
        case EAURACRONObjectiveType::ZephyrAnchor:
        case EAURACRONObjectiveType::PurgatoryAnchor:
            // Adicionar efeito de âncora
            AddAnchorEffects(ObjectiveIndex, MeshComponent);
            break;

        case EAURACRONObjectiveType::StormCore:
            // Adicionar efeito do Storm Core
            AddStormCoreEffects(ObjectiveIndex, MeshComponent);
            break;

        default:
            // Efeitos padrão para outros tipos
            break;
    }

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGObjectiveSystem: Objetivo {Index} criado visualmente", ObjectiveIndex);
}

// Métodos auxiliares para criação de efeitos visuais
UStaticMesh* AAURACRONPCGObjectiveSystem::GetObjectiveMesh(EAURACRONObjectiveType ObjectiveType, EAURACRONEnvironmentType Environment)
{
    // Obter mesh apropriado para o tipo de objetivo e ambiente
    if (ObjectiveMeshesByType.Contains(ObjectiveType))
    {
        return ObjectiveMeshesByType[ObjectiveType];
    }
    else if (ObjectiveMeshesByEnvironment.Contains(Environment) && 
             ObjectiveMeshesByEnvironment[Environment].Num() > 0)
    {
        // Usar mesh padrão do ambiente se não houver específico para o tipo
        UStaticMeshComponent* MeshComponent = ObjectiveMeshesByEnvironment[Environment][0];
        return MeshComponent ? Cast<UStaticMesh>(MeshComponent->GetStaticMesh()) : DefaultObjectiveMesh;
    }
    
    // Mesh padrão se nenhum específico for encontrado
    return DefaultObjectiveMesh;
}

void AAURACRONPCGObjectiveSystem::AddPrismalNexusEffects(int32 ObjectiveIndex, UStaticMeshComponent* MeshComponent)
{
    if (ObjectiveIndex >= 0 && ObjectiveIndex < Objectives.Num() && MeshComponent)
    {
        // Adicionar efeito de partículas para o Nexus
        if (PrismalNexusParticleSystem)
        {
            UNiagaraComponent* NiagaraComp = NewObject<UNiagaraComponent>(this);
            NiagaraComp->SetAsset(PrismalNexusParticleSystem);
            NiagaraComp->SetupAttachment(MeshComponent);
            NiagaraComp->SetRelativeLocation(FVector(0.0f, 0.0f, 100.0f));
            NiagaraComp->SetRelativeScale3D(FVector(2.0f));
            NiagaraComp->RegisterComponent();
            
            // Armazenar referência para uso posterior
            if (ObjectiveIndex < ObjectiveEffectComponents.Num())
            {
                ObjectiveEffectComponents[ObjectiveIndex] = NiagaraComp;
            }
            else
            {
                ObjectiveEffectComponents.Add(NiagaraComp);
            }
        }
        
        // Adicionar luz para o Nexus
        UPointLightComponent* LightComp = NewObject<UPointLightComponent>(this);
        LightComp->SetupAttachment(MeshComponent);
        LightComp->SetRelativeLocation(FVector(0.0f, 0.0f, 150.0f));
        LightComp->SetLightColor(FLinearColor(0.1f, 0.8f, 1.0f));
        LightComp->SetIntensity(5000.0f);
        LightComp->SetAttenuationRadius(500.0f);
        LightComp->RegisterComponent();
    }
}

void AAURACRONPCGObjectiveSystem::AddAnchorEffects(int32 ObjectiveIndex, UStaticMeshComponent* MeshComponent)
{
    if (ObjectiveIndex >= 0 && ObjectiveIndex < Objectives.Num() && MeshComponent)
    {
        FAURACRONObjectiveInfo& Objective = Objectives[ObjectiveIndex];
        
        // Selecionar sistema de partículas com base no tipo de âncora
        UNiagaraSystem* ParticleSystem = nullptr;
        FLinearColor LightColor;
        
        switch (Objective.ObjectiveType)
        {
        case EAURACRONObjectiveType::RadiantAnchor:
            ParticleSystem = RadiantAnchorParticleSystem;
            LightColor = FLinearColor(1.0f, 0.8f, 0.2f); // Dourado
            break;
            
        case EAURACRONObjectiveType::ZephyrAnchor:
            ParticleSystem = ZephyrAnchorParticleSystem;
            LightColor = FLinearColor(0.2f, 0.8f, 1.0f); // Azul claro
            break;
            
        case EAURACRONObjectiveType::PurgatoryAnchor:
            ParticleSystem = PurgatoryAnchorParticleSystem;
            LightColor = FLinearColor(0.8f, 0.2f, 1.0f); // Roxo
            break;
            
        default:
            ParticleSystem = DefaultAnchorParticleSystem;
            LightColor = FLinearColor(0.5f, 0.5f, 0.5f); // Cinza
            break;
        }
        
        // Adicionar sistema de partículas
        if (ParticleSystem)
        {
            UNiagaraComponent* NiagaraComp = NewObject<UNiagaraComponent>(this);
            NiagaraComp->SetAsset(ParticleSystem);
            NiagaraComp->SetupAttachment(MeshComponent);
            NiagaraComp->SetRelativeLocation(FVector(0.0f, 0.0f, 50.0f));
            NiagaraComp->RegisterComponent();
            
            // Armazenar referência para uso posterior
            if (ObjectiveIndex < ObjectiveEffectComponents.Num())
            {
                ObjectiveEffectComponents[ObjectiveIndex] = NiagaraComp;
            }
            else
            {
                ObjectiveEffectComponents.Add(NiagaraComp);
            }
        }
        
        // Adicionar luz
        UPointLightComponent* LightComp = NewObject<UPointLightComponent>(this);
    }
}

void AAURACRONPCGObjectiveSystem::AddStormCoreEffects(int32 ObjectiveIndex, UStaticMeshComponent* MeshComponent)
{
    if (ObjectiveIndex >= 0 && ObjectiveIndex < Objectives.Num() && MeshComponent)
    {
        // Adicionar efeito de partículas para o Storm Core
        // Usar sistema de partículas padrão se não houver específico
        UNiagaraSystem* ParticleSystem = DefaultAnchorParticleSystem;
        
        if (ParticleSystem)
        {
            // Criar componente de partículas principal
            UNiagaraComponent* MainNiagaraComp = NewObject<UNiagaraComponent>(this);
            MainNiagaraComp->SetAsset(ParticleSystem);
            MainNiagaraComp->SetupAttachment(MeshComponent);
            MainNiagaraComp->SetRelativeLocation(FVector(0.0f, 0.0f, 100.0f));
            MainNiagaraComp->SetRelativeScale3D(FVector(1.5f));
            MainNiagaraComp->RegisterComponent();
            
            // Armazenar referência para uso posterior
            if (ObjectiveIndex < ObjectiveEffectComponents.Num())
            {
                ObjectiveEffectComponents[ObjectiveIndex] = MainNiagaraComp;
            }
            else
            {
                ObjectiveEffectComponents.Add(MainNiagaraComp);
            }
            
            // Criar componentes de partículas secundários (raios elétricos)
            for (int32 i = 0; i < 3; ++i)
            {
                UNiagaraComponent* SecondaryNiagaraComp = NewObject<UNiagaraComponent>(this);
                SecondaryNiagaraComp->SetAsset(ParticleSystem);
                SecondaryNiagaraComp->SetupAttachment(MeshComponent);
                
                // Posicionar em diferentes pontos ao redor do núcleo
                float Angle = i * 120.0f * PI / 180.0f;
                float Radius = 150.0f;
                SecondaryNiagaraComp->SetRelativeLocation(FVector(
                    FMath::Cos(Angle) * Radius,
                    FMath::Sin(Angle) * Radius,
                    50.0f
                ));
                
                SecondaryNiagaraComp->SetRelativeScale3D(FVector(0.7f));
                SecondaryNiagaraComp->RegisterComponent();
            }
        }
        
        // Adicionar luz principal (azul elétrico)
        UPointLightComponent* MainLightComp = NewObject<UPointLightComponent>(this);
        MainLightComp->SetupAttachment(MeshComponent);
        MainLightComp->SetRelativeLocation(FVector(0.0f, 0.0f, 150.0f));
        MainLightComp->SetLightColor(FLinearColor(0.2f, 0.4f, 1.0f)); // Azul elétrico
        MainLightComp->SetIntensity(8000.0f);
        MainLightComp->SetAttenuationRadius(600.0f);
        MainLightComp->SetCastShadows(true);
        MainLightComp->RegisterComponent();
        
        // Adicionar luz secundária pulsante
        UPointLightComponent* PulsingLightComp = NewObject<UPointLightComponent>(this);
        PulsingLightComp->SetupAttachment(MeshComponent);
        PulsingLightComp->SetRelativeLocation(FVector(0.0f, 0.0f, 50.0f));
        PulsingLightComp->SetLightColor(FLinearColor(0.6f, 0.8f, 1.0f)); // Azul claro
        PulsingLightComp->SetIntensity(4000.0f);
        PulsingLightComp->SetAttenuationRadius(400.0f);
        PulsingLightComp->SetCastShadows(false);
        PulsingLightComp->RegisterComponent();
        
        // Adicionar luz adicional
        UPointLightComponent* LightComp = NewObject<UPointLightComponent>(this);
        LightComp->SetupAttachment(MeshComponent);
        LightComp->SetRelativeLocation(FVector(0.0f, 0.0f, 100.0f));
        LightComp->SetLightColor(FLinearColor(0.5f, 0.7f, 1.0f)); // Assumindo uma cor azul similar
        LightComp->SetIntensity(2000.0f);
        LightComp->SetAttenuationRadius(300.0f);
        LightComp->SetCastShadows(false);
        LightComp->RegisterComponent();
    }
}

void AAURACRONPCGObjectiveSystem::ApplyEnvironmentCharacteristics(int32 ObjectiveIndex, EAURACRONEnvironmentType Environment)
{
    if (ObjectiveIndex >= 0 && ObjectiveIndex < Objectives.Num())
    {
        FAURACRONObjectiveInfo& Objective = Objectives[ObjectiveIndex];
        
        // Aplicar modificações baseadas no ambiente
        switch (Environment)
        {
        case EAURACRONEnvironmentType::RadiantPlains:
            // Aumentar visibilidade e reduzir tempo de captura
            Objective.ObjectiveRadius *= 1.2f;
            Objective.CaptureTime *= 0.9f;
            break;
            
        case EAURACRONEnvironmentType::ZephyrFirmament:
            // Aumentar altura e adicionar efeito de vento
            Objective.WorldPosition.Z += 100.0f;
            
            // Implementar efeitos específicos baseados no tipo de objetivo
            if (Objective.ObjectiveType == EAURACRONObjectiveType::StormCore)
            {
                // Núcleo de Tempestade - Aumenta dano de habilidades e velocidade de movimento
                Objective.MaxHealth *= 1.5f;
                Objective.CurrentHealth = Objective.MaxHealth;
                Objective.BuffDuration *= 2.0f;
                Objective.BuffStrength *= 1.8f;
                Objective.CaptureTime *= 1.2f; // Mais difícil de capturar
                
                // Configurar efeitos visuais de tempestade
                Objective.VisualEffectScale = 1.5f;
                Objective.EffectColor = FLinearColor(0.2f, 0.4f, 1.0f); // Azul elétrico
            }
            else if (Objective.ObjectiveType == EAURACRONObjectiveType::WindSanctuary)
            {
                // Santuários dos Ventos - Concede buff de velocidade e redução de cooldown
                Objective.MaxHealth *= 0.8f; // Mais fácil de capturar
                Objective.CurrentHealth = Objective.MaxHealth;
                Objective.BuffDuration *= 1.5f;
                Objective.CaptureTime *= 0.8f; // Mais rápido de capturar
                
                // Configurar efeitos visuais de vento
                Objective.VisualEffectScale = 1.2f;
                Objective.EffectColor = FLinearColor(0.8f, 0.9f, 1.0f); // Branco azulado
            }
            
            // Efeito de vento implementado via sistema de partículas
            break;
            
        case EAURACRONEnvironmentType::PurgatoryRealm:
            // Aumentar dificuldade e recompensas
            Objective.MaxHealth *= 1.3f;
            Objective.CurrentHealth = Objective.MaxHealth;
            Objective.BuffDuration *= 1.5f;
            break;
            
        case EAURACRONEnvironmentType::CrystalCaverns:
            // Adicionar efeito de cristalização e aumentar defesa
            Objective.MaxHealth *= 1.2f;
            Objective.CurrentHealth = Objective.MaxHealth;
            break;
            
        default:
            break;
        }
        
        UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Características do ambiente %d aplicadas ao objetivo %d"), 
               static_cast<int32>(Environment), ObjectiveIndex);
    }
}

void AAURACRONPCGObjectiveSystem::StartRespawnTimer(int32 ObjectiveIndex)
{
    if (ObjectiveIndex >= 0 && ObjectiveIndex < Objectives.Num())
    {
        Objectives[ObjectiveIndex].CurrentState = EAURACRONObjectiveState::Respawning;
    }
}

void AAURACRONPCGObjectiveSystem::OnObjectiveRespawn(int32 ObjectiveIndex)
{
    if (ObjectiveIndex >= 0 && ObjectiveIndex < Objectives.Num())
    {
        FAURACRONObjectiveInfo& Objective = Objectives[ObjectiveIndex];
        Objective.CurrentState = EAURACRONObjectiveState::Available;
        Objective.CurrentHealth = Objective.MaxHealth;
        Objective.ControllingTeam = -1;
        Objective.TimeUntilRespawn = 0.0f;

        UpdateObjectiveVisibility();

        UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Objetivo %d respawnou"), ObjectiveIndex);
    }
}

void AAURACRONPCGObjectiveSystem::UpdateObjectiveVisibility()
{
    // Atualizar visibilidade de todos os objetivos com base no ambiente atual e fase do mapa
    for (int32 i = 0; i < Objectives.Num(); ++i)
    {
        FAURACRONObjectiveInfo& Objective = Objectives[i];
        bool bShouldBeVisible = false;
        
        // Verificar se o objetivo deve estar visível com base na fase do mapa
        if (Objective.bIsActiveInCurrentPhase)
        {
            // Verificar se o objetivo está em um estado visível
            if (Objective.CurrentState != EAURACRONObjectiveState::Inactive && 
                Objective.CurrentState != EAURACRONObjectiveState::Respawning)
            {
                bShouldBeVisible = true;
            }
        }
        
        // Atualizar visibilidade dos componentes visuais do objetivo
        if (i < ObjectiveCollisionComponents.Num() && ObjectiveCollisionComponents[i])
        {
            ObjectiveCollisionComponents[i]->SetVisibility(bShouldBeVisible);
            ObjectiveCollisionComponents[i]->SetCollisionEnabled(
                bShouldBeVisible ? ECollisionEnabled::QueryAndPhysics : ECollisionEnabled::NoCollision);
        }
    }
    
    // Atualizar visibilidade dos objetivos procedurais
    for (const FAURACRONProceduralObjective& ProcObjective : ActiveProceduralObjectives)
    {
        // Implementação específica para objetivos procedurais
        // Seria necessário manter uma referência aos componentes visuais
    }
    
    UE_LOG(LogTemp, Verbose, TEXT("AURACRONPCGObjectiveSystem: Visibilidade dos objetivos atualizada"));
}

void AAURACRONPCGObjectiveSystem::ClearObjectivesForEnvironment(EAURACRONEnvironmentType Environment)
{
    // Remover objetivos procedurais do ambiente específico
    TArray<FAURACRONProceduralObjective> RemainingObjectives;
    
    for (const FAURACRONProceduralObjective& Objective : ActiveProceduralObjectives)
    {
        if (Objective.EnvironmentType != Environment)
        {
            // Manter objetivos de outros ambientes
            RemainingObjectives.Add(Objective);
        }
        else
        {
            // Destruir componentes visuais associados a este objetivo
            // Seria necessário manter uma referência aos componentes visuais
            UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Removendo objetivo procedural no ambiente %d"), 
                   static_cast<int32>(Environment));
        }
    }
    
    // Atualizar a lista de objetivos ativos
    ActiveProceduralObjectives = RemainingObjectives;
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Objetivos do ambiente %d removidos"), 
           static_cast<int32>(Environment));
}

void AAURACRONPCGObjectiveSystem::GeneratePhaseSpecificObjectives(EAURACRONMapPhase MapPhase)
{
    // Gerar objetivos específicos para cada fase do mapa
    switch (MapPhase)
    {
    case EAURACRONMapPhase::Awakening:
        // Fase inicial - gerar objetivos básicos como ResourceNode e PowerCore
        for (int32 i = 0; i < 3; ++i)
        {
            FAURACRONProceduralObjective NewObjective = GenerateNewProceduralObjective(EAURACRONObjectiveType::ResourceNode);
            ActiveProceduralObjectives.Add(NewObjective);
        }
        for (int32 i = 0; i < 2; ++i)
        {
            FAURACRONProceduralObjective NewObjective = GenerateNewProceduralObjective(EAURACRONObjectiveType::PowerCore);
            ActiveProceduralObjectives.Add(NewObjective);
        }
        break;
        
    case EAURACRONMapPhase::Convergence:
        // Fase média - gerar objetivos mais valiosos como AncientRelic e EnergyConduit
        for (int32 i = 0; i < 2; ++i)
        {
            FAURACRONProceduralObjective NewObjective = GenerateNewProceduralObjective(EAURACRONObjectiveType::AncientRelic);
            ActiveProceduralObjectives.Add(NewObjective);
        }
        for (int32 i = 0; i < 2; ++i)
        {
            FAURACRONProceduralObjective NewObjective = GenerateNewProceduralObjective(EAURACRONObjectiveType::EnergyConduit);
            ActiveProceduralObjectives.Add(NewObjective);
        }
        break;
        
    case EAURACRONMapPhase::Intensification:
        // Fase avançada - gerar objetivos poderosos como DefenseTower e NexusFragment
        for (int32 i = 0; i < 2; ++i)
        {
            FAURACRONProceduralObjective NewObjective = GenerateNewProceduralObjective(EAURACRONObjectiveType::DefenseTower);
            ActiveProceduralObjectives.Add(NewObjective);
        }
        for (int32 i = 0; i < 3; ++i)
        {
            FAURACRONProceduralObjective NewObjective = GenerateNewProceduralObjective(EAURACRONObjectiveType::NexusFragment);
            ActiveProceduralObjectives.Add(NewObjective);
        }
        break;
        
    case EAURACRONMapPhase::Resolution:
        // Fase final - gerar objetivos decisivos como TemporalRift e FusionCatalyst
        for (int32 i = 0; i < 2; ++i)
        {
            FAURACRONProceduralObjective NewObjective = GenerateNewProceduralObjective(EAURACRONObjectiveType::TemporalRift);
            ActiveProceduralObjectives.Add(NewObjective);
        }
        for (int32 i = 0; i < 2; ++i)
        {
            FAURACRONProceduralObjective NewObjective = GenerateNewProceduralObjective(EAURACRONObjectiveType::FusionCatalyst);
            ActiveProceduralObjectives.Add(NewObjective);
        }
        break;
        
    default:
        break;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Objetivos específicos da fase %d gerados"), 
           static_cast<int32>(MapPhase));
}

void AAURACRONPCGObjectiveSystem::OnMapPhaseChanged(EAURACRONMapPhase PreviousPhase, EAURACRONMapPhase NewPhase)
{
    // Notificar outros sistemas sobre a mudança de fase
    if (PCGActorReferences.IsValid())
    {
        // Notificar ilhas
        for (AAURACRONPCGIsland* Island : PCGActorReferences.IslandActors)
        {
            if (IsValid(Island))
            {
                Island->UpdateForMapPhase(NewPhase);
            }
        }
        
        // Notificar trilhas e atualizar conexões com objetivos
        for (AAURACRONPCGTrail* Trail : PCGActorReferences.TrailActors)
        {
            if (IsValid(Trail))
            {
                Trail->UpdateForMapPhase(NewPhase);
                
                // Atualizar conexões entre trilhas e objetivos
                UpdateTrailObjectiveConnections(Trail, NewPhase);
            }
        }
        
        // Notificar fluxo prismal
        if (IsValid(PCGActorReferences.PrismalFlowActor))
        {
            PCGActorReferences.PrismalFlowActor->UpdateForMapPhase(NewPhase);
        }
        
        // Notificar gerenciador de ambientes
        UWorld* World = GetWorld();
        if (World)
        {
            TArray<AActor*> FoundManagers;
            UGameplayStatics::GetAllActorsOfClass(World, AAURACRONPCGEnvironmentManager::StaticClass(), FoundManagers);
            
            if (FoundManagers.Num() > 0)
            {
                if (AAURACRONPCGEnvironmentManager* EnvironmentManager = Cast<AAURACRONPCGEnvironmentManager>(FoundManagers[0]))
                {
                    EnvironmentManager->UpdateForMapPhase(NewPhase);
                    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Notificado EnvironmentManager sobre mudança de fase"));
                }
            }
        }
    }
}

void AAURACRONPCGObjectiveSystem::UpdateTrailObjectiveConnections(AAURACRONPCGTrail* Trail, EAURACRONMapPhase MapPhase)
{
    if (!IsValid(Trail))
    {
        return;
    }
    
    // Obter objetivos ativos na fase atual
    TArray<FVector> ObjectivePositions;
    
    // Adicionar posições dos objetivos principais
    for (const FAURACRONObjectiveInfo& Objective : Objectives)
    {
        if (Objective.bIsActiveInCurrentPhase && 
            (Objective.CurrentState == EAURACRONObjectiveState::Available || 
             Objective.CurrentState == EAURACRONObjectiveState::Captured))
        {
            // Obter posição do objetivo
            int32 Index = &Objective - &Objectives[0];
            if (ObjectiveCollisionComponents.IsValidIndex(Index))
            {
                if (UPrimitiveComponent* Component = ObjectiveCollisionComponents[Index])
                {
                    ObjectivePositions.Add(Component->GetComponentLocation());
                }
            }
        }
    }
    
    // Adicionar posições dos objetivos procedurais
    for (const FAURACRONProceduralObjective& Objective : ActiveProceduralObjectives)
    {
        if (Objective.CurrentState == EAURACRONObjectiveState::Available || 
            Objective.CurrentState == EAURACRONObjectiveState::Captured)
        {
            ObjectivePositions.Add(Objective.WorldPosition);
        }
    }
    
    // Atualizar conexões da trilha com os objetivos
    Trail->UpdateObjectiveConnections(ObjectivePositions, MapPhase);
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Atualizadas conexões entre trilha e %d objetivos"),
           ObjectivePositions.Num());
}

void AAURACRONPCGObjectiveSystem::ApplyMapPhaseEffects()
{
    // Aplicar efeitos específicos da fase atual do mapa aos objetivos
    switch (CurrentMapPhase)
    {
    case EAURACRONMapPhase::Awakening:
        // Fase inicial - objetivos básicos
        for (FAURACRONObjectiveInfo& Objective : Objectives)
        {
            // Reduzir recompensas na fase inicial
            Objective.BuffDuration *= 0.7f;
        }
        break;
        
    case EAURACRONMapPhase::Convergence:
        // Fase média - aumentar importância dos objetivos
        for (FAURACRONObjectiveInfo& Objective : Objectives)
        {
            // Aumentar recompensas na fase média
            Objective.BuffDuration *= 1.2f;
            
            // Reduzir tempo de respawn
            Objective.RespawnTime *= 0.8f;
        }
        break;
        
    case EAURACRONMapPhase::Intensification:
        // Fase avançada - objetivos mais poderosos
        for (FAURACRONObjectiveInfo& Objective : Objectives)
        {
            // Aumentar significativamente as recompensas
            Objective.BuffDuration *= 1.5f;
            
            // Aumentar resistência dos objetivos
            Objective.MaxHealth *= 1.3f;
            Objective.CurrentHealth = Objective.MaxHealth;
        }
        break;
        
    case EAURACRONMapPhase::Resolution:
        // Fase final - objetivos decisivos
        for (FAURACRONObjectiveInfo& Objective : Objectives)
        {
            // Recompensas máximas na fase final
            Objective.BuffDuration *= 2.0f;
            
            // Reduzir tempo de respawn ao mínimo
            Objective.RespawnTime *= 0.5f;
        }
        break;
        
    default:
        break;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Efeitos da fase %d aplicados aos objetivos"), 
           static_cast<int32>(CurrentMapPhase));
}

void AAURACRONPCGObjectiveSystem::StartChaosIslandRotation()
{
    // Iniciar rotação das Chaos Islands a cada 3 minutos
    GetWorld()->GetTimerManager().SetTimer(
        ChaosIslandEventTimer,
        this,
        &AAURACRONPCGObjectiveSystem::OnChaosIslandRotation,
        FAURACRONMapDimensions::CHAOS_ISLAND_ROTATION_TIME,
        true
    );
}

void AAURACRONPCGObjectiveSystem::OnChaosIslandRotation()
{
    // Ativar próxima Chaos Island
    TriggerChaosIslandEvent(NextChaosIslandIndex);
    NextChaosIslandIndex = (NextChaosIslandIndex + 1) % 3; // Rotacionar entre 0, 1, 2
}

void AAURACRONPCGObjectiveSystem::ApplyObjectiveBuffsToTeam(int32 ObjectiveIndex, int32 TeamIndex)
{
    if (ObjectiveIndex >= 0 && ObjectiveIndex < Objectives.Num() && TeamIndex >= 0)
    {
        FAURACRONObjectiveInfo& Objective = Objectives[ObjectiveIndex];
        
        // Aplicar buffs específicos com base no tipo de objetivo
        switch (Objective.ObjectiveType)
        {
        case EAURACRONObjectiveType::PrismalNexus:
            // Buff principal - aumenta poder de habilidades e regeneração
            ApplyPrismalNexusBuff(TeamIndex, Objective.BuffDuration);
            break;
            
        case EAURACRONObjectiveType::RadiantAnchor:
            // Buff de movimento e ataque
            ApplyRadiantAnchorBuff(TeamIndex, Objective.BuffDuration);
            break;
            
        case EAURACRONObjectiveType::ZephyrAnchor:
            // Buff de velocidade e cooldown
            ApplyZephyrAnchorBuff(TeamIndex, Objective.BuffDuration);
            break;
            
        case EAURACRONObjectiveType::PurgatoryAnchor:
            // Buff de resistência e dano
            ApplyPurgatoryAnchorBuff(TeamIndex, Objective.BuffDuration);
            break;
            
        case EAURACRONObjectiveType::PowerCore:
            // Buff de energia e mana
            ApplyPowerCoreBuff(TeamIndex, Objective.BuffDuration);
            break;
            
        case EAURACRONObjectiveType::StormCore:
            // Buff de dano e velocidade
            ApplyStormCoreBuff(TeamIndex, Objective.BuffDuration);
            break;
            
        case EAURACRONObjectiveType::WindSanctuary:
            // Buff de velocidade e redução de cooldown
            ApplyWindSanctuaryBuff(TeamIndex, Objective.BuffDuration);
            break;
            
        default:
            // Buff genérico para outros tipos
            ApplyGenericBuff(TeamIndex, Objective.BuffDuration);
            break;
        }
        
        // Notificar outros sistemas sobre o buff aplicado
        OnObjectiveCaptured.Broadcast(ObjectiveIndex, TeamIndex);
        
        UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Buffs do objetivo %d aplicados ao Team %d por %.1f segundos"), 
               ObjectiveIndex, TeamIndex, Objective.BuffDuration);
    }
}

// Métodos auxiliares para aplicação de buffs específicos
void AAURACRONPCGObjectiveSystem::ApplyPrismalNexusBuff(int32 TeamIndex, float Duration)
{
    // Implementação do buff do Prismal Nexus
    // Aumenta poder de habilidades e regeneração
    // Aplicar buffs do Prismal Nexus usando sistema robusto
    if (AGameStateBase* GameState = GetWorld()->GetGameState())
    {
        // SpellPower +25%
        if (OnObjectiveBuffApplied.IsBound())
        {
            OnObjectiveBuffApplied.Broadcast(TeamIndex, EAURACRONBuffType::SpellPower, 0.25f, Duration);
        }

        // HealthRegeneration +30%
        if (OnObjectiveBuffApplied.IsBound())
        {
            OnObjectiveBuffApplied.Broadcast(TeamIndex, EAURACRONBuffType::HealthRegeneration, 0.30f, Duration);
        }

        // ManaRegeneration +30%
        if (OnObjectiveBuffApplied.IsBound())
        {
            OnObjectiveBuffApplied.Broadcast(TeamIndex, EAURACRONBuffType::ManaRegeneration, 0.30f, Duration);
        }

        UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Prismal Nexus buffs aplicados ao Team %d - SpellPower +25%%, HealthRegeneration +30%%, ManaRegeneration +30%% por %.1fs"),
               TeamIndex, Duration);
    }
}

void AAURACRONPCGObjectiveSystem::ApplyRadiantAnchorBuff(int32 TeamIndex, float Duration)
{
    // Implementação do buff do Radiant Anchor
    // Aumenta velocidade de movimento e dano de ataque
    // Aplicar buff de Radiant Anchor ao time usando sistema robusto
    if (AGameStateBase* GameState = GetWorld()->GetGameState())
    {
        // Aplicar buffs específicos do Radiant Anchor
        // MovementSpeed +15%
        if (OnObjectiveBuffApplied.IsBound())
        {
            OnObjectiveBuffApplied.Broadcast(TeamIndex, EAURACRONBuffType::MovementSpeed, 0.15f, Duration);
        }

        // AttackDamage +10%
        if (OnObjectiveBuffApplied.IsBound())
        {
            OnObjectiveBuffApplied.Broadcast(TeamIndex, EAURACRONBuffType::DamageBoost, 0.10f, Duration);
        }

        UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Radiant Anchor buffs aplicados ao Team %d - MovementSpeed +15%%, DamageBoost +10%% por %.1fs"),
               TeamIndex, Duration);
    }
}

void AAURACRONPCGObjectiveSystem::ApplyZephyrAnchorBuff(int32 TeamIndex, float Duration)
{
    // Implementação do buff do Zephyr Anchor
    // Reduz cooldowns e aumenta velocidade de ataque
    // Implementação do buff do Zephyr Anchor
    // Reduz cooldowns e aumenta velocidade de ataque
    ApplyTeamBuff(TeamIndex, EAURACRONBuffType::CooldownReduction, 0.15f, Duration);
    ApplyTeamBuff(TeamIndex, EAURACRONBuffType::AttackSpeed, 0.20f, Duration);

    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Zephyr Anchor buff aplicado ao Team %d"), TeamIndex);
}

void AAURACRONPCGObjectiveSystem::ApplyPurgatoryAnchorBuff(int32 TeamIndex, float Duration)
{
    // Implementação do buff do Purgatory Anchor
    // Aumenta resistência e dano mágico
    // Implementação do buff do Purgatory Anchor
    // Aumenta resistências e poder mágico
    ApplyTeamBuff(TeamIndex, EAURACRONBuffType::Armor, 0.20f, Duration);
    ApplyTeamBuff(TeamIndex, EAURACRONBuffType::MagicResistance, 0.20f, Duration);
    ApplyTeamBuff(TeamIndex, EAURACRONBuffType::SpellPower, 0.15f, Duration);

    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Purgatory Anchor buff aplicado ao Team %d"), TeamIndex);
}

void AAURACRONPCGObjectiveSystem::ApplyPowerCoreBuff(int32 TeamIndex, float Duration)
{
    // Implementação do buff do Power Core
    // Aumenta regeneração de energia e mana
    // Implementação do buff do Power Core
    // Aumenta regeneração e chance crítica
    ApplyTeamBuff(TeamIndex, EAURACRONBuffType::HealthRegeneration, 0.25f, Duration);
    ApplyTeamBuff(TeamIndex, EAURACRONBuffType::ManaRegeneration, 0.25f, Duration);
    ApplyTeamBuff(TeamIndex, EAURACRONBuffType::CriticalChance, 0.10f, Duration);

    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Power Core buff aplicado ao Team %d"), TeamIndex);
}

void AAURACRONPCGObjectiveSystem::ApplyStormCoreBuff(int32 TeamIndex, float Duration)
{
    // Implementação do buff do Storm Core
    // Aumenta dano de ataque, velocidade de movimento e resistência a controle de grupo
    // Implementação do buff do Storm Core
    // Aumenta dano, velocidade de movimento e tenacidade
    ApplyTeamBuff(TeamIndex, EAURACRONBuffType::DamageBoost, 0.15f, Duration);
    ApplyTeamBuff(TeamIndex, EAURACRONBuffType::MovementSpeed, 0.10f, Duration);
    ApplyTeamBuff(TeamIndex, EAURACRONBuffType::Tenacity, 0.25f, Duration);
    ApplyTeamBuff(TeamIndex, EAURACRONBuffType::CriticalChance, 0.05f, Duration);

    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Storm Core buff aplicado ao Team %d"), TeamIndex);
}

void AAURACRONPCGObjectiveSystem::ApplyWindSanctuaryBuff(int32 TeamIndex, float Duration)
{
    // Implementação do buff do Wind Sanctuary
    // Aumenta velocidade de movimento e reduz cooldowns de habilidades
    // Implementação do buff do Wind Sanctuary
    // Aumenta velocidade, reduz cooldowns e aumenta chance crítica
    ApplyTeamBuff(TeamIndex, EAURACRONBuffType::MovementSpeed, 0.20f, Duration);
    ApplyTeamBuff(TeamIndex, EAURACRONBuffType::CooldownReduction, 0.15f, Duration);
    ApplyTeamBuff(TeamIndex, EAURACRONBuffType::CriticalChance, 0.10f, Duration);

    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Wind Sanctuary buff aplicado ao Team %d"), TeamIndex);
}

void AAURACRONPCGObjectiveSystem::ApplyGenericBuff(int32 TeamIndex, float Duration)
{
    // Implementação de buff adaptativo baseado na fase do mapa
    // Implementação de buffs adaptativos baseados na fase atual
    switch (CurrentMapPhase)
    {
        case EAURACRONMapPhase::Awakening:
            // Buff de exploração e movimento
            ApplyTeamBuff(TeamIndex, EAURACRONBuffType::MovementSpeed, 0.15f, Duration);
            ApplyTeamBuff(TeamIndex, EAURACRONBuffType::SpellPower, 0.20f, Duration);
            break;

        case EAURACRONMapPhase::Intensification:
            // Buff de combate e resistência
            ApplyTeamBuff(TeamIndex, EAURACRONBuffType::DamageBoost, 0.10f, Duration);
            ApplyTeamBuff(TeamIndex, EAURACRONBuffType::DefenseBoost, 0.10f, Duration);
            break;

        case EAURACRONMapPhase::Resolution:
            // Buff de regeneração e cooldown
            ApplyTeamBuff(TeamIndex, EAURACRONBuffType::HealthRegeneration, 0.25f, Duration);
            ApplyTeamBuff(TeamIndex, EAURACRONBuffType::CooldownReduction, 0.15f, Duration);
            break;

        default:
            // Fallback para buff balanceado
            ApplyTeamBuff(TeamIndex, EAURACRONBuffType::DamageBoost, 0.08f, Duration);
            break;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGObjectiveSystem: Buff adaptativo aplicado ao Team %d na fase %d"),
           TeamIndex, static_cast<int32>(CurrentMapPhase));
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES FALTANTES - UE 5.6 APIS MODERNAS
// ========================================

void AAURACRONPCGObjectiveSystem::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    // Replicar propriedades usando APIs modernas do UE 5.6
    DOREPLIFETIME(AAURACRONPCGObjectiveSystem, Objectives);
    DOREPLIFETIME(AAURACRONPCGObjectiveSystem, CurrentEnvironment);
    DOREPLIFETIME(AAURACRONPCGObjectiveSystem, CurrentMapPhase);
    DOREPLIFETIME(AAURACRONPCGObjectiveSystem, NextChaosIslandIndex);
    DOREPLIFETIME(AAURACRONPCGObjectiveSystem, bAutoGenerate);
}

void AAURACRONPCGObjectiveSystem::StartObjectiveSystem()
{
    // Iniciar sistema de objetivos procedurais usando APIs modernas do UE 5.6
    if (!HasAuthority())
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGObjectiveSystem::StartObjectiveSystem - Only server can start objective system"));
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGObjectiveSystem::StartObjectiveSystem - Starting objective system with modern UE 5.6 APIs"));

    // Parar qualquer timer existente
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().ClearAllTimersForObject(this);
    }

    // Gerar objetivos iniciais
    GenerateObjectives();

    // Configurar timer de geração procedural usando APIs modernas
    if (UWorld* World = GetWorld())
    {
        FTimerHandle ProceduralTimer;
        World->GetTimerManager().SetTimer(ProceduralTimer, this,
            &AAURACRONPCGObjectiveSystem::OnProceduralGenerationTimerExpired,
            300.0f, // 5 minutos
            true); // Repetir
    }

    // Configurar timer de rotação das Chaos Islands
    if (UWorld* World = GetWorld())
    {
        FTimerHandle ChaosRotationTimer;
        World->GetTimerManager().SetTimer(ChaosRotationTimer, this,
            &AAURACRONPCGObjectiveSystem::OnChaosIslandRotation,
            600.0f, // 10 minutos
            true); // Repetir
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGObjectiveSystem::StartObjectiveSystem - Objective system started successfully"));
}

void AAURACRONPCGObjectiveSystem::StopObjectiveSystem()
{
    // Parar sistema de objetivos procedurais usando APIs modernas do UE 5.6
    if (!HasAuthority())
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGObjectiveSystem::StopObjectiveSystem - Only server can stop objective system"));
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGObjectiveSystem::StopObjectiveSystem - Stopping objective system"));

    // Parar todos os timers
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().ClearAllTimersForObject(this);
    }

    // Limpar objetivos ativos (mas não remover - apenas desativar)
    for (FAURACRONObjectiveInfo& Objective : Objectives)
    {
        if (Objective.bIsActive)
        {
            Objective.bIsActive = false;
            Objective.RespawnTime = 0.0f;

            // Desativar componentes visuais se existirem
            if (Objective.ObjectiveActor && IsValid(Objective.ObjectiveActor))
            {
                Objective.ObjectiveActor->SetActorHiddenInGame(true);
                Objective.ObjectiveActor->SetActorEnableCollision(false);
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGObjectiveSystem::StopObjectiveSystem - Objective system stopped successfully"));
}

void AAURACRONPCGObjectiveSystem::ForceGenerateObjective(EAURACRONObjectiveType ObjectiveType)
{
    // Forçar geração de objetivo procedural usando APIs modernas do UE 5.6
    if (!HasAuthority())
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGObjectiveSystem::ForceGenerateObjective - Only server can force generate objectives"));
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGObjectiveSystem::ForceGenerateObjective - Forcing generation of objective type %d"), (int32)ObjectiveType);

    // Se tipo específico foi solicitado
    if (ObjectiveType != EAURACRONObjectiveType::None)
    {
        // Buscar objetivo do tipo solicitado
        for (int32 i = 0; i < Objectives.Num(); ++i)
        {
            if (Objectives[i].ObjectiveType == ObjectiveType)
            {
                // Forçar respawn imediato
                Objectives[i].bIsActive = true;
                Objectives[i].RespawnTime = 0.0f;
                Objectives[i].ControllingTeam = -1; // Neutro

                // Reativar ator se existir
                if (Objectives[i].ObjectiveActor && IsValid(Objectives[i].ObjectiveActor))
                {
                    Objectives[i].ObjectiveActor->SetActorHiddenInGame(false);
                    Objectives[i].ObjectiveActor->SetActorEnableCollision(true);
                }

                UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGObjectiveSystem::ForceGenerateObjective - Forced respawn of objective %d"), i);
                return;
            }
        }

        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGObjectiveSystem::ForceGenerateObjective - Objective type %d not found"), (int32)ObjectiveType);
    }
    else
    {
        // Gerar todos os objetivos
        GenerateObjectives();
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGObjectiveSystem::ForceGenerateObjective - Regenerated all objectives"));
    }
}

void AAURACRONPCGObjectiveSystem::OnProceduralGenerationTimerExpired()
{
    // Callback para timer de geração procedural usando APIs modernas do UE 5.6
    if (!HasAuthority())
    {
        return;
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGObjectiveSystem::OnProceduralGenerationTimerExpired - Procedural generation timer expired"));

    // Verificar se algum objetivo precisa ser regenerado
    bool bNeedsRegeneration = false;

    for (const FAURACRONObjectiveInfo& Objective : Objectives)
    {
        // Se objetivo não está ativo e não tem time controlador, pode ser regenerado
        if (!Objective.bIsActive && Objective.ControllingTeam == -1)
        {
            bNeedsRegeneration = true;
            break;
        }
    }

    if (bNeedsRegeneration)
    {
        // Regenerar objetivos baseado na fase atual do mapa
        switch (CurrentMapPhase)
        {
            case EAURACRONMapPhase::Awakening:
                // Fase inicial: gerar objetivos básicos
                ForceGenerateObjective(EAURACRONObjectiveType::RadiantAnchor);
                break;

            case EAURACRONMapPhase::Convergence:
                // Fase média: gerar objetivos estratégicos
                ForceGenerateObjective(EAURACRONObjectiveType::ZephyrAnchor);
                break;

            case EAURACRONMapPhase::Intensification:
                // Fase avançada: gerar objetivos épicos
                ForceGenerateObjective(EAURACRONObjectiveType::PurgatoryAnchor);
                break;

            case EAURACRONMapPhase::Resolution:
                // Fase final: gerar todos os objetivos
                ForceGenerateObjective(EAURACRONObjectiveType::None);
                break;
        }

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGObjectiveSystem::OnProceduralGenerationTimerExpired - Regenerated objectives for map phase %d"), (int32)CurrentMapPhase);
    }
}

int32 AAURACRONPCGObjectiveSystem::GetStencilValueForObjectiveType(EAURACRONObjectiveType ObjectiveType) const
{
    // Implementação robusta para valores de stencil baseados no tipo de objetivo
    switch (ObjectiveType)
    {
        case EAURACRONObjectiveType::PrismalNexus:
            return 100; // Valor único para Prismal Nexus
        case EAURACRONObjectiveType::RadiantAnchor:
            return 101; // Valor único para Radiant Anchor
        case EAURACRONObjectiveType::ZephyrAnchor:
            return 102; // Valor único para Zephyr Anchor
        case EAURACRONObjectiveType::PurgatoryAnchor:
            return 103; // Valor único para Purgatory Anchor
        case EAURACRONObjectiveType::StormCore:
            return 104; // Valor único para Storm Core
        case EAURACRONObjectiveType::PowerCore:
            return 105; // Valor único para Power Core
        case EAURACRONObjectiveType::AncientRelic:
            return 106; // Valor único para Ancient Relic
        case EAURACRONObjectiveType::EnergyConduit:
            return 107; // Valor único para Energy Conduit
        case EAURACRONObjectiveType::DefenseTower:
            return 108; // Valor único para Defense Tower
        case EAURACRONObjectiveType::None:
        default:
            return 0; // Valor padrão para tipos não definidos
    }
}

// Implementação das funções ausentes usando APIs modernas do UE 5.6

FAURACRONProceduralObjective AAURACRONPCGObjectiveSystem::GenerateNewProceduralObjective(EAURACRONObjectiveType ForcedType)
{
    FAURACRONProceduralObjective NewObjective;

    // Determinar tipo do objetivo
    if (ForcedType != EAURACRONObjectiveType::None)
    {
        NewObjective.ObjectiveType = ForcedType;
    }
    else
    {
        NewObjective.ObjectiveType = DetermineObjectiveType();
    }

    // Gerar ID único
    NewObjective.ObjectiveID = FGuid::NewGuid().ToString();

    // Configurar propriedades baseadas no tipo
    switch (NewObjective.ObjectiveType)
    {
        case EAURACRONObjectiveType::NexusFragment:
        {
            NewObjective.ObjectiveName = TEXT("Fragmento do Nexus");
            NewObjective.ObjectiveDescription = TEXT("Colete este fragmento para fortalecer seu Nexus");
            NewObjective.RewardPoints = 150;
            NewObjective.ExperienceReward = 200;
            NewObjective.GoldReward = 150;
            NewObjective.Duration = 300.0f; // 5 minutos
            NewObjective.LifeTime = 300.0f;
            NewObjective.ObjectiveCategory = EAURACRONObjectiveCategory::Exploration;
            NewObjective.RequiredPlayers = 1;
            NewObjective.MaxHealth = 100.0f;
            NewObjective.CurrentHealth = 100.0f;
            NewObjective.MaxValue = 100.0f;
            NewObjective.AccumulatedValue = 0.0f;
            NewObjective.Priority = 3;
            NewObjective.StrategicValue = 0.7f;
            break;
        }

        case EAURACRONObjectiveType::FusionCatalyst:
        {
            NewObjective.ObjectiveName = TEXT("Catalisador de Fusão");
            NewObjective.ObjectiveDescription = TEXT("Controle este catalisador para acelerar a fusão de Sígilos");
            NewObjective.RewardPoints = 200;
            NewObjective.ExperienceReward = 300;
            NewObjective.GoldReward = 200;
            NewObjective.Duration = 240.0f; // 4 minutos
            NewObjective.LifeTime = 240.0f;
            NewObjective.ObjectiveCategory = EAURACRONObjectiveCategory::Combat;
            NewObjective.RequiredPlayers = 2;
            NewObjective.MaxHealth = 250.0f;
            NewObjective.CurrentHealth = 250.0f;
            NewObjective.MaxValue = 250.0f;
            NewObjective.AccumulatedValue = 0.0f;
            NewObjective.Priority = 5;
            NewObjective.StrategicValue = 0.8f;
            break;
        }

        case EAURACRONObjectiveType::TransitionPortal:
        {
            NewObjective.ObjectiveName = TEXT("Portal de Transição");
            NewObjective.ObjectiveDescription = TEXT("Ative este portal para facilitar movimentação entre ambientes");
            NewObjective.RewardPoints = 100;
            NewObjective.ExperienceReward = 150;
            NewObjective.GoldReward = 100;
            NewObjective.Duration = 180.0f; // 3 minutos
            NewObjective.LifeTime = 180.0f;
            NewObjective.ObjectiveCategory = EAURACRONObjectiveCategory::Strategic;
            NewObjective.RequiredPlayers = 1;
            NewObjective.MaxHealth = 150.0f;
            NewObjective.CurrentHealth = 150.0f;
            NewObjective.MaxValue = 150.0f;
            NewObjective.AccumulatedValue = 0.0f;
            NewObjective.Priority = 4;
            NewObjective.StrategicValue = 0.6f;
            break;
        }

        case EAURACRONObjectiveType::TemporalRift:
        {
            NewObjective.ObjectiveName = TEXT("Fenda Temporal");
            NewObjective.ObjectiveDescription = TEXT("Estabilize esta fenda para obter vantagens temporais");
            NewObjective.RewardPoints = 300;
            NewObjective.ExperienceReward = 400;
            NewObjective.GoldReward = 300;
            NewObjective.Duration = 360.0f; // 6 minutos
            NewObjective.LifeTime = 360.0f;
            NewObjective.ObjectiveCategory = EAURACRONObjectiveCategory::Elite;
            NewObjective.RequiredPlayers = 3;
            NewObjective.MaxHealth = 400.0f;
            NewObjective.CurrentHealth = 400.0f;
            NewObjective.MaxValue = 400.0f;
            NewObjective.AccumulatedValue = 0.0f;
            NewObjective.Priority = 7;
            NewObjective.StrategicValue = 0.9f;
            break;
        }

        default:
        {
            // Objetivo genérico
            NewObjective.ObjectiveName = TEXT("Objetivo Procedural");
            NewObjective.ObjectiveDescription = TEXT("Complete este objetivo para obter recompensas");
            NewObjective.RewardPoints = 100;
            NewObjective.ExperienceReward = 100;
            NewObjective.GoldReward = 100;
            NewObjective.Duration = 300.0f;
            NewObjective.LifeTime = 300.0f;
            NewObjective.ObjectiveCategory = EAURACRONObjectiveCategory::Standard;
            NewObjective.RequiredPlayers = 1;
            NewObjective.MaxHealth = 100.0f;
            NewObjective.CurrentHealth = 100.0f;
            NewObjective.MaxValue = 100.0f;
            NewObjective.AccumulatedValue = 0.0f;
            NewObjective.Priority = 2;
            NewObjective.StrategicValue = 0.5f;
            break;
        }
    }

    // Configurar estado inicial
    NewObjective.CurrentState = EAURACRONObjectiveState::Available;
    NewObjective.ControllingTeam = -1;
    NewObjective.TimeUntilRespawn = 0.0f;
    NewObjective.CreationTime = GetWorld()->GetTimeSeconds();
    NewObjective.RespawnTime = 0.0f;
    NewObjective.TimeAlive = 0.0f;
    NewObjective.RemainingLifeTime = NewObjective.LifeTime;

    // Encontrar posição válida
    EAURACRONEnvironmentType TargetEnvironment;
    NewObjective.WorldPosition = FindValidObjectivePosition(NewObjective.ObjectiveType, TargetEnvironment);
    NewObjective.EnvironmentType = TargetEnvironment;
    NewObjective.Environment = TargetEnvironment;
    NewObjective.Position = NewObjective.WorldPosition;

    // Aplicar modificadores baseados na fase do mapa
    switch (CurrentMapPhase)
    {
        case EAURACRONMapPhase::Awakening:
            NewObjective.RewardPoints = FMath::RoundToInt(NewObjective.RewardPoints * 0.8f);
            NewObjective.GoldReward = FMath::RoundToInt(NewObjective.GoldReward * 0.8f);
            NewObjective.Duration *= 1.2f;
            NewObjective.LifeTime *= 1.2f;
            NewObjective.RemainingLifeTime = NewObjective.LifeTime;
            break;

        case EAURACRONMapPhase::Convergence:
            NewObjective.RewardPoints = FMath::RoundToInt(NewObjective.RewardPoints * 1.0f);
            NewObjective.GoldReward = FMath::RoundToInt(NewObjective.GoldReward * 1.0f);
            NewObjective.Duration *= 1.0f;
            NewObjective.LifeTime *= 1.0f;
            NewObjective.RemainingLifeTime = NewObjective.LifeTime;
            break;

        case EAURACRONMapPhase::Intensification:
            NewObjective.RewardPoints = FMath::RoundToInt(NewObjective.RewardPoints * 1.3f);
            NewObjective.GoldReward = FMath::RoundToInt(NewObjective.GoldReward * 1.3f);
            NewObjective.Duration *= 0.8f;
            NewObjective.LifeTime *= 0.8f;
            NewObjective.RemainingLifeTime = NewObjective.LifeTime;
            break;

        case EAURACRONMapPhase::Resolution:
            NewObjective.RewardPoints = FMath::RoundToInt(NewObjective.RewardPoints * 1.5f);
            NewObjective.GoldReward = FMath::RoundToInt(NewObjective.GoldReward * 1.5f);
            NewObjective.Duration *= 0.6f;
            NewObjective.LifeTime *= 0.6f;
            NewObjective.RemainingLifeTime = NewObjective.LifeTime;
            break;
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGObjectiveSystem: Generated procedural objective '%s' at %s"),
        *NewObjective.ObjectiveName, *NewObjective.WorldPosition.ToString());

    return NewObjective;
}

EAURACRONEnvironmentType AAURACRONPCGObjectiveSystem::DetermineEnvironmentFromPosition(const FVector& Position)
{
    // Determinar ambiente baseado na posição no mapa usando APIs modernas do UE 5.6

    // Coordenadas baseadas na documentação do AURACRON
    // Mapa dividido em quadrantes com diferentes ambientes

    float X = Position.X;
    float Y = Position.Y;

    // Centro do mapa (área neutra) - geralmente Purgatory Realm
    if (FMath::Abs(X) < 2000.0f && FMath::Abs(Y) < 2000.0f)
    {
        return EAURACRONEnvironmentType::PurgatoryRealm;
    }

    // Quadrante nordeste - Radiant Plains
    if (X > 0 && Y > 0)
    {
        // Verificar se está na área mais externa (mais radiante)
        if (X > 4000.0f || Y > 4000.0f)
        {
            return EAURACRONEnvironmentType::RadiantPlains;
        }
        else
        {
            // Área de transição
            return EAURACRONEnvironmentType::PurgatoryRealm;
        }
    }

    // Quadrante noroeste - Zephyr Firmament
    if (X < 0 && Y > 0)
    {
        // Verificar se está na área mais externa (mais aérea)
        if (FMath::Abs(X) > 4000.0f || Y > 4000.0f)
        {
            return EAURACRONEnvironmentType::ZephyrFirmament;
        }
        else
        {
            // Área de transição
            return EAURACRONEnvironmentType::PurgatoryRealm;
        }
    }

    // Quadrante sudeste - Radiant Plains (extensão)
    if (X > 0 && Y < 0)
    {
        if (X > 4000.0f || FMath::Abs(Y) > 4000.0f)
        {
            return EAURACRONEnvironmentType::RadiantPlains;
        }
        else
        {
            return EAURACRONEnvironmentType::PurgatoryRealm;
        }
    }

    // Quadrante sudoeste - Purgatory Realm (área sombria)
    if (X < 0 && Y < 0)
    {
        // Esta área tende a ser mais sombria/purgatorial
        return EAURACRONEnvironmentType::PurgatoryRealm;
    }

    // Verificação adicional baseada na altura (Z)
    float Z = Position.Z;

    // Altitudes elevadas tendem a ser Zephyr Firmament
    if (Z > 1000.0f)
    {
        return EAURACRONEnvironmentType::ZephyrFirmament;
    }

    // Altitudes muito baixas tendem a ser Purgatory Realm
    if (Z < -500.0f)
    {
        return EAURACRONEnvironmentType::PurgatoryRealm;
    }

    // Verificação baseada em distância do centro
    float DistanceFromCenter = FVector2D(X, Y).Size();

    if (DistanceFromCenter > 8000.0f)
    {
        // Áreas muito distantes do centro
        if (X > 0)
        {
            return EAURACRONEnvironmentType::RadiantPlains;
        }
        else
        {
            return EAURACRONEnvironmentType::ZephyrFirmament;
        }
    }

    // Fallback padrão
    return EAURACRONEnvironmentType::PurgatoryRealm;
}

float AAURACRONPCGObjectiveSystem::CalculateAreaActivity(const FVector& Position)
{
    // Calcular atividade da área baseado em vários fatores usando APIs modernas do UE 5.6

    float ActivityLevel = 0.0f;
    float SearchRadius = 2000.0f; // 20 metros de raio de busca

    if (!GetWorld())
    {
        return 0.0f;
    }

    // Contar jogadores na área
    int32 PlayerCount = 0;
    for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        APlayerController* PC = Iterator->Get();
        if (PC && PC->GetPawn())
        {
            float Distance = FVector::Dist(Position, PC->GetPawn()->GetActorLocation());
            if (Distance <= SearchRadius)
            {
                PlayerCount++;
                // Atividade base por jogador
                ActivityLevel += 0.2f;

                // Bônus se o jogador está em combate
                if (PC->GetPawn()->GetVelocity().Size() > 300.0f) // Movimento rápido indica combate
                {
                    ActivityLevel += 0.1f;
                }
            }
        }
    }

    // Contar NPCs/inimigos na área
    int32 NPCCount = 0;
    for (TActorIterator<APawn> ActorIterator(GetWorld()); ActorIterator; ++ActorIterator)
    {
        APawn* Pawn = *ActorIterator;
        if (Pawn && !Pawn->IsPlayerControlled())
        {
            float Distance = FVector::Dist(Position, Pawn->GetActorLocation());
            if (Distance <= SearchRadius)
            {
                NPCCount++;
                ActivityLevel += 0.1f; // NPCs contribuem menos para atividade
            }
        }
    }

    // Verificar objetivos próximos
    for (const FAURACRONObjectiveInfo& Objective : Objectives)
    {
        float Distance = FVector::Dist(Position, Objective.WorldPosition);
        if (Distance <= SearchRadius)
        {
            // Objetivos ativos aumentam atividade
            if (Objective.CurrentState == EAURACRONObjectiveState::Active ||
                Objective.CurrentState == EAURACRONObjectiveState::Contested)
            {
                ActivityLevel += 0.3f;
            }
            else if (Objective.CurrentState == EAURACRONObjectiveState::Available)
            {
                ActivityLevel += 0.1f;
            }
        }
    }

    // Verificar objetivos procedurais próximos
    for (const FAURACRONProceduralObjective& ProcObjective : ActiveProceduralObjectives)
    {
        float Distance = FVector::Dist(Position, ProcObjective.WorldPosition);
        if (Distance <= SearchRadius)
        {
            if (ProcObjective.CurrentState == EAURACRONObjectiveState::Active ||
                ProcObjective.CurrentState == EAURACRONObjectiveState::Contested)
            {
                ActivityLevel += 0.25f;
            }
            else if (ProcObjective.CurrentState == EAURACRONObjectiveState::Available)
            {
                ActivityLevel += 0.1f;
            }
        }
    }

    // Modificador baseado no ambiente
    EAURACRONEnvironmentType Environment = DetermineEnvironmentFromPosition(Position);
    switch (Environment)
    {
        case EAURACRONEnvironmentType::RadiantPlains:
            ActivityLevel *= 1.1f; // Radiant Plains tendem a ser mais ativas
            break;

        case EAURACRONEnvironmentType::ZephyrFirmament:
            ActivityLevel *= 1.0f; // Neutro
            break;

        case EAURACRONEnvironmentType::PurgatoryRealm:
            ActivityLevel *= 1.2f; // Purgatory Realm é área central, mais ativa
            break;
    }

    // Modificador baseado na fase do mapa
    switch (CurrentMapPhase)
    {
        case EAURACRONMapPhase::Awakening:
            ActivityLevel *= 0.8f; // Menos atividade no início
            break;

        case EAURACRONMapPhase::Convergence:
            ActivityLevel *= 1.0f; // Atividade normal
            break;

        case EAURACRONMapPhase::Intensification:
            ActivityLevel *= 1.3f; // Mais atividade
            break;

        case EAURACRONMapPhase::Resolution:
            ActivityLevel *= 1.5f; // Máxima atividade
            break;
    }

    // Modificador baseado na distância do centro
    float DistanceFromCenter = FVector2D(Position.X, Position.Y).Size();
    if (DistanceFromCenter < 3000.0f)
    {
        ActivityLevel *= 1.2f; // Centro do mapa é mais ativo
    }
    else if (DistanceFromCenter > 8000.0f)
    {
        ActivityLevel *= 0.7f; // Bordas do mapa são menos ativas
    }

    // Clampar entre 0 e 1
    ActivityLevel = FMath::Clamp(ActivityLevel, 0.0f, 1.0f);

    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGObjectiveSystem: Area activity at %s: %.3f (Players: %d, NPCs: %d)"),
        *Position.ToString(), ActivityLevel, PlayerCount, NPCCount);

    return ActivityLevel;
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES QUE ESTAVAM FALTANDO - UE 5.6 MODERN APIS
// ========================================

EAURACRONObjectiveType AAURACRONPCGObjectiveSystem::DetermineObjectiveType()
{
    // Implementação robusta para determinar tipo de objetivo baseado no contexto atual

    // Determinar baseado na fase atual do mapa
    switch (CurrentMapPhase)
    {
        case EAURACRONMapPhase::Awakening:
        {
            // Fase inicial - objetivos básicos
            TArray<EAURACRONObjectiveType> AwakeningObjectives = {
                EAURACRONObjectiveType::ResourceNode,
                EAURACRONObjectiveType::PowerCore,
                EAURACRONObjectiveType::RadiantShrine,
                EAURACRONObjectiveType::WindSanctuary
            };

            int32 RandomIndex = FMath::RandRange(0, AwakeningObjectives.Num() - 1);
            return AwakeningObjectives[RandomIndex];
        }

        case EAURACRONMapPhase::Convergence:
        {
            // Fase média - objetivos mais valiosos
            TArray<EAURACRONObjectiveType> ConvergenceObjectives = {
                EAURACRONObjectiveType::AncientRelic,
                EAURACRONObjectiveType::EnergyConduit,
                EAURACRONObjectiveType::StormCore,
                EAURACRONObjectiveType::ChaosRift
            };

            int32 RandomIndex = FMath::RandRange(0, ConvergenceObjectives.Num() - 1);
            return ConvergenceObjectives[RandomIndex];
        }

        case EAURACRONMapPhase::Intensification:
        {
            // Fase avançada - objetivos poderosos
            TArray<EAURACRONObjectiveType> IntensificationObjectives = {
                EAURACRONObjectiveType::DefenseTower,
                EAURACRONObjectiveType::NexusFragment,
                EAURACRONObjectiveType::FusionCatalyst,
                EAURACRONObjectiveType::TransitionPortal
            };

            int32 RandomIndex = FMath::RandRange(0, IntensificationObjectives.Num() - 1);
            return IntensificationObjectives[RandomIndex];
        }

        case EAURACRONMapPhase::Resolution:
        {
            // Fase final - objetivos decisivos
            TArray<EAURACRONObjectiveType> ResolutionObjectives = {
                EAURACRONObjectiveType::TemporalRift,
                EAURACRONObjectiveType::PrismalNexus,
                EAURACRONObjectiveType::RadiantAnchor,
                EAURACRONObjectiveType::ZephyrAnchor,
                EAURACRONObjectiveType::PurgatoryAnchor
            };

            int32 RandomIndex = FMath::RandRange(0, ResolutionObjectives.Num() - 1);
            return ResolutionObjectives[RandomIndex];
        }

        default:
        {
            // Fallback - objetivo genérico
            return EAURACRONObjectiveType::ResourceNode;
        }
    }
}

FVector AAURACRONPCGObjectiveSystem::FindValidObjectivePosition(EAURACRONObjectiveType ObjectiveType, EAURACRONEnvironmentType& OutEnvironmentType)
{
    // Implementação robusta para encontrar posição válida para objetivo

    UWorld* World = GetWorld();
    if (!World)
    {
        OutEnvironmentType = EAURACRONEnvironmentType::RadiantPlains;
        return FVector::ZeroVector;
    }

    // Determinar ambiente preferido baseado no tipo de objetivo
    TArray<EAURACRONEnvironmentType> PreferredEnvironments;

    switch (ObjectiveType)
    {
        case EAURACRONObjectiveType::RadiantShrine:
        case EAURACRONObjectiveType::RadiantAnchor:
            PreferredEnvironments.Add(EAURACRONEnvironmentType::RadiantPlains);
            break;

        case EAURACRONObjectiveType::WindSanctuary:
        case EAURACRONObjectiveType::StormCore:
        case EAURACRONObjectiveType::ZephyrAnchor:
            PreferredEnvironments.Add(EAURACRONEnvironmentType::ZephyrFirmament);
            break;

        case EAURACRONObjectiveType::PurgatoryShrine:
        case EAURACRONObjectiveType::PurgatoryAnchor:
            PreferredEnvironments.Add(EAURACRONEnvironmentType::PurgatoryRealm);
            break;

        case EAURACRONObjectiveType::ChaosRift:
            PreferredEnvironments.Add(EAURACRONEnvironmentType::CrystalCaverns);
            break;

        default:
            // Objetivos genéricos podem aparecer em qualquer ambiente
            PreferredEnvironments.Add(EAURACRONEnvironmentType::RadiantPlains);
            PreferredEnvironments.Add(EAURACRONEnvironmentType::ZephyrFirmament);
            PreferredEnvironments.Add(EAURACRONEnvironmentType::PurgatoryRealm);
            break;
    }

    // Selecionar ambiente aleatório dos preferidos
    int32 RandomEnvIndex = FMath::RandRange(0, PreferredEnvironments.Num() - 1);
    OutEnvironmentType = PreferredEnvironments[RandomEnvIndex];

    // Gerar posição baseada no ambiente selecionado
    FVector BasePosition = FVector::ZeroVector;

    // Usar medidas do mapa para determinar posições válidas
    switch (OutEnvironmentType)
    {
        case EAURACRONEnvironmentType::RadiantPlains:
        {
            // Posições nas planícies radiantes (área central-sul)
            float X = FMath::RandRange(-5000.0f, 5000.0f);
            float Y = FMath::RandRange(-8000.0f, -2000.0f);
            float Z = FMath::RandRange(100.0f, 300.0f);
            BasePosition = FVector(X, Y, Z);
            break;
        }

        case EAURACRONEnvironmentType::ZephyrFirmament:
        {
            // Posições no firmamento zephyr (área elevada)
            float X = FMath::RandRange(-3000.0f, 3000.0f);
            float Y = FMath::RandRange(2000.0f, 8000.0f);
            float Z = FMath::RandRange(500.0f, 1000.0f);
            BasePosition = FVector(X, Y, Z);
            break;
        }

        case EAURACRONEnvironmentType::PurgatoryRealm:
        {
            // Posições no reino do purgatório (área oeste)
            float X = FMath::RandRange(-8000.0f, -3000.0f);
            float Y = FMath::RandRange(-3000.0f, 3000.0f);
            float Z = FMath::RandRange(50.0f, 200.0f);
            BasePosition = FVector(X, Y, Z);
            break;
        }

        case EAURACRONEnvironmentType::CrystalCaverns:
        {
            // Posições nas cavernas de cristal (área leste)
            float X = FMath::RandRange(3000.0f, 8000.0f);
            float Y = FMath::RandRange(-3000.0f, 3000.0f);
            float Z = FMath::RandRange(200.0f, 600.0f);
            BasePosition = FVector(X, Y, Z);
            break;
        }

        default:
        {
            // Posição central como fallback
            BasePosition = FVector(0.0f, 0.0f, 200.0f);
            break;
        }
    }

    // Verificar se a posição é válida (não está dentro de geometria sólida)
    FHitResult HitResult;
    FVector StartTrace = BasePosition + FVector(0.0f, 0.0f, 1000.0f);
    FVector EndTrace = BasePosition - FVector(0.0f, 0.0f, 1000.0f);

    if (World->LineTraceSingleByChannel(HitResult, StartTrace, EndTrace, ECC_WorldStatic))
    {
        // Ajustar posição para ficar no chão
        BasePosition = HitResult.Location + FVector(0.0f, 0.0f, 100.0f);
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGObjectiveSystem::FindValidObjectivePosition - Found position %s for objective type %d in environment %d"),
           *BasePosition.ToString(), (int32)ObjectiveType, (int32)OutEnvironmentType);

    return BasePosition;
}
