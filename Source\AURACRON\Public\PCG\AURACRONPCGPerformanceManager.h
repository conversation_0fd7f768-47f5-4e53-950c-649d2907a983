// AURACRONPCGPerformanceManager.h
// Sistema de Otimização de Performance para PCG AURACRON - UE 5.6
// Gerencia LOD dinâmico, culling inteligente e streaming de assets

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Data/AURACRONEnums.h"
#include "Engine/World.h"
#include "Components/PrimitiveComponent.h"
#include "Engine/StreamableManager.h"
#include "Engine/AssetManager.h"
#include "RenderingThread.h"
#include "AURACRONPCGPerformanceManager.generated.h"

// Definições de STAT para profiling - UE 5.6
DECLARE_STATS_GROUP(TEXT("AURACRON PCG"), STATGROUP_AURACRONPCG, STATCAT_Advanced);
DECLARE_CYCLE_STAT_EXTERN(TEXT("PCG LOD Update"), STAT_PCGLODUpdate, STATGROUP_AURACRONPCG, );

class AAURACRONPCGEnvironment;
class AAURACRONPCGTrail;
class AAURACRONPCGIsland;
class AAURACRONPCGPrismalFlow;

/**
 * Níveis de LOD para elementos PCG
 */
UENUM(BlueprintType)
enum class EAURACRONPCGLODLevel : uint8
{
    LOD0_Highest    UMETA(DisplayName = "LOD 0 - Highest Quality"),
    LOD1_High       UMETA(DisplayName = "LOD 1 - High Quality"),
    LOD2_Medium     UMETA(DisplayName = "LOD 2 - Medium Quality"),
    LOD3_Low        UMETA(DisplayName = "LOD 3 - Low Quality"),
    LOD4_Culled     UMETA(DisplayName = "LOD 4 - Culled"),
};

// Enum EAURACRONDeviceType definido em Data/AURACRONEnums.h

/**
 * Configurações específicas por tipo de dispositivo
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONDevicePerformanceProfile
{
    GENERATED_BODY()

    /** Tipo do dispositivo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Device")
    EAURACRONDeviceType DeviceType;

    /** Multiplicador de qualidade geral (0.0 - 2.0) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "0.1", ClampMax = "2.0"))
    float QualityMultiplier;

    /** Distância máxima de renderização */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "1000.0", ClampMax = "10000.0"))
    float MaxRenderDistance;

    /** Número máximo de elementos PCG visíveis */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "50", ClampMax = "1000"))
    int32 MaxVisibleElements;

    /** Nível de LOD padrão para este dispositivo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    EAURACRONPCGLODLevel DefaultLODLevel;

    /** Se deve usar culling agressivo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bUseAggressiveCulling;

    /** Multiplicador de volatilidade do Prismal Flow */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "0.1", ClampMax = "2.0"))
    float PrismalFlowVolatilityMultiplier;

    /** Multiplicador de intersecção de trilhos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance", meta = (ClampMin = "0.1", ClampMax = "2.0"))
    float LaneIntersectionMultiplier;

    FAURACRONDevicePerformanceProfile()
        : DeviceType(EAURACRONDeviceType::Unknown)
        , QualityMultiplier(1.0f)
        , MaxRenderDistance(5000.0f)
        , MaxVisibleElements(200)
        , DefaultLODLevel(EAURACRONPCGLODLevel::LOD2_Medium)
        , bUseAggressiveCulling(false)
        , PrismalFlowVolatilityMultiplier(1.0f)
        , LaneIntersectionMultiplier(1.0f)
    {
    }
};

/**
 * Configurações de performance para elementos PCG
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONPCGPerformanceConfig
{
    GENERATED_BODY()

    /** Distâncias para cada nível de LOD */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "0.0"))
    TArray<float> LODDistances;
    
    /** Número máximo de elementos visíveis por tipo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "1"))
    int32 MaxVisibleElements;
    
    /** Intervalo de atualização de LOD em segundos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "0.1", ClampMax = "5.0"))
    float LODUpdateInterval;
    
    /** Se deve usar culling por frustum */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bUseFrustumCulling;
    
    /** Se deve usar occlusion culling */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bUseOcclusionCulling;

    /** Distância máxima para LOD */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "100.0"))
    float MaxLODDistance;

    /** Distância de culling */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "100.0"))
    float CullingDistance;

    /** Se deve habilitar occlusion culling */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bEnableOcclusion;

    /** Se deve habilitar frustum culling */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bEnableFrustumCulling;
    
    /** Distância máxima de renderização */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "1000.0"))
    float MaxRenderDistance;
    
    /** Fator de escala de qualidade baseado na performance */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "0.1", ClampMax = "2.0"))
    float QualityScaleFactor;
    
    FAURACRONPCGPerformanceConfig()
        : MaxVisibleElements(100)
        , LODUpdateInterval(0.5f)
        , bUseFrustumCulling(true)
        , bUseOcclusionCulling(true)
        , MaxRenderDistance(10000.0f)
        , QualityScaleFactor(1.0f)
    {
        // Distâncias padrão para LOD (em cm)
        LODDistances = {1000.0f, 2500.0f, 5000.0f, 7500.0f, 10000.0f};
    }
};

/**
 * Informações de performance de um elemento PCG
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONPCGElementPerformanceInfo
{
    GENERATED_BODY()

    /** Referência ao ator */
    UPROPERTY()
    TWeakObjectPtr<AActor> Actor;
    
    /** Nível de LOD atual */
    UPROPERTY()
    EAURACRONPCGLODLevel CurrentLOD;
    
    /** Distância do viewer */
    UPROPERTY()
    float DistanceToViewer;
    
    /** Se está visível no frustum */
    UPROPERTY()
    bool bIsInFrustum;
    
    /** Se está ocluído */
    UPROPERTY()
    bool bIsOccluded;
    
    /** Prioridade de renderização */
    UPROPERTY()
    float RenderPriority;
    
    /** Última vez que foi atualizado */
    UPROPERTY()
    float LastUpdateTime;

    /** Elemento PCG */
    UPROPERTY()
    TWeakObjectPtr<AActor> PCGElement;

    /** Tempo do último frame */
    UPROPERTY()
    float LastFrameTime;
    
    FAURACRONPCGElementPerformanceInfo()
        : CurrentLOD(EAURACRONPCGLODLevel::LOD0_Highest)
        , DistanceToViewer(0.0f)
        , bIsInFrustum(true)
        , bIsOccluded(false)
        , RenderPriority(1.0f)
        , LastUpdateTime(0.0f)
    {
    }
};

/**
 * Métricas de performance do sistema PCG
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONPCGPerformanceMetrics
{
    GENERATED_BODY()

    /** FPS atual */
    UPROPERTY(BlueprintReadOnly)
    float CurrentFPS;
    
    /** Tempo de frame em ms */
    UPROPERTY(BlueprintReadOnly)
    float FrameTime;
    
    /** Número de elementos renderizados */
    UPROPERTY(BlueprintReadOnly)
    int32 RenderedElements;
    
    /** Número de elementos culled */
    UPROPERTY(BlueprintReadOnly)
    int32 CulledElements;
    
    /** Uso de memória de GPU em MB */
    UPROPERTY(BlueprintReadOnly)
    float GPUMemoryUsage;
    
    /** Número de draw calls */
    UPROPERTY(BlueprintReadOnly)
    int32 DrawCalls;
    
    /** Tempo gasto em culling em ms */
    UPROPERTY(BlueprintReadOnly)
    float CullingTime;

    /** FPS médio */
    UPROPERTY(BlueprintReadOnly)
    float AverageFPS;

    /** FPS mínimo */
    UPROPERTY(BlueprintReadOnly)
    float MinFPS;

    /** FPS máximo */
    UPROPERTY(BlueprintReadOnly)
    float MaxFPS;

    /** Memória usada em MB */
    UPROPERTY(BlueprintReadOnly)
    float UsedMemoryMB;

    /** Memória disponível em MB */
    UPROPERTY(BlueprintReadOnly)
    float AvailableMemoryMB;

    /** Largura do render target */
    UPROPERTY(BlueprintReadOnly)
    int32 RenderTargetWidth;

    /** Altura do render target */
    UPROPERTY(BlueprintReadOnly)
    int32 RenderTargetHeight;

    /** Elementos PCG ativos */
    UPROPERTY(BlueprintReadOnly)
    int32 ActivePCGElements;

    /** Elementos PCG visíveis */
    UPROPERTY(BlueprintReadOnly)
    int32 VisiblePCGElements;

    /** Uso de memória PCG em MB */
    UPROPERTY(BlueprintReadOnly)
    float PCGMemoryUsageMB;

    /** Razão de eficiência PCG */
    UPROPERTY(BlueprintReadOnly)
    float PCGEfficiencyRatio;

    /** Tempo da thread do jogo */
    UPROPERTY(BlueprintReadOnly)
    float GameThreadTime;

    /** Draw calls estimados */
    UPROPERTY(BlueprintReadOnly)
    int32 EstimatedDrawCalls;

    /** Distância média dos elementos */
    UPROPERTY(BlueprintReadOnly)
    float AverageElementDistance;

    FAURACRONPCGPerformanceMetrics()
        : CurrentFPS(60.0f)
        , FrameTime(16.67f)
        , RenderedElements(0)
        , CulledElements(0)
        , GPUMemoryUsage(0.0f)
        , DrawCalls(0)
        , CullingTime(0.0f)
        , AverageFPS(60.0f)
        , MinFPS(60.0f)
        , MaxFPS(60.0f)
        , UsedMemoryMB(0.0f)
        , AvailableMemoryMB(0.0f)
        , RenderTargetWidth(1920)
        , RenderTargetHeight(1080)
        , ActivePCGElements(0)
        , VisiblePCGElements(0)
        , PCGMemoryUsageMB(0.0f)
        , PCGEfficiencyRatio(1.0f)
        , GameThreadTime(0.0f)
        , EstimatedDrawCalls(0)
        , AverageElementDistance(0.0f)
    {
    }
};

/**
 * Gerenciador de performance para sistema PCG AURACRON
 * Implementa LOD dinâmico, culling inteligente e streaming de assets usando UE 5.6
 */
UCLASS()
class AURACRON_API AAURACRONPCGPerformanceManager : public AActor
{
    GENERATED_BODY()

public:
    AAURACRONPCGPerformanceManager();

    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

    // ========================================
    // FUNÇÕES PÚBLICAS DE CONTROLE
    // ========================================
    
    /** Inicializar sistema de performance */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Performance")
    void InitializePerformanceSystem();
    
    /** Registrar elemento PCG para gerenciamento */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Performance")
    void RegisterPCGElement(AActor* Element);
    
    /** Desregistrar elemento PCG */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Performance")
    void UnregisterPCGElement(AActor* Element);
    
    /** Forçar atualização de LOD */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Performance")
    void ForceUpdateLOD();
    
    /** Obter métricas de performance */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Performance")
    FAURACRONPCGPerformanceMetrics GetPerformanceMetrics() const { return CurrentMetrics; }

    /** Obter FPS alvo atual */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Performance")
    float GetTargetFPS() const { return TargetFPS; }
    
    /** Definir configurações de performance */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Performance")
    void SetPerformanceConfig(const FAURACRONPCGPerformanceConfig& NewConfig);
    
    /** Ativar/desativar otimizações automáticas */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Performance")
    void SetAutoOptimizationEnabled(bool bEnabled);

    /** Detectar tipo de dispositivo atual */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Performance")
    EAURACRONDeviceType DetectDeviceType();

    /** Obter perfil de performance do dispositivo atual */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Performance")
    FAURACRONDevicePerformanceProfile GetCurrentDeviceProfile() const { return CurrentDeviceProfile; }

    /** Aplicar configurações específicas do dispositivo */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Performance")
    void ApplyDeviceSpecificSettings();

    /** Obter multiplicador de qualidade baseado no dispositivo */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Performance")
    float GetDeviceQualityMultiplier() const { return CurrentDeviceProfile.QualityMultiplier; }

    /** Verificar se deve usar configurações de Entry device */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Performance")
    bool IsEntryDevice() const { return CurrentDeviceType == EAURACRONDeviceType::Entry; }

    /** Verificar se deve usar configurações de Mid device */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Performance")
    bool IsMidDevice() const { return CurrentDeviceType == EAURACRONDeviceType::Mid; }

    /** Verificar se deve usar configurações de High device */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Performance")
    bool IsHighDevice() const { return CurrentDeviceType == EAURACRONDeviceType::High; }

    /** Obter configurações de qualidade baseadas no dispositivo */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Performance")
    void GetDeviceBasedQualitySettings(float& OutQualityMultiplier, int32& OutMaxElements, float& OutMaxDistance);

protected:
    // ========================================
    // CONFIGURAÇÕES
    // ========================================
    
    /** Configurações de performance */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|Performance")
    FAURACRONPCGPerformanceConfig PerformanceConfig;
    
    /** Se otimizações automáticas estão ativas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|Performance")
    bool bAutoOptimizationEnabled;
    
    /** FPS alvo para otimizações automáticas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|Performance", meta = (ClampMin = "30.0", ClampMax = "120.0"))
    float TargetFPS;
    
    /** Margem de tolerância para FPS */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|Performance", meta = (ClampMin = "1.0", ClampMax = "10.0"))
    float FPSTolerance;

private:
    // ========================================
    // ESTADO INTERNO
    // ========================================
    
    /** Elementos PCG registrados */
    UPROPERTY()
    TArray<FAURACRONPCGElementPerformanceInfo> RegisteredElements;
    
    /** Métricas atuais de performance */
    UPROPERTY()
    FAURACRONPCGPerformanceMetrics CurrentMetrics;
    
    /** Timer para atualização de LOD */
    UPROPERTY()
    FTimerHandle LODUpdateTimer;
    
    /** Timer para coleta de métricas */
    UPROPERTY()
    FTimerHandle MetricsTimer;
    
    /** Streamable manager para assets */
    FStreamableManager StreamableManager;
    
    /** Cache de viewers para culling */
    UPROPERTY()
    TArray<FVector> ViewerLocations;
    
    /** Histórico de FPS para suavização */
    UPROPERTY()
    TArray<float> FPSHistory;

    /** Tipo de dispositivo atual detectado */
    UPROPERTY()
    EAURACRONDeviceType CurrentDeviceType;

    /** Perfil de performance do dispositivo atual */
    UPROPERTY()
    FAURACRONDevicePerformanceProfile CurrentDeviceProfile;

    /** Perfis pré-configurados para cada tipo de dispositivo */
    UPROPERTY()
    TMap<EAURACRONDeviceType, FAURACRONDevicePerformanceProfile> DeviceProfiles;

    /** Se a detecção de dispositivo já foi executada */
    UPROPERTY()
    bool bDeviceDetected;

    // ========================================
    // FUNÇÕES INTERNAS
    // ========================================
    
    /** Atualizar LOD de todos os elementos */
    void UpdateLODLevels();
    
    /** Calcular nível de LOD para um elemento */
    EAURACRONPCGLODLevel CalculateLODLevel(const FAURACRONPCGElementPerformanceInfo& ElementInfo);
    
    /** Aplicar LOD a um elemento */
    void ApplyLODToElement(FAURACRONPCGElementPerformanceInfo& ElementInfo, EAURACRONPCGLODLevel NewLOD);
    
    /** Realizar culling de elementos */
    void PerformCulling();
    
    /** Verificar se elemento está no frustum */
    bool IsElementInFrustum(const FVector& ElementLocation, float ElementRadius);
    
    /** Verificar oclusão de elemento */
    bool IsElementOccluded(const FVector& ElementLocation);
    
    /** Atualizar localizações dos viewers */
    void UpdateViewerLocations();
    
    /** Coletar métricas de performance */
    void CollectPerformanceMetrics();
    
    /** Aplicar otimizações automáticas */
    void ApplyAutoOptimizations();
    
    /** Calcular prioridade de renderização */
    float CalculateRenderPriority(const FAURACRONPCGElementPerformanceInfo& ElementInfo);
    
    /** Otimizar baseado na performance atual */
    void OptimizeBasedOnPerformance();
    
    /** Callback para timer de LOD */
    UFUNCTION()
    void OnLODUpdateTimer();
    
    /** Callback para timer de métricas */
    UFUNCTION()
    void OnMetricsTimer();
    
    /** Limpar elementos inválidos */
    void CleanupInvalidElements();
    
    /** Aplicar configurações de qualidade */
    void ApplyQualitySettings();
    
    /** Gerenciar streaming de assets */
    void ManageAssetStreaming();
    
    /** Calcular distância efetiva considerando importância */
    float CalculateEffectiveDistance(const FAURACRONPCGElementPerformanceInfo& ElementInfo);

    /** Inicializar perfis de dispositivos padrão */
    void InitializeDeviceProfiles();

    /** Detectar tipo de dispositivo baseado em hardware */
    EAURACRONDeviceType DetectDeviceTypeInternal();

    /** Configurar perfil baseado no tipo de dispositivo */
    void ConfigureDeviceProfile(EAURACRONDeviceType DeviceType);

    /** Aplicar configurações específicas para Entry devices */
    void ApplyEntryDeviceSettings();

    /** Aplicar configurações específicas para Mid devices */
    void ApplyMidDeviceSettings();

    /** Aplicar configurações específicas para High devices */
    void ApplyHighDeviceSettings();


};
