// AURACRONPCGNexusIsland.cpp
// Implementação da classe ANexusIsland para o sistema Prismal Flow

#include "PCG/AURACRONPCGNexusIsland.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Components/SphereComponent.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "GameFramework/Character.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/StaticMeshActor.h"
#include "Net/UnrealNetwork.h"
#include "AbilitySystemComponent.h"
#include "AbilitySystemInterface.h"
#include "GameplayEffect.h"
#include "Engine/StreamableManager.h"
#include "Engine/AssetManager.h"
#include "TimerManager.h"
#include "Logging/StructuredLog.h"
#include "Engine/DataTable.h"
#include "Components/AudioComponent.h"
#include "Components/PointLightComponent.h"
#include "Engine/World.h"
#include "Components/StaticMeshComponent.h"
#include "Sound/SoundBase.h"

ANexusIsland::ANexusIsland()
{
    // Configuração padrão
    PrimaryActorTick.bCanEverTick = true;

    // Inicializar propriedades
    PowerIntensity = 2.0f;
    PowerDuration = 30.0f;
    AccumulatedTime = 0.0f;

    // Inicializar o mapa de efeitos ativos
    ActiveFlowManipulationEffects.Empty();

    // Inicializar StreamableManager para carregamento assíncrono
    StreamableManager = &UAssetManager::GetStreamableManager();

    // Configurar componentes específicos da Nexus Island

    // Torre de controle central
    CentralTower = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("CentralTower"));
    CentralTower->SetupAttachment(RootComponent);
    CentralTower->SetRelativeLocation(FVector(0.0f, 0.0f, 200.0f));
    CentralTower->SetRelativeScale3D(FVector(1.0f, 1.0f, 3.0f));
    CentralTower->SetCollisionProfileName(TEXT("BlockAll"));

    // Efeito de energia da torre
    TowerEnergyEffect = CreateDefaultSubobject<UNiagaraComponent>(TEXT("TowerEnergyEffect"));
    TowerEnergyEffect->SetupAttachment(CentralTower);
    TowerEnergyEffect->SetRelativeLocation(FVector(0.0f, 0.0f, 300.0f));

    // Sistema de áudio da torre
    TowerAudioComponent = CreateDefaultSubobject<UAudioComponent>(TEXT("TowerAudioComponent"));
    TowerAudioComponent->SetupAttachment(CentralTower);
    TowerAudioComponent->bAutoActivate = false;

    // Iluminação da torre
    TowerLightComponent = CreateDefaultSubobject<UPointLightComponent>(TEXT("TowerLightComponent"));
    TowerLightComponent->SetupAttachment(CentralTower);
    TowerLightComponent->SetRelativeLocation(FVector(0.0f, 0.0f, 350.0f));
    TowerLightComponent->SetLightColor(FLinearColor(0.2f, 0.8f, 1.0f, 1.0f));
    TowerLightComponent->SetIntensity(2000.0f);
    TowerLightComponent->SetAttenuationRadius(1000.0f);

    // Plataformas defensivas em múltiplos níveis
    for (int32 i = 0; i < 3; ++i)
    {
        FString PlatformName = FString::Printf(TEXT("DefensivePlatform_%d"), i);
        UStaticMeshComponent* Platform = CreateDefaultSubobject<UStaticMeshComponent>(*PlatformName);
        Platform->SetupAttachment(RootComponent);

        // Posicionar em diferentes alturas e distâncias do centro
        float Angle = 2.0f * PI * i / 3.0f;
        float Radius = 300.0f + i * 100.0f;
        float Height = 50.0f + i * 75.0f;

        Platform->SetRelativeLocation(FVector(Radius * FMath::Cos(Angle), Radius * FMath::Sin(Angle), Height));
        Platform->SetRelativeScale3D(FVector(1.0f + i * 0.2f));
        Platform->SetCollisionProfileName(TEXT("BlockAll"));

        DefensivePlatforms.Add(Platform);
    }

    // Geradores de recursos
    ResourceGenerator = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("ResourceGenerator"));
    ResourceGenerator->SetupAttachment(RootComponent);
    ResourceGenerator->SetRelativeLocation(FVector(0.0f, 0.0f, 100.0f));
    ResourceGenerator->SetCollisionProfileName(TEXT("BlockAll"));

    // Efeito do gerador de recursos
    ResourceEffect = CreateDefaultSubobject<UNiagaraComponent>(TEXT("ResourceEffect"));
    ResourceEffect->SetupAttachment(ResourceGenerator);

    // Implementar os 4 setores da Ilha Central Auracron conforme documentação
    InitializeIslandSectors();
}

void ANexusIsland::BeginPlay()
{
    Super::BeginPlay();

    // Configurar Timer otimizado para atualização de efeitos visuais (0.1f para performance)
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().SetTimer(
            VisualUpdateTimerHandle,
            this,
            &ANexusIsland::UpdateVisualEffects,
            0.1f,
            true
        );

        UE_LOGFMT(LogTemp, Log, "ANexusIsland::BeginPlay - Timer de atualização visual configurado para {0}", *GetName());
    }

    // Carregar assets assincronamente
    LoadIslandAssetsAsync();
}

void ANexusIsland::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Limpar timer
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().ClearTimer(VisualUpdateTimerHandle);
    }

    // Limpar handles de carregamento assíncrono
    if (AssetLoadHandle.IsValid())
    {
        AssetLoadHandle->CancelHandle();
    }

    Super::EndPlay(EndPlayReason);
}

void ANexusIsland::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Apenas acumular tempo, efeitos visuais são atualizados via Timer otimizado
    AccumulatedTime += DeltaTime;
}

void ANexusIsland::UpdateVisualEffects()
{
    // Atualizar efeitos visuais (chamado via Timer otimizado a cada 0.1f)
    if (!bIsActive)
    {
        return;
    }

    // Atualizar efeito da torre central
    if (TowerEnergyEffect && IsValid(TowerEnergyEffect))
    {
        float PulseIntensity = 1.0f + 0.5f * FMath::Sin(AccumulatedTime * 2.0f);
        TowerEnergyEffect->SetFloatParameter(FName("Intensity"), PulseIntensity * PowerIntensity);

        // Rotação da torre
        if (CentralTower && IsValid(CentralTower))
        {
            FRotator NewRotation = CentralTower->GetRelativeRotation();
            NewRotation.Yaw += 0.1f * 10.0f; // 0.1f é o intervalo do timer
            CentralTower->SetRelativeRotation(NewRotation);
        }
    }

    // Atualizar iluminação da torre
    if (TowerLightComponent && IsValid(TowerLightComponent))
    {
        float LightPulse = 1.0f + 0.3f * FMath::Sin(AccumulatedTime * 1.8f);
        TowerLightComponent->SetIntensity(2000.0f * LightPulse * PowerIntensity);
    }

    // Atualizar efeito do gerador de recursos
    if (ResourceEffect && IsValid(ResourceEffect))
    {
        float ResourcePulse = 1.0f + 0.3f * FMath::Cos(AccumulatedTime * 1.5f);
        ResourceEffect->SetFloatParameter(FName("GenerationRate"), ResourcePulse);
    }

    // Atualizar plataformas defensivas
    for (int32 i = 0; i < DefensivePlatforms.Num(); ++i)
    {
        if (DefensivePlatforms[i] && IsValid(DefensivePlatforms[i]))
        {
            // Movimento sutil de flutuação
            FVector BaseLocation = DefensivePlatforms[i]->GetRelativeLocation();
            float HeightOffset = 5.0f * FMath::Sin(AccumulatedTime * 0.5f + i * 0.7f);

            // Aplicar offset apenas no Z, mantendo posição base
            float Angle = 2.0f * PI * i / 3.0f;
            float Radius = 300.0f + i * 100.0f;
            float BaseHeight = 50.0f + i * 75.0f;

            FVector NewLocation = FVector(
                Radius * FMath::Cos(Angle),
                Radius * FMath::Sin(Angle),
                BaseHeight + HeightOffset
            );

            DefensivePlatforms[i]->SetRelativeLocation(NewLocation);
        }
    }

    // Atualizar setores da ilha
    UpdateIslandSectors();
}

void ANexusIsland::ApplyIslandEffect(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
    Super::ApplyIslandEffect(OverlappedComponent, OtherActor, OtherComp, OtherBodyIndex, bFromSweep, SweepResult);

    if (!OtherActor)
    {
        UE_LOGFMT(LogTemp, Warning, "ANexusIsland::ApplyIslandEffect - OtherActor é nulo para {0}", *GetName());
        return;
    }

    UE_LOGFMT(LogTemp, Log, "ANexusIsland::ApplyIslandEffect - Ator {0} entrou na área de efeito da ilha {1}", *OtherActor->GetName(), *GetName());

    // Verificar se o ator é um personagem jogável
    ACharacter* Character = Cast<ACharacter>(OtherActor);
    if (Character && Character->IsPlayerControlled())
    {
        UE_LOGFMT(LogTemp, Log, "ANexusIsland::ApplyIslandEffect - Aplicando efeito da Nexus Island ao jogador {0}", *Character->GetName());

        // Conceder habilidade de manipulação do flow
        GrantFlowManipulationAbility(Character);

        // Aplicar efeito visual ao personagem
        if (PowerEffect)
        {
            UNiagaraComponent* EffectComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
                PowerEffect,
                Character->GetRootComponent(),
                NAME_None,
                FVector::ZeroVector,
                FRotator::ZeroRotator,
                EAttachLocation::SnapToTarget,
                true
            );

            if (EffectComponent)
            {
                EffectComponent->SetFloatParameter(FName("Duration"), PowerDuration);
                EffectComponent->SetFloatParameter(FName("Intensity"), PowerIntensity);
                UE_LOGFMT(LogTemp, Log, "ANexusIsland::ApplyIslandEffect - Efeito visual aplicado ao jogador {0} com duração {1} e intensidade {2}", *Character->GetName(), PowerDuration, PowerIntensity);
            }
            else
            {
                UE_LOGFMT(LogTemp, Warning, "ANexusIsland::ApplyIslandEffect - Falha ao criar componente de efeito visual para {0}", *Character->GetName());
            }
        }
        else
        {
            UE_LOGFMT(LogTemp, Warning, "ANexusIsland::ApplyIslandEffect - PowerEffect não configurado para {0}", *GetName());
        }

        // Reproduzir efeito sonoro de ativação
        if (TowerAudioComponent && IsValid(TowerAudioComponent))
        {
            TowerAudioComponent->Play();
            UE_LOGFMT(LogTemp, Log, "ANexusIsland::ApplyIslandEffect - Efeito sonoro reproduzido para {0}", *Character->GetName());
        }
    }
    else
    {
        UE_LOGFMT(LogTemp, Verbose, "ANexusIsland::ApplyIslandEffect - Ator {0} não é um personagem jogável, ignorando", *OtherActor->GetName());
    }
}

void ANexusIsland::GrantFlowManipulationAbility(AActor* TargetActor)
{
    // Implementação da concessão de habilidade de manipulação do flow
    // Integração com o sistema de habilidades do jogo

    if (!TargetActor)
    {
        UE_LOGFMT(LogTemp, Warning, "ANexusIsland::GrantFlowManipulationAbility - TargetActor inválido para ilha {0}", *GetName());
        return;
    }

    // Obter o AbilitySystemComponent do ator alvo
    UAbilitySystemComponent* TargetASC = TargetActor->FindComponentByClass<UAbilitySystemComponent>();
    if (!TargetASC)
    {
        UE_LOGFMT(LogTemp, Warning, "ANexusIsland::GrantFlowManipulationAbility - Ator {0} não possui AbilitySystemComponent", *TargetActor->GetName());
        return;
    }

    UE_LOGFMT(LogTemp, Log, "ANexusIsland::GrantFlowManipulationAbility - Concedendo habilidade de manipulação do flow para {0} por {1} segundos", *TargetActor->GetName(), PowerDuration);

    // Criar contexto do efeito
    FGameplayEffectContextHandle EffectContext = TargetASC->MakeEffectContext();
    EffectContext.AddSourceObject(this);

    // Aplicar efeito temporário de manipulação do fluxo
    // Nota: FlowManipulationEffect deve ser definido como uma propriedade da classe
    if (FlowManipulationEffect)
    {
        FGameplayEffectSpecHandle SpecHandle = TargetASC->MakeOutgoingSpec(FlowManipulationEffect, 1.0f, EffectContext);
        if (SpecHandle.IsValid())
        {
            // Definir duração do efeito
            SpecHandle.Data->SetDuration(PowerDuration, false);

            // Aplicar efeito
            FActiveGameplayEffectHandle ActiveEffectHandle = TargetASC->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());

            // Armazenar o handle do efeito para referência futura
            ActiveFlowManipulationEffects.Add(TargetActor, ActiveEffectHandle);

            UE_LOGFMT(LogTemp, Log, "ANexusIsland::GrantFlowManipulationAbility - Efeito de manipulação do flow aplicado com sucesso para {0}", *TargetActor->GetName());
        }
        else
        {
            UE_LOGFMT(LogTemp, Error, "ANexusIsland::GrantFlowManipulationAbility - Falha ao criar SpecHandle para {0}", *TargetActor->GetName());
        }
    }
    else
    {
        UE_LOGFMT(LogTemp, Warning, "ANexusIsland::GrantFlowManipulationAbility - FlowManipulationEffect não configurado para ilha {0}", *GetName());
    }

    // Configurar timer para remover a habilidade após a duração
    FTimerHandle TimerHandle;
    FTimerDelegate TimerDelegate;
    TimerDelegate.BindUFunction(this, FName("RemoveFlowManipulationAbility"), TargetActor);
    GetWorldTimerManager().SetTimer(TimerHandle, TimerDelegate, PowerDuration, false);

    // Aplicar benefícios específicos dos setores controlados
    ApplySectorBenefits(TargetActor);
}

void ANexusIsland::RemoveFlowManipulationAbility(AActor* TargetActor)
{
    // Remover a habilidade de manipulação do flow
    if (!TargetActor)
    {
        UE_LOGFMT(LogTemp, Warning, "ANexusIsland::RemoveFlowManipulationAbility - TargetActor inválido para ilha {0}", *GetName());
        return;
    }

    UE_LOGFMT(LogTemp, Log, "ANexusIsland::RemoveFlowManipulationAbility - Removendo habilidade de manipulação do flow de {0}", *TargetActor->GetName());

    // Obter o AbilitySystemComponent do ator alvo
    UAbilitySystemComponent* TargetASC = TargetActor->FindComponentByClass<UAbilitySystemComponent>();
    if (!TargetASC)
    {
        UE_LOGFMT(LogTemp, Warning, "ANexusIsland::RemoveFlowManipulationAbility - Ator {0} não possui AbilitySystemComponent", *TargetActor->GetName());
        return;
    }

    // Remover o efeito ativo se existir
    FActiveGameplayEffectHandle* EffectHandle = ActiveFlowManipulationEffects.Find(TargetActor);
    if (EffectHandle && EffectHandle->IsValid())
    {
        TargetASC->RemoveActiveGameplayEffect(*EffectHandle);
        ActiveFlowManipulationEffects.Remove(TargetActor);
        UE_LOGFMT(LogTemp, Log, "ANexusIsland::RemoveFlowManipulationAbility - Efeito de manipulação do flow removido com sucesso de {0}", *TargetActor->GetName());
    }
    else
    {
        UE_LOGFMT(LogTemp, Warning, "ANexusIsland::RemoveFlowManipulationAbility - Nenhum efeito ativo encontrado para {0}", *TargetActor->GetName());
    }

    // Remover benefícios específicos dos setores
    RemoveSectorBenefits(TargetActor);
}

void ANexusIsland::UpdateIslandVisuals()
{
    Super::UpdateIslandVisuals();
    
    // Atualizar visuais específicos da Nexus Island
    if (IslandMaterial)
    {
        // Configurar parâmetros específicos da Nexus Island
        IslandMaterial->SetVectorParameterValue(FName("NexusColor"), FLinearColor(0.2f, 0.8f, 1.0f, 1.0f));
        IslandMaterial->SetScalarParameterValue(FName("NexusPower"), PowerIntensity);
    }
    
    // Atualizar efeito da ilha
    if (IslandEffect)
    {
        IslandEffect->SetFloatParameter(FName("NexusIntensity"), PowerIntensity);
    }
    
    // Atualizar torre central
    if (CentralTower && IsValid(CentralTower))
    {
        // Criar material dinâmico para a torre se necessário
        UMaterialInterface* BaseMaterial = CentralTower->GetMaterial(0);
        if (BaseMaterial)
        {
            UMaterialInstanceDynamic* TowerMaterial = CentralTower->CreateAndSetMaterialInstanceDynamic(0);
            if (TowerMaterial)
            {
                TowerMaterial->SetVectorParameterValue(FName("TowerColor"), FLinearColor(0.1f, 0.6f, 1.0f, 1.0f));
                TowerMaterial->SetScalarParameterValue(FName("TowerPower"), PowerIntensity * 1.5f);
            }
        }
    }
}

void ANexusIsland::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    // Replicar propriedades relevantes
    DOREPLIFETIME(ANexusIsland, PowerIntensity);
    DOREPLIFETIME(ANexusIsland, PowerDuration);
    DOREPLIFETIME(ANexusIsland, FlowManipulationEffect);
    DOREPLIFETIME(ANexusIsland, ControlledSectors);
    DOREPLIFETIME(ANexusIsland, NexusSectorComponent);
    DOREPLIFETIME(ANexusIsland, SanctuarySectorComponent);
    DOREPLIFETIME(ANexusIsland, ArsenalSectorComponent);
    DOREPLIFETIME(ANexusIsland, ChaosSectorComponent);
}

// ========================================
// IMPLEMENTAÇÃO DOS SETORES DA ILHA CENTRAL AURACRON
// Conforme AURACRON_GAME_DESIGN_DOCUMENT_UNIFIED.md
// ========================================

void ANexusIsland::InitializeIslandSectors()
{
    UE_LOGFMT(LogTemp, Log, "ANexusIsland::InitializeIslandSectors - Inicializando os 4 setores da Ilha Central Auracron para {0}", *GetName());

    // Setor Nexus: Geradores de recursos e manipulação do Fluxo
    NexusSectorComponent = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("NexusSectorComponent"));
    NexusSectorComponent->SetupAttachment(RootComponent);
    NexusSectorComponent->SetRelativeLocation(FVector(200.0f, 0.0f, 50.0f));
    NexusSectorComponent->SetRelativeScale3D(FVector(1.5f, 1.5f, 1.0f));
    NexusSectorComponent->SetCollisionProfileName(TEXT("BlockAll"));

    // Efeito visual do Setor Nexus
    NexusSectorEffect = CreateDefaultSubobject<UNiagaraComponent>(TEXT("NexusSectorEffect"));
    NexusSectorEffect->SetupAttachment(NexusSectorComponent);
    NexusSectorEffect->SetRelativeLocation(FVector(0.0f, 0.0f, 100.0f));

    // Setor Santuário: Fontes de cura e amplificadores de visão
    SanctuarySectorComponent = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("SanctuarySectorComponent"));
    SanctuarySectorComponent->SetupAttachment(RootComponent);
    SanctuarySectorComponent->SetRelativeLocation(FVector(-200.0f, 0.0f, 50.0f));
    SanctuarySectorComponent->SetRelativeScale3D(FVector(1.5f, 1.5f, 1.0f));
    SanctuarySectorComponent->SetCollisionProfileName(TEXT("BlockAll"));

    // Efeito visual do Setor Santuário
    SanctuarySectorEffect = CreateDefaultSubobject<UNiagaraComponent>(TEXT("SanctuarySectorEffect"));
    SanctuarySectorEffect->SetupAttachment(SanctuarySectorComponent);
    SanctuarySectorEffect->SetRelativeLocation(FVector(0.0f, 0.0f, 100.0f));

    // Setor Arsenal: Upgrades de armas e potencializadores de habilidades
    ArsenalSectorComponent = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("ArsenalSectorComponent"));
    ArsenalSectorComponent->SetupAttachment(RootComponent);
    ArsenalSectorComponent->SetRelativeLocation(FVector(0.0f, 200.0f, 50.0f));
    ArsenalSectorComponent->SetRelativeScale3D(FVector(1.5f, 1.5f, 1.0f));
    ArsenalSectorComponent->SetCollisionProfileName(TEXT("BlockAll"));

    // Efeito visual do Setor Arsenal
    ArsenalSectorEffect = CreateDefaultSubobject<UNiagaraComponent>(TEXT("ArsenalSectorEffect"));
    ArsenalSectorEffect->SetupAttachment(ArsenalSectorComponent);
    ArsenalSectorEffect->SetRelativeLocation(FVector(0.0f, 0.0f, 100.0f));

    // Setor Caos: Perigos ambientais com recompensas de alto risco
    ChaosSectorComponent = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("ChaosSectorComponent"));
    ChaosSectorComponent->SetupAttachment(RootComponent);
    ChaosSectorComponent->SetRelativeLocation(FVector(0.0f, -200.0f, 50.0f));
    ChaosSectorComponent->SetRelativeScale3D(FVector(1.5f, 1.5f, 1.0f));
    ChaosSectorComponent->SetCollisionProfileName(TEXT("BlockAll"));

    // Efeito visual do Setor Caos
    ChaosSectorEffect = CreateDefaultSubobject<UNiagaraComponent>(TEXT("ChaosSectorEffect"));
    ChaosSectorEffect->SetupAttachment(ChaosSectorComponent);
    ChaosSectorEffect->SetRelativeLocation(FVector(0.0f, 0.0f, 100.0f));

    // Inicializar mapa de setores controlados
    ControlledSectors.Empty();
    ControlledSectors.Add(EIslandSectorType::Nexus, nullptr);
    ControlledSectors.Add(EIslandSectorType::Sanctuary, nullptr);
    ControlledSectors.Add(EIslandSectorType::Arsenal, nullptr);
    ControlledSectors.Add(EIslandSectorType::Chaos, nullptr);

    UE_LOGFMT(LogTemp, Log, "ANexusIsland::InitializeIslandSectors - Setores da Ilha Central Auracron inicializados com sucesso para {0}", *GetName());
}

void ANexusIsland::UpdateIslandSectors()
{
    // Atualizar efeitos visuais dos setores baseado no controle
    if (NexusSectorEffect && IsValid(NexusSectorEffect))
    {
        bool bNexusControlled = ControlledSectors[EIslandSectorType::Nexus] != nullptr;
        float NexusIntensity = bNexusControlled ? PowerIntensity * 1.2f : PowerIntensity * 0.5f;
        NexusSectorEffect->SetFloatParameter(FName("SectorIntensity"), NexusIntensity);
        NexusSectorEffect->SetVectorParameter(FName("SectorColor"), bNexusControlled ? FVector(0.2f, 0.8f, 1.0f) : FVector(0.5f, 0.5f, 0.5f));
    }

    if (SanctuarySectorEffect && IsValid(SanctuarySectorEffect))
    {
        bool bSanctuaryControlled = ControlledSectors[EIslandSectorType::Sanctuary] != nullptr;
        float SanctuaryIntensity = bSanctuaryControlled ? PowerIntensity * 1.2f : PowerIntensity * 0.5f;
        SanctuarySectorEffect->SetFloatParameter(FName("SectorIntensity"), SanctuaryIntensity);
        SanctuarySectorEffect->SetVectorParameter(FName("SectorColor"), bSanctuaryControlled ? FVector(0.8f, 1.0f, 0.2f) : FVector(0.5f, 0.5f, 0.5f));
    }

    if (ArsenalSectorEffect && IsValid(ArsenalSectorEffect))
    {
        bool bArsenalControlled = ControlledSectors[EIslandSectorType::Arsenal] != nullptr;
        float ArsenalIntensity = bArsenalControlled ? PowerIntensity * 1.2f : PowerIntensity * 0.5f;
        ArsenalSectorEffect->SetFloatParameter(FName("SectorIntensity"), ArsenalIntensity);
        ArsenalSectorEffect->SetVectorParameter(FName("SectorColor"), bArsenalControlled ? FVector(1.0f, 0.2f, 0.2f) : FVector(0.5f, 0.5f, 0.5f));
    }

    if (ChaosSectorEffect && IsValid(ChaosSectorEffect))
    {
        bool bChaosControlled = ControlledSectors[EIslandSectorType::Chaos] != nullptr;
        float ChaosIntensity = bChaosControlled ? PowerIntensity * 1.2f : PowerIntensity * 0.5f;
        ChaosSectorEffect->SetFloatParameter(FName("SectorIntensity"), ChaosIntensity);
        ChaosSectorEffect->SetVectorParameter(FName("SectorColor"), bChaosControlled ? FVector(0.8f, 0.2f, 0.8f) : FVector(0.5f, 0.5f, 0.5f));
    }
}

void ANexusIsland::CaptureSector(EIslandSectorType SectorType, AActor* ControllingTeam)
{
    if (!ControllingTeam)
    {
        UE_LOGFMT(LogTemp, Warning, "ANexusIsland::CaptureSector - ControllingTeam é nulo para setor {0}", static_cast<int32>(SectorType));
        return;
    }

    UE_LOGFMT(LogTemp, Log, "ANexusIsland::CaptureSector - Equipe {0} capturou setor {1} da ilha {2}", *ControllingTeam->GetName(), static_cast<int32>(SectorType), *GetName());

    // Atualizar controle do setor
    ControlledSectors[SectorType] = ControllingTeam;

    // Aplicar benefícios específicos do setor
    ApplySectorSpecificBenefits(SectorType, ControllingTeam);

    // Verificar se a equipe tem controle total da ilha
    CheckTotalIslandControl(ControllingTeam);
}

void ANexusIsland::ReleaseSector(EIslandSectorType SectorType)
{
    AActor* PreviousController = ControlledSectors[SectorType];
    if (PreviousController)
    {
        UE_LOGFMT(LogTemp, Log, "ANexusIsland::ReleaseSector - Setor {0} da ilha {1} foi liberado pela equipe {2}", static_cast<int32>(SectorType), *GetName(), *PreviousController->GetName());

        // Remover benefícios específicos do setor
        RemoveSectorSpecificBenefits(SectorType, PreviousController);
    }

    // Limpar controle do setor
    ControlledSectors[SectorType] = nullptr;
}

void ANexusIsland::ApplySectorSpecificBenefits(EIslandSectorType SectorType, AActor* ControllingTeam)
{
    if (!ControllingTeam)
    {
        return;
    }

    switch (SectorType)
    {
        case EIslandSectorType::Nexus:
            // Setor Nexus: Geradores de recursos e manipulação do Fluxo
            UE_LOGFMT(LogTemp, Log, "ANexusIsland::ApplySectorSpecificBenefits - Aplicando benefícios do Setor Nexus para {0}", *ControllingTeam->GetName());
            // Implementar benefícios de geração de recursos e manipulação do Fluxo Prismal
            break;

        case EIslandSectorType::Sanctuary:
            // Setor Santuário: Fontes de cura e amplificadores de visão
            UE_LOGFMT(LogTemp, Log, "ANexusIsland::ApplySectorSpecificBenefits - Aplicando benefícios do Setor Santuário para {0}", *ControllingTeam->GetName());
            // Implementar benefícios de cura e visão ampliada
            break;

        case EIslandSectorType::Arsenal:
            // Setor Arsenal: Upgrades de armas e potencializadores de habilidades
            UE_LOGFMT(LogTemp, Log, "ANexusIsland::ApplySectorSpecificBenefits - Aplicando benefícios do Setor Arsenal para {0}", *ControllingTeam->GetName());
            // Implementar benefícios de upgrade de armas e habilidades
            break;

        case EIslandSectorType::Chaos:
            // Setor Caos: Perigos ambientais com recompensas de alto risco
            UE_LOGFMT(LogTemp, Log, "ANexusIsland::ApplySectorSpecificBenefits - Aplicando benefícios do Setor Caos para {0}", *ControllingTeam->GetName());
            // Implementar benefícios de alto risco/alta recompensa
            break;
    }
}

void ANexusIsland::RemoveSectorSpecificBenefits(EIslandSectorType SectorType, AActor* PreviousController)
{
    if (!PreviousController)
    {
        return;
    }

    switch (SectorType)
    {
        case EIslandSectorType::Nexus:
            UE_LOGFMT(LogTemp, Log, "ANexusIsland::RemoveSectorSpecificBenefits - Removendo benefícios do Setor Nexus de {0}", *PreviousController->GetName());
            // Implementar remoção de benefícios de geração de recursos
            break;

        case EIslandSectorType::Sanctuary:
            UE_LOGFMT(LogTemp, Log, "ANexusIsland::RemoveSectorSpecificBenefits - Removendo benefícios do Setor Santuário de {0}", *PreviousController->GetName());
            // Implementar remoção de benefícios de cura e visão
            break;

        case EIslandSectorType::Arsenal:
            UE_LOGFMT(LogTemp, Log, "ANexusIsland::RemoveSectorSpecificBenefits - Removendo benefícios do Setor Arsenal de {0}", *PreviousController->GetName());
            // Implementar remoção de benefícios de upgrade de armas
            break;

        case EIslandSectorType::Chaos:
            UE_LOGFMT(LogTemp, Log, "ANexusIsland::RemoveSectorSpecificBenefits - Removendo benefícios do Setor Caos de {0}", *PreviousController->GetName());
            // Implementar remoção de benefícios de alto risco
            break;
    }
}

void ANexusIsland::CheckTotalIslandControl(AActor* ControllingTeam)
{
    if (!ControllingTeam)
    {
        return;
    }

    // Verificar se a equipe controla todos os 4 setores
    bool bHasTotalControl = true;
    for (const auto& SectorPair : ControlledSectors)
    {
        if (SectorPair.Value != ControllingTeam)
        {
            bHasTotalControl = false;
            break;
        }
    }

    if (bHasTotalControl)
    {
        UE_LOGFMT(LogTemp, Log, "ANexusIsland::CheckTotalIslandControl - Equipe {0} tem controle total da Ilha Central Auracron {1}", *ControllingTeam->GetName(), *GetName());
        ApplyTotalControlBenefits(ControllingTeam);
    }
}

void ANexusIsland::ApplyTotalControlBenefits(AActor* ControllingTeam)
{
    if (!ControllingTeam)
    {
        return;
    }

    UE_LOGFMT(LogTemp, Log, "ANexusIsland::ApplyTotalControlBenefits - Aplicando benefícios de controle total para equipe {0}", *ControllingTeam->GetName());

    // Benefícios de controle total conforme documentação:
    // - Acesso a todos os benefícios dos setores
    // - Bônus adicional por controle completo
    // - Manipulação total do Fluxo Prismal

    // Aumentar intensidade de poder para controle total
    PowerIntensity = FMath::Min(PowerIntensity * 1.5f, 5.0f); // Cap em 5.0f

    // Reproduzir efeito sonoro de controle total
    if (TowerAudioComponent && IsValid(TowerAudioComponent))
    {
        TowerAudioComponent->SetVolumeMultiplier(1.5f);
        TowerAudioComponent->Play();
    }

    // Intensificar iluminação da torre
    if (TowerLightComponent && IsValid(TowerLightComponent))
    {
        TowerLightComponent->SetIntensity(3000.0f);
        TowerLightComponent->SetLightColor(FLinearColor(1.0f, 0.8f, 0.2f, 1.0f)); // Cor dourada para controle total
    }
}

void ANexusIsland::ApplySectorBenefits(AActor* TargetActor)
{
    if (!TargetActor)
    {
        return;
    }

    UE_LOGFMT(LogTemp, Log, "ANexusIsland::ApplySectorBenefits - Aplicando benefícios dos setores controlados para {0}", *TargetActor->GetName());

    // Aplicar benefícios baseados nos setores controlados pela equipe do jogador
    for (const auto& SectorPair : ControlledSectors)
    {
        if (SectorPair.Value && SectorPair.Value == TargetActor)
        {
            ApplySectorSpecificBenefits(SectorPair.Key, TargetActor);
        }
    }
}

void ANexusIsland::RemoveSectorBenefits(AActor* TargetActor)
{
    if (!TargetActor)
    {
        return;
    }

    UE_LOGFMT(LogTemp, Log, "ANexusIsland::RemoveSectorBenefits - Removendo benefícios dos setores de {0}", *TargetActor->GetName());

    // Remover benefícios de todos os setores
    for (const auto& SectorPair : ControlledSectors)
    {
        if (SectorPair.Value && SectorPair.Value == TargetActor)
        {
            RemoveSectorSpecificBenefits(SectorPair.Key, TargetActor);
        }
    }
}

void ANexusIsland::LoadIslandAssetsAsync()
{
    if (!StreamableManager)
    {
        UE_LOGFMT(LogTemp, Error, "ANexusIsland::LoadIslandAssetsAsync - StreamableManager é nulo para {0}", *GetName());
        return;
    }

    UE_LOGFMT(LogTemp, Log, "ANexusIsland::LoadIslandAssetsAsync - Iniciando carregamento assíncrono de assets para {0}", *GetName());

    // Assets para carregamento assíncrono
    TArray<FSoftObjectPath> AssetsToLoad;

    // Efeitos Niagara para os setores
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/VFX/Islands/NexusIsland/NS_NexusSectorEffect.NS_NexusSectorEffect")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/VFX/Islands/NexusIsland/NS_SanctuarySectorEffect.NS_SanctuarySectorEffect")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/VFX/Islands/NexusIsland/NS_ArsenalSectorEffect.NS_ArsenalSectorEffect")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/VFX/Islands/NexusIsland/NS_ChaosSectorEffect.NS_ChaosSectorEffect")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/VFX/Islands/NexusIsland/NS_TowerEnergyEffect.NS_TowerEnergyEffect")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/VFX/Islands/NexusIsland/NS_PowerEffect.NS_PowerEffect")));

    // Meshes dos setores
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/Meshes/Islands/NexusIsland/SM_NexusSector.SM_NexusSector")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/Meshes/Islands/NexusIsland/SM_SanctuarySector.SM_SanctuarySector")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/Meshes/Islands/NexusIsland/SM_ArsenalSector.SM_ArsenalSector")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/Meshes/Islands/NexusIsland/SM_ChaosSector.SM_ChaosSector")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/Meshes/Islands/NexusIsland/SM_CentralTower.SM_CentralTower")));

    // Materiais
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/Materials/Islands/NexusIsland/M_NexusSectorMaterial.M_NexusSectorMaterial")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/Materials/Islands/NexusIsland/M_TowerMaterial.M_TowerMaterial")));

    // Efeitos sonoros
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/Audio/Islands/NexusIsland/SFX_TowerActivation.SFX_TowerActivation")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/Audio/Islands/NexusIsland/SFX_SectorCapture.SFX_SectorCapture")));

    // Iniciar carregamento assíncrono
    AssetLoadHandle = StreamableManager->RequestAsyncLoad(
        AssetsToLoad,
        FStreamableDelegate::CreateUObject(this, &ANexusIsland::OnAssetsLoaded),
        FStreamableManager::AsyncLoadHighPriority
    );

    if (AssetLoadHandle.IsValid())
    {
        UE_LOGFMT(LogTemp, Log, "ANexusIsland::LoadIslandAssetsAsync - Carregamento assíncrono iniciado com sucesso para {0} assets", AssetsToLoad.Num());
    }
    else
    {
        UE_LOGFMT(LogTemp, Error, "ANexusIsland::LoadIslandAssetsAsync - Falha ao iniciar carregamento assíncrono para {0}", *GetName());
    }
}

void ANexusIsland::OnAssetsLoaded()
{
    UE_LOGFMT(LogTemp, Log, "ANexusIsland::OnAssetsLoaded - Assets carregados com sucesso para {0}", *GetName());

    if (!AssetLoadHandle.IsValid())
    {
        UE_LOGFMT(LogTemp, Warning, "ANexusIsland::OnAssetsLoaded - AssetLoadHandle inválido para {0}", *GetName());
        return;
    }

    // Aplicar assets carregados aos componentes
    ApplyLoadedAssets();

    // Limpar handle
    AssetLoadHandle.Reset();
}

void ANexusIsland::ApplyLoadedAssets()
{
    UE_LOGFMT(LogTemp, Log, "ANexusIsland::ApplyLoadedAssets - Aplicando assets carregados para {0}", *GetName());

    // Aplicar efeitos Niagara aos setores
    if (NexusSectorEffect && IsValid(NexusSectorEffect))
    {
        if (UNiagaraSystem* NexusEffect = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Islands/NexusIsland/NS_NexusSectorEffect.NS_NexusSectorEffect")))
        {
            NexusSectorEffect->SetAsset(NexusEffect);
            UE_LOGFMT(LogTemp, Log, "ANexusIsland::ApplyLoadedAssets - Efeito Niagara do Setor Nexus aplicado");
        }
    }

    if (SanctuarySectorEffect && IsValid(SanctuarySectorEffect))
    {
        if (UNiagaraSystem* SanctuaryEffect = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Islands/NexusIsland/NS_SanctuarySectorEffect.NS_SanctuarySectorEffect")))
        {
            SanctuarySectorEffect->SetAsset(SanctuaryEffect);
            UE_LOGFMT(LogTemp, Log, "ANexusIsland::ApplyLoadedAssets - Efeito Niagara do Setor Santuário aplicado");
        }
    }

    if (ArsenalSectorEffect && IsValid(ArsenalSectorEffect))
    {
        if (UNiagaraSystem* ArsenalEffect = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Islands/NexusIsland/NS_ArsenalSectorEffect.NS_ArsenalSectorEffect")))
        {
            ArsenalSectorEffect->SetAsset(ArsenalEffect);
            UE_LOGFMT(LogTemp, Log, "ANexusIsland::ApplyLoadedAssets - Efeito Niagara do Setor Arsenal aplicado");
        }
    }

    if (ChaosSectorEffect && IsValid(ChaosSectorEffect))
    {
        if (UNiagaraSystem* ChaosEffect = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Islands/NexusIsland/NS_ChaosSectorEffect.NS_ChaosSectorEffect")))
        {
            ChaosSectorEffect->SetAsset(ChaosEffect);
            UE_LOGFMT(LogTemp, Log, "ANexusIsland::ApplyLoadedAssets - Efeito Niagara do Setor Caos aplicado");
        }
    }

    // Aplicar meshes aos componentes
    if (CentralTower && IsValid(CentralTower))
    {
        if (UStaticMesh* TowerMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Game/Meshes/Islands/NexusIsland/SM_CentralTower.SM_CentralTower")))
        {
            CentralTower->SetStaticMesh(TowerMesh);
            UE_LOGFMT(LogTemp, Log, "ANexusIsland::ApplyLoadedAssets - Mesh da Torre Central aplicado");
        }
    }

    // Aplicar efeitos sonoros
    if (TowerAudioComponent && IsValid(TowerAudioComponent))
    {
        if (USoundBase* TowerSound = LoadObject<USoundBase>(nullptr, TEXT("/Game/Audio/Islands/NexusIsland/SFX_TowerActivation.SFX_TowerActivation")))
        {
            TowerAudioComponent->SetSound(TowerSound);
            UE_LOGFMT(LogTemp, Log, "ANexusIsland::ApplyLoadedAssets - Efeito sonoro da Torre aplicado");
        }
    }

    UE_LOGFMT(LogTemp, Log, "ANexusIsland::ApplyLoadedAssets - Todos os assets foram aplicados com sucesso para {0}", *GetName());
}