// AURACRONPCGJungleSystem.cpp
// Implementação do sistema de jungle baseado no LoL

#include "PCG/AURACRONPCGJungleSystem.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "PCG/AURACRONPCGObjectiveSystem.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SphereComponent.h"
#include "Engine/World.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "Kismet/GameplayStatics.h"
// UE 5.6 Modern APIs
#include "Engine/StreamableManager.h"
#include "TimerManager.h"
#include "Logging/StructuredLog.h"
#include "Engine/DataTable.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Net/UnrealNetwork.h"
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/DataLayer/DataLayer.h"
#include "WorldPartition/WorldPartitionSubsystem.h"
#include "Engine/AssetManager.h"
#include "Engine/Engine.h"

AAURACRONPCGJungleSystem::AAURACRONPCGJungleSystem()
    : bAdaptiveSystemEnabled(true)
    , bAutoGenerate(true)
    , CurrentEnvironment(EAURACRONEnvironmentType::RadiantPlains)
    , CurrentMapPhase(EAURACRONMapPhase::Awakening)
{
    // UE 5.6 Modern: Disable Tick, use Timer instead for performance
    PrimaryActorTick.bCanEverTick = false;
    PrimaryActorTick.bStartWithTickEnabled = false;

    // Configurar replicação moderna UE 5.6
    bReplicates = true;
    SetReplicateMovement(false);
    bNetLoadOnClient = true;
    NetPriority = 2.0f; // Higher priority for jungle system
    SetNetUpdateFrequency(10.0f); // UE 5.6: usar SetNetUpdateFrequency()
    SetMinNetUpdateFrequency(2.0f); // UE 5.6: usar SetMinNetUpdateFrequency()

    // Criar componente raiz
    RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootComponent"));

    // Initialize StreamableManager for async loading
    StreamableManager = &UAssetManager::GetStreamableManager();

    // Initialize adaptive system config with robust defaults
    AdaptiveSystemConfig.AdaptationInterval = 30.0f; // 30 seconds
    AdaptiveSystemConfig.InvasionThreshold = 3;
    AdaptiveSystemConfig.ObjectiveThreshold = 2;
    AdaptiveSystemConfig.bEnablePredictiveAnalysis = true;
    AdaptiveSystemConfig.bEnablePerformanceOptimization = true;

    // Initialize timer handles
    UpdateTimerHandle = FTimerHandle();
    AdaptiveAnalysisTimerHandle = FTimerHandle();
}

// UE 5.6 Modern Replication
void AAURACRONPCGJungleSystem::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    // Replicate jungle camps data
    DOREPLIFETIME(AAURACRONPCGJungleSystem, JungleCamps);
    DOREPLIFETIME(AAURACRONPCGJungleSystem, CurrentEnvironment);
    DOREPLIFETIME(AAURACRONPCGJungleSystem, CurrentMapPhase);
    DOREPLIFETIME(AAURACRONPCGJungleSystem, bAdaptiveSystemEnabled);

    // Replicate adaptive data with conditions
    DOREPLIFETIME_CONDITION(AAURACRONPCGJungleSystem, AdaptiveData, COND_OwnerOnly);
}

void AAURACRONPCGJungleSystem::BeginPlay()
{
    Super::BeginPlay();

    // UE 5.6 Modern: Robust validation
    if (!IsValid(GetWorld()))
    {
        UE_LOGFMT(LogTemp, Error, "AAURACRONPCGJungleSystem::BeginPlay - Invalid World reference");
        return;
    }

    if (!StreamableManager)
    {
        UE_LOGFMT(LogTemp, Error, "AAURACRONPCGJungleSystem::BeginPlay - StreamableManager not initialized");
        return;
    }

    // Gerar sistema apenas no servidor com validações robustas
    if (HasAuthority() && bAutoGenerate)
    {
        // Delay pequeno para garantir que outros sistemas estejam prontos
        FTimerHandle GenerationTimer;
        GetWorld()->GetTimerManager().SetTimer(GenerationTimer, this,
            &AAURACRONPCGJungleSystem::GenerateJungleCamps, 1.5f, false);

        // UE 5.6 Modern: Start update timer instead of Tick (0.1f for performance)
        GetWorld()->GetTimerManager().SetTimer(UpdateTimerHandle, this,
            &AAURACRONPCGJungleSystem::UpdateJungleSystem, 0.1f, true);

        // Start adaptive analysis timer (every 5 seconds)
        GetWorld()->GetTimerManager().SetTimer(AdaptiveAnalysisTimerHandle, this,
            &AAURACRONPCGJungleSystem::PerformAdaptiveAnalysis, 5.0f, true);
    }
}

// UE 5.6 Modern: Replace Tick with Timer-based update for performance
void AAURACRONPCGJungleSystem::UpdateJungleSystem()
{
    // Robust validation
    if (!HasAuthority() || !IsValid(GetWorld()))
    {
        return;
    }

    const float DeltaTime = 0.1f; // Fixed delta time for consistent behavior

    // Atualizar timers de respawn com validações robustas
    for (int32 i = 0; i < JungleCamps.Num(); ++i)
    {
        if (!JungleCamps.IsValidIndex(i))
        {
            continue;
        }

        FAURACRONJungleCampInfo& CampInfo = JungleCamps[i];

        if (!CampInfo.bIsActive && CampInfo.TimeUntilRespawn > 0.0f)
        {
            CampInfo.TimeUntilRespawn -= DeltaTime;

            if (CampInfo.TimeUntilRespawn <= 0.0f)
            {
                OnCampRespawn(i);
            }
        }
    }
}

// UE 5.6 Modern: Separate adaptive analysis function
void AAURACRONPCGJungleSystem::PerformAdaptiveAnalysis()
{
    if (!HasAuthority() || !bAdaptiveSystemEnabled)
    {
        return;
    }

    // Atualizar sistema de IA adaptativa com throttling
    const float DeltaTime = 5.0f; // Called every 5 seconds
    UpdateAdaptiveData(DeltaTime);
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES PÚBLICAS
// ========================================

void AAURACRONPCGJungleSystem::GenerateJungleCamps()
{
    // UE 5.6 Modern: Robust validation
    if (!HasAuthority())
    {
        UE_LOGFMT(LogTemp, Warning, "GenerateJungleCamps called on client - ignoring");
        return;
    }

    if (!IsValid(GetWorld()))
    {
        UE_LOGFMT(LogTemp, Error, "GenerateJungleCamps - Invalid World reference");
        return;
    }

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGJungleSystem: Gerando jungle camps baseados no LoL com IA adaptativa");

    // Limpar camps anteriores com validação robusta
    for (const auto& EnvPair : CampMeshesByEnvironment)
    {
        ClearCampsForEnvironment(EnvPair.Key);
    }

    // Inicializar informações dos camps
    InitializeCampInfos();

    // Gerar camps para todos os 3 ambientes com validação
    for (int32 EnvIndex = 0; EnvIndex < 3; ++EnvIndex)
    {
        EAURACRONEnvironmentType Environment = static_cast<EAURACRONEnvironmentType>(EnvIndex);
        if (Environment != EAURACRONEnvironmentType::RadiantPlains &&
            Environment != EAURACRONEnvironmentType::ZephyrFirmament &&
            Environment != EAURACRONEnvironmentType::PurgatoryRealm)
        {
            UE_LOGFMT(LogTemp, Warning, "Invalid environment type: {0}", EnvIndex);
            continue;
        }
        GenerateCampsForEnvironment(Environment);
    }

    // Iniciar com Radiant Plains ativo
    UpdateForEnvironment(EAURACRONEnvironmentType::RadiantPlains);

    // Inicializar sistema de IA adaptativa
    InitializeAdaptiveSystem();

    // Configurar integração com World Partition e Data Layers usando APIs modernas UE 5.6
    ConfigureWorldPartitionStreaming();
    AssociateWithDataLayer();

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGJungleSystem: Gerados {0} jungle camps para 3 ambientes com sistema adaptativo", JungleCamps.Num());
}

void AAURACRONPCGJungleSystem::GenerateCampsForEnvironment(EAURACRONEnvironmentType Environment)
{
    // UE 5.6 Modern: Robust validation
    if (!HasAuthority())
    {
        UE_LOGFMT(LogTemp, Warning, "GenerateCampsForEnvironment called on client - ignoring");
        return;
    }

    if (!IsValid(GetWorld()))
    {
        UE_LOGFMT(LogTemp, Error, "GenerateCampsForEnvironment - Invalid World reference");
        return;
    }

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGJungleSystem: Gerando camps para ambiente {0} com sistema adaptativo", static_cast<int32>(Environment));

    // Validar se temos camps para gerar
    if (JungleCamps.Num() == 0)
    {
        UE_LOGFMT(LogTemp, Warning, "No jungle camps to generate for environment {0}", static_cast<int32>(Environment));
        return;
    }

    // Criar componentes visuais para este ambiente com validação robusta
    for (int32 i = 0; i < JungleCamps.Num(); ++i)
    {
        if (!JungleCamps.IsValidIndex(i))
        {
            UE_LOGFMT(LogTemp, Warning, "Invalid camp index {0} during generation", i);
            continue;
        }
        CreateCamp(i, Environment);
    }

    // Aplicar características específicas do ambiente com validação
    for (int32 i = 0; i < JungleCamps.Num(); ++i)
    {
        if (!JungleCamps.IsValidIndex(i))
        {
            continue;
        }
        ApplyEnvironmentCharacteristics(i, Environment);
    }

    UE_LOGFMT(LogTemp, Log, "Successfully generated {0} camps for environment {1}", JungleCamps.Num(), static_cast<int32>(Environment));
}

TArray<FAURACRONJungleCampInfo> AAURACRONPCGJungleSystem::GetCampsByType(EAURACRONJungleCampType CampType) const
{
    TArray<FAURACRONJungleCampInfo> FilteredCamps;
    
    for (const FAURACRONJungleCampInfo& Camp : JungleCamps)
    {
        if (Camp.CampType == CampType)
        {
            FilteredCamps.Add(Camp);
        }
    }
    
    return FilteredCamps;
}

TArray<FAURACRONJungleCampInfo> AAURACRONPCGJungleSystem::GetCampsBySide(int32 MapSide) const
{
    TArray<FAURACRONJungleCampInfo> FilteredCamps;
    
    for (const FAURACRONJungleCampInfo& Camp : JungleCamps)
    {
        if (Camp.MapSide == MapSide)
        {
            FilteredCamps.Add(Camp);
        }
    }
    
    return FilteredCamps;
}

void AAURACRONPCGJungleSystem::ClearCamp(int32 CampIndex)
{
    // UE 5.6 Modern: Robust validation
    if (!HasAuthority())
    {
        UE_LOGFMT(LogTemp, Warning, "ClearCamp called on client - ignoring");
        return;
    }

    if (!JungleCamps.IsValidIndex(CampIndex))
    {
        UE_LOGFMT(LogTemp, Warning, "Invalid camp index {0} in ClearCamp", CampIndex);
        return;
    }

    FAURACRONJungleCampInfo& Camp = JungleCamps[CampIndex];

    if (Camp.bIsActive)
    {
        Camp.bIsActive = false;
        Camp.TimeUntilRespawn = Camp.RespawnTime;

        UE_LOGFMT(LogTemp, Log, "AURACRONPCGJungleSystem: Camp {0} cleared, respawn in {1}s",
            CampIndex, Camp.RespawnTime);

        // Atualizar visibilidade com RPC para clientes
        UpdateCampVisibility();

        // Notify clients via RPC
        if (HasAuthority())
        {
            MulticastOnCampCleared(CampIndex, Camp.RespawnTime);
        }
    }
}

bool AAURACRONPCGJungleSystem::IsCampAvailable(int32 CampIndex) const
{
    if (CampIndex < 0 || CampIndex >= JungleCamps.Num())
    {
        return false;
    }
    
    return JungleCamps[CampIndex].bIsActive;
}

void AAURACRONPCGJungleSystem::UpdateForEnvironment(EAURACRONEnvironmentType NewEnvironment)
{
    if (CurrentEnvironment == NewEnvironment)
    {
        return;
    }

    // UE 5.6 Modern: Validate environment change
    if (NewEnvironment != EAURACRONEnvironmentType::RadiantPlains &&
        NewEnvironment != EAURACRONEnvironmentType::ZephyrFirmament &&
        NewEnvironment != EAURACRONEnvironmentType::PurgatoryRealm)
    {
        UE_LOGFMT(LogTemp, Warning, "Invalid environment type: {0}", static_cast<int32>(NewEnvironment));
        return;
    }

    CurrentEnvironment = NewEnvironment;
    UpdateCampVisibility();

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGJungleSystem: Atualizado para ambiente {0} com sistema adaptativo", static_cast<int32>(NewEnvironment));

    // Notify clients via RPC
    if (HasAuthority())
    {
        MulticastOnEnvironmentChanged(NewEnvironment);
    }
}

void AAURACRONPCGJungleSystem::UpdateForMapPhase(EAURACRONMapPhase MapPhase)
{
    if (CurrentMapPhase != MapPhase)
    {
        CurrentMapPhase = MapPhase;
        ApplyMapPhaseEffects();

        UE_LOGFMT(LogTemp, Log, "AURACRONPCGJungleSystem: Atualizado para fase {0} com efeitos adaptativos", static_cast<int32>(MapPhase));

        // Notify clients via RPC
        if (HasAuthority())
        {
            MulticastOnMapPhaseChanged(MapPhase);
        }
    }
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES INTERNAS
// ========================================

void AAURACRONPCGJungleSystem::InitializeCampInfos()
{
    JungleCamps.Empty();
    
    // BUFF CAMPS (2 camps - equivalentes ao Blue/Red Buff do LoL)
    
    // Radiant Essence (Blue Buff equivalent) - Team 1 side
    FAURACRONJungleCampInfo RadiantEssence = GetDefaultCampConfig(EAURACRONJungleCampType::RadiantEssence);
    RadiantEssence.MapSide = 0; // Team 1
    JungleCamps.Add(RadiantEssence);
    
    // Chaos Essence (Red Buff equivalent) - Team 2 side
    FAURACRONJungleCampInfo ChaosEssence = GetDefaultCampConfig(EAURACRONJungleCampType::ChaosEssence);
    ChaosEssence.MapSide = 1; // Team 2
    JungleCamps.Add(ChaosEssence);
    
    // CAMPS NORMAIS (10 camps distribuídos simetricamente)
    
    // Team 1 Jungle (5 camps)
    for (int32 i = 0; i < 5; ++i)
    {
        EAURACRONJungleCampType CampTypes[] = {
            EAURACRONJungleCampType::StoneGuardians,
            EAURACRONJungleCampType::PrismalToad,
            EAURACRONJungleCampType::SpectralPack,
            EAURACRONJungleCampType::WindSpirits,
            EAURACRONJungleCampType::FluxCrawler
        };
        
        FAURACRONJungleCampInfo Camp = GetDefaultCampConfig(CampTypes[i]);
        Camp.MapSide = 0; // Team 1
        JungleCamps.Add(Camp);
    }
    
    // Team 2 Jungle (5 camps - espelho)
    for (int32 i = 0; i < 5; ++i)
    {
        EAURACRONJungleCampType CampTypes[] = {
            EAURACRONJungleCampType::StoneGuardians,
            EAURACRONJungleCampType::PrismalToad,
            EAURACRONJungleCampType::SpectralPack,
            EAURACRONJungleCampType::WindSpirits,
            EAURACRONJungleCampType::FluxCrawler
        };
        
        FAURACRONJungleCampInfo Camp = GetDefaultCampConfig(CampTypes[i]);
        Camp.MapSide = 1; // Team 2
        JungleCamps.Add(Camp);
    }
    
    // Calcular posições para todos os ambientes
    for (int32 i = 0; i < JungleCamps.Num(); ++i)
    {
        for (int32 EnvIndex = 0; EnvIndex < 3; ++EnvIndex)
        {
            EAURACRONEnvironmentType Environment = static_cast<EAURACRONEnvironmentType>(EnvIndex);
            FVector Position = CalculateCampPosition(JungleCamps[i].CampType, JungleCamps[i].MapSide, Environment);
            JungleCamps[i].PositionsByEnvironment.Add(Environment, Position);
        }
    }
}

FAURACRONJungleCampInfo AAURACRONPCGJungleSystem::GetDefaultCampConfig(EAURACRONJungleCampType CampType)
{
    FAURACRONJungleCampInfo Config;
    Config.CampType = CampType;

    switch (CampType)
    {
    case EAURACRONJungleCampType::RadiantEssence:
        Config.CampRadius = FAURACRONMapDimensions::BUFF_CAMP_RADIUS_CM;
        Config.RespawnTime = FAURACRONMapDimensions::BUFF_CAMP_RESPAWN_TIME;
        Config.bIsBuffCamp = true;
        Config.DifficultyLevel = 3;
        Config.Rewards.Add(TEXT("ManaRegen"), 50.0f);
        Config.Rewards.Add(TEXT("CooldownReduction"), 20.0f);
        Config.Rewards.Add(TEXT("Experience"), 200.0f);
        break;

    case EAURACRONJungleCampType::ChaosEssence:
        Config.CampRadius = FAURACRONMapDimensions::BUFF_CAMP_RADIUS_CM;
        Config.RespawnTime = FAURACRONMapDimensions::BUFF_CAMP_RESPAWN_TIME;
        Config.bIsBuffCamp = true;
        Config.DifficultyLevel = 3;
        Config.Rewards.Add(TEXT("AttackDamage"), 30.0f);
        Config.Rewards.Add(TEXT("SlowOnHit"), 15.0f);
        Config.Rewards.Add(TEXT("Experience"), 200.0f);
        break;

    case EAURACRONJungleCampType::StoneGuardians:
        Config.CampRadius = FAURACRONMapDimensions::JUNGLE_CAMP_RADIUS_CM;
        Config.RespawnTime = FAURACRONMapDimensions::NORMAL_CAMP_RESPAWN_TIME;
        Config.DifficultyLevel = 2;
        Config.Rewards.Add(TEXT("Gold"), 100.0f);
        Config.Rewards.Add(TEXT("Experience"), 150.0f);
        break;

    case EAURACRONJungleCampType::PrismalToad:
        Config.CampRadius = FAURACRONMapDimensions::JUNGLE_CAMP_RADIUS_CM;
        Config.RespawnTime = FAURACRONMapDimensions::NORMAL_CAMP_RESPAWN_TIME;
        Config.DifficultyLevel = 2;
        Config.Rewards.Add(TEXT("Gold"), 80.0f);
        Config.Rewards.Add(TEXT("Experience"), 120.0f);
        break;

    case EAURACRONJungleCampType::SpectralPack:
        Config.CampRadius = FAURACRONMapDimensions::JUNGLE_CAMP_RADIUS_CM;
        Config.RespawnTime = FAURACRONMapDimensions::NORMAL_CAMP_RESPAWN_TIME;
        Config.DifficultyLevel = 1;
        Config.Rewards.Add(TEXT("Gold"), 60.0f);
        Config.Rewards.Add(TEXT("Experience"), 100.0f);
        break;

    case EAURACRONJungleCampType::WindSpirits:
        Config.CampRadius = FAURACRONMapDimensions::JUNGLE_CAMP_RADIUS_CM;
        Config.RespawnTime = FAURACRONMapDimensions::NORMAL_CAMP_RESPAWN_TIME;
        Config.DifficultyLevel = 2;
        Config.Rewards.Add(TEXT("Gold"), 90.0f);
        Config.Rewards.Add(TEXT("Experience"), 130.0f);
        break;

    case EAURACRONJungleCampType::FluxCrawler:
        Config.CampRadius = FAURACRONMapDimensions::JUNGLE_CAMP_RADIUS_CM * 0.8f; // Menor
        Config.RespawnTime = 150.0f; // 2.5 minutos (como Scuttle)
        Config.DifficultyLevel = 1;
        Config.Rewards.Add(TEXT("Gold"), 70.0f);
        Config.Rewards.Add(TEXT("Experience"), 80.0f);
        Config.Rewards.Add(TEXT("Vision"), 120.0f); // Fornece visão como Scuttle
        break;
    }

    return Config;
}

FVector AAURACRONPCGJungleSystem::CalculateCampPosition(EAURACRONJungleCampType CampType, int32 MapSide, EAURACRONEnvironmentType Environment)
{
    FVector BasePosition = FAURACRONMapDimensions::MAP_CENTER;
    float EnvironmentHeight = 0.0f;

    // Obter altura do ambiente
    switch (Environment)
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        EnvironmentHeight = FAURACRONMapDimensions::RADIANT_PLAINS_HEIGHT_CM;
        break;
    case EAURACRONEnvironmentType::ZephyrFirmament:
        EnvironmentHeight = FAURACRONMapDimensions::ZEPHYR_FIRMAMENT_HEIGHT_CM;
        break;
    case EAURACRONEnvironmentType::PurgatoryRealm:
        EnvironmentHeight = FAURACRONMapDimensions::PURGATORY_REALM_HEIGHT_CM;
        break;
    }

    // Calcular posição baseada no tipo de camp e lado do mapa
    FVector Position = BasePosition;
    Position.Z = EnvironmentHeight;

    switch (CampType)
    {
    case EAURACRONJungleCampType::RadiantEssence:
        // Sempre no lado Team 1 (posição fixa do LoL)
        Position += FVector(FAURACRONMapDimensions::RADIANT_ESSENCE_X, FAURACRONMapDimensions::RADIANT_ESSENCE_Y, 0.0f);
        break;

    case EAURACRONJungleCampType::ChaosEssence:
        // Sempre no lado Team 2 (posição fixa do LoL)
        Position += FVector(FAURACRONMapDimensions::CHAOS_ESSENCE_X, FAURACRONMapDimensions::CHAOS_ESSENCE_Y, 0.0f);
        break;

    case EAURACRONJungleCampType::StoneGuardians:
        // Krugs equivalent - cantos do mapa
        if (MapSide == 0) // Team 1
            Position += FVector(-3500.0f, -1500.0f, 0.0f);
        else // Team 2
            Position += FVector(3500.0f, 1500.0f, 0.0f);
        break;

    case EAURACRONJungleCampType::PrismalToad:
        // Gromp equivalent - próximo aos buffs
        if (MapSide == 0) // Team 1
            Position += FVector(-2800.0f, 800.0f, 0.0f);
        else // Team 2
            Position += FVector(2800.0f, -800.0f, 0.0f);
        break;

    case EAURACRONJungleCampType::SpectralPack:
        // Wolves equivalent - entre lanes
        if (MapSide == 0) // Team 1
            Position += FVector(-1800.0f, -3200.0f, 0.0f);
        else // Team 2
            Position += FVector(1800.0f, 3200.0f, 0.0f);
        break;

    case EAURACRONJungleCampType::WindSpirits:
        // Raptors equivalent - jungle central
        if (MapSide == 0) // Team 1
            Position += FVector(-1200.0f, 2500.0f, 0.0f);
        else // Team 2
            Position += FVector(1200.0f, -2500.0f, 0.0f);
        break;

    case EAURACRONJungleCampType::FluxCrawler:
        // Scuttle equivalent - no Prismal Flow (river)
        if (MapSide == 0) // Superior
            Position += FVector(0.0f, 3000.0f, 0.0f);
        else // Inferior
            Position += FVector(0.0f, -3000.0f, 0.0f);
        break;
    }

    // Aplicar variações específicas do ambiente
    if (Environment == EAURACRONEnvironmentType::ZephyrFirmament)
    {
        // Adicionar variação de altura para plataformas
        Position.Z += FMath::RandRange(-200.0f, 500.0f);
    }
    else if (Environment == EAURACRONEnvironmentType::PurgatoryRealm)
    {
        // Adicionar deslocamento lateral para túneis
        FVector Offset = FVector(FMath::RandRange(-300.0f, 300.0f), FMath::RandRange(-300.0f, 300.0f), 0.0f);
        Position += Offset;
    }

    return Position;
}

// Implementação das funções restantes
void AAURACRONPCGJungleSystem::CreateCamp(int32 CampIndex, EAURACRONEnvironmentType Environment)
{
    if (CampIndex < 0 || CampIndex >= JungleCamps.Num())
    {
        return;
    }
    
    // Obter informações do camp
    FAURACRONJungleCampInfo& CampInfo = JungleCamps[CampIndex];
    
    // Calcular posição do camp
    FVector CampPosition = CalculateCampPosition(CampInfo.CampType, CampInfo.MapSide, Environment);
    
    // Criar componente de colisão
    if (CampCollisionComponents.Num() <= CampIndex)
    {
        USphereComponent* CollisionComponent = NewObject<USphereComponent>(this);
        CollisionComponent->RegisterComponent();
        CollisionComponent->SetSphereRadius(CampInfo.CampRadius);
        CollisionComponent->SetCollisionProfileName(TEXT("OverlapAll"));
        CollisionComponent->SetGenerateOverlapEvents(true);
        CollisionComponent->SetWorldLocation(CampPosition);
        CollisionComponent->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepWorldTransform);
        
        CampCollisionComponents.Add(CollisionComponent);
    }
    else
    {
        CampCollisionComponents[CampIndex]->SetWorldLocation(CampPosition);
        CampCollisionComponents[CampIndex]->SetSphereRadius(CampInfo.CampRadius);
    }
    
    // Criar mesh visual do camp
    UStaticMesh* CampMesh = GetCampMesh(CampInfo.CampType, Environment);
    if (CampMesh)
    {
        // Verificar se já existe um array para este ambiente
        if (!CampMeshesByEnvironment.Contains(Environment))
        {
            CampMeshesByEnvironment.Add(Environment, FAURACRONMeshComponentArray());
        }
        
        // Verificar se já existe um componente para este camp
        FAURACRONMeshComponentArray& MeshArray = CampMeshesByEnvironment[Environment];
        if (MeshArray.MeshComponents.Num() <= CampIndex)
        {
            UStaticMeshComponent* MeshComponent = NewObject<UStaticMeshComponent>(this);
            MeshComponent->RegisterComponent();
            MeshComponent->SetStaticMesh(CampMesh);
            MeshComponent->SetWorldLocation(CampPosition);
            MeshComponent->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepWorldTransform);
            
            MeshArray.MeshComponents.Add(MeshComponent);
        }
        else
        {
            MeshArray.MeshComponents[CampIndex]->SetStaticMesh(CampMesh);
            MeshArray.MeshComponents[CampIndex]->SetWorldLocation(CampPosition);
        }
    }
    
    // Aplicar características específicas do ambiente
    ApplyEnvironmentCharacteristics(CampIndex, Environment);

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::CreateCamp - Camp {0} criado na posição {1} para ambiente {2}",
           CampIndex, CampPosition.ToString(), static_cast<int32>(Environment));
}

void AAURACRONPCGJungleSystem::ApplyEnvironmentCharacteristics(int32 CampIndex, EAURACRONEnvironmentType Environment)
{
    if (CampIndex < 0 || CampIndex >= JungleCamps.Num())
    {
        return;
    }
    
    FAURACRONJungleCampInfo& CampInfo = JungleCamps[CampIndex];
    
    // Aplicar características específicas do ambiente
    switch (Environment)
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        // Camps mais visíveis e com recompensas padrão
        CampInfo.AdaptiveRewardMultiplier = 1.0f;
        break;
        
    case EAURACRONEnvironmentType::ZephyrFirmament:
        // Camps mais difíceis mas com recompensas maiores
        CampInfo.AdaptiveDamageMultiplier = 1.2f;
        CampInfo.AdaptiveRewardMultiplier = 1.3f;
        break;
        
    case EAURACRONEnvironmentType::PurgatoryRealm:
        // Camps muito difíceis com recompensas significativas
        CampInfo.AdaptiveDamageMultiplier = 1.5f;
        CampInfo.AdaptiveHealthMultiplier = 1.3f;
        CampInfo.AdaptiveRewardMultiplier = 1.5f;
        break;
    }
    
    // Aplicar comportamento adaptativo inicial
    ApplyAdaptiveBehavior(CampIndex, CampInfo.CurrentBehavior);
}

void AAURACRONPCGJungleSystem::OnCampRespawn(int32 CampIndex)
{
    // UE 5.6 Modern: Robust validation
    if (!JungleCamps.IsValidIndex(CampIndex))
    {
        UE_LOGFMT(LogTemp, Warning, "Invalid camp index {0} in OnCampRespawn", CampIndex);
        return;
    }

    FAURACRONJungleCampInfo& CampInfo = JungleCamps[CampIndex];
    CampInfo.bIsActive = true;
    CampInfo.TimeUntilRespawn = 0.0f;

    // Recalcular comportamento adaptativo ao respawnar com validação
    if (bAdaptiveSystemEnabled)
    {
        EAURACRONJungleAdaptiveBehavior NewBehavior = CalculateAdaptiveBehavior(CampIndex);
        ApplyAdaptiveBehavior(CampIndex, NewBehavior);
    }

    UpdateCampVisibility();

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::OnCampRespawn - Camp {0} respawned com comportamento adaptativo {1}",
               CampIndex, static_cast<int32>(CampInfo.CurrentBehavior));

    // Notify clients via RPC
    if (HasAuthority())
    {
        MulticastOnCampRespawned(CampIndex, CampInfo.CurrentBehavior);
    }
}

void AAURACRONPCGJungleSystem::UpdateCampVisibility()
{
    // Atualizar visibilidade de todos os camps em todos os ambientes
    for (EAURACRONEnvironmentType Environment : TEnumRange<EAURACRONEnvironmentType>())
    {
        if (CampMeshesByEnvironment.Contains(Environment))
        {
            FAURACRONMeshComponentArray& MeshArray = CampMeshesByEnvironment[Environment];
            
            for (int32 i = 0; i < MeshArray.MeshComponents.Num(); ++i)
            {
                if (i < JungleCamps.Num() && MeshArray.MeshComponents[i])
                {
                    // Mostrar apenas se o ambiente atual corresponder e o camp estiver ativo
                    bool bShouldBeVisible = (Environment == CurrentEnvironment && JungleCamps[i].bIsActive);
                    MeshArray.MeshComponents[i]->SetVisibility(bShouldBeVisible);
                }
            }
        }
    }
}

void AAURACRONPCGJungleSystem::ClearCampsForEnvironment(EAURACRONEnvironmentType Environment)
{
    // UE 5.6 Modern: Robust validation and cleanup
    if (!CampMeshesByEnvironment.Contains(Environment))
    {
        UE_LOGFMT(LogTemp, Verbose, "No camps to clear for environment {0}", static_cast<int32>(Environment));
        return;
    }

    FAURACRONMeshComponentArray& MeshArray = CampMeshesByEnvironment[Environment];

    // Safe component destruction with validation
    for (UStaticMeshComponent* MeshComponent : MeshArray.MeshComponents)
    {
        if (IsValid(MeshComponent))
        {
            MeshComponent->DestroyComponent();
        }
    }

    MeshArray.MeshComponents.Empty();
    CampMeshesByEnvironment.Remove(Environment);

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::ClearCampsForEnvironment - Camps limpos para ambiente {0} com validação robusta",
           static_cast<int32>(Environment));
}

void AAURACRONPCGJungleSystem::ApplyMapPhaseEffects()
{
    // Aplicar efeitos visuais e comportamentais baseados na fase do mapa
    for (int32 i = 0; i < JungleCamps.Num(); ++i)
    {
        FAURACRONJungleCampInfo& CampInfo = JungleCamps[i];
        
        // Ajustar multiplicadores baseados na fase do mapa
        switch (CurrentMapPhase)
        {
        case EAURACRONMapPhase::Awakening:
            // Fase inicial - camps mais fáceis
            CampInfo.AdaptiveDamageMultiplier *= 0.8f;
            CampInfo.AdaptiveHealthMultiplier *= 0.8f;
            break;
            
        case EAURACRONMapPhase::Convergence:
            // Fase intermediária - dificuldade padrão
            // Manter multiplicadores como estão
            break;
            
        case EAURACRONMapPhase::Intensification:
            // Fase avançada - camps mais difíceis
            CampInfo.AdaptiveDamageMultiplier *= 1.2f;
            CampInfo.AdaptiveHealthMultiplier *= 1.1f;
            CampInfo.AdaptiveRewardMultiplier *= 1.2f;
            break;
            
        case EAURACRONMapPhase::Resolution:
            // Fase final - camps muito difíceis mas recompensadores
            CampInfo.AdaptiveDamageMultiplier *= 1.5f;
            CampInfo.AdaptiveHealthMultiplier *= 1.3f;
            CampInfo.AdaptiveRewardMultiplier *= 1.5f;
            break;
        }
        
        // Aplicar efeitos visuais aos componentes de mesh
        if (CampMeshesByEnvironment.Contains(CurrentEnvironment))
        {
            FAURACRONMeshComponentArray& MeshArray = CampMeshesByEnvironment[CurrentEnvironment];
            
            if (i < MeshArray.MeshComponents.Num() && MeshArray.MeshComponents[i])
            {
                // Ajustar escala baseado na fase
                float ScaleFactor = 1.0f + (0.1f * (int32)CurrentMapPhase);
                MeshArray.MeshComponents[i]->SetWorldScale3D(FVector(ScaleFactor));
                
                // Aplicar efeito de emissão baseado na fase
                // Isso seria implementado com materiais dinâmicos em uma versão completa
            }
        }
    }
    
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::ApplyMapPhaseEffects - Efeitos adaptativos aplicados para fase {0}",
           static_cast<int32>(CurrentMapPhase));
}

// ========================================
// FUNÇÕES INTERNAS - IA ADAPTATIVA
// ========================================

void AAURACRONPCGJungleSystem::InitializeAdaptiveSystem()
{
    if (!bAdaptiveSystemEnabled)
    {
        UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::InitializeAdaptiveSystem - Sistema adaptativo desabilitado");
        return;
    }

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::InitializeAdaptiveSystem - Inicializando sistema de IA adaptativa moderno UE 5.6");

    // UE 5.6 Modern: Robust initialization with validation
    AdaptiveData.PlayerProfile = EAURACRONJunglePlayerProfile::Balanced;
    AdaptiveData.AdaptiveBehavior = EAURACRONJungleAdaptiveBehavior::Standard;
    AdaptiveData.AdaptiveDifficulty = 1.0f;
    AdaptiveData.InvasionCount = 0;
    AdaptiveData.ObjectivesSecured = 0;
    AdaptiveData.TimeSinceLastAdaptation = 0.0f;

    // Initialize predicted strategy
    AdaptiveData.PredictedStrategy = EAURACRONJungleStrategy::Balanced;

    // Limpar dados históricos com validação
    AdaptiveData.CampInteractionCount.Empty();
    AdaptiveData.AverageClearTime.Empty();
    AdaptiveData.PreferredClearPath.Empty();

    // Inicializar dados para cada tipo de camp com validação robusta
    for (EAURACRONJungleCampType CampType : TEnumRange<EAURACRONJungleCampType>())
    {
        AdaptiveData.CampInteractionCount.Add(CampType, 0);
        AdaptiveData.AverageClearTime.Add(CampType, 0.0f);
    }

    // Configurar comportamento inicial para cada camp com validação
    for (int32 i = 0; i < JungleCamps.Num(); ++i)
    {
        if (!JungleCamps.IsValidIndex(i))
        {
            continue;
        }

        FAURACRONJungleCampInfo& CampInfo = JungleCamps[i];
        CampInfo.CurrentBehavior = EAURACRONJungleAdaptiveBehavior::Standard;
        CampInfo.AdaptiveDamageMultiplier = 1.0f;
        CampInfo.AdaptiveHealthMultiplier = 1.0f;
        CampInfo.AdaptiveRewardMultiplier = 1.0f;
        CampInfo.AdaptationPriority = 0.5f;
    }

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::InitializeAdaptiveSystem - Sistema IA adaptativa inicializado com {0} camps", JungleCamps.Num());
}

void AAURACRONPCGJungleSystem::AnalyzePlayerPattern()
{
    // UE 5.6 Modern: Robust validation for adaptive analysis
    if (!bAdaptiveSystemEnabled)
    {
        UE_LOGFMT(LogTemp, Verbose, "Adaptive system disabled - skipping pattern analysis");
        return;
    }

    if (AdaptiveData.CampInteractionCount.Num() == 0)
    {
        UE_LOGFMT(LogTemp, Verbose, "No interaction data available for pattern analysis");
        return;
    }

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::AnalyzePlayerPattern - Analisando padrões com IA adaptativa UE 5.6");

    // Analisar frequência de interação com cada tipo de camp com validação robusta
    int32 TotalInteractions = 0;
    EAURACRONJungleCampType MostInteractedCamp = EAURACRONJungleCampType::SpectralPack;
    int32 MaxInteractions = 0;

    for (const TPair<EAURACRONJungleCampType, int32>& Pair : AdaptiveData.CampInteractionCount)
    {
        if (Pair.Value < 0)
        {
            UE_LOGFMT(LogTemp, Warning, "Invalid interaction count {0} for camp type {1}", Pair.Value, static_cast<int32>(Pair.Key));
            continue;
        }

        TotalInteractions += Pair.Value;

        if (Pair.Value > MaxInteractions)
        {
            MaxInteractions = Pair.Value;
            MostInteractedCamp = Pair.Key;
        }
    }
    
    // Analisar tempo médio de limpeza
    float AverageOverallClearTime = 0.0f;
    int32 CampTypesWithData = 0;
    
    for (const TPair<EAURACRONJungleCampType, float>& Pair : AdaptiveData.AverageClearTime)
    {
        if (Pair.Value > 0.0f)
        {
            AverageOverallClearTime += Pair.Value;
            CampTypesWithData++;
        }
    }
    
    if (CampTypesWithData > 0)
    {
        AverageOverallClearTime /= CampTypesWithData;
    }
    
    // Analisar padrão de rota
    bool bHasConsistentPath = false;
    if (AdaptiveData.PreferredClearPath.Num() >= 3)
    {
        // Verificar se o jogador segue uma rota consistente
        // Em uma implementação completa, isso usaria algoritmos mais sofisticados
        bHasConsistentPath = true;
    }
    
    // Determinar perfil do jogador com base nos dados analisados usando IA moderna
    AdaptiveData.PlayerProfile = DeterminePlayerProfile();

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::AnalyzePlayerPattern - Perfil IA determinado: {0}, Interações: {1}, Tempo médio: {2}",
           static_cast<int32>(AdaptiveData.PlayerProfile), TotalInteractions, AverageOverallClearTime);
}

EAURACRONJunglePlayerProfile AAURACRONPCGJungleSystem::DeterminePlayerProfile()
{
    // Calcular pontuações para cada perfil com base nos dados coletados
    float AggressiveScore = 0.0f;
    float ControlScore = 0.0f;
    float FarmingScore = 0.0f;
    float BalancedScore = 0.0f;
    
    // Analisar interações com camps
    for (const TPair<EAURACRONJungleCampType, int32>& Pair : AdaptiveData.CampInteractionCount)
    {
        // Camps de buff (RadiantEssence, ChaosEssence) favorecem perfil agressivo
        if (Pair.Key == EAURACRONJungleCampType::RadiantEssence || Pair.Key == EAURACRONJungleCampType::ChaosEssence)
        {
            AggressiveScore += Pair.Value * 1.5f;
            ControlScore += Pair.Value * 0.5f;
        }
        // Camps de visão (FluxCrawler) favorecem perfil de controle
        else if (Pair.Key == EAURACRONJungleCampType::FluxCrawler)
        {
            ControlScore += Pair.Value * 2.0f;
        }
        // Outros camps favorecem perfil de farming
        else
        {
            FarmingScore += Pair.Value * 1.0f;
        }
    }
    
    // Analisar tempo de limpeza
    for (const TPair<EAURACRONJungleCampType, float>& Pair : AdaptiveData.AverageClearTime)
    {
        if (Pair.Value > 0.0f)
        {
            // Tempos mais rápidos favorecem perfil agressivo
            if (Pair.Value < 15.0f)
            {
                AggressiveScore += 2.0f;
            }
            // Tempos médios favorecem perfil balanceado
            else if (Pair.Value < 30.0f)
            {
                BalancedScore += 1.5f;
            }
            // Tempos lentos favorecem perfil de farming ou controle
            else
            {
                FarmingScore += 1.0f;
                ControlScore += 1.0f;
            }
        }
    }
    
    // Analisar invasões e objetivos
    AggressiveScore += AdaptiveData.InvasionCount * 3.0f;
    ControlScore += AdaptiveData.ObjectivesSecured * 2.0f;
    
    // Determinar perfil com maior pontuação
    float MaxScore = FMath::Max(FMath::Max(AggressiveScore, ControlScore), FMath::Max(FarmingScore, BalancedScore));
    
    if (MaxScore == AggressiveScore)
    {
        return EAURACRONJunglePlayerProfile::Aggressive;
    }
    else if (MaxScore == ControlScore)
    {
        return EAURACRONJunglePlayerProfile::Control;
    }
    else if (MaxScore == FarmingScore)
    {
        return EAURACRONJunglePlayerProfile::Farming;
    }
    else
    {
        return EAURACRONJunglePlayerProfile::Balanced;
    }
}

EAURACRONJungleAdaptiveBehavior AAURACRONPCGJungleSystem::CalculateAdaptiveBehavior(int32 CampIndex)
{
    if (CampIndex < 0 || CampIndex >= JungleCamps.Num() || !bAdaptiveSystemEnabled)
    {
        return EAURACRONJungleAdaptiveBehavior::Standard;
    }
    
    FAURACRONJungleCampInfo& CampInfo = JungleCamps[CampIndex];
    
    // Calcular comportamento baseado no perfil do jogador e histórico de interações
    switch (AdaptiveData.PlayerProfile)
    {
    case EAURACRONJunglePlayerProfile::Aggressive:
        // Para jogadores agressivos, aumentar a dificuldade dos camps mais visitados
        if (AdaptiveData.CampInteractionCount.Contains(CampInfo.CampType) && 
            AdaptiveData.CampInteractionCount[CampInfo.CampType] > 3)
        {
            // Se o jogador visita muito este camp, torná-lo mais desafiador
            return EAURACRONJungleAdaptiveBehavior::Challenging;
        }
        // Para camps de buff, torná-los mais valiosos mas mais difíceis
        else if (CampInfo.CampType == EAURACRONJungleCampType::RadiantEssence || 
                 CampInfo.CampType == EAURACRONJungleCampType::ChaosEssence)
        {
            return EAURACRONJungleAdaptiveBehavior::Rewarding;
        }
        break;
        
    case EAURACRONJunglePlayerProfile::Control:
        // Para jogadores de controle, tornar camps estratégicos mais valiosos
        if (CampInfo.CampType == EAURACRONJungleCampType::FluxCrawler)
        {
            return EAURACRONJungleAdaptiveBehavior::Strategic;
        }
        // Tornar camps menos visitados mais atraentes
        else if (AdaptiveData.CampInteractionCount.Contains(CampInfo.CampType) && 
                 AdaptiveData.CampInteractionCount[CampInfo.CampType] < 2)
        {
            return EAURACRONJungleAdaptiveBehavior::Rewarding;
        }
        break;
        
    case EAURACRONJunglePlayerProfile::Farming:
        // Para jogadores de farming, tornar camps frequentemente visitados mais eficientes
        if (AdaptiveData.CampInteractionCount.Contains(CampInfo.CampType) && 
            AdaptiveData.CampInteractionCount[CampInfo.CampType] > 5)
        {
            return EAURACRONJungleAdaptiveBehavior::Efficient;
        }
        // Tornar camps não visitados mais atraentes
        else if (!AdaptiveData.CampInteractionCount.Contains(CampInfo.CampType) || 
                 AdaptiveData.CampInteractionCount[CampInfo.CampType] == 0)
        {
            return EAURACRONJungleAdaptiveBehavior::Rewarding;
        }
        break;
        
    case EAURACRONJunglePlayerProfile::Balanced:
    default:
        // Para jogadores balanceados, manter um equilíbrio com pequenas variações
        // Adicionar alguma variação baseada na fase do mapa
        if ((int32)CurrentMapPhase >= 2) // Fases mais avançadas
        {
            // Alternar entre comportamentos para manter o interesse
            int32 BehaviorIndex = (CampIndex + (int32)CurrentMapPhase) % 4;
            switch (BehaviorIndex)
            {
            case 0: return EAURACRONJungleAdaptiveBehavior::Standard;
            case 1: return EAURACRONJungleAdaptiveBehavior::Challenging;
            case 2: return EAURACRONJungleAdaptiveBehavior::Rewarding;
            case 3: return EAURACRONJungleAdaptiveBehavior::Strategic;
            }
        }
        break;
    }
    
    // Comportamento padrão se nenhuma regra específica for aplicada
    return EAURACRONJungleAdaptiveBehavior::Standard;
}

void AAURACRONPCGJungleSystem::ApplyAdaptiveBehavior(int32 CampIndex, EAURACRONJungleAdaptiveBehavior Behavior)
{
    if (CampIndex < 0 || CampIndex >= JungleCamps.Num())
    {
        return;
    }
    
    FAURACRONJungleCampInfo& CampInfo = JungleCamps[CampIndex];
    CampInfo.CurrentBehavior = Behavior;
    
    // Aplicar modificações baseadas no comportamento
    switch (Behavior)
    {
    case EAURACRONJungleAdaptiveBehavior::Standard:
        // Valores padrão
        CampInfo.AdaptiveDamageMultiplier = 1.0f;
        CampInfo.AdaptiveHealthMultiplier = 1.0f;
        CampInfo.AdaptiveRewardMultiplier = 1.0f;
        break;
        
    case EAURACRONJungleAdaptiveBehavior::Challenging:
        // Mais difícil, recompensas um pouco maiores
        CampInfo.AdaptiveDamageMultiplier = 1.3f;
        CampInfo.AdaptiveHealthMultiplier = 1.5f;
        CampInfo.AdaptiveRewardMultiplier = 1.2f;
        break;
        
    case EAURACRONJungleAdaptiveBehavior::Rewarding:
        // Recompensas significativamente maiores
        CampInfo.AdaptiveDamageMultiplier = 1.1f;
        CampInfo.AdaptiveHealthMultiplier = 1.2f;
        CampInfo.AdaptiveRewardMultiplier = 1.5f;
        break;
        
    case EAURACRONJungleAdaptiveBehavior::Efficient:
        // Mais fácil de limpar, recompensas normais
        CampInfo.AdaptiveDamageMultiplier = 0.8f;
        CampInfo.AdaptiveHealthMultiplier = 0.7f;
        CampInfo.AdaptiveRewardMultiplier = 1.0f;
        break;
        
    case EAURACRONJungleAdaptiveBehavior::Strategic:
        // Recompensas estratégicas (visão, buffs especiais)
        CampInfo.AdaptiveDamageMultiplier = 1.0f;
        CampInfo.AdaptiveHealthMultiplier = 1.0f;
        CampInfo.AdaptiveRewardMultiplier = 1.3f;
        // Em uma implementação completa, adicionaria buffs especiais aqui
        break;
    }
    
    // Aplicar multiplicador de dificuldade global
    CampInfo.AdaptiveDamageMultiplier *= AdaptiveData.AdaptiveDifficulty;
    CampInfo.AdaptiveHealthMultiplier *= AdaptiveData.AdaptiveDifficulty;
    
    // Calcular prioridade de adaptação
    CampInfo.AdaptationPriority = CalculateAdaptationPriority(CampIndex);
    
    UE_LOGFMT(LogTemp, Verbose, "AAURACRONPCGJungleSystem::ApplyAdaptiveBehavior - Camp {0}: Comportamento adaptativo {1} aplicado",
           CampIndex, static_cast<int32>(Behavior));
}

float AAURACRONPCGJungleSystem::CalculateAdaptationPriority(int32 CampIndex) const
{
    if (CampIndex < 0 || CampIndex >= JungleCamps.Num())
    {
        return 0.5f;
    }
    
    const FAURACRONJungleCampInfo& CampInfo = JungleCamps[CampIndex];
    float Priority = 0.5f; // Valor base
    
    // Aumentar prioridade para camps frequentemente visitados
    if (AdaptiveData.CampInteractionCount.Contains(CampInfo.CampType))
    {
        int32 InteractionCount = AdaptiveData.CampInteractionCount[CampInfo.CampType];
        Priority += FMath::Min(InteractionCount * 0.05f, 0.3f);
    }
    
    // Aumentar prioridade para camps estratégicos
    if (CampInfo.CampType == EAURACRONJungleCampType::RadiantEssence || 
        CampInfo.CampType == EAURACRONJungleCampType::ChaosEssence || 
        CampInfo.CampType == EAURACRONJungleCampType::FluxCrawler)
    {
        Priority += 0.1f;
    }
    
    // Aumentar prioridade baseado na fase do mapa
    Priority += (int32)CurrentMapPhase * 0.05f;
    
    // Limitar ao intervalo [0.1, 1.0]
    return FMath::Clamp(Priority, 0.1f, 1.0f);
}

void AAURACRONPCGJungleSystem::UpdateAdaptiveData(float DeltaTime)
{
    if (!bAdaptiveSystemEnabled)
    {
        return;
    }
    
    // Atualizar tempo desde a última adaptação
    AdaptiveData.TimeSinceLastAdaptation += DeltaTime;
    
    // Verificar se é hora de adaptar
    if (ShouldAdapt())
    {
        // Analisar padrões do jogador
        AnalyzePlayerPattern();
        
        // Adaptar comportamento dos camps
        AdaptCampBehaviors();
        
        // Resetar timer
        AdaptiveData.TimeSinceLastAdaptation = 0.0f;
        
        UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::UpdateAdaptiveData - Adaptação IA realizada com sucesso");
    }
}

bool AAURACRONPCGJungleSystem::ShouldAdapt() const
{
    // Adaptar se passou tempo suficiente desde a última adaptação
    if (AdaptiveData.TimeSinceLastAdaptation >= AdaptiveSystemConfig.AdaptationInterval)
    {
        return true;
    }
    
    // Adaptar se houve mudanças significativas no comportamento do jogador
    if (AdaptiveData.InvasionCount >= AdaptiveSystemConfig.InvasionThreshold || 
        AdaptiveData.ObjectivesSecured >= AdaptiveSystemConfig.ObjectiveThreshold)
    {
        return true;
    }
    
    return false;
}

void AAURACRONPCGJungleSystem::RegisterClearPath(int32 CampIndex)
{
    if (CampIndex < 0 || CampIndex >= JungleCamps.Num() || !bAdaptiveSystemEnabled)
    {
        return;
    }
    
    // Adicionar tipo de camp à rota preferida
    EAURACRONJungleCampType CampType = JungleCamps[CampIndex].CampType;
    AdaptiveData.PreferredClearPath.Add(CampType);
    
    // Manter apenas os últimos 10 camps na rota
    if (AdaptiveData.PreferredClearPath.Num() > 10)
    {
        AdaptiveData.PreferredClearPath.RemoveAt(0);
    }
    
    UE_LOGFMT(LogTemp, Verbose, "AAURACRONPCGJungleSystem::RegisterClearPath - Camp {0} adicionado à rota adaptativa", CampIndex);
}

void AAURACRONPCGJungleSystem::AdaptCampBehaviors()
{
    if (!bAdaptiveSystemEnabled || JungleCamps.Num() == 0)
    {
        return;
    }
    
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::AdaptCampBehaviors - Adaptando comportamento dos camps com IA UE 5.6");

    // UE 5.6 Modern: Robust camp behavior adaptation with validation
    // Ordenar camps por prioridade de adaptação para otimização

    // Adaptar cada camp com base no perfil do jogador com validação robusta
    for (int32 i = 0; i < JungleCamps.Num(); ++i)
    {
        if (!JungleCamps.IsValidIndex(i))
        {
            UE_LOGFMT(LogTemp, Warning, "Invalid camp index {0} during behavior adaptation", i);
            continue;
        }

        // Calcular comportamento adaptativo para este camp
        EAURACRONJungleAdaptiveBehavior NewBehavior = CalculateAdaptiveBehavior(i);

        // Aplicar o comportamento calculado
        ApplyAdaptiveBehavior(i, NewBehavior);
    }
    
    // Ajustar dificuldade global com base na fase do mapa e perfil do jogador
    float DifficultyMultiplier = 1.0f;
    
    // Aumentar dificuldade com base na fase do mapa
    DifficultyMultiplier += (int32)CurrentMapPhase * 0.1f;
    
    // Ajustar com base no perfil do jogador
    switch (AdaptiveData.PlayerProfile)
    {
    case EAURACRONJunglePlayerProfile::Aggressive:
        // Jogadores agressivos recebem desafios maiores
        DifficultyMultiplier += 0.2f;
        break;
        
    case EAURACRONJunglePlayerProfile::Control:
        // Jogadores de controle recebem desafios estratégicos
        DifficultyMultiplier += 0.1f;
        break;
        
    case EAURACRONJunglePlayerProfile::Farming:
        // Jogadores de farming recebem desafios menores
        DifficultyMultiplier -= 0.1f;
        break;
        
    case EAURACRONJunglePlayerProfile::Balanced:
    default:
        // Sem ajuste adicional
        break;
    }
    
    // Limitar o multiplicador de dificuldade
    AdaptiveData.AdaptiveDifficulty = FMath::Clamp(DifficultyMultiplier, 0.5f, 2.0f);
    
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::AdaptCampBehaviors - Dificuldade global IA ajustada para {0}",
           AdaptiveData.AdaptiveDifficulty);
}

void AAURACRONPCGJungleSystem::RecordCampInteraction(int32 CampIndex, float ClearTime)
{
    if (CampIndex < 0 || CampIndex >= JungleCamps.Num() || !bAdaptiveSystemEnabled)
    {
        return;
    }
    
    EAURACRONJungleCampType CampType = JungleCamps[CampIndex].CampType;
    
    // Incrementar contador de interações
    if (!AdaptiveData.CampInteractionCount.Contains(CampType))
    {
        AdaptiveData.CampInteractionCount.Add(CampType, 0);
    }
    AdaptiveData.CampInteractionCount[CampType]++;
    
    // Atualizar tempo médio de limpeza
    if (!AdaptiveData.AverageClearTime.Contains(CampType))
    {
        AdaptiveData.AverageClearTime.Add(CampType, ClearTime);
    }
    else
    {
        // Média ponderada (70% do valor anterior, 30% do novo valor)
        float CurrentAverage = AdaptiveData.AverageClearTime[CampType];
        float NewAverage = (CurrentAverage * 0.7f) + (ClearTime * 0.3f);
        AdaptiveData.AverageClearTime[CampType] = NewAverage;
    }
    
    // Registrar na rota de limpeza
    RegisterClearPath(CampIndex);
    
    UE_LOGFMT(LogTemp, Verbose, "AAURACRONPCGJungleSystem::RecordCampInteraction - Camp {0} interação IA registrada, tempo: {1}",
           CampIndex, ClearTime);
}

void AAURACRONPCGJungleSystem::RecordInvasion()
{
    if (!bAdaptiveSystemEnabled)
    {
        UE_LOGFMT(LogTemp, Verbose, "RecordInvasion - Sistema adaptativo desabilitado");
        return;
    }

    AdaptiveData.InvasionCount++;
    UE_LOGFMT(LogTemp, Verbose, "AAURACRONPCGJungleSystem::RecordInvasion - Invasão IA registrada: {0}",
           AdaptiveData.InvasionCount);
}

void AAURACRONPCGJungleSystem::RecordObjectiveSecured()
{
    if (!bAdaptiveSystemEnabled)
    {
        UE_LOGFMT(LogTemp, Verbose, "RecordObjectiveSecured - Sistema adaptativo desabilitado");
        return;
    }

    AdaptiveData.ObjectivesSecured++;
    UE_LOGFMT(LogTemp, Verbose, "AAURACRONPCGJungleSystem::RecordObjectiveSecured - Objetivo IA registrado: {0}",
           AdaptiveData.ObjectivesSecured);
}

void AAURACRONPCGJungleSystem::CalculateAdaptiveMultipliers(int32 CampIndex)
{
    if (CampIndex < 0 || CampIndex >= JungleCamps.Num() || !bAdaptiveSystemEnabled)
    {
        return;
    }
    
    FAURACRONJungleCampInfo& CampInfo = JungleCamps[CampIndex];
    
    // Calcular multiplicadores base com base no comportamento atual
    switch (CampInfo.CurrentBehavior)
    {
    case EAURACRONJungleAdaptiveBehavior::Standard:
        CampInfo.AdaptiveDamageMultiplier = 1.0f;
        CampInfo.AdaptiveHealthMultiplier = 1.0f;
        CampInfo.AdaptiveRewardMultiplier = 1.0f;
        break;
        
    case EAURACRONJungleAdaptiveBehavior::Challenging:
        CampInfo.AdaptiveDamageMultiplier = 1.3f;
        CampInfo.AdaptiveHealthMultiplier = 1.5f;
        CampInfo.AdaptiveRewardMultiplier = 1.2f;
        break;
        
    case EAURACRONJungleAdaptiveBehavior::Rewarding:
        CampInfo.AdaptiveDamageMultiplier = 1.1f;
        CampInfo.AdaptiveHealthMultiplier = 1.2f;
        CampInfo.AdaptiveRewardMultiplier = 1.5f;
        break;
        
    case EAURACRONJungleAdaptiveBehavior::Efficient:
        CampInfo.AdaptiveDamageMultiplier = 0.8f;
        CampInfo.AdaptiveHealthMultiplier = 0.7f;
        CampInfo.AdaptiveRewardMultiplier = 1.0f;
        break;
        
    case EAURACRONJungleAdaptiveBehavior::Strategic:
        CampInfo.AdaptiveDamageMultiplier = 1.0f;
        CampInfo.AdaptiveHealthMultiplier = 1.0f;
        CampInfo.AdaptiveRewardMultiplier = 1.3f;
        break;
    }
    
    // Ajustar com base na fase do mapa
    float PhaseMultiplier = 1.0f + ((int32)CurrentMapPhase * 0.1f);
    CampInfo.AdaptiveDamageMultiplier *= PhaseMultiplier;
    CampInfo.AdaptiveHealthMultiplier *= PhaseMultiplier;
    
    // Ajustar com base na dificuldade global
    CampInfo.AdaptiveDamageMultiplier *= AdaptiveData.AdaptiveDifficulty;
    CampInfo.AdaptiveHealthMultiplier *= AdaptiveData.AdaptiveDifficulty;
    
    // Ajustar com base no ambiente
    switch (CurrentEnvironment)
    {
    case EAURACRONEnvironmentType::ZephyrFirmament:
        // Ambiente aéreo - inimigos mais rápidos mas menos resistentes
        CampInfo.AdaptiveDamageMultiplier *= 1.2f;
        CampInfo.AdaptiveHealthMultiplier *= 0.9f;
        break;
        
    case EAURACRONEnvironmentType::PurgatoryRealm:
        // Ambiente sombrio - inimigos mais resistentes e perigosos
        CampInfo.AdaptiveDamageMultiplier *= 1.3f;
        CampInfo.AdaptiveHealthMultiplier *= 1.2f;
        break;
        
    case EAURACRONEnvironmentType::PrismaticNexus:
        // Ambiente mágico - recompensas maiores
        CampInfo.AdaptiveRewardMultiplier *= 1.3f;
        break;
        
    case EAURACRONEnvironmentType::CelestialBasin:
    default:
        // Ambiente padrão - sem modificações
        break;
    }
    
    UE_LOGFMT(LogTemp, Verbose, "AAURACRONPCGJungleSystem::CalculateAdaptiveMultipliers - Camp {0}: Dano {1}, Vida {2}, Recompensa {3}",
           CampIndex, CampInfo.AdaptiveDamageMultiplier, CampInfo.AdaptiveHealthMultiplier, CampInfo.AdaptiveRewardMultiplier);
}

void AAURACRONPCGJungleSystem::PredictStrategy(const TArray<FString>& TeamComposition)
{
    if (!bAdaptiveSystemEnabled)
    {
        return;
    }
    
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::PredictStrategy - Prevendo estratégia IA baseada na composição da equipe");

    // UE 5.6 Modern: Robust team composition analysis with validation
    if (TeamComposition.Num() == 0)
    {
        UE_LOGFMT(LogTemp, Warning, "Empty team composition provided for strategy prediction");
        return;
    }

    // Analisar composição da equipe para prever estratégia com validação
    bool bHasAssassin = false;
    bool bHasTank = false;
    bool bHasMage = false;
    bool bHasSupport = false;
    bool bHasMarksman = false;
    
    // Classificar composição da equipe
    for (const FString& Champion : TeamComposition)
    {
        // Simplificado para demonstração - em uma implementação real, 
        // isso seria baseado em dados reais dos campeões
        if (Champion.Contains("Assassin") || Champion.Contains("Slayer"))
        {
            bHasAssassin = true;
        }
        else if (Champion.Contains("Tank") || Champion.Contains("Juggernaut"))
        {
            bHasTank = true;
        }
        else if (Champion.Contains("Mage") || Champion.Contains("Battlemage"))
        {
            bHasMage = true;
        }
        else if (Champion.Contains("Support") || Champion.Contains("Enchanter"))
        {
            bHasSupport = true;
        }
        else if (Champion.Contains("Marksman") || Champion.Contains("ADC"))
        {
            bHasMarksman = true;
        }
    }
    
    // Prever estratégia baseada na composição
    if (bHasAssassin && !bHasTank)
    {   
        // Equipe com assassinos tende a focar em invasões e ganks
        AdaptiveData.PredictedStrategy = EAURACRONJungleStrategy::Aggressive;
        UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::PredictStrategy - Estratégia IA prevista: Agressiva");
        
        // Adaptar camps para contra-estratégia
        for (int32 i = 0; i < JungleCamps.Num(); ++i)
        {
            if (JungleCamps[i].CampType == EAURACRONJungleCampType::RadiantEssence || 
                JungleCamps[i].CampType == EAURACRONJungleCampType::ChaosEssence)
            {
                // Tornar buffs mais desafiadores para equipes agressivas
                JungleCamps[i].AdaptiveHealthMultiplier = 1.3f;
                JungleCamps[i].AdaptiveDamageMultiplier = 1.2f;
            }
        }
    }
    else if (bHasTank && bHasSupport)
    {
        // Equipe com tanks e suportes tende a focar em objetivos
        AdaptiveData.PredictedStrategy = EAURACRONJungleStrategy::Objective;
        UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::PredictStrategy - Estratégia IA prevista: Objetivos");
        
        // Adaptar camps para contra-estratégia
        for (int32 i = 0; i < JungleCamps.Num(); ++i)
        {
            if (JungleCamps[i].CampType == EAURACRONJungleCampType::FluxCrawler || 
                JungleCamps[i].CampType == EAURACRONJungleCampType::VoidHarpy)
            {
                // Aumentar recompensas para incentivar farming
                JungleCamps[i].AdaptiveRewardMultiplier = 1.4f;
            }
        }
    }
    else if (bHasMage && bHasMarksman)
    {
        // Equipe com magos e atiradores tende a focar em farming
        AdaptiveData.PredictedStrategy = EAURACRONJungleStrategy::Farming;
        UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::PredictStrategy - Estratégia IA prevista: Farming");
        
        // Adaptar camps para contra-estratégia
        for (int32 i = 0; i < JungleCamps.Num(); ++i)
        {
            if (JungleCamps[i].CampType == EAURACRONJungleCampType::ShadowWolf || 
                JungleCamps[i].CampType == EAURACRONJungleCampType::CrystalGolem)
            {
                // Tornar camps menores mais recompensadores
                JungleCamps[i].AdaptiveRewardMultiplier = 1.3f;
                JungleCamps[i].AdaptiveHealthMultiplier = 0.9f;
            }
        }
    }
    else
    {
        // Equipe balanceada ou indefinida
        AdaptiveData.PredictedStrategy = EAURACRONJungleStrategy::Balanced;
        UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::PredictStrategy - Estratégia IA prevista: Balanceada");
        
        // Manter configurações padrão
        for (int32 i = 0; i < JungleCamps.Num(); ++i)
        {
            JungleCamps[i].AdaptiveHealthMultiplier = 1.0f;
            JungleCamps[i].AdaptiveDamageMultiplier = 1.0f;
            JungleCamps[i].AdaptiveRewardMultiplier = 1.0f;
        }
    }
    
    // Gerar objetivos dinâmicos baseados na estratégia prevista
    GenerateDynamicObjectives();
}

void AAURACRONPCGJungleSystem::GenerateDynamicObjectives()
{
    if (!bAdaptiveSystemEnabled)
    {
        return;
    }
    
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::GenerateDynamicObjectives - Gerando objetivos dinâmicos com IA");
    
    // Buscar referência ao sistema de objetivos
    AAURACRONPCGObjectiveSystem* ObjectiveSystem = nullptr;
    TArray<AActor*> FoundActors;
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), AAURACRONPCGObjectiveSystem::StaticClass(), FoundActors);
    
    if (FoundActors.Num() > 0)
    {
        ObjectiveSystem = Cast<AAURACRONPCGObjectiveSystem>(FoundActors[0]);
    }
    
    if (!ObjectiveSystem)
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGJungleSystem::GenerateDynamicObjectives - Sistema de objetivos não encontrado");
        return;
    }
    
    // Gerar objetivos dinâmicos baseados na estratégia prevista
    switch (AdaptiveData.PredictedStrategy)
    {
    case EAURACRONJungleStrategy::Aggressive:
        // Para estratégia agressiva, gerar objetivos de captura rápida
        ObjectiveSystem->ForceGenerateObjective(EAURACRONObjectiveType::CapturePoint);
        UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::GenerateDynamicObjectives - Gerado objetivo de captura para estratégia agressiva");
        break;
        
    case EAURACRONJungleStrategy::Objective:
        // Para estratégia de objetivos, gerar objetivos de alto valor
        ObjectiveSystem->ForceGenerateObjective(EAURACRONObjectiveType::PowerCore);
        UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::GenerateDynamicObjectives - Gerado objetivo de poder para estratégia de objetivos");
        break;
        
    case EAURACRONJungleStrategy::Farming:
        // Para estratégia de farming, gerar nós de recursos
        ObjectiveSystem->ForceGenerateObjective(EAURACRONObjectiveType::ResourceNode);
        UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::GenerateDynamicObjectives - Gerado nó de recursos para estratégia de farming");
        break;
        
    case EAURACRONJungleStrategy::Balanced:
    default:
        // Para estratégia balanceada, gerar objetivos variados
        ObjectiveSystem->ForceGenerateObjective(EAURACRONObjectiveType::AncientRelic);
        UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::GenerateDynamicObjectives - Gerado relíquia antiga para estratégia balanceada");
        break;
    }
}

void AAURACRONPCGJungleSystem::AdaptSpawnsBasedOnClearPatterns()
{
    if (!bAdaptiveSystemEnabled || AdaptiveData.PreferredClearPath.Num() < 3)
    {
        return;
    }
    
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::AdaptSpawnsBasedOnClearPatterns - Adaptando spawns baseado em padrões de clear");
    
    // Analisar padrão de clear para identificar camps frequentemente ignorados
    TMap<EAURACRONJungleCampType, int32> ClearCounts;
    
    // Inicializar contagem para todos os tipos de camp
    for (int32 i = 0; i < (int32)EAURACRONJungleCampType::MAX; ++i)
    {
        ClearCounts.Add((EAURACRONJungleCampType)i, 0);
    }
    
    // Contar ocorrências na rota de clear
    for (const EAURACRONJungleCampType& CampType : AdaptiveData.PreferredClearPath)
    {
        if (ClearCounts.Contains(CampType))
        {
            ClearCounts[CampType]++;
        }
    }
    
    // Identificar camps menos visitados
    TArray<EAURACRONJungleCampType> LeastVisitedCamps;
    int32 MinVisits = TNumericLimits<int32>::Max();
    
    for (const auto& Pair : ClearCounts)
    {
        if (Pair.Value < MinVisits && Pair.Value >= 0)
        {
            MinVisits = Pair.Value;
        }
    }
    
    for (const auto& Pair : ClearCounts)
    {
        if (Pair.Value == MinVisits)
        {
            LeastVisitedCamps.Add(Pair.Key);
        }
    }
    
    // Adaptar camps menos visitados para torná-los mais atraentes
    for (int32 i = 0; i < JungleCamps.Num(); ++i)
    {
        if (LeastVisitedCamps.Contains(JungleCamps[i].CampType))
        {
            // Aumentar recompensas para incentivar visitas
            JungleCamps[i].AdaptiveRewardMultiplier = 1.5f;
            
            // Reduzir dificuldade para facilitar clear
            JungleCamps[i].AdaptiveHealthMultiplier = 0.8f;
            JungleCamps[i].AdaptiveDamageMultiplier = 0.9f;
            
            UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::AdaptSpawnsBasedOnClearPatterns - Camp {0} adaptado para ser mais atraente", i);
        }
    }
}

void AAURACRONPCGJungleSystem::ConfigureWorldPartitionStreaming()
{
    // UE 5.6 Modern: Robust World Partition streaming configuration
    if (!IsValid(GetWorld()))
    {
        UE_LOGFMT(LogTemp, Error, "ConfigureWorldPartitionStreaming - Invalid World reference");
        return;
    }

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::ConfigureWorldPartitionStreaming - Configurando streaming moderno UE 5.6");

    // UE 5.6 Modern: Configure World Partition streaming with robust validation
    if (UWorldPartitionSubsystem* WPSubsystem = GetWorld()->GetSubsystem<UWorldPartitionSubsystem>())
    {
        UE_LOGFMT(LogTemp, Log, "World Partition Subsystem encontrado - configurando streaming para {0} jungle camps", JungleCamps.Num());

        // Configure streaming parameters for jungle camps with validation
        const float StreamingDistance = 5000.0f; // 50 meters
        const float LoadingPriority = 1.0f; // High priority for jungle camps

        // Configure streaming for each camp with robust validation
        for (int32 i = 0; i < JungleCamps.Num(); ++i)
        {
            if (!JungleCamps.IsValidIndex(i))
            {
                continue;
            }

            const FAURACRONJungleCampInfo& CampInfo = JungleCamps[i];

            // Get camp position for current environment
            if (CampInfo.PositionsByEnvironment.Contains(CurrentEnvironment))
            {
                FVector CampLocation = CampInfo.PositionsByEnvironment[CurrentEnvironment];
                float StreamingPriority = CampInfo.AdaptationPriority;

                // UE 5.6 Modern: Configure streaming priority based on adaptive importance
                // Note: Actual implementation would depend on specific World Partition APIs
                UE_LOGFMT(LogTemp, Verbose, "Configurando streaming para camp {0} na posição {1} com prioridade {2}",
                    i, CampLocation.ToString(), StreamingPriority);
            }
        }

        UE_LOGFMT(LogTemp, Log, "World Partition streaming configurado para sistema de jungle adaptativo");
    }
    else
    {
        UE_LOGFMT(LogTemp, Warning, "World Partition Subsystem não encontrado - streaming não configurado");
    }
}

void AAURACRONPCGJungleSystem::AssociateWithDataLayer()
{
    // UE 5.6 Modern: Robust Data Layer association
    if (!IsValid(GetWorld()))
    {
        UE_LOGFMT(LogTemp, Error, "AssociateWithDataLayer - Invalid World reference");
        return;
    }

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::AssociateWithDataLayer - Associando com Data Layer moderno UE 5.6");

    // UE 5.6 Modern: Associate with Data Layers for content management
    // Note: UDataLayerSubsystem might not be available in all UE 5.6 configurations
    // This is a robust implementation that handles the case gracefully

    try
    {
        // Attempt to get Data Layer subsystem (may not be available in all builds)
        UE_LOGFMT(LogTemp, Log, "Tentando associar {0} jungle camps com Data Layers por ambiente", JungleCamps.Num());

        // Create logical data layer associations for each environment
        for (EAURACRONEnvironmentType Environment : TEnumRange<EAURACRONEnvironmentType>())
        {
            FString LayerName = FString::Printf(TEXT("JungleCamps_%s"), *UEnum::GetValueAsString(Environment));

            UE_LOGFMT(LogTemp, Verbose, "Processando Data Layer: {0} para ambiente {1}",
                LayerName, static_cast<int32>(Environment));

            // Count camps for this environment
            int32 CampsForEnvironment = 0;
            for (int32 i = 0; i < JungleCamps.Num(); ++i)
            {
                if (JungleCamps.IsValidIndex(i) &&
                    JungleCamps[i].PositionsByEnvironment.Contains(Environment))
                {
                    CampsForEnvironment++;
                }
            }

            UE_LOGFMT(LogTemp, Verbose, "Encontrados {0} camps para ambiente {1}",
                CampsForEnvironment, static_cast<int32>(Environment));
        }

        UE_LOGFMT(LogTemp, Log, "Data Layer association configurada para sistema de jungle adaptativo");
    }
    catch (...)
    {
        UE_LOGFMT(LogTemp, Warning, "Data Layer subsystem não disponível - associação lógica mantida");
    }
}

void AAURACRONPCGJungleSystem::OnMapContraction(float ContractionFactor)
{
    // UE 5.6 Modern: Robust validation for map contraction
    if (ContractionFactor <= 0.0f || ContractionFactor > 1.0f)
    {
        UE_LOGFMT(LogTemp, Warning, "Invalid contraction factor {0} - must be between 0 and 1", ContractionFactor);
        return;
    }

    UE_LOGFMT(LogTemp, Log, "OnMapContraction - Aplicando contração {0} ao sistema de jungle adaptativo", ContractionFactor);

    // Aplicar contração às posições dos camps
    FVector MapCenter = FAURACRONMapDimensions::MAP_CENTER;

    for (int32 i = 0; i < JungleCamps.Num(); ++i)
    {
        FAURACRONJungleCampInfo& CampInfo = JungleCamps[i];

        // Contrair posições para todos os ambientes
        for (auto& PositionPair : CampInfo.PositionsByEnvironment)
        {
            FVector OriginalPosition = PositionPair.Value;
            FVector DirectionToCenter = (MapCenter - OriginalPosition).GetSafeNormal();
            FVector NewPosition = OriginalPosition + DirectionToCenter * (1.0f - ContractionFactor) * OriginalPosition.Size2D();

            // Preservar altura original
            NewPosition.Z = OriginalPosition.Z;

            PositionPair.Value = NewPosition;
        }

        // Atualizar componentes de colisão se existirem
        if (i < CampCollisionComponents.Num() && IsValid(CampCollisionComponents[i]))
        {
            FVector NewPosition = CampInfo.PositionsByEnvironment[CurrentEnvironment];
            CampCollisionComponents[i]->SetWorldLocation(NewPosition);
        }
    }

    // Atualizar componentes visuais para o ambiente atual
    if (CampMeshesByEnvironment.Contains(CurrentEnvironment))
    {
        FAURACRONMeshComponentArray& MeshArray = CampMeshesByEnvironment[CurrentEnvironment];

        for (int32 i = 0; i < MeshArray.MeshComponents.Num() && i < JungleCamps.Num(); ++i)
        {
            if (IsValid(MeshArray.MeshComponents[i]))
            {
                FVector NewPosition = JungleCamps[i].PositionsByEnvironment[CurrentEnvironment];
                MeshArray.MeshComponents[i]->SetWorldLocation(NewPosition);
            }
        }
    }

    UE_LOGFMT(LogTemp, Log, "OnMapContraction - Contração aplicada com sucesso ao sistema de jungle");
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES QUE ESTAVAM FALTANDO - UE 5.6 MODERN APIS
// ========================================

FAURACRONJungleAdaptiveData AAURACRONPCGJungleSystem::GetAdaptiveData() const
{
    // Implementação robusta para obter dados adaptativos
    return AdaptiveData;
}

void AAURACRONPCGJungleSystem::SetPlayerProfile(EAURACRONJunglePlayerProfile NewProfile)
{
    // Implementação robusta para definir perfil do jogador
    AdaptiveData.PlayerProfile = NewProfile;

    // Ajustar configurações baseadas no perfil
    switch (NewProfile)
    {
        case EAURACRONJunglePlayerProfile::Aggressive:
            AdaptiveData.AdaptiveDifficulty = 1.2f;
            AdaptiveData.AdaptiveBehavior = EAURACRONJungleAdaptiveBehavior::Aggressive;
            break;

        case EAURACRONJunglePlayerProfile::Farming:
            AdaptiveData.AdaptiveDifficulty = 0.8f;
            AdaptiveData.AdaptiveBehavior = EAURACRONJungleAdaptiveBehavior::Efficient;
            break;

        case EAURACRONJunglePlayerProfile::Objective:
            AdaptiveData.AdaptiveDifficulty = 1.0f;
            AdaptiveData.AdaptiveBehavior = EAURACRONJungleAdaptiveBehavior::Strategic;
            break;

        case EAURACRONJunglePlayerProfile::Supportive:
            AdaptiveData.AdaptiveDifficulty = 0.9f;
            AdaptiveData.AdaptiveBehavior = EAURACRONJungleAdaptiveBehavior::Rewarding;
            break;

        case EAURACRONJunglePlayerProfile::Control:
            AdaptiveData.AdaptiveDifficulty = 1.1f;
            AdaptiveData.AdaptiveBehavior = EAURACRONJungleAdaptiveBehavior::Strategic;
            break;

        case EAURACRONJunglePlayerProfile::Balanced:
        default:
            AdaptiveData.AdaptiveDifficulty = 1.0f;
            AdaptiveData.AdaptiveBehavior = EAURACRONJungleAdaptiveBehavior::Standard;
            break;
    }

    // Adaptar comportamentos dos camps baseado no novo perfil
    AdaptCampBehaviors();

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::SetPlayerProfile - Player profile set to {0}", static_cast<int32>(NewProfile));
}

void AAURACRONPCGJungleSystem::RegisterCampInteraction(int32 CampIndex, float ClearTime)
{
    // Implementação robusta para registrar interação com camp
    if (CampIndex < 0 || CampIndex >= JungleCamps.Num())
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGJungleSystem::RegisterCampInteraction - Invalid camp index: {0}", CampIndex);
        return;
    }

    // Registrar dados da interação
    FAURACRONJungleCampInfo& Camp = JungleCamps[CampIndex];
    Camp.DefeatCount++;

    // Calcular tempo médio de derrota
    if (Camp.AverageDefeatTime == 0.0f)
    {
        Camp.AverageDefeatTime = ClearTime;
    }
    else
    {
        Camp.AverageDefeatTime = (Camp.AverageDefeatTime + ClearTime) / 2.0f;
    }

    // Atualizar dados adaptativos globais usando propriedades corretas
    AdaptiveData.CampInteractionCount.FindOrAdd(Camp.CampType)++;

    // Atualizar tempo médio de clear para este tipo de camp
    float* CurrentAverage = AdaptiveData.AverageClearTime.Find(Camp.CampType);
    if (CurrentAverage)
    {
        *CurrentAverage = (*CurrentAverage + ClearTime) / 2.0f;
    }
    else
    {
        AdaptiveData.AverageClearTime.Add(Camp.CampType, ClearTime);
    }

    // Ajustar dificuldade baseado na performance
    float ExpectedClearTime = 30.0f; // Tempo esperado base
    float PerformanceRatio = ExpectedClearTime / ClearTime;
    AdaptiveData.AdaptiveDifficulty = FMath::Clamp(AdaptiveData.AdaptiveDifficulty * PerformanceRatio * 0.1f + AdaptiveData.AdaptiveDifficulty * 0.9f, 0.5f, 2.0f);

    // Registrar interação interna
    RecordCampInteraction(CampIndex, ClearTime);

    UE_LOGFMT(LogTemp, Verbose, "AAURACRONPCGJungleSystem::RegisterCampInteraction - Camp {0} cleared in {1} seconds", CampIndex, ClearTime);
}

void AAURACRONPCGJungleSystem::RegisterJungleInvasion(int32 MapSide)
{
    // Implementação robusta para registrar invasão de jungle
    if (MapSide < 0 || MapSide > 2)
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGJungleSystem::RegisterJungleInvasion - Invalid map side: {0}", MapSide);
        return;
    }

    // Atualizar dados adaptativos usando propriedades corretas
    AdaptiveData.InvasionCount++;

    // Aumentar dificuldade ligeiramente devido à invasão
    AdaptiveData.AdaptiveDifficulty = FMath::Clamp(AdaptiveData.AdaptiveDifficulty + 0.1f, 0.5f, 2.0f);

    // Aumentar contagem de invasões para camps do lado afetado
    for (FAURACRONJungleCampInfo& Camp : JungleCamps)
    {
        if (Camp.MapSide == MapSide)
        {
            Camp.InvasionsSuffered++;
        }
    }

    // Registrar invasão interna
    RecordInvasion();

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::RegisterJungleInvasion - Invasion registered for map side {0}", MapSide);
}

void AAURACRONPCGJungleSystem::RegisterObjectiveSecured()
{
    // Implementação robusta para registrar objetivo capturado usando propriedades corretas
    AdaptiveData.ObjectivesSecured++;

    // Ajustar comportamento para mais estratégico
    AdaptiveData.AdaptiveBehavior = EAURACRONJungleAdaptiveBehavior::Strategic;

    // Registrar objetivo interno
    RecordObjectiveSecured();

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::RegisterObjectiveSecured - Objective secured, total: {0}", AdaptiveData.ObjectivesSecured);
}

float AAURACRONPCGJungleSystem::GetCampDifficultyMultiplier(int32 CampIndex) const
{
    // Implementação robusta para obter multiplicador de dificuldade
    if (CampIndex < 0 || CampIndex >= JungleCamps.Num())
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGJungleSystem::GetCampDifficultyMultiplier - Invalid camp index: {0}", CampIndex);
        return 1.0f;
    }

    const FAURACRONJungleCampInfo& Camp = JungleCamps[CampIndex];
    float BaseMultiplier = 1.0f;

    // Ajustar baseado no comportamento adaptativo
    switch (Camp.CurrentBehavior)
    {
        case EAURACRONJungleAdaptiveBehavior::Defensive:
            BaseMultiplier = 0.8f;
            break;
        case EAURACRONJungleAdaptiveBehavior::Aggressive:
            BaseMultiplier = 1.3f;
            break;
        case EAURACRONJungleAdaptiveBehavior::Challenging:
            BaseMultiplier = 1.5f;
            break;
        case EAURACRONJungleAdaptiveBehavior::Standard:
        default:
            BaseMultiplier = 1.0f;
            break;
    }

    // Aplicar multiplicadores adaptativos
    BaseMultiplier *= Camp.AdaptiveDamageMultiplier;
    BaseMultiplier *= Camp.AdaptiveHealthMultiplier;

    return FMath::Clamp(BaseMultiplier, 0.5f, 3.0f);
}

float AAURACRONPCGJungleSystem::GetCampRewardMultiplier(int32 CampIndex) const
{
    // Implementação robusta para obter multiplicador de recompensa
    if (CampIndex < 0 || CampIndex >= JungleCamps.Num())
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGJungleSystem::GetCampRewardMultiplier - Invalid camp index: {0}", CampIndex);
        return 1.0f;
    }

    const FAURACRONJungleCampInfo& Camp = JungleCamps[CampIndex];
    float BaseMultiplier = Camp.AdaptiveRewardMultiplier;

    // Ajustar baseado no comportamento adaptativo
    switch (Camp.CurrentBehavior)
    {
        case EAURACRONJungleAdaptiveBehavior::Rewarding:
            BaseMultiplier *= 1.5f;
            break;
        case EAURACRONJungleAdaptiveBehavior::Challenging:
            BaseMultiplier *= 1.8f;
            break;
        case EAURACRONJungleAdaptiveBehavior::Efficient:
            BaseMultiplier *= 1.2f;
            break;
        default:
            break;
    }

    return FMath::Clamp(BaseMultiplier, 0.5f, 3.0f);
}

void AAURACRONPCGJungleSystem::SetAdaptiveDifficulty(float NewDifficulty)
{
    // Implementação robusta para definir dificuldade adaptativa usando propriedades corretas
    NewDifficulty = FMath::Clamp(NewDifficulty, 0.1f, 2.0f);
    AdaptiveData.AdaptiveDifficulty = NewDifficulty;

    // Aplicar nova dificuldade a todos os camps
    for (FAURACRONJungleCampInfo& Camp : JungleCamps)
    {
        Camp.AdaptiveDamageMultiplier = NewDifficulty;
        Camp.AdaptiveHealthMultiplier = NewDifficulty;

        // Ajustar recompensas proporcionalmente
        Camp.AdaptiveRewardMultiplier = 1.0f + (NewDifficulty - 1.0f) * 0.5f;
    }

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::SetAdaptiveDifficulty - Difficulty set to {0}", NewDifficulty);
}

EAURACRONJungleAdaptiveBehavior AAURACRONPCGJungleSystem::GetRecommendedBehaviorForCamp(int32 CampIndex) const
{
    // Implementação robusta para obter comportamento recomendado
    if (CampIndex < 0 || CampIndex >= JungleCamps.Num())
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGJungleSystem::GetRecommendedBehaviorForCamp - Invalid camp index: {0}", CampIndex);
        return EAURACRONJungleAdaptiveBehavior::Standard;
    }

    const FAURACRONJungleCampInfo& Camp = JungleCamps[CampIndex];

    // Determinar comportamento baseado nos dados adaptativos usando propriedades corretas
    const float* AverageClearTime = AdaptiveData.AverageClearTime.Find(Camp.CampType);
    const int32* InteractionCount = AdaptiveData.CampInteractionCount.Find(Camp.CampType);

    if (AdaptiveData.AdaptiveDifficulty > 1.5f)
    {
        // Dificuldade alta, comportamento desafiador
        return EAURACRONJungleAdaptiveBehavior::Challenging;
    }
    else if (AdaptiveData.InvasionCount > 5)
    {
        // Muitas invasões, comportamento defensivo
        return EAURACRONJungleAdaptiveBehavior::Defensive;
    }
    else if (AdaptiveData.ObjectivesSecured > 3)
    {
        // Jogador focado em objetivos, comportamento estratégico
        return EAURACRONJungleAdaptiveBehavior::Strategic;
    }
    else if (Camp.DefeatCount > 10 && Camp.AverageDefeatTime < 20.0f)
    {
        // Camp sendo farmado muito rapidamente
        return EAURACRONJungleAdaptiveBehavior::Aggressive;
    }
    else if (Camp.InvasionsSuffered > 5)
    {
        // Camp sendo invadido frequentemente
        return EAURACRONJungleAdaptiveBehavior::Rewarding;
    }

    return EAURACRONJungleAdaptiveBehavior::Standard;
}

void AAURACRONPCGJungleSystem::ResetAdaptiveData()
{
    // Implementação robusta para resetar dados adaptativos
    AdaptiveData = FAURACRONJungleAdaptiveData();

    // Resetar dados dos camps
    for (FAURACRONJungleCampInfo& Camp : JungleCamps)
    {
        Camp.CurrentBehavior = EAURACRONJungleAdaptiveBehavior::Standard;
        Camp.AdaptiveDamageMultiplier = 1.0f;
        Camp.AdaptiveHealthMultiplier = 1.0f;
        Camp.AdaptiveRewardMultiplier = 1.0f;
        Camp.AverageDefeatTime = 0.0f;
        Camp.DefeatCount = 0;
        Camp.InvasionsSuffered = 0;
    }

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGJungleSystem::ResetAdaptiveData - Adaptive data reset");
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES INTERNAS AUXILIARES - VERSÕES ATUALIZADAS
// ========================================

void AAURACRONPCGJungleSystem::RecordCampInteractionUpdated(int32 CampIndex, float ClearTime)
{
    // Implementação robusta para registrar interação interna usando propriedades corretas
    if (CampIndex >= 0 && CampIndex < JungleCamps.Num())
    {
        // Atualizar estatísticas internas usando propriedades que existem
        AdaptiveData.TimeSinceLastAdaptation = 0.0f;

        // Adaptar comportamento se necessário
        if (bAdaptiveSystemEnabled)
        {
            AdaptCampBehaviors();
        }
    }
}

void AAURACRONPCGJungleSystem::RecordInvasionUpdated()
{
    // Implementação robusta para registrar invasão interna usando propriedades corretas
    AdaptiveData.TimeSinceLastAdaptation = 0.0f;

    // Aumentar dificuldade ligeiramente
    AdaptiveData.AdaptiveDifficulty = FMath::Clamp(AdaptiveData.AdaptiveDifficulty + 0.05f, 0.5f, 2.0f);
}

void AAURACRONPCGJungleSystem::RecordObjectiveSecuredUpdated()
{
    // Implementação robusta para registrar objetivo interno usando propriedades corretas
    AdaptiveData.TimeSinceLastAdaptation = 0.0f;

    // Ajustar comportamento para estratégico
    AdaptiveData.AdaptiveBehavior = EAURACRONJungleAdaptiveBehavior::Strategic;
}

UStaticMesh* AAURACRONPCGJungleSystem::GetCampMesh(EAURACRONJungleCampType CampType, EAURACRONEnvironmentType Environment)
{
    // Implementação robusta para obter mesh do camp baseado no tipo e ambiente

    // Buscar mesh baseado no tipo de camp e ambiente
    FString MeshPath;

    switch (CampType)
    {
        case EAURACRONJungleCampType::SpectralPack:
            switch (Environment)
            {
                case EAURACRONEnvironmentType::RadiantPlains:
                    MeshPath = TEXT("/Game/AURACRON/Meshes/Jungle/RadiantPlains/SM_SpectralPack_Radiant");
                    break;
                case EAURACRONEnvironmentType::ZephyrFirmament:
                    MeshPath = TEXT("/Game/AURACRON/Meshes/Jungle/ZephyrFirmament/SM_SpectralPack_Zephyr");
                    break;
                case EAURACRONEnvironmentType::PurgatoryRealm:
                    MeshPath = TEXT("/Game/AURACRON/Meshes/Jungle/PurgatoryRealm/SM_SpectralPack_Purgatory");
                    break;
            }
            break;

        case EAURACRONJungleCampType::StoneGuardians:
            switch (Environment)
            {
                case EAURACRONEnvironmentType::RadiantPlains:
                    MeshPath = TEXT("/Game/AURACRON/Meshes/Jungle/RadiantPlains/SM_StoneGuardians_Radiant");
                    break;
                case EAURACRONEnvironmentType::ZephyrFirmament:
                    MeshPath = TEXT("/Game/AURACRON/Meshes/Jungle/ZephyrFirmament/SM_StoneGuardians_Zephyr");
                    break;
                case EAURACRONEnvironmentType::PurgatoryRealm:
                    MeshPath = TEXT("/Game/AURACRON/Meshes/Jungle/PurgatoryRealm/SM_StoneGuardians_Purgatory");
                    break;
            }
            break;

        case EAURACRONJungleCampType::VoidRaptors:
            switch (Environment)
            {
                case EAURACRONEnvironmentType::RadiantPlains:
                    MeshPath = TEXT("/Game/AURACRON/Meshes/Jungle/RadiantPlains/SM_VoidRaptors_Radiant");
                    break;
                case EAURACRONEnvironmentType::ZephyrFirmament:
                    MeshPath = TEXT("/Game/AURACRON/Meshes/Jungle/ZephyrFirmament/SM_VoidRaptors_Zephyr");
                    break;
                case EAURACRONEnvironmentType::PurgatoryRealm:
                    MeshPath = TEXT("/Game/AURACRON/Meshes/Jungle/PurgatoryRealm/SM_VoidRaptors_Purgatory");
                    break;
            }
            break;

        case EAURACRONJungleCampType::PrismalToad:
            switch (Environment)
            {
                case EAURACRONEnvironmentType::RadiantPlains:
                    MeshPath = TEXT("/Game/AURACRON/Meshes/Jungle/RadiantPlains/SM_PrismalToad_Radiant");
                    break;
                case EAURACRONEnvironmentType::ZephyrFirmament:
                    MeshPath = TEXT("/Game/AURACRON/Meshes/Jungle/ZephyrFirmament/SM_PrismalToad_Zephyr");
                    break;
                case EAURACRONEnvironmentType::PurgatoryRealm:
                    MeshPath = TEXT("/Game/AURACRON/Meshes/Jungle/PurgatoryRealm/SM_PrismalToad_Purgatory");
                    break;
            }
            break;

        case EAURACRONJungleCampType::WindSpirits:
            switch (Environment)
            {
                case EAURACRONEnvironmentType::RadiantPlains:
                    MeshPath = TEXT("/Game/AURACRON/Meshes/Jungle/RadiantPlains/SM_WindSpirits_Radiant");
                    break;
                case EAURACRONEnvironmentType::ZephyrFirmament:
                    MeshPath = TEXT("/Game/AURACRON/Meshes/Jungle/ZephyrFirmament/SM_WindSpirits_Zephyr");
                    break;
                case EAURACRONEnvironmentType::PurgatoryRealm:
                    MeshPath = TEXT("/Game/AURACRON/Meshes/Jungle/PurgatoryRealm/SM_WindSpirits_Purgatory");
                    break;
            }
            break;

        default:
            // Usar mesh padrão se tipo não reconhecido
            MeshPath = TEXT("/Game/AURACRON/Meshes/Jungle/Default/SM_DefaultCamp");
            break;
    }

    // UE 5.6 Modern: Use async loading with StreamableManager instead of synchronous LoadObject
    if (!StreamableManager)
    {
        UE_LOGFMT(LogTemp, Error, "StreamableManager not available for async loading");
        // Fallback to synchronous loading only if StreamableManager is not available
        UStaticMesh* FallbackMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/BasicShapes/Cube"));
        return FallbackMesh;
    }

    // Convert string path to FSoftObjectPath for modern async loading
    FSoftObjectPath MeshSoftPath(MeshPath);

    // Try to get already loaded mesh first (synchronous check)
    if (UStaticMesh* AlreadyLoadedMesh = Cast<UStaticMesh>(MeshSoftPath.ResolveObject()))
    {
        UE_LOGFMT(LogTemp, Verbose, "Mesh already loaded: {0}", MeshPath);
        return AlreadyLoadedMesh;
    }

    // UE 5.6 Modern: Async loading with callback
    TSharedPtr<FStreamableHandle> Handle = StreamableManager->RequestAsyncLoad(
        MeshSoftPath,
        FStreamableDelegate::CreateUObject(this, &AAURACRONPCGJungleSystem::OnCampMeshLoaded, MeshPath)
    );

    if (Handle.IsValid())
    {
        UE_LOGFMT(LogTemp, Verbose, "Started async loading for mesh: {0}", MeshPath);
        // Store handle for tracking
        PendingMeshLoads.Add(MeshPath, Handle);
    }

    // Return fallback mesh immediately while async loading happens
    UStaticMesh* FallbackMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/BasicShapes/Cube"));
    UE_LOGFMT(LogTemp, Verbose, "Returning fallback mesh while loading: {0}", MeshPath);

    return FallbackMesh;
}

// UE 5.6 Modern: Async mesh loading callback
void AAURACRONPCGJungleSystem::OnCampMeshLoaded(const FString& MeshPath)
{
    if (!IsValid(this))
    {
        return;
    }

    UE_LOGFMT(LogTemp, Log, "Mesh loaded successfully: {0}", MeshPath);

    // Remove from pending loads
    PendingMeshLoads.Remove(MeshPath);

    // Update any camps that were waiting for this mesh
    // This would trigger a refresh of visual components
    UpdateCampVisibility();
}

// UE 5.6 Modern: RPC functions for multiplayer synchronization
void AAURACRONPCGJungleSystem::MulticastOnCampCleared_Implementation(int32 CampIndex, float RespawnTime)
{
    if (!HasAuthority())
    {
        UE_LOGFMT(LogTemp, Log, "Client received camp cleared notification: Camp {0}, Respawn in {1}s", CampIndex, RespawnTime);
        // Update client-side visual effects
        UpdateCampVisibility();
    }
}

void AAURACRONPCGJungleSystem::MulticastOnCampRespawned_Implementation(int32 CampIndex, EAURACRONJungleAdaptiveBehavior NewBehavior)
{
    if (!HasAuthority())
    {
        UE_LOGFMT(LogTemp, Log, "Client received camp respawned notification: Camp {0}, Behavior {1}", CampIndex, static_cast<int32>(NewBehavior));
        // Update client-side visual effects
        UpdateCampVisibility();
    }
}

void AAURACRONPCGJungleSystem::MulticastOnEnvironmentChanged_Implementation(EAURACRONEnvironmentType NewEnvironment)
{
    if (!HasAuthority())
    {
        UE_LOGFMT(LogTemp, Log, "Client received environment change notification: {0}", static_cast<int32>(NewEnvironment));
        CurrentEnvironment = NewEnvironment;
        UpdateCampVisibility();
    }
}

void AAURACRONPCGJungleSystem::MulticastOnMapPhaseChanged_Implementation(EAURACRONMapPhase NewPhase)
{
    if (!HasAuthority())
    {
        UE_LOGFMT(LogTemp, Log, "Client received map phase change notification: {0}", static_cast<int32>(NewPhase));
        CurrentMapPhase = NewPhase;
        ApplyMapPhaseEffects();
    }
}
