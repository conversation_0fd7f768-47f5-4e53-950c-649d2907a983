// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Data/AURACRONEnums.h"

#ifdef AURACRON_AURACRONEnums_generated_h
#error "AURACRONEnums.generated.h already included, missing '#pragma once' in AURACRONEnums.h"
#endif
#define AURACRON_AURACRONEnums_generated_h

#include "Templates/IsUEnumClass.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ReflectedTypeAccessors.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_Data_AURACRONEnums_h

// ********** Begin Enum EAURACRONMapPhase *********************************************************
#define FOREACH_ENUM_EAURACRONMAPPHASE(op) \
	op(EAURACRONMapPhase::Awakening) \
	op(EAURACRONMapPhase::Expansion) \
	op(EAURACRONMapPhase::Convergence) \
	op(EAURACRONMapPhase::Intensification) \
	op(EAURACRONMapPhase::Resolution) 

enum class EAURACRONMapPhase : uint8;
template<> struct TIsUEnumClass<EAURACRONMapPhase> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONMapPhase>();
// ********** End Enum EAURACRONMapPhase ***********************************************************

// ********** Begin Enum EAURACRONEnvironmentType **************************************************
#define FOREACH_ENUM_EAURACRONENVIRONMENTTYPE(op) \
	op(EAURACRONEnvironmentType::RadiantPlains) \
	op(EAURACRONEnvironmentType::ZephyrFirmament) \
	op(EAURACRONEnvironmentType::PurgatoryRealm) \
	op(EAURACRONEnvironmentType::PrismaticNexus) \
	op(EAURACRONEnvironmentType::CelestialBasin) \
	op(EAURACRONEnvironmentType::CrystalCaverns) 

enum class EAURACRONEnvironmentType : uint8;
template<> struct TIsUEnumClass<EAURACRONEnvironmentType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONEnvironmentType>();
// ********** End Enum EAURACRONEnvironmentType ****************************************************

// ********** Begin Enum EAURACRONObjectiveType ****************************************************
#define FOREACH_ENUM_EAURACRONOBJECTIVETYPE(op) \
	op(EAURACRONObjectiveType::None) \
	op(EAURACRONObjectiveType::FragmentAuracron) \
	op(EAURACRONObjectiveType::NexusFragment) \
	op(EAURACRONObjectiveType::TemporalRift) \
	op(EAURACRONObjectiveType::EnvironmentAnchor) \
	op(EAURACRONObjectiveType::MapAnchor) \
	op(EAURACRONObjectiveType::FusionCatalyst) \
	op(EAURACRONObjectiveType::TransitionPortal) \
	op(EAURACRONObjectiveType::PrismalGuardian) \
	op(EAURACRONObjectiveType::PrismalNexus) \
	op(EAURACRONObjectiveType::StormCore) \
	op(EAURACRONObjectiveType::SpectralGuardian) \
	op(EAURACRONObjectiveType::UmbraticLeviathan) \
	op(EAURACRONObjectiveType::RadiantShrine) \
	op(EAURACRONObjectiveType::WindSanctuary) \
	op(EAURACRONObjectiveType::PurgatoryShrine) \
	op(EAURACRONObjectiveType::ChaosRift) \
	op(EAURACRONObjectiveType::RadiantAnchor) \
	op(EAURACRONObjectiveType::ZephyrAnchor) \
	op(EAURACRONObjectiveType::PurgatoryAnchor) \
	op(EAURACRONObjectiveType::NexusIsland) \
	op(EAURACRONObjectiveType::SanctuaryIsland) \
	op(EAURACRONObjectiveType::ArsenalIsland) \
	op(EAURACRONObjectiveType::ChaosIsland) \
	op(EAURACRONObjectiveType::CapturePoint) \
	op(EAURACRONObjectiveType::PowerCore) \
	op(EAURACRONObjectiveType::ResourceNode) \
	op(EAURACRONObjectiveType::AncientRelic) \
	op(EAURACRONObjectiveType::EnergyConduit) \
	op(EAURACRONObjectiveType::DefenseTower) 

enum class EAURACRONObjectiveType : uint8;
template<> struct TIsUEnumClass<EAURACRONObjectiveType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONObjectiveType>();
// ********** End Enum EAURACRONObjectiveType ******************************************************

// ********** Begin Enum EAURACRONBuffType *********************************************************
#define FOREACH_ENUM_EAURACRONBUFFTYPE(op) \
	op(EAURACRONBuffType::MovementSpeed) \
	op(EAURACRONBuffType::DamageBoost) \
	op(EAURACRONBuffType::DefenseBoost) \
	op(EAURACRONBuffType::CooldownReduction) \
	op(EAURACRONBuffType::HealthRegeneration) \
	op(EAURACRONBuffType::ManaRegeneration) \
	op(EAURACRONBuffType::CriticalChance) \
	op(EAURACRONBuffType::AttackSpeed) \
	op(EAURACRONBuffType::SpellPower) \
	op(EAURACRONBuffType::Armor) \
	op(EAURACRONBuffType::MagicResistance) \
	op(EAURACRONBuffType::Lifesteal) \
	op(EAURACRONBuffType::Tenacity) 

enum class EAURACRONBuffType : uint8;
template<> struct TIsUEnumClass<EAURACRONBuffType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONBuffType>();
// ********** End Enum EAURACRONBuffType ***********************************************************

// ********** Begin Enum EAURACRONTemporalEffectType ***********************************************
#define FOREACH_ENUM_EAURACRONTEMPORALEFFECTTYPE(op) \
	op(EAURACRONTemporalEffectType::None) \
	op(EAURACRONTemporalEffectType::Rewind) \
	op(EAURACRONTemporalEffectType::Slow) \
	op(EAURACRONTemporalEffectType::Accelerate) \
	op(EAURACRONTemporalEffectType::Freeze) \
	op(EAURACRONTemporalEffectType::Loop) \
	op(EAURACRONTemporalEffectType::TimeAcceleration) \
	op(EAURACRONTemporalEffectType::TimeDeceleration) \
	op(EAURACRONTemporalEffectType::ChronoShield) \
	op(EAURACRONTemporalEffectType::TemporalEcho) 

enum class EAURACRONTemporalEffectType : uint8;
template<> struct TIsUEnumClass<EAURACRONTemporalEffectType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONTemporalEffectType>();
// ********** End Enum EAURACRONTemporalEffectType *************************************************

// ********** Begin Enum EAURACRONTrailType ********************************************************
#define FOREACH_ENUM_EAURACRONTRAILTYPE(op) \
	op(EAURACRONTrailType::None) \
	op(EAURACRONTrailType::Solar) \
	op(EAURACRONTrailType::Axis) \
	op(EAURACRONTrailType::Lunar) \
	op(EAURACRONTrailType::PrismalFlow) \
	op(EAURACRONTrailType::EtherealPath) \
	op(EAURACRONTrailType::NexusConnection) 

enum class EAURACRONTrailType : uint8;
template<> struct TIsUEnumClass<EAURACRONTrailType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONTrailType>();
// ********** End Enum EAURACRONTrailType **********************************************************

// ********** Begin Enum EAURACRONHardwareQuality **************************************************
#define FOREACH_ENUM_EAURACRONHARDWAREQUALITY(op) \
	op(EAURACRONHardwareQuality::Entry) \
	op(EAURACRONHardwareQuality::MidRange) \
	op(EAURACRONHardwareQuality::HighEnd) 

enum class EAURACRONHardwareQuality : uint8;
template<> struct TIsUEnumClass<EAURACRONHardwareQuality> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONHardwareQuality>();
// ********** End Enum EAURACRONHardwareQuality ****************************************************

// ********** Begin Enum EAURACRONSigilType ********************************************************
#define FOREACH_ENUM_EAURACRONSIGILTYPE(op) \
	op(EAURACRONSigilType::Aegis) \
	op(EAURACRONSigilType::Ruin) \
	op(EAURACRONSigilType::Vesper) 

enum class EAURACRONSigilType : uint8;
template<> struct TIsUEnumClass<EAURACRONSigilType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONSigilType>();
// ********** End Enum EAURACRONSigilType **********************************************************

// ********** Begin Enum EAURACRONNetworkState *****************************************************
#define FOREACH_ENUM_EAURACRONNETWORKSTATE(op) \
	op(EAURACRONNetworkState::Disconnected) \
	op(EAURACRONNetworkState::Connecting) \
	op(EAURACRONNetworkState::Connected) \
	op(EAURACRONNetworkState::Synchronizing) \
	op(EAURACRONNetworkState::Ready) \
	op(EAURACRONNetworkState::InGame) \
	op(EAURACRONNetworkState::Reconnecting) \
	op(EAURACRONNetworkState::Error) 

enum class EAURACRONNetworkState : uint8;
template<> struct TIsUEnumClass<EAURACRONNetworkState> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONNetworkState>();
// ********** End Enum EAURACRONNetworkState *******************************************************

// ********** Begin Enum EAURACRONObjectiveCategory ************************************************
#define FOREACH_ENUM_EAURACRONOBJECTIVECATEGORY(op) \
	op(EAURACRONObjectiveCategory::Core) \
	op(EAURACRONObjectiveCategory::CatchUp) \
	op(EAURACRONObjectiveCategory::Bonus) \
	op(EAURACRONObjectiveCategory::Event) \
	op(EAURACRONObjectiveCategory::Exploration) \
	op(EAURACRONObjectiveCategory::Combat) \
	op(EAURACRONObjectiveCategory::Strategic) \
	op(EAURACRONObjectiveCategory::Elite) \
	op(EAURACRONObjectiveCategory::Standard) \
	op(EAURACRONObjectiveCategory::Environment) 

enum class EAURACRONObjectiveCategory : uint8;
template<> struct TIsUEnumClass<EAURACRONObjectiveCategory> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONObjectiveCategory>();
// ********** End Enum EAURACRONObjectiveCategory **************************************************

// ********** Begin Enum EAURACRONObjectiveState ***************************************************
#define FOREACH_ENUM_EAURACRONOBJECTIVESTATE(op) \
	op(EAURACRONObjectiveState::Inactive) \
	op(EAURACRONObjectiveState::Active) \
	op(EAURACRONObjectiveState::Available) \
	op(EAURACRONObjectiveState::InProgress) \
	op(EAURACRONObjectiveState::InCombat) \
	op(EAURACRONObjectiveState::Contested) \
	op(EAURACRONObjectiveState::Captured) \
	op(EAURACRONObjectiveState::Completed) \
	op(EAURACRONObjectiveState::Respawning) \
	op(EAURACRONObjectiveState::Expired) 

enum class EAURACRONObjectiveState : uint8;
template<> struct TIsUEnumClass<EAURACRONObjectiveState> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONObjectiveState>();
// ********** End Enum EAURACRONObjectiveState *****************************************************

// ********** Begin Enum EAURACRONEnergyType *******************************************************
#define FOREACH_ENUM_EAURACRONENERGYTYPE(op) \
	op(EAURACRONEnergyType::Golden) \
	op(EAURACRONEnergyType::Silver) \
	op(EAURACRONEnergyType::Violet) \
	op(EAURACRONEnergyType::Solar) \
	op(EAURACRONEnergyType::Lunar) \
	op(EAURACRONEnergyType::Prismal) \
	op(EAURACRONEnergyType::Chaos) \
	op(EAURACRONEnergyType::Void) 

enum class EAURACRONEnergyType : uint8;
template<> struct TIsUEnumClass<EAURACRONEnergyType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONEnergyType>();
// ********** End Enum EAURACRONEnergyType *********************************************************

// ********** Begin Enum EAURACRONIslandType *******************************************************
#define FOREACH_ENUM_EAURACRONISLANDTYPE(op) \
	op(EAURACRONIslandType::None) \
	op(EAURACRONIslandType::Nexus) \
	op(EAURACRONIslandType::Sanctuary) \
	op(EAURACRONIslandType::Arsenal) \
	op(EAURACRONIslandType::Chaos) \
	op(EAURACRONIslandType::Battlefield) 

enum class EAURACRONIslandType : uint8;
template<> struct TIsUEnumClass<EAURACRONIslandType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONIslandType>();
// ********** End Enum EAURACRONIslandType *********************************************************

// ********** Begin Enum EAURACRONJungleCampType ***************************************************
#define FOREACH_ENUM_EAURACRONJUNGLECAMPTYPE(op) \
	op(EAURACRONJungleCampType::None) \
	op(EAURACRONJungleCampType::RadiantEssence) \
	op(EAURACRONJungleCampType::ChaosEssence) \
	op(EAURACRONJungleCampType::SpectralPack) \
	op(EAURACRONJungleCampType::EtherealGrove) \
	op(EAURACRONJungleCampType::VoidRaptors) \
	op(EAURACRONJungleCampType::CrystalWolves) \
	op(EAURACRONJungleCampType::StoneGuardians) \
	op(EAURACRONJungleCampType::PrismalToad) \
	op(EAURACRONJungleCampType::WindSpirits) \
	op(EAURACRONJungleCampType::FluxCrawler) \
	op(EAURACRONJungleCampType::PrismalDragon) \
	op(EAURACRONJungleCampType::AncientGuardian) \
	op(EAURACRONJungleCampType::VoidHarpy) \
	op(EAURACRONJungleCampType::ShadowWolf) \
	op(EAURACRONJungleCampType::CrystalGolem) \
	op(EAURACRONJungleCampType::Small) \
	op(EAURACRONJungleCampType::Medium) \
	op(EAURACRONJungleCampType::Large) \
	op(EAURACRONJungleCampType::Epic) 

enum class EAURACRONJungleCampType : uint8;
template<> struct TIsUEnumClass<EAURACRONJungleCampType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONJungleCampType>();
// ********** End Enum EAURACRONJungleCampType *****************************************************

// ********** Begin Enum EAURACRONDeviceType *******************************************************
#define FOREACH_ENUM_EAURACRONDEVICETYPE(op) \
	op(EAURACRONDeviceType::None) \
	op(EAURACRONDeviceType::Entry) \
	op(EAURACRONDeviceType::Mid) \
	op(EAURACRONDeviceType::High) \
	op(EAURACRONDeviceType::Unknown) \
	op(EAURACRONDeviceType::Generator) \
	op(EAURACRONDeviceType::Amplifier) \
	op(EAURACRONDeviceType::Stabilizer) \
	op(EAURACRONDeviceType::Resonator) 

enum class EAURACRONDeviceType : uint8;
template<> struct TIsUEnumClass<EAURACRONDeviceType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONDeviceType>();
// ********** End Enum EAURACRONDeviceType *********************************************************

// ********** Begin Enum EAURACRONStreamingQualityProfile ******************************************
#define FOREACH_ENUM_EAURACRONSTREAMINGQUALITYPROFILE(op) \
	op(EAURACRONStreamingQualityProfile::Low) \
	op(EAURACRONStreamingQualityProfile::Medium) \
	op(EAURACRONStreamingQualityProfile::High) \
	op(EAURACRONStreamingQualityProfile::Ultra) \
	op(EAURACRONStreamingQualityProfile::Custom) 

enum class EAURACRONStreamingQualityProfile : uint8;
template<> struct TIsUEnumClass<EAURACRONStreamingQualityProfile> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONStreamingQualityProfile>();
// ********** End Enum EAURACRONStreamingQualityProfile ********************************************

// ********** Begin Enum EEnvironmentBlurType ******************************************************
#define FOREACH_ENUM_EENVIRONMENTBLURTYPE(op) \
	op(EEnvironmentBlurType::Gaussian) \
	op(EEnvironmentBlurType::Atmospheric) \
	op(EEnvironmentBlurType::Spectral) \
	op(EEnvironmentBlurType::Radial) \
	op(EEnvironmentBlurType::Motion) \
	op(EEnvironmentBlurType::Depth) 

enum class EEnvironmentBlurType : uint8;
template<> struct TIsUEnumClass<EEnvironmentBlurType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EEnvironmentBlurType>();
// ********** End Enum EEnvironmentBlurType ********************************************************

// ********** Begin Enum EAURACRONPerformanceLevel *************************************************
#define FOREACH_ENUM_EAURACRONPERFORMANCELEVEL(op) \
	op(EAURACRONPerformanceLevel::Entry) \
	op(EAURACRONPerformanceLevel::Mid) \
	op(EAURACRONPerformanceLevel::High) \
	op(EAURACRONPerformanceLevel::Ultra) \
	op(EAURACRONPerformanceLevel::Custom) 

enum class EAURACRONPerformanceLevel : uint8;
template<> struct TIsUEnumClass<EAURACRONPerformanceLevel> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONPerformanceLevel>();
// ********** End Enum EAURACRONPerformanceLevel ***************************************************

// ********** Begin Enum EAURACRONIslandSector *****************************************************
#define FOREACH_ENUM_EAURACRONISLANDSECTOR(op) \
	op(EAURACRONIslandSector::None) \
	op(EAURACRONIslandSector::Nexus) \
	op(EAURACRONIslandSector::Sanctuary) \
	op(EAURACRONIslandSector::Arsenal) \
	op(EAURACRONIslandSector::Chaos) 

enum class EAURACRONIslandSector : uint8;
template<> struct TIsUEnumClass<EAURACRONIslandSector> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EAURACRONIslandSector>();
// ********** End Enum EAURACRONIslandSector *******************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
