// Copyright Aura Cronos Studios, Inc. All Rights Reserved.

#include "PCG/AURACRONPCGChaosPortal.h"
#include "Data/AURACRONEnums.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "NiagaraSystem.h"
#include "Components/StaticMeshComponent.h"
#include "Components/PointLightComponent.h"
#include "Components/AudioComponent.h"
#include "Components/SphereComponent.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetMathLibrary.h"
#include "GameFramework/Character.h"
#include "GameFramework/PlayerController.h"
#include "Engine/World.h"
#include "Engine/DataTable.h"
#include "Engine/StreamableManager.h"
#include "Engine/AssetManager.h"
#include "EngineUtils.h"
#include "TimerManager.h"
#include "Logging/StructuredLog.h"
#include "Net/UnrealNetwork.h"
#include "HAL/Platform.h"
#include "Math/UnrealMathUtility.h"
#include "ProfilingDebugging/CsvProfiler.h"
#include "Sound/SoundBase.h"
#include "Materials/MaterialInterface.h"
#include "Components/MeshComponent.h"
#include "Engine/CollisionProfile.h"
#include "Engine/HitResult.h"
#include "CollisionQueryParams.h"

// Sets default values
AAURACRONPCGChaosPortal::AAURACRONPCGChaosPortal()
{
    // Otimização UE 5.6: Usar timers em vez de Tick constante para melhor performance
    PrimaryActorTick.bCanEverTick = false;

    // Criar componente raiz
    RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootComponent"));

    // Criar componente de malha estática
    PortalMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("PortalMesh"));
    PortalMesh->SetupAttachment(RootComponent);
    PortalMesh->SetCollisionProfileName(TEXT("NoCollision"));
    PortalMesh->SetGenerateOverlapEvents(false);

    // Criar componente de partículas Niagara
    PortalEffect = CreateDefaultSubobject<UNiagaraComponent>(TEXT("PortalEffect"));
    PortalEffect->SetupAttachment(RootComponent);

    // Criar componente de luz
    PortalLight = CreateDefaultSubobject<UPointLightComponent>(TEXT("PortalLight"));
    PortalLight->SetupAttachment(RootComponent);
    PortalLight->SetLightColor(FLinearColor(0.8f, 0.2f, 0.9f, 1.0f)); // Roxo
    PortalLight->SetIntensity(8000.0f);
    PortalLight->SetAttenuationRadius(1000.0f);
    PortalLight->SetCastShadows(false);

    // Criar componente de áudio
    PortalSound = CreateDefaultSubobject<UAudioComponent>(TEXT("PortalSound"));
    PortalSound->SetupAttachment(RootComponent);
    PortalSound->bAutoActivate = false;

    // Criar componente de colisão para trigger
    TriggerSphere = CreateDefaultSubobject<USphereComponent>(TEXT("TriggerSphere"));
    TriggerSphere->SetupAttachment(RootComponent);
    TriggerSphere->SetSphereRadius(500.0f);
    TriggerSphere->SetCollisionProfileName(TEXT("Trigger"));
    TriggerSphere->SetGenerateOverlapEvents(true);

    // Valores padrão
    EffectRadius = 500.0f;
    PortalDuration = 0.0f; // Permanente por padrão
    PortalIntensity = 1.0f;
    PortalColor = FLinearColor(0.8f, 0.2f, 0.9f, 1.0f); // Roxo
    RotationSpeed = 20.0f;
    PulsateFrequency = 1.0f;
    PulsateIntensity = 0.3f;
    EffectInterval = 5.0f;
    QualityScale = 1.0f;
    CurrentMapPhase = EAURACRONMapPhase::Awakening;
    ElapsedTime = 0.0f;
    TimeSinceLastEffect = 0.0f;
    bPortalActive = false;
    bFadingOut = false;
    FadeOutTime = 1.0f;
    FadeOutElapsed = 0.0f;
}

// Called when the game starts or when spawned
void AAURACRONPCGChaosPortal::BeginPlay()
{
    Super::BeginPlay();
    
    // Configurar trigger events
    TriggerSphere->OnComponentBeginOverlap.AddDynamic(this, &AAURACRONPCGChaosPortal::OnPlayerEnterPortalRadius);
    TriggerSphere->OnComponentEndOverlap.AddDynamic(this, &AAURACRONPCGChaosPortal::OnPlayerExitPortalRadius);
    
    // Criar material dinâmico
    if (PortalMesh && PortalMesh->GetMaterial(0))
    {
        PortalDynamicMaterial = UMaterialInstanceDynamic::Create(PortalMesh->GetMaterial(0), this);
        PortalMesh->SetMaterial(0, PortalDynamicMaterial);
    }
    
    // Desativar portal inicialmente
    PortalMesh->SetVisibility(false);
    PortalEffect->Deactivate();
    PortalLight->SetVisibility(false);
    
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGChaosPortal: Inicializado");
}

void AAURACRONPCGChaosPortal::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Limpeza robusta para UE 5.6
    StopPortalTimers();

    // Limpar delegates
    if (TriggerSphere)
    {
        TriggerSphere->OnComponentBeginOverlap.RemoveAll(this);
        TriggerSphere->OnComponentEndOverlap.RemoveAll(this);
    }

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGChaosPortal: EndPlay executado, razão: {EndPlayReason}", static_cast<int32>(EndPlayReason));

    Super::EndPlay(EndPlayReason);
}

// Called every frame - Otimizado para UE 5.6 usando Timers
void AAURACRONPCGChaosPortal::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Sistema otimizado: Tick apenas processa fade out se necessário
    // Outros processamentos foram movidos para Timers para melhor performance
    if (bPortalActive && bFadingOut)
    {
        ProcessFadeOut(DeltaTime);
    }
}

void AAURACRONPCGChaosPortal::ActivatePortal(float Duration, float Intensity)
{
    // Validações robustas
    if (!IsValid(this) || !GetWorld())
    {
        UE_LOGFMT(LogTemp, Error, "AAURACRONPCGChaosPortal::ActivatePortal: Invalid world or actor");
        return;
    }

    if (Intensity <= 0.0f || !FMath::IsFinite(Intensity))
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGChaosPortal::ActivatePortal: Invalid intensity {0}, clamping to 1.0", Intensity);
        Intensity = 1.0f;
    }

    if (Duration < 0.0f || !FMath::IsFinite(Duration))
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGChaosPortal::ActivatePortal: Invalid duration {0}, setting to permanent", Duration);
        Duration = 0.0f;
    }

    // Configurar parâmetros do portal
    PortalDuration = Duration;
    PortalIntensity = FMath::Clamp(Intensity, 0.1f, 5.0f);

    // Reiniciar timers
    ElapsedTime = 0.0f;
    TimeSinceLastEffect = 0.0f;

    // Ativar portal
    bPortalActive = true;
    bFadingOut = false;

    // Ativar efeitos visuais
    if (PortalMesh)
    {
        PortalMesh->SetVisibility(true);
    }

    if (PortalEffect)
    {
        PortalEffect->Activate(true);
    }

    if (PortalLight)
    {
        PortalLight->SetVisibility(true);
    }

    if (PortalSound)
    {
        PortalSound->Play();
    }

    // Atualizar raio do trigger
    if (TriggerSphere)
    {
        TriggerSphere->SetSphereRadius(EffectRadius);
    }

    // Atualizar efeitos visuais iniciais
    UpdateVisualEffects(0.0f);

    // Sistema otimizado UE 5.6: Usar Timers em vez de Tick constante
    StartPortalTimers();

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGChaosPortal: Portal ativado com intensidade {0} e duração {1}",
           PortalIntensity, Duration);
}

void AAURACRONPCGChaosPortal::DeactivatePortal(float InFadeOutTime)
{
    // Validações robustas
    if (InFadeOutTime < 0.0f || !FMath::IsFinite(InFadeOutTime))
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGChaosPortal::DeactivatePortal: Invalid fade out time {0}, using default", InFadeOutTime);
        InFadeOutTime = 1.0f;
    }

    // Configurar fade out
    bFadingOut = true;
    FadeOutTime = FMath::Clamp(InFadeOutTime, 0.1f, 10.0f);
    FadeOutElapsed = 0.0f;

    // Parar timers de atualização (manter apenas fade out)
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(VisualEffectsTimerHandle);
        GetWorld()->GetTimerManager().ClearTimer(EffectTriggerTimerHandle);
        GetWorld()->GetTimerManager().ClearTimer(ExpirationTimerHandle);
    }

    // Habilitar Tick apenas para fade out
    PrimaryActorTick.bCanEverTick = true;

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGChaosPortal: Iniciando desativação do portal com fade out de {0} segundos",
           FadeOutTime);
}

void AAURACRONPCGChaosPortal::SetQualityScale(float NewQualityScale)
{
    QualityScale = FMath::Clamp(NewQualityScale, 0.1f, 1.0f);
    
    // Ajustar qualidade dos efeitos visuais
    if (PortalEffect)
    {
        // Ajustar densidade de partículas baseado na escala de qualidade
        PortalEffect->SetFloatParameter(FName("ParticleDensity"), QualityScale);
        
        // Ajustar qualidade de iluminação
        PortalLight->SetIntensity(8000.0f * QualityScale);
    }
    
    UE_LOGFMT(LogTemp, Verbose, "AAURACRONPCGChaosPortal: Escala de qualidade ajustada para {0}", QualityScale);
}

void AAURACRONPCGChaosPortal::UpdateForMapPhase(EAURACRONMapPhase MapPhase)
{
    CurrentMapPhase = MapPhase;
    
    // Ajustar parâmetros baseados na fase do mapa
    switch (CurrentMapPhase)
    {
        case EAURACRONMapPhase::Awakening:
            PortalColor = FLinearColor(0.5f, 0.2f, 0.8f, 1.0f); // Roxo claro
            PulsateIntensity = 0.2f;
            PulsateFrequency = 0.5f;
            RotationSpeed = 10.0f;
            break;
            
        case EAURACRONMapPhase::Convergence:
            PortalColor = FLinearColor(0.6f, 0.2f, 0.9f, 1.0f); // Roxo médio
            PulsateIntensity = 0.3f;
            PulsateFrequency = 0.8f;
            RotationSpeed = 15.0f;
            break;
            
        case EAURACRONMapPhase::Intensification:
            PortalColor = FLinearColor(0.7f, 0.1f, 1.0f, 1.0f); // Roxo intenso
            PulsateIntensity = 0.4f;
            PulsateFrequency = 1.2f;
            RotationSpeed = 20.0f;
            break;
            
        case EAURACRONMapPhase::Resolution:
            PortalColor = FLinearColor(0.9f, 0.1f, 1.0f, 1.0f); // Roxo brilhante
            PulsateIntensity = 0.5f;
            PulsateFrequency = 1.5f;
            RotationSpeed = 25.0f;
            break;
            
        default:
            break;
    }
    
    // Atualizar efeitos visuais
    if (bPortalActive)
    {
        UpdateVisualEffects(0.0f);
    }
    
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGChaosPortal: Atualizado para fase {CurrentMapPhase}", static_cast<int32>(CurrentMapPhase));
}

void AAURACRONPCGChaosPortal::TriggerPortalEffect(float SpawnProbability)
{
    // Verificar se devemos spawnar um efeito baseado na probabilidade
    if (FMath::FRand() > SpawnProbability)
    {
        return;
    }
    
    // Determinar tipo de efeito baseado na fase do mapa
    int32 EffectType = FMath::RandRange(0, 2);
    
    // Aumentar chance de efeitos mais intensos na fase Resolution
    if (CurrentMapPhase == EAURACRONMapPhase::Resolution)
    {
        EffectType = FMath::RandRange(1, 3);
    }
    
    // Aplicar efeito baseado no tipo
    switch (EffectType)
    {
        case 0: // Pulso de energia menor
            {
                // Criar efeito visual temporário
                UNiagaraSystem* PulseSystem = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Portal/NS_ChaosPortalPulse"));
                if (!PulseSystem)
                {
                    // Criar sistema de partículas específico para pulso do portal
                    PulseSystem = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Portal/NS_ChaosPortalPulse"));
                    if (!PulseSystem)
                    {
                        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGChaosPortal: Sistema de pulso não encontrado, usando efeito padrão do portal");
                        PulseSystem = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Portal/NS_ChaosPortalDefault"));
                    }
                }

                UNiagaraComponent* PulseEffect = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                    GetWorld(),
                    PulseSystem,
                    GetActorLocation(),
                    GetActorRotation(),
                    FVector(1.0f),
                    true,
                    true,
                    ENCPoolMethod::AutoRelease
                );

                if (PulseEffect)
                {
                    PulseEffect->SetColorParameter(FName("Color"), PortalColor);
                    PulseEffect->SetFloatParameter(FName("Size"), 200.0f * PortalIntensity);
                }

                UE_LOGFMT(LogTemp, Verbose, "AAURACRONPCGChaosPortal: Efeito de pulso menor gerado");
            }
            break;
            
        case 1: // Distorção espacial
            {
                // Criar efeito de distorção
                UNiagaraSystem* DistortionSystem = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Portal/NS_ChaosPortalDistortion"));
                if (!DistortionSystem)
                {
                    // Criar sistema de partículas específico para distorção do portal
                    DistortionSystem = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Portal/NS_ChaosPortalDistortionFallback"));
                    if (!DistortionSystem)
                    {
                        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGChaosPortal: Sistema de distorção não encontrado, usando efeito padrão do portal");
                        DistortionSystem = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Portal/NS_ChaosPortalDefault"));
                    }
                }

                UNiagaraComponent* DistortionEffect = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                    GetWorld(),
                    DistortionSystem,
                    GetActorLocation(),
                    GetActorRotation(),
                    FVector(1.0f),
                    true,
                    true,
                    ENCPoolMethod::AutoRelease
                );

                if (DistortionEffect)
                {
                    DistortionEffect->SetColorParameter(FName("Color"), PortalColor);
                    DistortionEffect->SetFloatParameter(FName("Intensity"), PortalIntensity);
                    DistortionEffect->SetFloatParameter(FName("Duration"), 3.0f);
                }

                // Aplicar efeito de distorção de tela aos jogadores próximos
                ApplyEffectsToPlayers();

                UE_LOGFMT(LogTemp, Verbose, "AAURACRONPCGChaosPortal: Efeito de distorção espacial gerado");
            }
            break;
            
        case 2: // Spawn de objeto de caos
            {
                // Calcular posição aleatória próxima ao portal
                FVector SpawnLocation = GetActorLocation();
                SpawnLocation += FMath::VRand() * FMath::RandRange(100.0f, 300.0f);
                SpawnLocation.Z = GetActorLocation().Z; // Manter mesma altura
                
                // Spawnar objeto de caos
                TSubclassOf<AActor> ChaosObjectClass = LoadClass<AActor>(nullptr, TEXT("/Game/Blueprints/Chaos/BP_ChaosObject"));
                if (!ChaosObjectClass)
                {
                    // Tentar carregar classe alternativa de objeto de caos
                    ChaosObjectClass = LoadClass<AActor>(nullptr, TEXT("/Game/Blueprints/Chaos/BP_ChaosObjectBasic"));
                    if (!ChaosObjectClass)
                    {
                        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGChaosPortal: Classe de objeto de caos não encontrada, usando objeto padrão");
                        ChaosObjectClass = LoadClass<AActor>(nullptr, TEXT("/Game/Blueprints/Chaos/BP_ChaosObjectDefault"));
                        if (!ChaosObjectClass)
                        {
                            ChaosObjectClass = AActor::StaticClass();
                        }
                    }
                }

                AActor* ChaosObject = GetWorld()->SpawnActor<AActor>(
                    ChaosObjectClass,
                    SpawnLocation,
                    FRotator::ZeroRotator
                );

                if (ChaosObject)
                {
                    // Configurar objeto de caos com duração limitada
                    ChaosObject->SetLifeSpan(10.0f + FMath::RandRange(-2.0f, 5.0f));

                    // Adicionar componente de efeito visual se disponível
                    if (UStaticMeshComponent* MeshComp = ChaosObject->FindComponentByClass<UStaticMeshComponent>())
                    {
                        MeshComp->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
                        MeshComp->SetCollisionResponseToAllChannels(ECR_Overlap);
                    }
                }

                UE_LOGFMT(LogTemp, Verbose, "AAURACRONPCGChaosPortal: Objeto de caos gerado");
            }
            break;
            
        case 3: // Efeito de caos maior (apenas na fase Resolution)
            {
                // Criar efeito visual maior
                UNiagaraSystem* MajorSystem = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Portal/NS_ChaosPortalMajor"));
                if (!MajorSystem)
                {
                    // Fallback para sistema genérico
                    MajorSystem = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Engine/VFX/Niagara/Systems/NS_GPUSprites"));
                }
                
                UNiagaraComponent* MajorEffect = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                    GetWorld(),
                    MajorSystem,
                    GetActorLocation(),
                    GetActorRotation(),
                    FVector(2.0f),
                    true,
                    true,
                    ENCPoolMethod::AutoRelease
                );
                
                if (MajorEffect)
                {
                    MajorEffect->SetColorParameter(FName("Color"), PortalColor);
                    MajorEffect->SetFloatParameter(FName("Intensity"), PortalIntensity * 2.0f);
                    MajorEffect->SetFloatParameter(FName("Duration"), 5.0f);
                }
                
                // Aplicar efeito maior aos jogadores próximos
                ApplyEffectsToPlayers();

                UE_LOGFMT(LogTemp, Verbose, "AAURACRONPCGChaosPortal: Efeito de caos maior gerado");
            }
            break;
            
        default:
            break;
    }
}

void AAURACRONPCGChaosPortal::UpdateVisualEffects(float DeltaTime)
{
    // Atualizar rotação do portal
    if (PortalMesh)
    {
        FRotator NewRotation = PortalMesh->GetRelativeRotation();
        NewRotation.Yaw += RotationSpeed * DeltaTime;
        PortalMesh->SetRelativeRotation(NewRotation);
    }
    
    // Calcular pulsação
    float PulseValue = FMath::Sin(ElapsedTime * PulsateFrequency * PI * 2.0f) * PulsateIntensity + 1.0f;
    
    // Atualizar material dinâmico
    if (PortalDynamicMaterial)
    {
        PortalDynamicMaterial->SetVectorParameterValue(FName("Color"), PortalColor);
        PortalDynamicMaterial->SetScalarParameterValue(FName("Intensity"), PortalIntensity * PulseValue);
        PortalDynamicMaterial->SetScalarParameterValue(FName("Opacity"), 1.0f);
    }
    
    // Atualizar efeito de partículas
    if (PortalEffect)
    {
        PortalEffect->SetColorParameter(FName("Color"), PortalColor);
        PortalEffect->SetFloatParameter(FName("Size"), EffectRadius * PulseValue * 0.5f);
        PortalEffect->SetFloatParameter(FName("Intensity"), PortalIntensity * PulseValue);
    }
    
    // Atualizar luz
    if (PortalLight)
    {
        PortalLight->SetLightColor(PortalColor);
        PortalLight->SetIntensity(8000.0f * PortalIntensity * PulseValue * QualityScale);
    }
}

void AAURACRONPCGChaosPortal::ApplyEffectsToPlayers()
{
    // Obter todos os jogadores
    TArray<AActor*> Players;
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), ACharacter::StaticClass(), Players);
    
    // Aplicar efeitos aos jogadores dentro do raio
    for (AActor* Player : Players)
    {
        if (!Player)
        {
            continue;
        }
        
        // Calcular distância do jogador ao portal
        float Distance = FVector::Dist(Player->GetActorLocation(), GetActorLocation());
        
        // Verificar se o jogador está dentro do raio de efeito
        if (Distance <= EffectRadius)
        {
            // Calcular intensidade baseada na distância (mais forte no centro)
            float DistanceFactor = 1.0f - (Distance / EffectRadius);
            float EffectIntensity = PortalIntensity * DistanceFactor;
            
            // Aplicar efeito ao jogador (distorção de tela, dano, etc.)
            // Implementação específica depende do design do jogo
            
            // Exemplo: Aplicar efeito de post-process ao jogador
            APlayerController* PC = Cast<APlayerController>(UGameplayStatics::GetPlayerController(GetWorld(), 0));
            if (PC)
            {
                // Implementar efeito de post-process
            }
            
            UE_LOGFMT(LogTemp, Verbose, "AAURACRONPCGChaosPortal: Aplicando efeito ao jogador {0} com intensidade {1}",
                   Player->GetName(), EffectIntensity);
        }
    }
}

void AAURACRONPCGChaosPortal::ProcessFadeOut(float DeltaTime)
{
    // Atualizar tempo de fade out
    FadeOutElapsed += DeltaTime;
    
    // Calcular alpha de fade out (1.0 -> 0.0)
    float Alpha = 1.0f - FMath::Clamp(FadeOutElapsed / FadeOutTime, 0.0f, 1.0f);
    
    // Atualizar material dinâmico
    if (PortalDynamicMaterial)
    {
        PortalDynamicMaterial->SetScalarParameterValue(FName("Opacity"), Alpha);
    }
    
    // Atualizar efeito de partículas
    if (PortalEffect)
    {
        PortalEffect->SetFloatParameter(FName("Intensity"), PortalIntensity * Alpha);
    }
    
    // Atualizar luz
    if (PortalLight)
    {
        PortalLight->SetIntensity(8000.0f * PortalIntensity * Alpha * QualityScale);
    }
    
    // Verificar se o fade out terminou
    if (FadeOutElapsed >= FadeOutTime)
    {
        // Desativar portal
        bPortalActive = false;
        bFadingOut = false;

        // Parar todos os timers
        StopPortalTimers();

        // Desativar efeitos visuais com validações robustas
        if (PortalMesh)
        {
            PortalMesh->SetVisibility(false);
        }

        if (PortalEffect)
        {
            PortalEffect->Deactivate();
        }

        if (PortalLight)
        {
            PortalLight->SetVisibility(false);
        }

        if (PortalSound)
        {
            PortalSound->Stop();
        }

        UE_LOGFMT(LogTemp, Log, "AAURACRONPCGChaosPortal: Portal desativado após fade out");
    }
}

void AAURACRONPCGChaosPortal::OnPlayerEnterPortalRadius(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, 
                                                      UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, 
                                                      bool bFromSweep, const FHitResult& SweepResult)
{
    // Verificar se é um jogador
    ACharacter* Character = Cast<ACharacter>(OtherActor);
    if (!Character || !Character->IsPlayerControlled())
    {
        return;
    }
    
    // Aplicar efeito inicial ao jogador
    // Implementação específica depende do design do jogo
    
    UE_LOGFMT(LogTemp, Verbose, "AAURACRONPCGChaosPortal: Jogador {0} entrou no raio do portal",
           OtherActor->GetName());
}

void AAURACRONPCGChaosPortal::OnPlayerExitPortalRadius(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, 
                                                     UPrimitiveComponent* OtherComp, int32 OtherBodyIndex)
{
    // Verificar se é um jogador
    ACharacter* Character = Cast<ACharacter>(OtherActor);
    if (!Character || !Character->IsPlayerControlled())
    {
        return;
    }
    
    // Remover efeito do jogador
    // Implementação específica depende do design do jogo
    
    UE_LOGFMT(LogTemp, Verbose, "AAURACRONPCGChaosPortal: Jogador {0} saiu do raio do portal",
           OtherActor->GetName());
}

// Implementação das funções ausentes usando APIs modernas do UE 5.6

void AAURACRONPCGChaosPortal::SpawnHighRiskReward(float RewardTier)
{
    if (!bPortalActive)
    {
        return;
    }

    // Verificar probabilidade de spawn
    float RandomValue = FMath::RandRange(0.0f, 1.0f);
    if (RandomValue > HighRiskRewardProbability)
    {
        return;
    }

    // Calcular posição de spawn baseada no portal
    FVector SpawnLocation = GetActorLocation();
    SpawnLocation.Z += 100.0f; // Elevar um pouco acima do portal

    // Adicionar variação aleatória na posição
    FVector RandomOffset = FVector(
        FMath::RandRange(-EffectRadius * 0.5f, EffectRadius * 0.5f),
        FMath::RandRange(-EffectRadius * 0.5f, EffectRadius * 0.5f),
        0.0f
    );
    SpawnLocation += RandomOffset;

    // Spawnar efeito visual de recompensa
    SpawnHighRiskRewardVisual(SpawnLocation, RewardTier);

    // Buscar tabela de recompensas se disponível
    if (HighRiskRewardsTable)
    {
        // Determinar tier da recompensa baseado no RewardTier
        FString RowName;
        if (RewardTier >= 3.0f)
        {
            RowName = TEXT("Legendary");
        }
        else if (RewardTier >= 2.0f)
        {
            RowName = TEXT("Epic");
        }
        else
        {
            RowName = TEXT("Rare");
        }

        // Tentar obter dados da tabela usando API moderna do UE 5.6
        FTableRowBase* RowData = HighRiskRewardsTable->FindRow<FTableRowBase>(FName(*RowName), TEXT("SpawnHighRiskReward"));
        if (RowData)
        {
            UE_LOGFMT(LogTemp, Log, "AAURACRONPCGChaosPortal: Spawning high-risk reward of tier {0} at location {1}",
                RowName, SpawnLocation.ToString());
        }
    }

    // Spawnar item físico no mundo (implementação robusta)
    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
    SpawnParams.bNoFail = true;

    // Buscar classe de item de recompensa usando APIs modernas
    UClass* RewardItemClass = LoadClass<AActor>(nullptr, TEXT("/Game/Items/HighRiskReward_BP"));
    if (RewardItemClass)
    {
        AActor* SpawnedReward = GetWorld()->SpawnActor<AActor>(RewardItemClass, SpawnLocation, FRotator::ZeroRotator, SpawnParams);
        if (SpawnedReward)
        {
            // Configurar propriedades do item baseado no tier
            if (UPrimitiveComponent* PrimComp = SpawnedReward->FindComponentByClass<UPrimitiveComponent>())
            {
                // Aplicar escala baseada no tier
                float ScaleMultiplier = 1.0f + (RewardTier * 0.3f);
                SpawnedReward->SetActorScale3D(FVector(ScaleMultiplier));

                // Aplicar glow effect baseado no tier
                if (UMeshComponent* MeshComp = SpawnedReward->FindComponentByClass<UMeshComponent>())
                {
                    if (UMaterialInterface* BaseMaterial = MeshComp->GetMaterial(0))
                    {
                        UMaterialInstanceDynamic* DynamicMaterial = MeshComp->CreateDynamicMaterialInstance(0, BaseMaterial);
                        if (DynamicMaterial)
                        {
                            FLinearColor GlowColor = FLinearColor::White;
                            if (RewardTier >= 3.0f)
                            {
                                GlowColor = FLinearColor(1.0f, 0.5f, 0.0f); // Laranja lendário
                            }
                            else if (RewardTier >= 2.0f)
                            {
                                GlowColor = FLinearColor(0.5f, 0.0f, 1.0f); // Roxo épico
                            }
                            else
                            {
                                GlowColor = FLinearColor(0.0f, 0.5f, 1.0f); // Azul raro
                            }

                            DynamicMaterial->SetVectorParameterValue(FName("GlowColor"), GlowColor);
                            DynamicMaterial->SetScalarParameterValue(FName("GlowIntensity"), RewardTier);
                        }
                    }
                }
            }

            UE_LOGFMT(LogTemp, Log, "AAURACRONPCGChaosPortal: Successfully spawned high-risk reward with tier {0}", RewardTier);
        }
    }

    // Som de spawn de recompensa
    if (USoundBase* RewardSpawnSound = LoadObject<USoundBase>(nullptr, TEXT("/Game/Audio/ChaosPortal/SFX_HighRiskRewardSpawn")))
    {
        UGameplayStatics::PlaySoundAtLocation(
            GetWorld(),
            RewardSpawnSound,
            SpawnLocation,
            1.0f + (RewardTier * 0.5f), // Volume baseado no tier
            1.0f,
            0.0f
        );
    }
}

void AAURACRONPCGChaosPortal::ActivateEnvironmentalHazard(float HazardIntensity)
{
    if (!bPortalActive)
    {
        return;
    }

    // Verificar probabilidade de ativação
    float RandomValue = FMath::RandRange(0.0f, 1.0f);
    if (RandomValue > EnvironmentalHazardProbability)
    {
        return;
    }

    // Calcular múltiplas posições de perigo em volta do portal
    TArray<FVector> HazardLocations;
    int32 NumHazards = FMath::RandRange(2, 5); // 2 a 5 perigos

    for (int32 i = 0; i < NumHazards; ++i)
    {
        float Angle = (2.0f * PI * i) / NumHazards;
        float Distance = FMath::RandRange(EffectRadius * 0.3f, EffectRadius * 0.8f);

        FVector HazardLocation = GetActorLocation();
        HazardLocation.X += FMath::Cos(Angle) * Distance;
        HazardLocation.Y += FMath::Sin(Angle) * Distance;

        // Ajustar altura baseado no terreno usando line trace
        FVector TraceStart = HazardLocation + FVector(0, 0, 1000);
        FVector TraceEnd = HazardLocation - FVector(0, 0, 1000);

        FHitResult HitResult;
        FCollisionQueryParams QueryParams;
        QueryParams.bTraceComplex = false;
        QueryParams.AddIgnoredActor(this);

        if (GetWorld()->LineTraceSingleByChannel(HitResult, TraceStart, TraceEnd, ECC_WorldStatic, QueryParams))
        {
            HazardLocation = HitResult.Location + FVector(0, 0, 10); // Elevar um pouco acima do chão
        }

        HazardLocations.Add(HazardLocation);
    }

    // Spawnar perigos ambientais em cada localização
    for (const FVector& Location : HazardLocations)
    {
        // Spawnar efeito visual
        SpawnEnvironmentalHazardVisual(Location, HazardIntensity);

        // Determinar tipo de perigo baseado na intensidade
        FString HazardType;
        if (HazardIntensity >= 2.5f)
        {
            HazardType = TEXT("Volcanic"); // Perigo vulcânico
        }
        else if (HazardIntensity >= 1.5f)
        {
            HazardType = TEXT("Toxic"); // Perigo tóxico
        }
        else
        {
            HazardType = TEXT("Energy"); // Perigo energético
        }

        // Spawnar ator de perigo ambiental usando APIs modernas
        FActorSpawnParameters SpawnParams;
        SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
        SpawnParams.bNoFail = true;

        FString HazardClassPath = FString::Printf(TEXT("/Game/Hazards/%s_Hazard_BP"), *HazardType);
        UClass* HazardClass = LoadClass<AActor>(nullptr, *HazardClassPath);

        if (HazardClass)
        {
            AActor* SpawnedHazard = GetWorld()->SpawnActor<AActor>(HazardClass, Location, FRotator::ZeroRotator, SpawnParams);
            if (SpawnedHazard)
            {
                // Configurar duração do perigo
                float HazardDuration = 15.0f + (HazardIntensity * 10.0f);

                // Configurar timer para destruir o perigo após a duração
                FTimerHandle HazardTimerHandle;
                GetWorld()->GetTimerManager().SetTimer(
                    HazardTimerHandle,
                    [SpawnedHazard]()
                    {
                        if (IsValid(SpawnedHazard))
                        {
                            SpawnedHazard->Destroy();
                        }
                    },
                    HazardDuration,
                    false
                );

                // Configurar propriedades do perigo baseado na intensidade
                if (UPrimitiveComponent* PrimComp = SpawnedHazard->FindComponentByClass<UPrimitiveComponent>())
                {
                    // Aplicar escala baseada na intensidade
                    float ScaleMultiplier = 0.8f + (HazardIntensity * 0.4f);
                    SpawnedHazard->SetActorScale3D(FVector(ScaleMultiplier));
                }

                UE_LOGFMT(LogTemp, Log, "AAURACRONPCGChaosPortal: Spawned {0} hazard with intensity {1} at {2}",
                    HazardType, HazardIntensity, Location.ToString());
            }
        }
        else
        {
            // Fallback: criar perigo genérico usando componentes básicos
            AActor* GenericHazard = GetWorld()->SpawnActor<AActor>(AActor::StaticClass(), Location, FRotator::ZeroRotator, SpawnParams);
            if (GenericHazard)
            {
                // Adicionar componente de malha
                UStaticMeshComponent* HazardMesh = NewObject<UStaticMeshComponent>(GenericHazard);
                GenericHazard->SetRootComponent(HazardMesh);

                // Carregar malha de perigo genérica
                UStaticMesh* HazardMeshAsset = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/BasicShapes/Sphere"));
                if (HazardMeshAsset)
                {
                    HazardMesh->SetStaticMesh(HazardMeshAsset);
                    HazardMesh->SetCollisionProfileName(TEXT("OverlapAll"));

                    // Criar material dinâmico para o perigo
                    UMaterialInterface* BaseMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial"));
                    if (BaseMaterial)
                    {
                        UMaterialInstanceDynamic* DynamicMaterial = HazardMesh->CreateDynamicMaterialInstance(0, BaseMaterial);
                        if (DynamicMaterial)
                        {
                            // Cor baseada no tipo de perigo
                            FLinearColor HazardColor = FLinearColor::Red;
                            if (HazardType == TEXT("Toxic"))
                            {
                                HazardColor = FLinearColor::Green;
                            }
                            else if (HazardType == TEXT("Energy"))
                            {
                                HazardColor = FLinearColor::Blue;
                            }

                            DynamicMaterial->SetVectorParameterValue(FName("BaseColor"), HazardColor);
                            DynamicMaterial->SetScalarParameterValue(FName("Metallic"), 0.1f);
                            DynamicMaterial->SetScalarParameterValue(FName("Roughness"), 0.8f);
                        }
                    }
                }

                UE_LOGFMT(LogTemp, Log, "AAURACRONPCGChaosPortal: Created generic hazard at {0}", Location.ToString());
            }
        }
    }

    // Som de ativação de perigo ambiental
    if (USoundBase* HazardSound = LoadObject<USoundBase>(nullptr, TEXT("/Game/Audio/ChaosPortal/SFX_EnvironmentalHazardActivate")))
    {
        UGameplayStatics::PlaySoundAtLocation(
            GetWorld(),
            HazardSound,
            GetActorLocation(),
            1.0f + (HazardIntensity * 0.3f),
            1.0f,
            0.0f
        );
    }

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGChaosPortal: Activated {0} environmental hazards with intensity {1}",
        HazardLocations.Num(), HazardIntensity);
}

void AAURACRONPCGChaosPortal::ActivateTerrainInstability(float InstabilityIntensity)
{
    if (!bPortalActive)
    {
        return;
    }

    // Verificar probabilidade de ativação
    float RandomValue = FMath::RandRange(0.0f, 1.0f);
    if (RandomValue > TerrainInstabilityProbability)
    {
        return;
    }

    // Calcular área de instabilidade em volta do portal
    float InstabilityRadius = EffectRadius * (0.6f + InstabilityIntensity * 0.4f);
    int32 NumInstabilityZones = FMath::RandRange(3, 8); // 3 a 8 zonas instáveis

    TArray<FVector> InstabilityLocations;

    for (int32 i = 0; i < NumInstabilityZones; ++i)
    {
        // Distribuir zonas em padrão circular com variação aleatória
        float BaseAngle = (2.0f * PI * i) / NumInstabilityZones;
        float AngleVariation = FMath::RandRange(-PI/6, PI/6); // ±30 graus de variação
        float Angle = BaseAngle + AngleVariation;

        float Distance = FMath::RandRange(InstabilityRadius * 0.2f, InstabilityRadius);

        FVector InstabilityLocation = GetActorLocation();
        InstabilityLocation.X += FMath::Cos(Angle) * Distance;
        InstabilityLocation.Y += FMath::Sin(Angle) * Distance;

        // Ajustar altura baseado no terreno
        FVector TraceStart = InstabilityLocation + FVector(0, 0, 500);
        FVector TraceEnd = InstabilityLocation - FVector(0, 0, 500);

        FHitResult HitResult;
        FCollisionQueryParams QueryParams;
        QueryParams.bTraceComplex = true;
        QueryParams.AddIgnoredActor(this);

        if (GetWorld()->LineTraceSingleByChannel(HitResult, TraceStart, TraceEnd, ECC_WorldStatic, QueryParams))
        {
            InstabilityLocation = HitResult.Location;
        }

        InstabilityLocations.Add(InstabilityLocation);
    }

    // Criar zonas de instabilidade
    for (const FVector& Location : InstabilityLocations)
    {
        // Spawnar efeito visual de instabilidade
        SpawnTerrainInstabilityVisual(Location, InstabilityIntensity);

        // Criar ator de zona instável usando APIs modernas do UE 5.6
        FActorSpawnParameters SpawnParams;
        SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
        SpawnParams.bNoFail = true;

        // Tentar carregar classe específica de instabilidade
        UClass* InstabilityClass = LoadClass<AActor>(nullptr, TEXT("/Game/Terrain/TerrainInstability_BP"));

        AActor* InstabilityZone = nullptr;
        if (InstabilityClass)
        {
            InstabilityZone = GetWorld()->SpawnActor<AActor>(InstabilityClass, Location, FRotator::ZeroRotator, SpawnParams);
        }
        else
        {
            // Fallback: criar zona instável genérica
            InstabilityZone = GetWorld()->SpawnActor<AActor>(AActor::StaticClass(), Location, FRotator::ZeroRotator, SpawnParams);

            if (InstabilityZone)
            {
                // Adicionar componente de malha para visualização
                UStaticMeshComponent* InstabilityMesh = NewObject<UStaticMeshComponent>(InstabilityZone);
                InstabilityZone->SetRootComponent(InstabilityMesh);

                // Carregar malha de plataforma instável
                UStaticMesh* PlatformMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/BasicShapes/Cylinder"));
                if (PlatformMesh)
                {
                    InstabilityMesh->SetStaticMesh(PlatformMesh);
                    InstabilityMesh->SetCollisionProfileName(TEXT("BlockAll"));

                    // Escalar baseado na intensidade
                    float ScaleMultiplier = 1.0f + (InstabilityIntensity * 0.5f);
                    FVector Scale = FVector(ScaleMultiplier * 2.0f, ScaleMultiplier * 2.0f, 0.2f); // Plataforma achatada
                    InstabilityZone->SetActorScale3D(Scale);

                    // Criar material dinâmico para mostrar instabilidade
                    UMaterialInterface* BaseMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial"));
                    if (BaseMaterial)
                    {
                        UMaterialInstanceDynamic* DynamicMaterial = InstabilityMesh->CreateDynamicMaterialInstance(0, BaseMaterial);
                        if (DynamicMaterial)
                        {
                            // Cor baseada na intensidade (vermelho = mais instável)
                            FLinearColor InstabilityColor = UKismetMathLibrary::LinearColorLerp(
                                FLinearColor(0.5f, 0.3f, 0.1f), // Marrom para baixa instabilidade
                                FLinearColor(1.0f, 0.2f, 0.0f), // Vermelho para alta instabilidade
                                InstabilityIntensity / 3.0f
                            );

                            DynamicMaterial->SetVectorParameterValue(FName("BaseColor"), InstabilityColor);
                            DynamicMaterial->SetScalarParameterValue(FName("Metallic"), 0.2f);
                            DynamicMaterial->SetScalarParameterValue(FName("Roughness"), 0.9f);
                        }
                    }
                }

                // Adicionar componente de trigger para detectar jogadores
                USphereComponent* TriggerComponent = NewObject<USphereComponent>(InstabilityZone);
                TriggerComponent->SetupAttachment(InstabilityZone->GetRootComponent());
                float TriggerScaleMultiplier = 1.0f + (InstabilityIntensity * 0.5f);
                TriggerComponent->SetSphereRadius(200.0f * TriggerScaleMultiplier);
                TriggerComponent->SetCollisionProfileName(TEXT("Trigger"));
                TriggerComponent->SetGenerateOverlapEvents(true);
            }
        }

        if (InstabilityZone)
        {
            // Configurar comportamento de instabilidade usando timer
            float InstabilityDuration = 20.0f + (InstabilityIntensity * 15.0f);
            float ShakeInterval = FMath::Max(0.5f, 2.0f - InstabilityIntensity * 0.5f);

            // Timer para movimento oscilatório
            FTimerHandle ShakeTimerHandle;
            GetWorld()->GetTimerManager().SetTimer(
                ShakeTimerHandle,
                [InstabilityZone, InstabilityIntensity]()
                {
                    if (IsValid(InstabilityZone))
                    {
                        // Aplicar movimento oscilatório
                        FVector CurrentLocation = InstabilityZone->GetActorLocation();
                        FVector ShakeOffset = FVector(
                            FMath::RandRange(-10.0f, 10.0f) * InstabilityIntensity,
                            FMath::RandRange(-10.0f, 10.0f) * InstabilityIntensity,
                            FMath::RandRange(-5.0f, 5.0f) * InstabilityIntensity
                        );

                        InstabilityZone->SetActorLocation(CurrentLocation + ShakeOffset);

                        // Rotação instável
                        FRotator CurrentRotation = InstabilityZone->GetActorRotation();
                        FRotator ShakeRotation = FRotator(
                            FMath::RandRange(-2.0f, 2.0f) * InstabilityIntensity,
                            FMath::RandRange(-2.0f, 2.0f) * InstabilityIntensity,
                            FMath::RandRange(-1.0f, 1.0f) * InstabilityIntensity
                        );

                        InstabilityZone->SetActorRotation(CurrentRotation + ShakeRotation);
                    }
                },
                ShakeInterval,
                true // Loop
            );

            // Timer para destruir a zona após a duração
            FTimerHandle DestroyTimerHandle;
            GetWorld()->GetTimerManager().SetTimer(
                DestroyTimerHandle,
                [InstabilityZone, ShakeTimerHandle, this]()
                {
                    if (IsValid(InstabilityZone))
                    {
                        // Limpar timer de shake - criar cópia não const
                        FTimerHandle ShakeHandleCopy = ShakeTimerHandle;
                        GetWorld()->GetTimerManager().ClearTimer(ShakeHandleCopy);

                        // Efeito de colapso antes de destruir
                        if (UNiagaraSystem* CollapseEffect = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Terrain/NS_TerrainCollapse")))
                        {
                            UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                                GetWorld(),
                                CollapseEffect,
                                InstabilityZone->GetActorLocation(),
                                FRotator::ZeroRotator,
                                FVector(1.0f),
                                true,
                                true,
                                ENCPoolMethod::AutoRelease
                            );
                        }

                        InstabilityZone->Destroy();
                    }
                },
                InstabilityDuration,
                false
            );

            UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosPortal: Created terrain instability zone at %s with intensity %.2f"),
                *Location.ToString(), InstabilityIntensity);
        }
    }

    // Som de ativação de instabilidade
    if (USoundBase* InstabilitySound = LoadObject<USoundBase>(nullptr, TEXT("/Game/Audio/ChaosPortal/SFX_TerrainInstabilityActivate")))
    {
        UGameplayStatics::PlaySoundAtLocation(
            GetWorld(),
            InstabilitySound,
            GetActorLocation(),
            1.0f + (InstabilityIntensity * 0.4f),
            0.8f + (InstabilityIntensity * 0.2f), // Pitch baseado na intensidade
            0.0f
        );
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosPortal: Activated %d terrain instability zones with intensity %.2f"),
        InstabilityLocations.Num(), InstabilityIntensity);
}

void AAURACRONPCGChaosPortal::SetPortalType(EChaosPortalType Type)
{
    PortalType = Type;

    // Configurar propriedades baseadas no tipo usando APIs modernas do UE 5.6
    switch (PortalType)
    {
        case EChaosPortalType::Standard:
        {
            // Portal padrão - configurações básicas
            PortalIntensity = 1.0f;
            EffectRadius = 800.0f;
            PortalColor = FLinearColor(0.6f, 0.2f, 0.8f, 1.0f); // Roxo padrão

            // Probabilidades padrão
            EnvironmentalHazardProbability = 0.3f;
            TerrainInstabilityProbability = 0.25f;
            HighRiskRewardProbability = 0.2f;

            // Configurações de luz
            if (PortalLight)
            {
                PortalLight->SetIntensity(8000.0f);
                PortalLight->SetLightColor(PortalColor);
                PortalLight->SetAttenuationRadius(1000.0f);
            }

            // Configurações de efeito
            PulsateFrequency = 1.0f;
            PulsateIntensity = 0.5f;
            RotationSpeed = 30.0f;
            EffectInterval = 8.0f;

            break;
        }

        case EChaosPortalType::Elite:
        {
            // Portal elite - configurações aprimoradas
            PortalIntensity = 1.8f;
            EffectRadius = 1200.0f;
            PortalColor = FLinearColor(0.8f, 0.4f, 1.0f, 1.0f); // Roxo mais intenso

            // Probabilidades aumentadas
            EnvironmentalHazardProbability = 0.5f;
            TerrainInstabilityProbability = 0.4f;
            HighRiskRewardProbability = 0.35f;

            // Configurações de luz mais intensas
            if (PortalLight)
            {
                PortalLight->SetIntensity(12000.0f);
                PortalLight->SetLightColor(PortalColor);
                PortalLight->SetAttenuationRadius(1500.0f);
                PortalLight->SetCastShadows(true); // Elite casts shadows
            }

            // Efeitos mais dinâmicos
            PulsateFrequency = 1.5f;
            PulsateIntensity = 0.8f;
            RotationSpeed = 45.0f;
            EffectInterval = 6.0f;

            break;
        }

        case EChaosPortalType::Legendary:
        {
            // Portal lendário - configurações máximas
            PortalIntensity = 2.5f;
            EffectRadius = 1600.0f;
            PortalColor = FLinearColor(1.0f, 0.6f, 0.2f, 1.0f); // Dourado/laranja lendário

            // Probabilidades máximas
            EnvironmentalHazardProbability = 0.7f;
            TerrainInstabilityProbability = 0.6f;
            HighRiskRewardProbability = 0.5f;

            // Configurações de luz épicas
            if (PortalLight)
            {
                PortalLight->SetIntensity(16000.0f);
                PortalLight->SetLightColor(PortalColor);
                PortalLight->SetAttenuationRadius(2000.0f);
                PortalLight->SetCastShadows(true);
                PortalLight->SetVolumetricScatteringIntensity(2.0f); // Volumetric lighting
            }

            // Efeitos mais dramáticos
            PulsateFrequency = 2.0f;
            PulsateIntensity = 1.2f;
            RotationSpeed = 60.0f;
            EffectInterval = 4.0f;

            break;
        }
    }

    // Atualizar material dinâmico do portal
    if (PortalMesh && PortalMesh->GetMaterial(0))
    {
        if (!PortalDynamicMaterial)
        {
            PortalDynamicMaterial = PortalMesh->CreateDynamicMaterialInstance(0, PortalMesh->GetMaterial(0));
        }

        if (PortalDynamicMaterial)
        {
            // Aplicar cor do tipo de portal
            PortalDynamicMaterial->SetVectorParameterValue(FName("PortalColor"), PortalColor);
            PortalDynamicMaterial->SetScalarParameterValue(FName("Intensity"), PortalIntensity);
            PortalDynamicMaterial->SetScalarParameterValue(FName("PulsateFrequency"), PulsateFrequency);
            PortalDynamicMaterial->SetScalarParameterValue(FName("RotationSpeed"), RotationSpeed / 60.0f); // Normalizar para shader

            // Configurações específicas por tipo
            switch (PortalType)
            {
                case EChaosPortalType::Standard:
                    PortalDynamicMaterial->SetScalarParameterValue(FName("Metallic"), 0.3f);
                    PortalDynamicMaterial->SetScalarParameterValue(FName("Roughness"), 0.4f);
                    break;

                case EChaosPortalType::Elite:
                    PortalDynamicMaterial->SetScalarParameterValue(FName("Metallic"), 0.6f);
                    PortalDynamicMaterial->SetScalarParameterValue(FName("Roughness"), 0.2f);
                    PortalDynamicMaterial->SetScalarParameterValue(FName("EmissiveMultiplier"), 1.5f);
                    break;

                case EChaosPortalType::Legendary:
                    PortalDynamicMaterial->SetScalarParameterValue(FName("Metallic"), 0.9f);
                    PortalDynamicMaterial->SetScalarParameterValue(FName("Roughness"), 0.1f);
                    PortalDynamicMaterial->SetScalarParameterValue(FName("EmissiveMultiplier"), 2.5f);
                    PortalDynamicMaterial->SetScalarParameterValue(FName("DistortionStrength"), 0.8f);
                    break;
            }
        }
    }

    // Atualizar efeito de partículas Niagara
    if (PortalEffect)
    {
        // Configurar parâmetros do sistema Niagara baseado no tipo
        PortalEffect->SetColorParameter(FName("PortalColor"), PortalColor);
        PortalEffect->SetFloatParameter(FName("IntensityMultiplier"), PortalIntensity);
        PortalEffect->SetFloatParameter(FName("SpawnRate"), 100.0f * PortalIntensity);
        PortalEffect->SetFloatParameter(FName("ParticleSize"), 1.0f + (PortalIntensity * 0.5f));
        PortalEffect->SetFloatParameter(FName("VelocityMultiplier"), PortalIntensity);

        // Configurações específicas por tipo
        switch (PortalType)
        {
            case EChaosPortalType::Standard:
                PortalEffect->SetFloatParameter(FName("LifetimeMultiplier"), 1.0f);
                PortalEffect->SetVectorParameter(FName("EmissionShape"), FVector(1.0f, 1.0f, 1.0f));
                break;

            case EChaosPortalType::Elite:
                PortalEffect->SetFloatParameter(FName("LifetimeMultiplier"), 1.3f);
                PortalEffect->SetVectorParameter(FName("EmissionShape"), FVector(1.2f, 1.2f, 1.5f));
                PortalEffect->SetFloatParameter(FName("TurbulenceStrength"), 0.5f);
                break;

            case EChaosPortalType::Legendary:
                PortalEffect->SetFloatParameter(FName("LifetimeMultiplier"), 1.6f);
                PortalEffect->SetVectorParameter(FName("EmissionShape"), FVector(1.5f, 1.5f, 2.0f));
                PortalEffect->SetFloatParameter(FName("TurbulenceStrength"), 1.0f);
                PortalEffect->SetFloatParameter(FName("LightningFrequency"), 2.0f);
                break;
        }
    }

    // Atualizar raio do trigger
    if (TriggerSphere)
    {
        TriggerSphere->SetSphereRadius(EffectRadius);
    }

    // Configurar som baseado no tipo
    if (PortalSound)
    {
        FString SoundPath;
        switch (PortalType)
        {
            case EChaosPortalType::Standard:
                SoundPath = TEXT("/Game/Audio/ChaosPortal/SFX_ChaosPortal_Standard");
                break;
            case EChaosPortalType::Elite:
                SoundPath = TEXT("/Game/Audio/ChaosPortal/SFX_ChaosPortal_Elite");
                break;
            case EChaosPortalType::Legendary:
                SoundPath = TEXT("/Game/Audio/ChaosPortal/SFX_ChaosPortal_Legendary");
                break;
        }

        if (USoundBase* PortalSoundAsset = LoadObject<USoundBase>(nullptr, *SoundPath))
        {
            PortalSound->SetSound(PortalSoundAsset);
            PortalSound->SetVolumeMultiplier(0.8f + (PortalIntensity * 0.4f));
            PortalSound->SetPitchMultiplier(0.9f + (PortalIntensity * 0.2f));
        }
    }

    // Log da mudança de tipo
    FString TypeName;
    switch (PortalType)
    {
        case EChaosPortalType::Standard: TypeName = TEXT("Standard"); break;
        case EChaosPortalType::Elite: TypeName = TEXT("Elite"); break;
        case EChaosPortalType::Legendary: TypeName = TEXT("Legendary"); break;
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosPortal: Portal type set to %s (Intensity: %.2f, Radius: %.0f)"),
        *TypeName, PortalIntensity, EffectRadius);
}

// Implementação robusta da função SpawnEnvironmentalHazardVisual
void AAURACRONPCGChaosPortal::SpawnEnvironmentalHazardVisual(FVector Location, float Intensity)
{
    // Validações robustas
    if (!GetWorld() || !IsValid(this))
    {
        UE_LOGFMT(LogTemp, Error, "AAURACRONPCGChaosPortal::SpawnEnvironmentalHazardVisual: Invalid world or actor");
        return;
    }

    // Validações robustas - UE 5.6: usar FMath::IsFinite para cada componente
    if (!FMath::IsFinite(Location.X) || !FMath::IsFinite(Location.Y) || !FMath::IsFinite(Location.Z))
    {
        UE_LOGFMT(LogTemp, Error, "AAURACRONPCGChaosPortal::SpawnEnvironmentalHazardVisual: Invalid location with non-finite values");
        return;
    }

    if (Intensity <= 0.0f || !FMath::IsFinite(Intensity))
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGChaosPortal::SpawnEnvironmentalHazardVisual: Invalid intensity value: {Intensity}", Intensity);
        return;
    }

    // Selecionar efeito baseado na intensidade usando API moderna UE 5.6
    FStreamableManager& StreamableManager = UAssetManager::GetStreamableManager();
    UNiagaraSystem* HazardEffect = nullptr;

    if (Intensity >= 2.5f)
    {
        // Carregamento síncrono para efeitos de alta intensidade
        FSoftObjectPath EffectPath(TEXT("/Game/AURACRON/FX/ChaosPortal/NS_VolcanicHazard"));
        HazardEffect = Cast<UNiagaraSystem>(StreamableManager.LoadSynchronous(EffectPath));
    }
    else if (Intensity >= 1.5f)
    {
        FSoftObjectPath EffectPath(TEXT("/Game/AURACRON/FX/ChaosPortal/NS_ToxicHazard"));
        HazardEffect = Cast<UNiagaraSystem>(StreamableManager.LoadSynchronous(EffectPath));
    }
    else
    {
        FSoftObjectPath EffectPath(TEXT("/Game/AURACRON/FX/ChaosPortal/NS_EnergeticHazard"));
        HazardEffect = Cast<UNiagaraSystem>(StreamableManager.LoadSynchronous(EffectPath));
    }

    // Fallback para efeito genérico
    if (!HazardEffect)
    {
        FSoftObjectPath FallbackPath(TEXT("/Game/AURACRON/FX/Generic/NS_GenericHazard"));
        HazardEffect = Cast<UNiagaraSystem>(StreamableManager.LoadSynchronous(FallbackPath));
    }

    if (HazardEffect)
    {
        UNiagaraComponent* SpawnedEffect = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            HazardEffect,
            Location,
            FRotator::ZeroRotator,
            FVector(Intensity), // Escala baseada na intensidade
            true,
            true,
            ENCPoolMethod::AutoRelease
        );

        if (SpawnedEffect)
        {
            // Configurar parâmetros do efeito
            SpawnedEffect->SetFloatParameter(FName("HazardIntensity"), Intensity);
            SpawnedEffect->SetFloatParameter(FName("LifetimeMultiplier"), 1.0f + (Intensity * 0.5f));

            // Cor baseada na intensidade
            FLinearColor HazardColor;
            if (Intensity >= 2.5f)
            {
                HazardColor = FLinearColor(1.0f, 0.3f, 0.0f); // Vermelho vulcânico
            }
            else if (Intensity >= 1.5f)
            {
                HazardColor = FLinearColor(0.2f, 1.0f, 0.2f); // Verde tóxico
            }
            else
            {
                HazardColor = FLinearColor(0.2f, 0.5f, 1.0f); // Azul energético
            }

            SpawnedEffect->SetColorParameter(FName("HazardColor"), HazardColor);
            SpawnedEffect->SetVectorParameter(FName("EmissionRate"), FVector(50.0f * Intensity, 0, 0));
        }

        UE_LOGFMT(LogTemp, Verbose, "AAURACRONPCGChaosPortal::SpawnEnvironmentalHazardVisual: Spawned hazard visual at {Location} with intensity {Intensity}", Location.ToString(), Intensity);
    }
    else
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGChaosPortal::SpawnEnvironmentalHazardVisual: Failed to load hazard effect for intensity {Intensity}", Intensity);
    }
}

// Código órfão removido - implementação robusta está mais abaixo no arquivo

bool AAURACRONPCGChaosPortal::IsAtFlowIntersection() const
{
    // Verificar se o portal está em um ponto de interseção do Fluxo Prismal
    // Implementação robusta usando APIs modernas do UE 5.6

    FVector PortalLocation = GetActorLocation();

    // Buscar sistema de Fluxo Prismal no mundo
    if (UWorld* World = GetWorld())
    {
        // Buscar ator de Fluxo Prismal
        for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
        {
            AActor* Actor = *ActorIterator;
            if (Actor && Actor->GetClass()->GetName().Contains(TEXT("PrismalFlow")))
            {
                // Verificar distância para pontos de interseção conhecidos
                float DistanceToFlow = FVector::Dist(PortalLocation, Actor->GetActorLocation());

                // Considerar que está em interseção se estiver próximo o suficiente
                if (DistanceToFlow <= 500.0f) // 5 metros de tolerância
                {
                    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGChaosPortal: Portal is at flow intersection (distance: %.2f)"), DistanceToFlow);
                    return true;
                }
            }
        }

        // Verificar usando sistema de medições do mapa
        // Buscar pontos de interseção conhecidos baseado na documentação
        TArray<FVector> KnownIntersections = {
            FVector(0, 0, 0),       // Centro do mapa
            FVector(5000, 5000, 0), // Quadrante NE
            FVector(-5000, 5000, 0), // Quadrante NW
            FVector(5000, -5000, 0), // Quadrante SE
            FVector(-5000, -5000, 0) // Quadrante SW
        };

        for (const FVector& Intersection : KnownIntersections)
        {
            float DistanceToIntersection = FVector::Dist(PortalLocation, Intersection);
            if (DistanceToIntersection <= 1000.0f) // 10 metros de tolerância para interseções conhecidas
            {
                UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGChaosPortal: Portal is near known intersection at %s (distance: %.2f)"),
                    *Intersection.ToString(), DistanceToIntersection);
                return true;
            }
        }
    }

    // Verificação adicional baseada em padrões de coordenadas
    // Interseções tendem a estar em coordenadas "redondas" ou simétricas
    float X = FMath::Abs(PortalLocation.X);
    float Y = FMath::Abs(PortalLocation.Y);

    // Verificar se está em coordenadas que indicam interseção (múltiplos de 1000, por exemplo)
    bool bXIsIntersection = FMath::Fmod(X, 1000.0f) < 100.0f || FMath::Fmod(X, 1000.0f) > 900.0f;
    bool bYIsIntersection = FMath::Fmod(Y, 1000.0f) < 100.0f || FMath::Fmod(Y, 1000.0f) > 900.0f;

    if (bXIsIntersection && bYIsIntersection)
    {
        UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGChaosPortal: Portal appears to be at coordinate-based intersection"));
        return true;
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGChaosPortal: Portal is not at a flow intersection");
    return false;
}

// Implementação duplicada removida - função já implementada acima
// Implementação duplicada removida - função já implementada acima

void AAURACRONPCGChaosPortal::SpawnTerrainInstabilityVisual(FVector Location, float Intensity)
{
    if (!IsValid(this) || !GetWorld())
    {
        UE_LOGFMT(LogTemp, Error, "AAURACRONPCGChaosPortal::SpawnTerrainInstabilityVisual: Invalid world or actor");
        return;
    }

    // Validações robustas - UE 5.6: usar FMath::IsFinite para cada componente
    if (!FMath::IsFinite(Location.X) || !FMath::IsFinite(Location.Y) || !FMath::IsFinite(Location.Z))
    {
        UE_LOGFMT(LogTemp, Error, "AAURACRONPCGChaosPortal::SpawnTerrainInstabilityVisual: Invalid location with non-finite values");
        return;
    }

    if (Intensity <= 0.0f || !FMath::IsFinite(Intensity))
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGChaosPortal::SpawnTerrainInstabilityVisual: Invalid intensity {0}, clamping to 1.0", Intensity);
        Intensity = 1.0f;
    }

    // Clamp intensity para valores seguros
    Intensity = FMath::Clamp(Intensity, 0.1f, 5.0f);

    // Usar StreamableManager para carregamento assíncrono - API moderna UE 5.6
    FStreamableManager& StreamableManager = UAssetManager::GetStreamableManager();

    // Determinar tipo de instabilidade baseado na intensidade
    FString InstabilityVFXPath;
    FLinearColor InstabilityColor;

    if (Intensity >= 4.0f)
    {
        InstabilityVFXPath = TEXT("/Game/VFX/Terrain/NS_TerrainCollapseMajor");
        InstabilityColor = FLinearColor(1.0f, 0.1f, 0.0f); // Vermelho crítico
    }
    else if (Intensity >= 2.5f)
    {
        InstabilityVFXPath = TEXT("/Game/VFX/Terrain/NS_TerrainCrackSevere");
        InstabilityColor = FLinearColor(1.0f, 0.4f, 0.0f); // Laranja severo
    }
    else if (Intensity >= 1.5f)
    {
        InstabilityVFXPath = TEXT("/Game/VFX/Terrain/NS_TerrainShakeModerate");
        InstabilityColor = FLinearColor(1.0f, 0.8f, 0.2f); // Amarelo moderado
    }
    else
    {
        InstabilityVFXPath = TEXT("/Game/VFX/Terrain/NS_TerrainShakeMinor");
        InstabilityColor = FLinearColor(0.8f, 0.6f, 0.4f); // Marrom leve
    }

    // Carregamento assíncrono usando APIs modernas
    FSoftObjectPath InstabilityVFXSoftPath(InstabilityVFXPath);
    TSharedPtr<FStreamableHandle> Handle = StreamableManager.RequestAsyncLoad(
        InstabilityVFXSoftPath,
        [this, Location, Intensity, InstabilityColor, InstabilityVFXPath]()
        {
            if (!IsValid(this) || !GetWorld())
            {
                return;
            }

            // Carregar sistema Niagara
            UNiagaraSystem* InstabilitySystem = Cast<UNiagaraSystem>(
                UAssetManager::GetStreamableManager().LoadSynchronous(FSoftObjectPath(InstabilityVFXPath))
            );

            if (!InstabilitySystem)
            {
                // Fallback para sistema genérico de partículas de terra
                InstabilitySystem = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Engine/VFX/Niagara/Systems/NS_GroundDust"));
                if (!InstabilitySystem)
                {
                    InstabilitySystem = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Engine/VFX/Niagara/Systems/NS_GPUSprites"));
                }
                UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGChaosPortal::SpawnTerrainInstabilityVisual: Using fallback VFX system");
            }

            if (InstabilitySystem)
            {
                // Spawnar múltiplos efeitos para simular instabilidade em área
                int32 NumEffects = FMath::RandRange(2, FMath::CeilToInt(Intensity * 2.0f));

                for (int32 i = 0; i < NumEffects; ++i)
                {
                    // Calcular posições variadas ao redor do ponto central
                    FVector EffectLocation = Location;
                    float Radius = 100.0f + (Intensity * 50.0f);
                    float Angle = (2.0f * PI * i) / NumEffects + FMath::RandRange(-PI/4, PI/4);

                    EffectLocation.X += FMath::Cos(Angle) * FMath::RandRange(Radius * 0.3f, Radius);
                    EffectLocation.Y += FMath::Sin(Angle) * FMath::RandRange(Radius * 0.3f, Radius);

                    // Ajustar altura baseado no terreno
                    FVector TraceStart = EffectLocation + FVector(0, 0, 500);
                    FVector TraceEnd = EffectLocation - FVector(0, 0, 500);

                    FHitResult HitResult;
                    FCollisionQueryParams QueryParams;
                    QueryParams.bTraceComplex = true;
                    QueryParams.AddIgnoredActor(this);

                    if (GetWorld()->LineTraceSingleByChannel(HitResult, TraceStart, TraceEnd, ECC_WorldStatic, QueryParams))
                    {
                        EffectLocation = HitResult.Location + FVector(0, 0, 5); // Elevar um pouco acima do chão
                    }

                    // Spawnar efeito usando APIs modernas do UE 5.6
                    UNiagaraComponent* InstabilityVFX = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                        GetWorld(),
                        InstabilitySystem,
                        EffectLocation,
                        FRotator::ZeroRotator,
                        FVector(0.8f + Intensity * 0.4f), // Escala baseada na intensidade
                        true,
                        true,
                        ENCPoolMethod::AutoRelease,
                        true // Auto destroy
                    );

                    if (InstabilityVFX)
                    {
                        // Configurar parâmetros usando APIs modernas
                        InstabilityVFX->SetColorParameter(FName("InstabilityColor"), InstabilityColor);
                        InstabilityVFX->SetFloatParameter(FName("Intensity"), Intensity);
                        InstabilityVFX->SetFloatParameter(FName("LifeTime"), 8.0f + Intensity * 3.0f);
                        InstabilityVFX->SetFloatParameter(FName("SpawnRate"), 30.0f * Intensity);
                        InstabilityVFX->SetVectorParameter(FName("EmitterLocation"), FVector(EffectLocation));

                        // Configurações específicas por intensidade
                        if (Intensity >= 4.0f)
                        {
                            InstabilityVFX->SetFloatParameter(FName("ParticleSize"), 3.0f);
                            InstabilityVFX->SetFloatParameter(FName("VelocityMultiplier"), 2.0f);
                            InstabilityVFX->SetFloatParameter(FName("ShakeAmplitude"), 5.0f);
                        }
                        else if (Intensity >= 2.5f)
                        {
                            InstabilityVFX->SetFloatParameter(FName("ParticleSize"), 2.0f);
                            InstabilityVFX->SetFloatParameter(FName("VelocityMultiplier"), 1.5f);
                            InstabilityVFX->SetFloatParameter(FName("ShakeAmplitude"), 3.0f);
                        }
                        else
                        {
                            InstabilityVFX->SetFloatParameter(FName("ParticleSize"), 1.0f);
                            InstabilityVFX->SetFloatParameter(FName("VelocityMultiplier"), 1.0f);
                            InstabilityVFX->SetFloatParameter(FName("ShakeAmplitude"), 1.5f);
                        }

                        // Adicionar delay aleatório para efeitos mais naturais
                        float DelayTime = FMath::RandRange(0.0f, 2.0f);
                        if (DelayTime > 0.1f)
                        {
                            InstabilityVFX->SetFloatParameter(FName("StartDelay"), DelayTime);
                        }
                    }
                }

                UE_LOGFMT(LogTemp, Log, "AAURACRONPCGChaosPortal::SpawnTerrainInstabilityVisual: Successfully spawned {0} instability VFX at {1} with intensity {2}",
                    NumEffects, Location.ToString(), Intensity);
            }
            else
            {
                UE_LOGFMT(LogTemp, Error, "AAURACRONPCGChaosPortal::SpawnTerrainInstabilityVisual: Failed to load Niagara system");
            }
        }
    );

    // Configurar timeout para o carregamento assíncrono
    if (Handle.IsValid())
    {
        FTimerHandle TimeoutHandle;
        GetWorld()->GetTimerManager().SetTimer(
            TimeoutHandle,
            [Handle]()
            {
                if (Handle.IsValid() && Handle->IsLoadingInProgress())
                {
                    Handle->CancelHandle();
                    UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGChaosPortal::SpawnTerrainInstabilityVisual: Async load timed out");
                }
            },
            5.0f, // 5 segundos de timeout
            false
        );
    }
}

void AAURACRONPCGChaosPortal::SpawnHighRiskRewardVisual(FVector Location, float RewardTier)
{
    if (!IsValid(this) || !GetWorld())
    {
        UE_LOGFMT(LogTemp, Error, "AAURACRONPCGChaosPortal::SpawnHighRiskRewardVisual: Invalid world or actor");
        return;
    }

    // Validações robustas - UE 5.6: usar FMath::IsFinite para cada componente
    if (!FMath::IsFinite(Location.X) || !FMath::IsFinite(Location.Y) || !FMath::IsFinite(Location.Z))
    {
        UE_LOGFMT(LogTemp, Error, "AAURACRONPCGChaosPortal::SpawnHighRiskRewardVisual: Invalid location with non-finite values");
        return;
    }

    if (RewardTier <= 0.0f || !FMath::IsFinite(RewardTier))
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGChaosPortal::SpawnHighRiskRewardVisual: Invalid reward tier {0}, clamping to 1.0", RewardTier);
        RewardTier = 1.0f;
    }

    // Clamp reward tier para valores seguros
    RewardTier = FMath::Clamp(RewardTier, 1.0f, 5.0f);

    // Usar StreamableManager para carregamento assíncrono - API moderna UE 5.6
    FStreamableManager& StreamableManager = UAssetManager::GetStreamableManager();

    // Determinar tipo de recompensa baseado no tier
    FString RewardVFXPath;
    FLinearColor RewardColor;
    FString RewardTierName;

    if (RewardTier >= 5.0f)
    {
        RewardVFXPath = TEXT("/Game/VFX/Rewards/NS_MythicReward");
        RewardColor = FLinearColor(1.0f, 0.0f, 1.0f); // Magenta mítico
        RewardTierName = TEXT("Mythic");
    }
    else if (RewardTier >= 4.0f)
    {
        RewardVFXPath = TEXT("/Game/VFX/Rewards/NS_LegendaryReward");
        RewardColor = FLinearColor(1.0f, 0.5f, 0.0f); // Laranja lendário
        RewardTierName = TEXT("Legendary");
    }
    else if (RewardTier >= 3.0f)
    {
        RewardVFXPath = TEXT("/Game/VFX/Rewards/NS_EpicReward");
        RewardColor = FLinearColor(0.5f, 0.0f, 1.0f); // Roxo épico
        RewardTierName = TEXT("Epic");
    }
    else if (RewardTier >= 2.0f)
    {
        RewardVFXPath = TEXT("/Game/VFX/Rewards/NS_RareReward");
        RewardColor = FLinearColor(0.0f, 0.5f, 1.0f); // Azul raro
        RewardTierName = TEXT("Rare");
    }
    else
    {
        RewardVFXPath = TEXT("/Game/VFX/Rewards/NS_UncommonReward");
        RewardColor = FLinearColor(0.2f, 1.0f, 0.2f); // Verde incomum
        RewardTierName = TEXT("Uncommon");
    }

    // Carregamento assíncrono usando APIs modernas
    FSoftObjectPath RewardVFXSoftPath(RewardVFXPath);
    TSharedPtr<FStreamableHandle> Handle = StreamableManager.RequestAsyncLoad(
        RewardVFXSoftPath,
        [this, Location, RewardTier, RewardColor, RewardVFXPath, RewardTierName]()
        {
            if (!IsValid(this) || !GetWorld())
            {
                return;
            }

            // Carregar sistema Niagara
            UNiagaraSystem* RewardSystem = Cast<UNiagaraSystem>(
                UAssetManager::GetStreamableManager().LoadSynchronous(FSoftObjectPath(RewardVFXPath))
            );

            if (!RewardSystem)
            {
                // Fallback para sistema genérico de recompensa
                RewardSystem = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Engine/VFX/Niagara/Systems/NS_Sparkles"));
                if (!RewardSystem)
                {
                    RewardSystem = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Engine/VFX/Niagara/Systems/NS_GPUSprites"));
                }
                UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGChaosPortal::SpawnHighRiskRewardVisual: Using fallback VFX system for {0} reward", RewardTierName);
            }

            if (RewardSystem)
            {
                // Spawnar efeito principal da recompensa
                UNiagaraComponent* RewardVFX = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                    GetWorld(),
                    RewardSystem,
                    Location + FVector(0, 0, 50), // Elevar um pouco acima do chão
                    FRotator::ZeroRotator,
                    FVector(1.0f + RewardTier * 0.3f), // Escala baseada no tier
                    true,
                    true,
                    ENCPoolMethod::AutoRelease,
                    true // Auto destroy
                );

                if (RewardVFX)
                {
                    // Configurar parâmetros usando APIs modernas
                    RewardVFX->SetColorParameter(FName("RewardColor"), RewardColor);
                    RewardVFX->SetFloatParameter(FName("RewardTier"), RewardTier);
                    RewardVFX->SetFloatParameter(FName("LifeTime"), 30.0f + RewardTier * 10.0f); // Duração baseada no tier
                    RewardVFX->SetFloatParameter(FName("SpawnRate"), 20.0f * RewardTier);
                    RewardVFX->SetVectorParameter(FName("EmitterLocation"), FVector(Location));

                    // Configurações específicas por tier
                    if (RewardTier >= 5.0f) // Mítico
                    {
                        RewardVFX->SetFloatParameter(FName("ParticleSize"), 4.0f);
                        RewardVFX->SetFloatParameter(FName("GlowIntensity"), 3.0f);
                        RewardVFX->SetFloatParameter(FName("PulseFrequency"), 2.0f);
                        RewardVFX->SetFloatParameter(FName("RotationSpeed"), 180.0f);
                    }
                    else if (RewardTier >= 4.0f) // Lendário
                    {
                        RewardVFX->SetFloatParameter(FName("ParticleSize"), 3.0f);
                        RewardVFX->SetFloatParameter(FName("GlowIntensity"), 2.5f);
                        RewardVFX->SetFloatParameter(FName("PulseFrequency"), 1.5f);
                        RewardVFX->SetFloatParameter(FName("RotationSpeed"), 120.0f);
                    }
                    else if (RewardTier >= 3.0f) // Épico
                    {
                        RewardVFX->SetFloatParameter(FName("ParticleSize"), 2.5f);
                        RewardVFX->SetFloatParameter(FName("GlowIntensity"), 2.0f);
                        RewardVFX->SetFloatParameter(FName("PulseFrequency"), 1.2f);
                        RewardVFX->SetFloatParameter(FName("RotationSpeed"), 90.0f);
                    }
                    else if (RewardTier >= 2.0f) // Raro
                    {
                        RewardVFX->SetFloatParameter(FName("ParticleSize"), 2.0f);
                        RewardVFX->SetFloatParameter(FName("GlowIntensity"), 1.5f);
                        RewardVFX->SetFloatParameter(FName("PulseFrequency"), 1.0f);
                        RewardVFX->SetFloatParameter(FName("RotationSpeed"), 60.0f);
                    }
                    else // Incomum
                    {
                        RewardVFX->SetFloatParameter(FName("ParticleSize"), 1.5f);
                        RewardVFX->SetFloatParameter(FName("GlowIntensity"), 1.0f);
                        RewardVFX->SetFloatParameter(FName("PulseFrequency"), 0.8f);
                        RewardVFX->SetFloatParameter(FName("RotationSpeed"), 30.0f);
                    }

                    // Spawnar efeitos adicionais para tiers altos
                    if (RewardTier >= 3.0f)
                    {
                        // Efeito de pillar de luz para recompensas épicas+
                        UNiagaraSystem* PillarSystem = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Rewards/NS_RewardPillar"));
                        if (!PillarSystem)
                        {
                            PillarSystem = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Engine/VFX/Niagara/Systems/NS_Beam"));
                        }

                        if (PillarSystem)
                        {
                            UNiagaraComponent* PillarVFX = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                                GetWorld(),
                                PillarSystem,
                                Location,
                                FRotator::ZeroRotator,
                                FVector(1.0f, 1.0f, 2.0f + RewardTier), // Altura baseada no tier
                                true,
                                true,
                                ENCPoolMethod::AutoRelease,
                                true
                            );

                            if (PillarVFX)
                            {
                                PillarVFX->SetColorParameter(FName("PillarColor"), RewardColor);
                                PillarVFX->SetFloatParameter(FName("Intensity"), RewardTier);
                                PillarVFX->SetFloatParameter(FName("Height"), 500.0f + RewardTier * 200.0f);
                            }
                        }
                    }

                    // Spawnar efeitos de partículas orbitais para tiers míticos
                    if (RewardTier >= 5.0f)
                    {
                        int32 NumOrbitals = 3;
                        for (int32 i = 0; i < NumOrbitals; ++i)
                        {
                            float Angle = (2.0f * PI * i) / NumOrbitals;
                            float OrbitRadius = 150.0f;

                            FVector OrbitalLocation = Location;
                            OrbitalLocation.X += FMath::Cos(Angle) * OrbitRadius;
                            OrbitalLocation.Y += FMath::Sin(Angle) * OrbitRadius;
                            OrbitalLocation.Z += 100.0f;

                            UNiagaraComponent* OrbitalVFX = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                                GetWorld(),
                                RewardSystem,
                                OrbitalLocation,
                                FRotator::ZeroRotator,
                                FVector(0.5f),
                                true,
                                true,
                                ENCPoolMethod::AutoRelease,
                                true
                            );

                            if (OrbitalVFX)
                            {
                                OrbitalVFX->SetColorParameter(FName("RewardColor"), RewardColor * 0.7f);
                                OrbitalVFX->SetFloatParameter(FName("OrbitRadius"), OrbitRadius);
                                OrbitalVFX->SetFloatParameter(FName("OrbitSpeed"), 45.0f);
                                OrbitalVFX->SetFloatParameter(FName("StartAngle"), Angle * 180.0f / PI);
                            }
                        }
                    }

                    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGChaosPortal::SpawnHighRiskRewardVisual: Successfully spawned {0} reward VFX at {1} with tier {2}",
                        RewardTierName, Location.ToString(), RewardTier);
                }
                else
                {
                    UE_LOGFMT(LogTemp, Error, "AAURACRONPCGChaosPortal::SpawnHighRiskRewardVisual: Failed to spawn Niagara component for {0} reward", RewardTierName);
                }
            }
            else
            {
                UE_LOGFMT(LogTemp, Error, "AAURACRONPCGChaosPortal::SpawnHighRiskRewardVisual: Failed to load Niagara system for {0} reward", RewardTierName);
            }
        }
    );

    // Configurar timeout para o carregamento assíncrono
    if (Handle.IsValid())
    {
        FTimerHandle TimeoutHandle;
        GetWorld()->GetTimerManager().SetTimer(
            TimeoutHandle,
            [Handle, RewardTierName]()
            {
                if (Handle.IsValid() && Handle->IsLoadingInProgress())
                {
                    Handle->CancelHandle();
                    UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGChaosPortal::SpawnHighRiskRewardVisual: Async load timed out for {0} reward", RewardTierName);
                }
            },
            5.0f, // 5 segundos de timeout
            false
        );
    }
}

// Funções de Timer otimizadas para UE 5.6

void AAURACRONPCGChaosPortal::StartPortalTimers()
{
    if (!IsValid(this) || !GetWorld())
    {
        return;
    }

    // Limpar timers existentes
    StopPortalTimers();

    // Timer para atualização de efeitos visuais (60 FPS)
    GetWorld()->GetTimerManager().SetTimer(
        VisualEffectsTimerHandle,
        this,
        &AAURACRONPCGChaosPortal::UpdateVisualEffectsTimer,
        1.0f / 60.0f, // 60 FPS
        true // Loop
    );

    // Timer para verificação de efeitos especiais
    GetWorld()->GetTimerManager().SetTimer(
        EffectTriggerTimerHandle,
        this,
        &AAURACRONPCGChaosPortal::CheckEffectTrigger,
        EffectInterval,
        true // Loop
    );

    // Timer para verificação de expiração do portal (se tiver duração)
    if (PortalDuration > 0.0f)
    {
        GetWorld()->GetTimerManager().SetTimer(
            ExpirationTimerHandle,
            this,
            &AAURACRONPCGChaosPortal::OnPortalExpired,
            PortalDuration,
            false // Uma vez só
        );
    }

    // Habilitar Tick apenas para fade out se necessário
    PrimaryActorTick.bCanEverTick = true;

    UE_LOGFMT(LogTemp, Verbose, "AAURACRONPCGChaosPortal: Portal timers started");
}

void AAURACRONPCGChaosPortal::StopPortalTimers()
{
    if (!GetWorld())
    {
        return;
    }

    // Limpar todos os timers
    GetWorld()->GetTimerManager().ClearTimer(VisualEffectsTimerHandle);
    GetWorld()->GetTimerManager().ClearTimer(EffectTriggerTimerHandle);
    GetWorld()->GetTimerManager().ClearTimer(ExpirationTimerHandle);

    // Desabilitar Tick para otimização
    PrimaryActorTick.bCanEverTick = false;

    UE_LOGFMT(LogTemp, Verbose, "AAURACRONPCGChaosPortal: Portal timers stopped");
}

void AAURACRONPCGChaosPortal::UpdateVisualEffectsTimer()
{
    if (!bPortalActive || bFadingOut)
    {
        return;
    }

    // Calcular delta time baseado na frequência do timer
    float DeltaTime = 1.0f / 60.0f;

    // Atualizar tempo decorrido
    ElapsedTime += DeltaTime;

    // Atualizar efeitos visuais
    UpdateVisualEffects(DeltaTime);
}

void AAURACRONPCGChaosPortal::CheckEffectTrigger()
{
    if (!bPortalActive || bFadingOut)
    {
        return;
    }

    // Chance de disparar um efeito baseado na fase do mapa
    float SpawnProbability = 0.2f;
    switch (CurrentMapPhase)
    {
        case EAURACRONMapPhase::Awakening:
            SpawnProbability = 0.1f;
            break;
        case EAURACRONMapPhase::Convergence:
            SpawnProbability = 0.2f;
            break;
        case EAURACRONMapPhase::Intensification:
            SpawnProbability = 0.3f;
            break;
        case EAURACRONMapPhase::Resolution:
            SpawnProbability = 0.5f;
            break;
        default:
            break;
    }

    // Disparar efeito com probabilidade calculada
    TriggerPortalEffect(SpawnProbability);
}

void AAURACRONPCGChaosPortal::OnPortalExpired()
{
    if (bPortalActive && !bFadingOut)
    {
        // Iniciar fade out
        DeactivatePortal(FadeOutTime);
        UE_LOGFMT(LogTemp, Log, "AAURACRONPCGChaosPortal: Portal expired after {0} seconds", PortalDuration);
    }
}