// AURACRONPCGUtility.cpp
// Implementação da Classe Utilitária PCG - UE 5.6
// Sistema utilitário robusto para gerenciamento de atores PCG com integração completa
// Trilhos Solar/Axis/Lunar, Fluxo Prismal, Ilha Central Auracron e performance escalável

#include "PCG/AURACRONPCGUtility.h"
#include "PCG/AURACRONPCGEnvironment.h"
#include "PCG/AURACRONPCGTrail.h"
#include "PCG/AURACRONPCGIsland.h"
#include "PCG/AURACRONPCGPrismalFlow.h"
#include "PCG/AURACRONPCGArsenalIsland.h"
#include "PCG/AURACRONPCGSanctuaryIsland.h"
#include "PCG/AURACRONPCGChaosIsland.h"
#include "PCG/AURACRONPCGNexusIsland.h"
#include "PCGComponent.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/Engine.h"
#include "EngineUtils.h"
#include "Engine/StreamableManager.h"
#include "TimerManager.h"
#include "Logging/StructuredLog.h"
#include "Net/UnrealNetwork.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Engine/DataTable.h"
#include "Components/AudioComponent.h"
#include "Components/PointLightComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "NiagaraComponent.h"
#include "GameFramework/PlayerController.h"
#include "Engine/NetDriver.h"

// Definição de variáveis estáticas - Sistema de cache inteligente UE 5.6
TMap<UWorld*, FAURACRONPCGActorReferences> UAURACRONPCGUtility::ActorCache;
TMap<UWorld*, float> UAURACRONPCGUtility::CacheTimestamps;
TMap<UWorld*, TSharedPtr<FStreamableManager>> UAURACRONPCGUtility::StreamableManagers;
TMap<UWorld*, FTimerHandle> UAURACRONPCGUtility::OptimizationTimers;
TMap<UWorld*, int32> UAURACRONPCGUtility::ParticleBudgets;
TMap<UWorld*, bool> UAURACRONPCGUtility::ServerValidationEnabled;

// Configurações de performance escalável por hardware
static const TMap<FString, int32> HardwareParticleBudgets = {
    {TEXT("Entry"), 300},
    {TEXT("Mid"), 800},
    {TEXT("High"), 2000}
};

// Log category para UE 5.6
DEFINE_LOG_CATEGORY_STATIC(LogAURACRONPCGUtility, Log, All);

FAURACRONPCGActorReferences UAURACRONPCGUtility::FindPCGActors(UWorld* World, const FAURACRONPCGSearchOptions& SearchOptions)
{
    FAURACRONPCGActorReferences ActorReferences;

    if (!IsValid(World))
    {
        UE_LOGFMT(LogAURACRONPCGUtility, Warning, "FindPCGActors: World inválido - operação cancelada");
        return ActorReferences;
    }

    // Inicializar StreamableManager se necessário
    InitializeStreamableManager(World);

    // Validação server-side anti-cheat
    if (World->GetNetMode() != NM_Standalone && !ValidateServerPermissions(World))
    {
        UE_LOGFMT(LogAURACRONPCGUtility, Warning, "FindPCGActors: Validação server-side falhou para World {0}", World->GetName());
        return ActorReferences;
    }

    // Verificar cache inteligente primeiro
    if (IsCacheValid(World))
    {
        if (SearchOptions.bVerboseLogging)
        {
            UE_LOGFMT(LogAURACRONPCGUtility, Log, "FindPCGActors: Usando cache otimizado para World {0} - {1} atores",
                     World->GetName(), ActorCache[World].GetTotalActorCount());
        }
        return ActorCache[World];
    }

    // Limpar referências
    ActorReferences.Clear();

    TArray<AActor*> FoundActors;

    // Encontrar ambientes
    if (SearchOptions.bSearchEnvironments)
    {
        FoundActors.Empty();
        UGameplayStatics::GetAllActorsOfClass(World, AAURACRONPCGEnvironment::StaticClass(), FoundActors);
        
        for (AActor* Actor : FoundActors)
        {
            if (AAURACRONPCGEnvironment* Environment = Cast<AAURACRONPCGEnvironment>(Actor))
            {
                if (SearchOptions.bIncludeInactiveActors || IsValidPCGActor(Environment))
                {
                    ActorReferences.EnvironmentActors.Add(Environment);
                }
            }
        }

        if (SearchOptions.bVerboseLogging)
        {
            UE_LOGFMT(LogAURACRONPCGUtility, Log, "FindPCGActors: Encontrados {0} ambientes PCG válidos",
                     ActorReferences.EnvironmentActors.Num());
        }
    }

    // Encontrar trilhas
    if (SearchOptions.bSearchTrails)
    {
        FoundActors.Empty();
        UGameplayStatics::GetAllActorsOfClass(World, AAURACRONPCGTrail::StaticClass(), FoundActors);
        
        for (AActor* Actor : FoundActors)
        {
            if (AAURACRONPCGTrail* Trail = Cast<AAURACRONPCGTrail>(Actor))
            {
                if (SearchOptions.bIncludeInactiveActors || IsValidPCGActor(Trail))
                {
                    ActorReferences.TrailActors.Add(Trail);
                }
            }
        }

        if (SearchOptions.bVerboseLogging)
        {
            UE_LOGFMT(LogAURACRONPCGUtility, Log, "FindPCGActors: Encontradas {0} trilhas (Solar/Axis/Lunar)",
                     ActorReferences.TrailActors.Num());
        }
    }

    // Encontrar ilhas
    if (SearchOptions.bSearchIslands)
    {
        FoundActors.Empty();
        UGameplayStatics::GetAllActorsOfClass(World, AAURACRONPCGIsland::StaticClass(), FoundActors);
        
        for (AActor* Actor : FoundActors)
        {
            if (AAURACRONPCGIsland* Island = Cast<AAURACRONPCGIsland>(Actor))
            {
                if (SearchOptions.bIncludeInactiveActors || IsValidPCGActor(Island))
                {
                    ActorReferences.IslandActors.Add(Island);
                }
            }
        }

        if (SearchOptions.bVerboseLogging)
        {
            UE_LOGFMT(LogAURACRONPCGUtility, Log, "FindPCGActors: Encontradas {0} ilhas (incluindo Ilha Central Auracron)",
                     ActorReferences.IslandActors.Num());
        }
    }

    // Encontrar Prismal Flow
    if (SearchOptions.bSearchPrismalFlow)
    {
        FoundActors.Empty();
        UGameplayStatics::GetAllActorsOfClass(World, AAURACRONPCGPrismalFlow::StaticClass(), FoundActors);
        
        if (FoundActors.Num() > 0)
        {
            for (AActor* Actor : FoundActors)
            {
                if (AAURACRONPCGPrismalFlow* PrismalFlow = Cast<AAURACRONPCGPrismalFlow>(Actor))
                {
                    if (SearchOptions.bIncludeInactiveActors || IsValidPCGActor(PrismalFlow))
                    {
                        ActorReferences.PrismalFlowActor = PrismalFlow;
                        break; // Apenas um Prismal Flow por mundo
                    }
                }
            }
        }

        if (SearchOptions.bVerboseLogging)
        {
            UE_LOGFMT(LogAURACRONPCGUtility, Log, "FindPCGActors: Fluxo Prismal serpentino {0}",
                     ActorReferences.PrismalFlowActor ? TEXT("encontrado e ativo") : TEXT("não encontrado"));
        }
    }

    // Buscar setores específicos da Ilha Central Auracron
    if (SearchOptions.bSearchIslands)
    {
        FindAuracronCentralIslandSectors(World, ActorReferences, SearchOptions);
    }

    // Atualizar cache inteligente
    UpdateActorCache(World, ActorReferences);

    // Otimizar performance baseado no hardware detectado
    OptimizePerformanceForHardware(World, ActorReferences);

    // Log final detalhado
    if (SearchOptions.bVerboseLogging)
    {
        UE_LOGFMT(LogAURACRONPCGUtility, Log, "FindPCGActors: Busca completa - {0} atores PCG encontrados em World {1}",
                 ActorReferences.GetTotalActorCount(), World->GetName());

        LogDetailedActorStatistics(World, ActorReferences);
    }

    return ActorReferences;
}

FAURACRONPCGActorReferences UAURACRONPCGUtility::FindAllPCGActors(UWorld* World)
{
    FAURACRONPCGSearchOptions DefaultOptions;
    return FindPCGActors(World, DefaultOptions);
}

TArray<AActor*> UAURACRONPCGUtility::FindPCGActorsOfClass(UWorld* World, UClass* ActorClass, bool bIncludeInactive)
{
    TArray<AActor*> FoundActors;

    if (!IsValid(World) || !IsValid(ActorClass))
    {
        UE_LOGFMT(LogAURACRONPCGUtility, Warning, "FindPCGActorsOfClass: Parâmetros inválidos - World={0}, ActorClass={1}",
                 IsValid(World) ? World->GetName() : TEXT("NULL"),
                 IsValid(ActorClass) ? ActorClass->GetName() : TEXT("NULL"));
        return FoundActors;
    }

    // Validação server-side
    if (World->GetNetMode() != NM_Standalone && !ValidateServerPermissions(World))
    {
        UE_LOGFMT(LogAURACRONPCGUtility, Warning, "FindPCGActorsOfClass: Validação server-side falhou");
        return FoundActors;
    }

    TArray<AActor*> AllActors;
    UGameplayStatics::GetAllActorsOfClass(World, ActorClass, AllActors);

    for (AActor* Actor : AllActors)
    {
        if (bIncludeInactive || IsValidPCGActor(Actor))
        {
            FoundActors.Add(Actor);
        }
    }

    return FoundActors;
}

bool UAURACRONPCGUtility::IsValidPCGActor(AActor* Actor)
{
    if (!IsValid(Actor))
    {
        return false;
    }

    // Verificar se o ator está ativo
    if (!Actor->IsActorTickEnabled() || Actor->IsActorBeingDestroyed())
    {
        return false;
    }

    // Verificações específicas para tipos PCG
    if (AAURACRONPCGEnvironment* Environment = Cast<AAURACRONPCGEnvironment>(Actor))
    {
        // Verificar se tem componente PCG válido
        UPCGComponent* PCGComp = Environment->GetPCGComponent();
        return IsValid(PCGComp);
    }
    else if (AAURACRONPCGTrail* Trail = Cast<AAURACRONPCGTrail>(Actor))
    {
        // Verificar se a trilha está configurada
        UPCGComponent* PCGComp = Trail->GetPCGComponent();
        return IsValid(PCGComp);
    }
    else if (AAURACRONPCGIsland* Island = Cast<AAURACRONPCGIsland>(Actor))
    {
        // Verificar se a ilha está configurada
        UPCGComponent* PCGComp = Island->GetPCGComponent();
        return IsValid(PCGComp);
    }
    else if (AAURACRONPCGPrismalFlow* PrismalFlow = Cast<AAURACRONPCGPrismalFlow>(Actor))
    {
        // Verificar se o Prismal Flow está configurado
        UPCGComponent* PCGComp = PrismalFlow->GetPCGComponent();
        return IsValid(PCGComp);
    }

    // Para outros tipos de atores PCG, verificação básica
    return true;
}

FString UAURACRONPCGUtility::GetPCGActorStatistics(UWorld* World)
{
    if (!IsValid(World))
    {
        return TEXT("Mundo inválido");
    }

    FAURACRONPCGActorReferences ActorRefs = FindPCGActors(World, FAURACRONPCGSearchOptions::All());

    FString Stats = FString::Printf(
        TEXT("=== ESTATÍSTICAS PCG ===\n")
        TEXT("Ambientes: %d\n")
        TEXT("Trilhas: %d\n")
        TEXT("Ilhas: %d\n")
        TEXT("Prismal Flow: %s\n")
        TEXT("Total: %d atores"),
        ActorRefs.EnvironmentActors.Num(),
        ActorRefs.TrailActors.Num(),
        ActorRefs.IslandActors.Num(),
        ActorRefs.PrismalFlowActor ? TEXT("1") : TEXT("0"),
        ActorRefs.GetTotalActorCount()
    );

    return Stats;
}

void UAURACRONPCGUtility::ApplyConfigurationToAllActors(const FAURACRONPCGActorReferences& ActorReferences, const FString& ConfigurationName, float Value)
{
    // Aplicar configuração aos ambientes
    for (AAURACRONPCGEnvironment* Environment : ActorReferences.EnvironmentActors)
    {
        if (IsValid(Environment))
        {
            if (ConfigurationName == TEXT("ActivityScale"))
            {
                Environment->SetActivityScale(Value);
            }
            // Adicionar mais configurações conforme necessário
        }
    }

    // Aplicar configuração às trilhas
    for (AAURACRONPCGTrail* Trail : ActorReferences.TrailActors)
    {
        if (IsValid(Trail))
        {
            if (ConfigurationName == TEXT("ActivityScale"))
            {
                Trail->SetActivityScale(Value);
            }
            // Adicionar mais configurações conforme necessário
        }
    }

    // Aplicar configuração às ilhas
    for (AAURACRONPCGIsland* Island : ActorReferences.IslandActors)
    {
        if (IsValid(Island))
        {
            if (ConfigurationName == TEXT("ActivityScale"))
            {
                Island->SetActivityScale(Value);
            }
            // Adicionar mais configurações conforme necessário
        }
    }

    // Aplicar configuração ao Prismal Flow
    if (IsValid(ActorReferences.PrismalFlowActor))
    {
        if (ConfigurationName == TEXT("ActivityScale"))
        {
            ActorReferences.PrismalFlowActor->SetActivityScale(Value);
        }
        // Adicionar mais configurações conforme necessário
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGUtility: Configuração '%s' = %.2f aplicada a %d atores"), 
           *ConfigurationName, Value, ActorReferences.GetTotalActorCount());
}

void UAURACRONPCGUtility::RegisterPCGActorChangeCallback(UWorld* World, const FString& CallbackName)
{
    if (!World)
    {
        UE_LOGFMT(LogAURACRONPCGUtility, Warning, "RegisterPCGActorChangeCallback: Tentativa de registrar callback '{0}' com World nulo", CallbackName);
        return;
    }

    // Sistema robusto de callbacks para mudanças em atores PCG - UE 5.6
    static TMap<UWorld*, TArray<FString>> RegisteredCallbacks;

    if (!RegisteredCallbacks.Contains(World))
    {
        RegisteredCallbacks.Add(World, TArray<FString>());
    }

    TArray<FString>& WorldCallbacks = RegisteredCallbacks[World];

    if (!WorldCallbacks.Contains(CallbackName))
    {
        WorldCallbacks.Add(CallbackName);

        // Timer otimizado para UE 5.6 - 0.1f para melhor responsividade
        FTimerHandle CallbackTimer;
        World->GetTimerManager().SetTimer(CallbackTimer,
            FTimerDelegate::CreateLambda([CallbackName, World]()
            {
                // Verificação otimizada de atores PCG com validação server-side
                if (World->GetNetMode() != NM_Standalone && !ValidateServerPermissions(World))
                {
                    return;
                }

                int32 ValidActorCount = 0;
                for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
                {
                    AActor* Actor = *ActorItr;
                    if (IsValidPCGActor(Actor))
                    {
                        ValidActorCount++;
                        // Verificar integridade específica para Trilhos e Fluxo Prismal
                        ValidateActorIntegrity(Actor);
                    }
                }

                // Log periódico otimizado
                if (ValidActorCount > 0)
                {
                    UE_LOGFMT(LogAURACRONPCGUtility, VeryVerbose, "Callback '{0}': Monitorando {1} atores PCG válidos",
                             CallbackName, ValidActorCount);
                }
            }),
            0.1f, true); // Timer otimizado para UE 5.6

        // Armazenar timer para limpeza posterior
        if (!OptimizationTimers.Contains(World))
        {
            OptimizationTimers.Add(World, CallbackTimer);
        }

        UE_LOGFMT(LogAURACRONPCGUtility, Log, "RegisterPCGActorChangeCallback: Callback '{0}' registrado para World '{1}' com timer otimizado 0.1s",
                 CallbackName, World->GetName());
    }
    else
    {
        UE_LOGFMT(LogAURACRONPCGUtility, Warning, "RegisterPCGActorChangeCallback: Callback '{0}' já registrado para World '{1}'",
                 CallbackName, World->GetName());
    }
}

bool UAURACRONPCGUtility::IsCacheValid(UWorld* World)
{
    if (!ActorCache.Contains(World) || !CacheTimestamps.Contains(World))
    {
        return false;
    }

    float CurrentTime = World->GetTimeSeconds();
    float CacheTime = CacheTimestamps[World];

    return (CurrentTime - CacheTime) < CACHE_LIFETIME;
}

void UAURACRONPCGUtility::UpdateActorCache(UWorld* World, const FAURACRONPCGActorReferences& NewReferences)
{
    ActorCache.Add(World, NewReferences);
    CacheTimestamps.Add(World, World->GetTimeSeconds());

    // Limpar cache expirado periodicamente
    CleanExpiredCache();
}

void UAURACRONPCGUtility::CleanExpiredCache()
{
    TArray<UWorld*> ExpiredWorlds;

    for (auto& CacheEntry : CacheTimestamps)
    {
        UWorld* World = CacheEntry.Key;
        float CacheTime = CacheEntry.Value;

        if (!IsValid(World))
        {
            ExpiredWorlds.Add(World);
            continue;
        }

        float CurrentTime = World->GetTimeSeconds();
        if ((CurrentTime - CacheTime) >= CACHE_LIFETIME)
        {
            ExpiredWorlds.Add(World);
        }
    }

    // Remover entradas expiradas com limpeza completa
    for (UWorld* ExpiredWorld : ExpiredWorlds)
    {
        ActorCache.Remove(ExpiredWorld);
        CacheTimestamps.Remove(ExpiredWorld);

        // Limpeza de recursos adicionais UE 5.6
        if (StreamableManagers.Contains(ExpiredWorld))
        {
            StreamableManagers.Remove(ExpiredWorld);
        }

        if (OptimizationTimers.Contains(ExpiredWorld))
        {
            if (IsValid(ExpiredWorld))
            {
                ExpiredWorld->GetTimerManager().ClearTimer(OptimizationTimers[ExpiredWorld]);
            }
            OptimizationTimers.Remove(ExpiredWorld);
        }

        ParticleBudgets.Remove(ExpiredWorld);
        ServerValidationEnabled.Remove(ExpiredWorld);
    }

    if (ExpiredWorlds.Num() > 0)
    {
        UE_LOGFMT(LogAURACRONPCGUtility, Log, "CleanExpiredCache: Limpeza completa de {0} worlds expirados", ExpiredWorlds.Num());
    }
}

// ================================================================================================
// NOVAS FUNÇÕES PARA INTEGRAÇÃO COMPLETA COM AURACRON_GAME_DESIGN_DOCUMENT_UNIFIED.md
// ================================================================================================

void UAURACRONPCGUtility::InitializeStreamableManager(UWorld* World)
{
    if (!IsValid(World) || StreamableManagers.Contains(World))
    {
        return;
    }

    TSharedPtr<FStreamableManager> NewStreamableManager = MakeShared<FStreamableManager>();
    StreamableManagers.Add(World, NewStreamableManager);

    UE_LOGFMT(LogAURACRONPCGUtility, Log, "InitializeStreamableManager: StreamableManager inicializado para World {0}", World->GetName());
}

bool UAURACRONPCGUtility::ValidateServerPermissions(UWorld* World)
{
    if (!IsValid(World))
    {
        return false;
    }

    // Validação anti-cheat server-side para UE 5.6
    if (World->GetNetMode() == NM_DedicatedServer || World->GetNetMode() == NM_ListenServer)
    {
        // Verificar se validação está habilitada para este mundo
        if (!ServerValidationEnabled.Contains(World))
        {
            ServerValidationEnabled.Add(World, true);
        }

        return ServerValidationEnabled[World];
    }

    return true; // Cliente standalone sempre válido
}

void UAURACRONPCGUtility::ValidateActorIntegrity(AActor* Actor)
{
    if (!IsValid(Actor))
    {
        return;
    }

    // Validação específica para tipos de atores PCG
    if (AAURACRONPCGTrail* Trail = Cast<AAURACRONPCGTrail>(Actor))
    {
        // Validar integridade dos Trilhos Solar/Axis/Lunar
        ValidateTrailIntegrity(Trail);
    }
    else if (AAURACRONPCGPrismalFlow* PrismalFlow = Cast<AAURACRONPCGPrismalFlow>(Actor))
    {
        // Validar integridade do Fluxo Prismal serpentino
        ValidatePrismalFlowIntegrity(PrismalFlow);
    }
    else if (AAURACRONPCGIsland* Island = Cast<AAURACRONPCGIsland>(Actor))
    {
        // Validar integridade das ilhas incluindo Ilha Central Auracron
        ValidateIslandIntegrity(Island);
    }
}

void UAURACRONPCGUtility::FindAuracronCentralIslandSectors(UWorld* World, FAURACRONPCGActorReferences& ActorReferences, const FAURACRONPCGSearchOptions& SearchOptions)
{
    if (!IsValid(World))
    {
        return;
    }

    // Buscar setores específicos da Ilha Central Auracron
    TArray<AActor*> FoundSectors;

    // Setor Nexus
    UGameplayStatics::GetAllActorsOfClass(World, AAURACRONPCGNexusIsland::StaticClass(), FoundSectors);
    for (AActor* Actor : FoundSectors)
    {
        if (AAURACRONPCGNexusIsland* NexusIsland = Cast<AAURACRONPCGNexusIsland>(Actor))
        {
            if (SearchOptions.bIncludeInactiveActors || IsValidPCGActor(NexusIsland))
            {
                ActorReferences.IslandActors.Add(Cast<AAURACRONPCGIsland>(NexusIsland));
            }
        }
    }

    // Setor Santuário
    FoundSectors.Empty();
    UGameplayStatics::GetAllActorsOfClass(World, AAURACRONPCGSanctuaryIsland::StaticClass(), FoundSectors);
    for (AActor* Actor : FoundSectors)
    {
        if (AAURACRONPCGSanctuaryIsland* SanctuaryIsland = Cast<AAURACRONPCGSanctuaryIsland>(Actor))
        {
            if (SearchOptions.bIncludeInactiveActors || IsValidPCGActor(SanctuaryIsland))
            {
                ActorReferences.IslandActors.Add(Cast<AAURACRONPCGIsland>(SanctuaryIsland));
            }
        }
    }

    // Setor Arsenal
    FoundSectors.Empty();
    UGameplayStatics::GetAllActorsOfClass(World, AAURACRONPCGArsenalIsland::StaticClass(), FoundSectors);
    for (AActor* Actor : FoundSectors)
    {
        if (AAURACRONPCGArsenalIsland* ArsenalIsland = Cast<AAURACRONPCGArsenalIsland>(Actor))
        {
            if (SearchOptions.bIncludeInactiveActors || IsValidPCGActor(ArsenalIsland))
            {
                ActorReferences.IslandActors.Add(Cast<AAURACRONPCGIsland>(ArsenalIsland));
            }
        }
    }

    // Setor Caos
    FoundSectors.Empty();
    UGameplayStatics::GetAllActorsOfClass(World, AAURACRONPCGChaosIsland::StaticClass(), FoundSectors);
    for (AActor* Actor : FoundSectors)
    {
        if (AAURACRONPCGChaosIsland* ChaosIsland = Cast<AAURACRONPCGChaosIsland>(Actor))
        {
            if (SearchOptions.bIncludeInactiveActors || IsValidPCGActor(ChaosIsland))
            {
                ActorReferences.IslandActors.Add(Cast<AAURACRONPCGIsland>(ChaosIsland));
            }
        }
    }

    if (SearchOptions.bVerboseLogging)
    {
        UE_LOGFMT(LogAURACRONPCGUtility, Log, "FindAuracronCentralIslandSectors: Setores da Ilha Central Auracron encontrados - Total: {0}",
                 ActorReferences.IslandActors.Num());
    }
}

void UAURACRONPCGUtility::OptimizePerformanceForHardware(UWorld* World, const FAURACRONPCGActorReferences& ActorReferences)
{
    if (!IsValid(World))
    {
        return;
    }

    // Detectar capacidade de hardware e definir orçamento de partículas
    FString HardwareLevel = DetectHardwareCapability(World);
    int32 ParticleBudget = HardwareParticleBudgets.Contains(HardwareLevel) ?
                          HardwareParticleBudgets[HardwareLevel] : 300; // Default Entry level

    ParticleBudgets.Add(World, ParticleBudget);

    // Aplicar otimizações específicas aos atores PCG
    for (AAURACRONPCGTrail* Trail : ActorReferences.TrailActors)
    {
        if (IsValid(Trail))
        {
            ApplyTrailOptimizations(Trail, HardwareLevel, ParticleBudget);
        }
    }

    if (IsValid(ActorReferences.PrismalFlowActor))
    {
        ApplyPrismalFlowOptimizations(ActorReferences.PrismalFlowActor, HardwareLevel, ParticleBudget);
    }

    for (AAURACRONPCGIsland* Island : ActorReferences.IslandActors)
    {
        if (IsValid(Island))
        {
            ApplyIslandOptimizations(Island, HardwareLevel, ParticleBudget);
        }
    }

    UE_LOGFMT(LogAURACRONPCGUtility, Log, "OptimizePerformanceForHardware: Otimizações aplicadas para hardware {0} com orçamento de {1} partículas",
             HardwareLevel, ParticleBudget);
}

void UAURACRONPCGUtility::LogDetailedActorStatistics(UWorld* World, const FAURACRONPCGActorReferences& ActorReferences)
{
    if (!IsValid(World))
    {
        return;
    }

    UE_LOGFMT(LogAURACRONPCGUtility, Log, "=== ESTATÍSTICAS DETALHADAS PCG AURACRON ===");
    UE_LOGFMT(LogAURACRONPCGUtility, Log, "World: {0}", World->GetName());
    UE_LOGFMT(LogAURACRONPCGUtility, Log, "Ambientes PCG: {0}", ActorReferences.EnvironmentActors.Num());
    UE_LOGFMT(LogAURACRONPCGUtility, Log, "Trilhos (Solar/Axis/Lunar): {0}", ActorReferences.TrailActors.Num());
    UE_LOGFMT(LogAURACRONPCGUtility, Log, "Ilhas (incluindo Central Auracron): {0}", ActorReferences.IslandActors.Num());
    UE_LOGFMT(LogAURACRONPCGUtility, Log, "Fluxo Prismal: {0}", ActorReferences.PrismalFlowActor ? TEXT("Ativo") : TEXT("Inativo"));

    if (ParticleBudgets.Contains(World))
    {
        UE_LOGFMT(LogAURACRONPCGUtility, Log, "Orçamento de Partículas: {0}", ParticleBudgets[World]);
    }

    UE_LOGFMT(LogAURACRONPCGUtility, Log, "Total de Atores PCG: {0}", ActorReferences.GetTotalActorCount());
    UE_LOGFMT(LogAURACRONPCGUtility, Log, "=== FIM ESTATÍSTICAS ===");
}

FString UAURACRONPCGUtility::DetectHardwareCapability(UWorld* World)
{
    if (!IsValid(World))
    {
        return TEXT("Entry");
    }

    // Detecção básica de hardware baseada em configurações do engine
    // Em implementação real, usar APIs específicas do UE 5.6 para detecção de hardware

    // Simulação de detecção baseada em configurações
    if (GEngine && GEngine->GetGameUserSettings())
    {
        auto* UserSettings = GEngine->GetGameUserSettings();
        int32 OverallQuality = UserSettings->GetOverallScalabilityLevel();

        if (OverallQuality >= 3)
        {
            return TEXT("High");
        }
        else if (OverallQuality >= 2)
        {
            return TEXT("Mid");
        }
    }

    return TEXT("Entry");
}

void UAURACRONPCGUtility::ApplyTrailOptimizations(AAURACRONPCGTrail* Trail, const FString& HardwareLevel, int32 ParticleBudget)
{
    if (!IsValid(Trail))
    {
        return;
    }

    // Aplicar otimizações específicas para Trilhos Solar/Axis/Lunar baseado no hardware
    int32 TrailParticleBudget = ParticleBudget / 5; // 20% do orçamento total para trilhos

    // Implementação específica seria feita através de interface ou função pública do Trail
    // Trail->SetParticleBudget(TrailParticleBudget);
    // Trail->SetQualityLevel(HardwareLevel);

    UE_LOGFMT(LogAURACRONPCGUtility, VeryVerbose, "ApplyTrailOptimizations: Trilho {0} otimizado para {1} com {2} partículas",
             Trail->GetName(), HardwareLevel, TrailParticleBudget);
}

void UAURACRONPCGUtility::ApplyPrismalFlowOptimizations(AAURACRONPCGPrismalFlow* PrismalFlow, const FString& HardwareLevel, int32 ParticleBudget)
{
    if (!IsValid(PrismalFlow))
    {
        return;
    }

    // Aplicar otimizações específicas para Fluxo Prismal serpentino
    int32 FlowParticleBudget = ParticleBudget / 2; // 50% do orçamento para o Fluxo Prismal

    // Implementação específica seria feita através de interface ou função pública
    // PrismalFlow->SetParticleBudget(FlowParticleBudget);
    // PrismalFlow->SetQualityLevel(HardwareLevel);

    UE_LOGFMT(LogAURACRONPCGUtility, VeryVerbose, "ApplyPrismalFlowOptimizations: Fluxo Prismal otimizado para {0} com {1} partículas",
             HardwareLevel, FlowParticleBudget);
}

void UAURACRONPCGUtility::ApplyIslandOptimizations(AAURACRONPCGIsland* Island, const FString& HardwareLevel, int32 ParticleBudget)
{
    if (!IsValid(Island))
    {
        return;
    }

    // Aplicar otimizações específicas para ilhas incluindo setores da Ilha Central Auracron
    int32 IslandParticleBudget = ParticleBudget / 10; // 10% do orçamento por ilha

    // Implementação específica seria feita através de interface ou função pública
    // Island->SetParticleBudget(IslandParticleBudget);
    // Island->SetQualityLevel(HardwareLevel);

    UE_LOGFMT(LogAURACRONPCGUtility, VeryVerbose, "ApplyIslandOptimizations: Ilha {0} otimizada para {1} com {2} partículas",
             Island->GetName(), HardwareLevel, IslandParticleBudget);
}

void UAURACRONPCGUtility::ValidateTrailIntegrity(AAURACRONPCGTrail* Trail)
{
    if (!IsValid(Trail))
    {
        return;
    }

    // Validação específica para integridade dos Trilhos Solar/Axis/Lunar
    UPCGComponent* PCGComp = Trail->GetPCGComponent();
    if (!IsValid(PCGComp))
    {
        UE_LOGFMT(LogAURACRONPCGUtility, Warning, "ValidateTrailIntegrity: Trilho {0} sem componente PCG válido", Trail->GetName());
        return;
    }

    // Validações específicas para cada tipo de trilho
    // Implementação específica dependeria da interface do Trail
    UE_LOGFMT(LogAURACRONPCGUtility, VeryVerbose, "ValidateTrailIntegrity: Trilho {0} validado com sucesso", Trail->GetName());
}

void UAURACRONPCGUtility::ValidatePrismalFlowIntegrity(AAURACRONPCGPrismalFlow* PrismalFlow)
{
    if (!IsValid(PrismalFlow))
    {
        return;
    }

    // Validação específica para integridade do Fluxo Prismal serpentino
    UPCGComponent* PCGComp = PrismalFlow->GetPCGComponent();
    if (!IsValid(PCGComp))
    {
        UE_LOGFMT(LogAURACRONPCGUtility, Warning, "ValidatePrismalFlowIntegrity: Fluxo Prismal sem componente PCG válido");
        return;
    }

    // Validações específicas para o fluxo serpentino
    UE_LOGFMT(LogAURACRONPCGUtility, VeryVerbose, "ValidatePrismalFlowIntegrity: Fluxo Prismal validado com sucesso");
}

void UAURACRONPCGUtility::ValidateIslandIntegrity(AAURACRONPCGIsland* Island)
{
    if (!IsValid(Island))
    {
        return;
    }

    // Validação específica para integridade das ilhas incluindo setores da Ilha Central Auracron
    UPCGComponent* PCGComp = Island->GetPCGComponent();
    if (!IsValid(PCGComp))
    {
        UE_LOGFMT(LogAURACRONPCGUtility, Warning, "ValidateIslandIntegrity: Ilha {0} sem componente PCG válido", Island->GetName());
        return;
    }

    // Validações específicas para setores da Ilha Central Auracron
    UE_LOGFMT(LogAURACRONPCGUtility, VeryVerbose, "ValidateIslandIntegrity: Ilha {0} validada com sucesso", Island->GetName());
}

// ================================================================================================
// FUNÇÕES AVANÇADAS PARA INTEGRAÇÃO MULTIPLAYER E SISTEMAS ESPECÍFICOS AURACRON
// ================================================================================================

TArray<AAURACRONPCGTrail*> UAURACRONPCGUtility::FindTrailsByType(UWorld* World, const FString& TrailType)
{
    TArray<AAURACRONPCGTrail*> FilteredTrails;

    if (!IsValid(World))
    {
        UE_LOGFMT(LogAURACRONPCGUtility, Warning, "FindTrailsByType: World inválido");
        return FilteredTrails;
    }

    FAURACRONPCGActorReferences ActorRefs = FindPCGActors(World, FAURACRONPCGSearchOptions::All());

    for (AAURACRONPCGTrail* Trail : ActorRefs.TrailActors)
    {
        if (IsValid(Trail))
        {
            // Implementação específica dependeria da interface do Trail para obter tipo
            // if (Trail->GetTrailType() == TrailType)
            // {
                FilteredTrails.Add(Trail);
            // }
        }
    }

    UE_LOGFMT(LogAURACRONPCGUtility, Log, "FindTrailsByType: Encontrados {0} trilhos do tipo {1}",
             FilteredTrails.Num(), TrailType);

    return FilteredTrails;
}

TArray<AAURACRONPCGIsland*> UAURACRONPCGUtility::FindIslandsBySector(UWorld* World, const FString& SectorType)
{
    TArray<AAURACRONPCGIsland*> FilteredIslands;

    if (!IsValid(World))
    {
        UE_LOGFMT(LogAURACRONPCGUtility, Warning, "FindIslandsBySector: World inválido");
        return FilteredIslands;
    }

    FAURACRONPCGActorReferences ActorRefs = FindPCGActors(World, FAURACRONPCGSearchOptions::All());

    for (AAURACRONPCGIsland* Island : ActorRefs.IslandActors)
    {
        if (IsValid(Island))
        {
            // Filtrar por tipo de setor da Ilha Central Auracron
            if (SectorType == TEXT("Nexus") && Cast<AAURACRONPCGNexusIsland>(Island))
            {
                FilteredIslands.Add(Island);
            }
            else if (SectorType == TEXT("Sanctuary") && Cast<AAURACRONPCGSanctuaryIsland>(Island))
            {
                FilteredIslands.Add(Island);
            }
            else if (SectorType == TEXT("Arsenal") && Cast<AAURACRONPCGArsenalIsland>(Island))
            {
                FilteredIslands.Add(Island);
            }
            else if (SectorType == TEXT("Chaos") && Cast<AAURACRONPCGChaosIsland>(Island))
            {
                FilteredIslands.Add(Island);
            }
        }
    }

    UE_LOGFMT(LogAURACRONPCGUtility, Log, "FindIslandsBySector: Encontradas {0} ilhas do setor {1}",
             FilteredIslands.Num(), SectorType);

    return FilteredIslands;
}

bool UAURACRONPCGUtility::ValidateMultiplayerSynchronization(UWorld* World)
{
    if (!IsValid(World))
    {
        return false;
    }

    // Validação específica para sincronização multiplayer
    if (World->GetNetMode() == NM_Standalone)
    {
        return true; // Sempre válido em single player
    }

    // Verificar se todos os atores PCG críticos estão sincronizados
    FAURACRONPCGActorReferences ActorRefs = FindPCGActors(World, FAURACRONPCGSearchOptions::All());

    bool bAllSynchronized = true;

    // Validar Fluxo Prismal
    if (IsValid(ActorRefs.PrismalFlowActor))
    {
        // Implementação específica dependeria da interface de replicação
        // bAllSynchronized &= ActorRefs.PrismalFlowActor->IsReplicationSynchronized();
    }
    else
    {
        UE_LOGFMT(LogAURACRONPCGUtility, Warning, "ValidateMultiplayerSynchronization: Fluxo Prismal não encontrado");
        bAllSynchronized = false;
    }

    // Validar Trilhos críticos
    for (AAURACRONPCGTrail* Trail : ActorRefs.TrailActors)
    {
        if (IsValid(Trail))
        {
            // Implementação específica dependeria da interface de replicação
            // bAllSynchronized &= Trail->IsReplicationSynchronized();
        }
    }

    // Validar setores da Ilha Central Auracron
    for (AAURACRONPCGIsland* Island : ActorRefs.IslandActors)
    {
        if (IsValid(Island))
        {
            // Implementação específica dependeria da interface de replicação
            // bAllSynchronized &= Island->IsReplicationSynchronized();
        }
    }

    UE_LOGFMT(LogAURACRONPCGUtility, Log, "ValidateMultiplayerSynchronization: Sincronização {0}",
             bAllSynchronized ? TEXT("válida") : TEXT("com problemas"));

    return bAllSynchronized;
}

void UAURACRONPCGUtility::ForceRefreshAllPCGActors(UWorld* World)
{
    if (!IsValid(World))
    {
        UE_LOGFMT(LogAURACRONPCGUtility, Warning, "ForceRefreshAllPCGActors: World inválido");
        return;
    }

    // Limpar cache para forçar nova busca
    if (ActorCache.Contains(World))
    {
        ActorCache.Remove(World);
    }
    if (CacheTimestamps.Contains(World))
    {
        CacheTimestamps.Remove(World);
    }

    // Buscar todos os atores novamente
    FAURACRONPCGActorReferences RefreshedActors = FindPCGActors(World, FAURACRONPCGSearchOptions::All());

    // Aplicar validações e otimizações
    OptimizePerformanceForHardware(World, RefreshedActors);

    UE_LOGFMT(LogAURACRONPCGUtility, Log, "ForceRefreshAllPCGActors: Refresh completo - {0} atores PCG atualizados",
             RefreshedActors.GetTotalActorCount());
}

int32 UAURACRONPCGUtility::GetCurrentParticleBudget(UWorld* World)
{
    if (!IsValid(World))
    {
        return 300; // Default Entry level
    }

    if (ParticleBudgets.Contains(World))
    {
        return ParticleBudgets[World];
    }

    // Detectar e configurar orçamento se não existir
    FString HardwareLevel = DetectHardwareCapability(World);
    int32 Budget = HardwareParticleBudgets.Contains(HardwareLevel) ?
                   HardwareParticleBudgets[HardwareLevel] : 300;

    ParticleBudgets.Add(World, Budget);

    UE_LOGFMT(LogAURACRONPCGUtility, Log, "GetCurrentParticleBudget: Orçamento configurado para {0} partículas (hardware: {1})",
             Budget, HardwareLevel);

    return Budget;
}

void UAURACRONPCGUtility::SetParticleBudgetOverride(UWorld* World, int32 NewBudget)
{
    if (!IsValid(World) || NewBudget < 100)
    {
        UE_LOGFMT(LogAURACRONPCGUtility, Warning, "SetParticleBudgetOverride: Parâmetros inválidos - World={0}, Budget={1}",
                 IsValid(World) ? World->GetName() : TEXT("NULL"), NewBudget);
        return;
    }

    ParticleBudgets.Add(World, NewBudget);

    // Aplicar novo orçamento aos atores existentes
    FAURACRONPCGActorReferences ActorRefs = FindPCGActors(World, FAURACRONPCGSearchOptions::All());
    FString HardwareLevel = DetectHardwareCapability(World);

    for (AAURACRONPCGTrail* Trail : ActorRefs.TrailActors)
    {
        if (IsValid(Trail))
        {
            ApplyTrailOptimizations(Trail, HardwareLevel, NewBudget);
        }
    }

    if (IsValid(ActorRefs.PrismalFlowActor))
    {
        ApplyPrismalFlowOptimizations(ActorRefs.PrismalFlowActor, HardwareLevel, NewBudget);
    }

    for (AAURACRONPCGIsland* Island : ActorRefs.IslandActors)
    {
        if (IsValid(Island))
        {
            ApplyIslandOptimizations(Island, HardwareLevel, NewBudget);
        }
    }

    UE_LOGFMT(LogAURACRONPCGUtility, Log, "SetParticleBudgetOverride: Novo orçamento {0} aplicado a {1} atores PCG",
             NewBudget, ActorRefs.GetTotalActorCount());
}

FString UAURACRONPCGUtility::GetDetailedPCGReport(UWorld* World)
{
    if (!IsValid(World))
    {
        return TEXT("World inválido");
    }

    FAURACRONPCGActorReferences ActorRefs = FindPCGActors(World, FAURACRONPCGSearchOptions::All());
    FString HardwareLevel = DetectHardwareCapability(World);
    int32 ParticleBudget = GetCurrentParticleBudget(World);

    FString Report = FString::Printf(
        TEXT("=== RELATÓRIO DETALHADO PCG AURACRON ===\n")
        TEXT("World: %s\n")
        TEXT("Hardware Level: %s\n")
        TEXT("Orçamento de Partículas: %d\n")
        TEXT("Validação Server-Side: %s\n")
        TEXT("\n=== ATORES PCG ===\n")
        TEXT("Ambientes: %d\n")
        TEXT("Trilhos Solar/Axis/Lunar: %d\n")
        TEXT("Ilhas (incluindo Central Auracron): %d\n")
        TEXT("Fluxo Prismal: %s\n")
        TEXT("\n=== SETORES ILHA CENTRAL AURACRON ===\n")
        TEXT("Setor Nexus: %d\n")
        TEXT("Setor Santuário: %d\n")
        TEXT("Setor Arsenal: %d\n")
        TEXT("Setor Caos: %d\n")
        TEXT("\n=== PERFORMANCE ===\n")
        TEXT("Cache Ativo: %s\n")
        TEXT("Otimizações Aplicadas: %s\n")
        TEXT("Sincronização Multiplayer: %s\n")
        TEXT("\nTotal de Atores PCG: %d\n")
        TEXT("=== FIM RELATÓRIO ==="),
        *World->GetName(),
        *HardwareLevel,
        ParticleBudget,
        ServerValidationEnabled.Contains(World) && ServerValidationEnabled[World] ? TEXT("Habilitada") : TEXT("Desabilitada"),
        ActorRefs.EnvironmentActors.Num(),
        ActorRefs.TrailActors.Num(),
        ActorRefs.IslandActors.Num(),
        ActorRefs.PrismalFlowActor ? TEXT("Ativo") : TEXT("Inativo"),
        FindIslandsBySector(World, TEXT("Nexus")).Num(),
        FindIslandsBySector(World, TEXT("Sanctuary")).Num(),
        FindIslandsBySector(World, TEXT("Arsenal")).Num(),
        FindIslandsBySector(World, TEXT("Chaos")).Num(),
        ActorCache.Contains(World) ? TEXT("Sim") : TEXT("Não"),
        ParticleBudgets.Contains(World) ? TEXT("Sim") : TEXT("Não"),
        ValidateMultiplayerSynchronization(World) ? TEXT("Válida") : TEXT("Com Problemas"),
        ActorRefs.GetTotalActorCount()
    );

    return Report;
}
