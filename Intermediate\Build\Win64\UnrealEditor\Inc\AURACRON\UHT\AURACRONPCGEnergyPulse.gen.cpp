// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGEnergyPulse.h"
#include "Engine/HitResult.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGEnergyPulse() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGEnergyPulse();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGEnergyPulse_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONEnergyType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONMapPhase();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONPortalType();
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UAudioComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UPointLightComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UPrimitiveComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USphereComponent_NoRegister();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FHitResult();
GAMEPLAYABILITIES_API UClass* Z_Construct_UClass_UGameplayEffect_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Class AAURACRONPCGEnergyPulse Function ApplyPulseEffects ***********************
struct Z_Construct_UFunction_AAURACRONPCGEnergyPulse_ApplyPulseEffects_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Aplicar efeitos aos jogadores e ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar efeitos aos jogadores e ambiente" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_ApplyPulseEffects_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnergyPulse, nullptr, "ApplyPulseEffects", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_ApplyPulseEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnergyPulse_ApplyPulseEffects_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGEnergyPulse_ApplyPulseEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnergyPulse_ApplyPulseEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnergyPulse::execApplyPulseEffects)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyPulseEffects();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnergyPulse Function ApplyPulseEffects *************************

// ********** Begin Class AAURACRONPCGEnergyPulse Function CreateEnergyPulseForPortalType **********
struct Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics
{
	struct AURACRONPCGEnergyPulse_eventCreateEnergyPulseForPortalType_Parms
	{
		EAURACRONPortalType PortalType;
		float Duration;
		float Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Criar pulso de energia baseado no tipo de portal */" },
#endif
		{ "CPP_Default_Duration", "0.000000" },
		{ "CPP_Default_Intensity", "1.000000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar pulso de energia baseado no tipo de portal" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_PortalType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PortalType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::NewProp_PortalType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::NewProp_PortalType = { "PortalType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventCreateEnergyPulseForPortalType_Parms, PortalType), Z_Construct_UEnum_AURACRON_EAURACRONPortalType, METADATA_PARAMS(0, nullptr) }; // 1562177233
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventCreateEnergyPulseForPortalType_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventCreateEnergyPulseForPortalType_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::NewProp_PortalType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::NewProp_PortalType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnergyPulse, nullptr, "CreateEnergyPulseForPortalType", Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::AURACRONPCGEnergyPulse_eventCreateEnergyPulseForPortalType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::AURACRONPCGEnergyPulse_eventCreateEnergyPulseForPortalType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnergyPulse::execCreateEnergyPulseForPortalType)
{
	P_GET_ENUM(EAURACRONPortalType,Z_Param_PortalType);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateEnergyPulseForPortalType(EAURACRONPortalType(Z_Param_PortalType),Z_Param_Duration,Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnergyPulse Function CreateEnergyPulseForPortalType ************

// ********** Begin Class AAURACRONPCGEnergyPulse Function CreateGoldenEnergyPulse *****************
struct Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse_Statics
{
	struct AURACRONPCGEnergyPulse_eventCreateGoldenEnergyPulse_Parms
	{
		float Duration;
		float Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Criar pulso de energia dourada (Portal Radiante) */" },
#endif
		{ "CPP_Default_Duration", "3.000000" },
		{ "CPP_Default_Intensity", "1.200000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar pulso de energia dourada (Portal Radiante)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventCreateGoldenEnergyPulse_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventCreateGoldenEnergyPulse_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnergyPulse, nullptr, "CreateGoldenEnergyPulse", Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse_Statics::AURACRONPCGEnergyPulse_eventCreateGoldenEnergyPulse_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse_Statics::AURACRONPCGEnergyPulse_eventCreateGoldenEnergyPulse_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnergyPulse::execCreateGoldenEnergyPulse)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateGoldenEnergyPulse(Z_Param_Duration,Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnergyPulse Function CreateGoldenEnergyPulse *******************

// ********** Begin Class AAURACRONPCGEnergyPulse Function CreateLunarEnergyPulse ******************
struct Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateLunarEnergyPulse_Statics
{
	struct AURACRONPCGEnergyPulse_eventCreateLunarEnergyPulse_Parms
	{
		float Duration;
		float Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Criar pulso de energia lunar (Trilhos Lunares) */" },
#endif
		{ "CPP_Default_Duration", "5.000000" },
		{ "CPP_Default_Intensity", "1.300000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar pulso de energia lunar (Trilhos Lunares)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateLunarEnergyPulse_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventCreateLunarEnergyPulse_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateLunarEnergyPulse_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventCreateLunarEnergyPulse_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateLunarEnergyPulse_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateLunarEnergyPulse_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateLunarEnergyPulse_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateLunarEnergyPulse_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateLunarEnergyPulse_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnergyPulse, nullptr, "CreateLunarEnergyPulse", Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateLunarEnergyPulse_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateLunarEnergyPulse_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateLunarEnergyPulse_Statics::AURACRONPCGEnergyPulse_eventCreateLunarEnergyPulse_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateLunarEnergyPulse_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateLunarEnergyPulse_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateLunarEnergyPulse_Statics::AURACRONPCGEnergyPulse_eventCreateLunarEnergyPulse_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateLunarEnergyPulse()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateLunarEnergyPulse_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnergyPulse::execCreateLunarEnergyPulse)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateLunarEnergyPulse(Z_Param_Duration,Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnergyPulse Function CreateLunarEnergyPulse ********************

// ********** Begin Class AAURACRONPCGEnergyPulse Function CreateSilverEnergyPulse *****************
struct Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse_Statics
{
	struct AURACRONPCGEnergyPulse_eventCreateSilverEnergyPulse_Parms
	{
		float Duration;
		float Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Criar pulso de energia prateada (Portal Zephyr) */" },
#endif
		{ "CPP_Default_Duration", "2.500000" },
		{ "CPP_Default_Intensity", "1.000000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar pulso de energia prateada (Portal Zephyr)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventCreateSilverEnergyPulse_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventCreateSilverEnergyPulse_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnergyPulse, nullptr, "CreateSilverEnergyPulse", Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse_Statics::AURACRONPCGEnergyPulse_eventCreateSilverEnergyPulse_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse_Statics::AURACRONPCGEnergyPulse_eventCreateSilverEnergyPulse_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnergyPulse::execCreateSilverEnergyPulse)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateSilverEnergyPulse(Z_Param_Duration,Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnergyPulse Function CreateSilverEnergyPulse *******************

// ********** Begin Class AAURACRONPCGEnergyPulse Function CreateSolarEnergyPulse ******************
struct Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSolarEnergyPulse_Statics
{
	struct AURACRONPCGEnergyPulse_eventCreateSolarEnergyPulse_Parms
	{
		float Duration;
		float Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Criar pulso de energia solar (Trilhos Solares) */" },
#endif
		{ "CPP_Default_Duration", "4.000000" },
		{ "CPP_Default_Intensity", "1.800000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar pulso de energia solar (Trilhos Solares)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSolarEnergyPulse_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventCreateSolarEnergyPulse_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSolarEnergyPulse_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventCreateSolarEnergyPulse_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSolarEnergyPulse_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSolarEnergyPulse_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSolarEnergyPulse_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSolarEnergyPulse_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSolarEnergyPulse_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnergyPulse, nullptr, "CreateSolarEnergyPulse", Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSolarEnergyPulse_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSolarEnergyPulse_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSolarEnergyPulse_Statics::AURACRONPCGEnergyPulse_eventCreateSolarEnergyPulse_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSolarEnergyPulse_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSolarEnergyPulse_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSolarEnergyPulse_Statics::AURACRONPCGEnergyPulse_eventCreateSolarEnergyPulse_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSolarEnergyPulse()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSolarEnergyPulse_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnergyPulse::execCreateSolarEnergyPulse)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateSolarEnergyPulse(Z_Param_Duration,Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnergyPulse Function CreateSolarEnergyPulse ********************

// ********** Begin Class AAURACRONPCGEnergyPulse Function CreateVioletEnergyPulse *****************
struct Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse_Statics
{
	struct AURACRONPCGEnergyPulse_eventCreateVioletEnergyPulse_Parms
	{
		float Duration;
		float Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Criar pulso de energia violeta (Portal Umbral) */" },
#endif
		{ "CPP_Default_Duration", "3.500000" },
		{ "CPP_Default_Intensity", "1.500000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Criar pulso de energia violeta (Portal Umbral)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventCreateVioletEnergyPulse_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventCreateVioletEnergyPulse_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnergyPulse, nullptr, "CreateVioletEnergyPulse", Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse_Statics::AURACRONPCGEnergyPulse_eventCreateVioletEnergyPulse_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse_Statics::AURACRONPCGEnergyPulse_eventCreateVioletEnergyPulse_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnergyPulse::execCreateVioletEnergyPulse)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateVioletEnergyPulse(Z_Param_Duration,Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnergyPulse Function CreateVioletEnergyPulse *******************

// ********** Begin Class AAURACRONPCGEnergyPulse Function MulticastOnTerritorialControlChanged ****
struct AURACRONPCGEnergyPulse_eventMulticastOnTerritorialControlChanged_Parms
{
	int32 NewControllingTeam;
};
static FName NAME_AAURACRONPCGEnergyPulse_MulticastOnTerritorialControlChanged = FName(TEXT("MulticastOnTerritorialControlChanged"));
void AAURACRONPCGEnergyPulse::MulticastOnTerritorialControlChanged(int32 NewControllingTeam)
{
	AURACRONPCGEnergyPulse_eventMulticastOnTerritorialControlChanged_Parms Parms;
	Parms.NewControllingTeam=NewControllingTeam;
	UFunction* Func = FindFunctionChecked(NAME_AAURACRONPCGEnergyPulse_MulticastOnTerritorialControlChanged);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AAURACRONPCGEnergyPulse_MulticastOnTerritorialControlChanged_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewControllingTeam;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_MulticastOnTerritorialControlChanged_Statics::NewProp_NewControllingTeam = { "NewControllingTeam", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventMulticastOnTerritorialControlChanged_Parms, NewControllingTeam), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnergyPulse_MulticastOnTerritorialControlChanged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_MulticastOnTerritorialControlChanged_Statics::NewProp_NewControllingTeam,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_MulticastOnTerritorialControlChanged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_MulticastOnTerritorialControlChanged_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnergyPulse, nullptr, "MulticastOnTerritorialControlChanged", Z_Construct_UFunction_AAURACRONPCGEnergyPulse_MulticastOnTerritorialControlChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_MulticastOnTerritorialControlChanged_Statics::PropPointers), sizeof(AURACRONPCGEnergyPulse_eventMulticastOnTerritorialControlChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00044CC1, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_MulticastOnTerritorialControlChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnergyPulse_MulticastOnTerritorialControlChanged_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONPCGEnergyPulse_eventMulticastOnTerritorialControlChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnergyPulse_MulticastOnTerritorialControlChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnergyPulse_MulticastOnTerritorialControlChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnergyPulse::execMulticastOnTerritorialControlChanged)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_NewControllingTeam);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->MulticastOnTerritorialControlChanged_Implementation(Z_Param_NewControllingTeam);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnergyPulse Function MulticastOnTerritorialControlChanged ******

// ********** Begin Class AAURACRONPCGEnergyPulse Function MulticastTriggerPulse *******************
struct AURACRONPCGEnergyPulse_eventMulticastTriggerPulse_Parms
{
	float Duration;
	float Intensity;
};
static FName NAME_AAURACRONPCGEnergyPulse_MulticastTriggerPulse = FName(TEXT("MulticastTriggerPulse"));
void AAURACRONPCGEnergyPulse::MulticastTriggerPulse(float Duration, float Intensity)
{
	AURACRONPCGEnergyPulse_eventMulticastTriggerPulse_Parms Parms;
	Parms.Duration=Duration;
	Parms.Intensity=Intensity;
	UFunction* Func = FindFunctionChecked(NAME_AAURACRONPCGEnergyPulse_MulticastTriggerPulse);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AAURACRONPCGEnergyPulse_MulticastTriggerPulse_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_MulticastTriggerPulse_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventMulticastTriggerPulse_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_MulticastTriggerPulse_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventMulticastTriggerPulse_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnergyPulse_MulticastTriggerPulse_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_MulticastTriggerPulse_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_MulticastTriggerPulse_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_MulticastTriggerPulse_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_MulticastTriggerPulse_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnergyPulse, nullptr, "MulticastTriggerPulse", Z_Construct_UFunction_AAURACRONPCGEnergyPulse_MulticastTriggerPulse_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_MulticastTriggerPulse_Statics::PropPointers), sizeof(AURACRONPCGEnergyPulse_eventMulticastTriggerPulse_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00044CC1, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_MulticastTriggerPulse_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnergyPulse_MulticastTriggerPulse_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(AURACRONPCGEnergyPulse_eventMulticastTriggerPulse_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnergyPulse_MulticastTriggerPulse()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnergyPulse_MulticastTriggerPulse_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnergyPulse::execMulticastTriggerPulse)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->MulticastTriggerPulse_Implementation(Z_Param_Duration,Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnergyPulse Function MulticastTriggerPulse *********************

// ********** Begin Class AAURACRONPCGEnergyPulse Function OnPlayerEnterPulseRadius ****************
struct Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics
{
	struct AURACRONPCGEnergyPulse_eventOnPlayerEnterPulseRadius_Parms
	{
		UPrimitiveComponent* OverlappedComponent;
		AActor* OtherActor;
		UPrimitiveComponent* OtherComp;
		int32 OtherBodyIndex;
		bool bFromSweep;
		FHitResult SweepResult;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Callback quando jogador entra no raio do pulso */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Callback quando jogador entra no raio do pulso" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverlappedComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OtherComp_MetaData[] = {
		{ "EditInline", "true" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SweepResult_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OverlappedComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherComp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OtherBodyIndex;
	static void NewProp_bFromSweep_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bFromSweep;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SweepResult;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::NewProp_OverlappedComponent = { "OverlappedComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventOnPlayerEnterPulseRadius_Parms, OverlappedComponent), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverlappedComponent_MetaData), NewProp_OverlappedComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::NewProp_OtherActor = { "OtherActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventOnPlayerEnterPulseRadius_Parms, OtherActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::NewProp_OtherComp = { "OtherComp", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventOnPlayerEnterPulseRadius_Parms, OtherComp), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OtherComp_MetaData), NewProp_OtherComp_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::NewProp_OtherBodyIndex = { "OtherBodyIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventOnPlayerEnterPulseRadius_Parms, OtherBodyIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::NewProp_bFromSweep_SetBit(void* Obj)
{
	((AURACRONPCGEnergyPulse_eventOnPlayerEnterPulseRadius_Parms*)Obj)->bFromSweep = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::NewProp_bFromSweep = { "bFromSweep", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGEnergyPulse_eventOnPlayerEnterPulseRadius_Parms), &Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::NewProp_bFromSweep_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::NewProp_SweepResult = { "SweepResult", nullptr, (EPropertyFlags)0x0010008008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventOnPlayerEnterPulseRadius_Parms, SweepResult), Z_Construct_UScriptStruct_FHitResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SweepResult_MetaData), NewProp_SweepResult_MetaData) }; // 267591329
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::NewProp_OverlappedComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::NewProp_OtherActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::NewProp_OtherComp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::NewProp_OtherBodyIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::NewProp_bFromSweep,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::NewProp_SweepResult,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnergyPulse, nullptr, "OnPlayerEnterPulseRadius", Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::AURACRONPCGEnergyPulse_eventOnPlayerEnterPulseRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00440401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::AURACRONPCGEnergyPulse_eventOnPlayerEnterPulseRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnergyPulse::execOnPlayerEnterPulseRadius)
{
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OverlappedComponent);
	P_GET_OBJECT(AActor,Z_Param_OtherActor);
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_OtherComp);
	P_GET_PROPERTY(FIntProperty,Z_Param_OtherBodyIndex);
	P_GET_UBOOL(Z_Param_bFromSweep);
	P_GET_STRUCT_REF(FHitResult,Z_Param_Out_SweepResult);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnPlayerEnterPulseRadius(Z_Param_OverlappedComponent,Z_Param_OtherActor,Z_Param_OtherComp,Z_Param_OtherBodyIndex,Z_Param_bFromSweep,Z_Param_Out_SweepResult);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnergyPulse Function OnPlayerEnterPulseRadius ******************

// ********** Begin Class AAURACRONPCGEnergyPulse Function SetPulseDuration ************************
struct Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseDuration_Statics
{
	struct AURACRONPCGEnergyPulse_eventSetPulseDuration_Parms
	{
		float Duration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configurar tempo de vida do pulso */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar tempo de vida do pulso" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseDuration_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventSetPulseDuration_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseDuration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseDuration_Statics::NewProp_Duration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseDuration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseDuration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnergyPulse, nullptr, "SetPulseDuration", Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseDuration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseDuration_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseDuration_Statics::AURACRONPCGEnergyPulse_eventSetPulseDuration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseDuration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseDuration_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseDuration_Statics::AURACRONPCGEnergyPulse_eventSetPulseDuration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseDuration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseDuration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnergyPulse::execSetPulseDuration)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPulseDuration(Z_Param_Duration);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnergyPulse Function SetPulseDuration **************************

// ********** Begin Class AAURACRONPCGEnergyPulse Function SetPulseIntensity ***********************
struct Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseIntensity_Statics
{
	struct AURACRONPCGEnergyPulse_eventSetPulseIntensity_Parms
	{
		float Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configurar intensidade do pulso */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar intensidade do pulso" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseIntensity_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventSetPulseIntensity_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseIntensity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseIntensity_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseIntensity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseIntensity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnergyPulse, nullptr, "SetPulseIntensity", Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseIntensity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseIntensity_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseIntensity_Statics::AURACRONPCGEnergyPulse_eventSetPulseIntensity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseIntensity_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseIntensity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseIntensity_Statics::AURACRONPCGEnergyPulse_eventSetPulseIntensity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseIntensity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseIntensity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnergyPulse::execSetPulseIntensity)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPulseIntensity(Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnergyPulse Function SetPulseIntensity *************************

// ********** Begin Class AAURACRONPCGEnergyPulse Function SetPulseRadius **************************
struct Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseRadius_Statics
{
	struct AURACRONPCGEnergyPulse_eventSetPulseRadius_Parms
	{
		float Radius;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configurar raio do pulso */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar raio do pulso" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseRadius_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventSetPulseRadius_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseRadius_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseRadius_Statics::NewProp_Radius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseRadius_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseRadius_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnergyPulse, nullptr, "SetPulseRadius", Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseRadius_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseRadius_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseRadius_Statics::AURACRONPCGEnergyPulse_eventSetPulseRadius_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseRadius_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseRadius_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseRadius_Statics::AURACRONPCGEnergyPulse_eventSetPulseRadius_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseRadius()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseRadius_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnergyPulse::execSetPulseRadius)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPulseRadius(Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnergyPulse Function SetPulseRadius ****************************

// ********** Begin Class AAURACRONPCGEnergyPulse Function SetQualityScale *************************
struct Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetQualityScale_Statics
{
	struct AURACRONPCGEnergyPulse_eventSetQualityScale_Parms
	{
		float NewQualityScale;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configurar escala de qualidade (para ajuste de performance) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar escala de qualidade (para ajuste de performance)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewQualityScale;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetQualityScale_Statics::NewProp_NewQualityScale = { "NewQualityScale", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventSetQualityScale_Parms, NewQualityScale), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetQualityScale_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetQualityScale_Statics::NewProp_NewQualityScale,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetQualityScale_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetQualityScale_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnergyPulse, nullptr, "SetQualityScale", Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetQualityScale_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetQualityScale_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetQualityScale_Statics::AURACRONPCGEnergyPulse_eventSetQualityScale_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetQualityScale_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetQualityScale_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetQualityScale_Statics::AURACRONPCGEnergyPulse_eventSetQualityScale_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetQualityScale()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetQualityScale_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnergyPulse::execSetQualityScale)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewQualityScale);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetQualityScale(Z_Param_NewQualityScale);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnergyPulse Function SetQualityScale ***************************

// ********** Begin Class AAURACRONPCGEnergyPulse Function TriggerPulse ****************************
struct Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse_Statics
{
	struct AURACRONPCGEnergyPulse_eventTriggerPulse_Parms
	{
		float Duration;
		float Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Disparar pulso com dura\xc3\xa7\xc3\xa3o e intensidade espec\xc3\xad""ficas */" },
#endif
		{ "CPP_Default_Duration", "0.000000" },
		{ "CPP_Default_Intensity", "1.000000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Disparar pulso com dura\xc3\xa7\xc3\xa3o e intensidade espec\xc3\xad""ficas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventTriggerPulse_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventTriggerPulse_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnergyPulse, nullptr, "TriggerPulse", Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse_Statics::AURACRONPCGEnergyPulse_eventTriggerPulse_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse_Statics::AURACRONPCGEnergyPulse_eventTriggerPulse_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnergyPulse::execTriggerPulse)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->TriggerPulse(Z_Param_Duration,Z_Param_Intensity);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnergyPulse Function TriggerPulse ******************************

// ********** Begin Class AAURACRONPCGEnergyPulse Function UpdateForMapPhase ***********************
struct Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase_Statics
{
	struct AURACRONPCGEnergyPulse_eventUpdateForMapPhase_Parms
	{
		EAURACRONMapPhase MapPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualizar pulso para fase do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar pulso para fase do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MapPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase_Statics::NewProp_MapPhase = { "MapPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnergyPulse_eventUpdateForMapPhase_Parms, MapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 2541365769
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase_Statics::NewProp_MapPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnergyPulse, nullptr, "UpdateForMapPhase", Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase_Statics::AURACRONPCGEnergyPulse_eventUpdateForMapPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase_Statics::AURACRONPCGEnergyPulse_eventUpdateForMapPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnergyPulse::execUpdateForMapPhase)
{
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_MapPhase);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateForMapPhase(EAURACRONMapPhase(Z_Param_MapPhase));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnergyPulse Function UpdateForMapPhase *************************

// ********** Begin Class AAURACRONPCGEnergyPulse **************************************************
void AAURACRONPCGEnergyPulse::StaticRegisterNativesAAURACRONPCGEnergyPulse()
{
	UClass* Class = AAURACRONPCGEnergyPulse::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyPulseEffects", &AAURACRONPCGEnergyPulse::execApplyPulseEffects },
		{ "CreateEnergyPulseForPortalType", &AAURACRONPCGEnergyPulse::execCreateEnergyPulseForPortalType },
		{ "CreateGoldenEnergyPulse", &AAURACRONPCGEnergyPulse::execCreateGoldenEnergyPulse },
		{ "CreateLunarEnergyPulse", &AAURACRONPCGEnergyPulse::execCreateLunarEnergyPulse },
		{ "CreateSilverEnergyPulse", &AAURACRONPCGEnergyPulse::execCreateSilverEnergyPulse },
		{ "CreateSolarEnergyPulse", &AAURACRONPCGEnergyPulse::execCreateSolarEnergyPulse },
		{ "CreateVioletEnergyPulse", &AAURACRONPCGEnergyPulse::execCreateVioletEnergyPulse },
		{ "MulticastOnTerritorialControlChanged", &AAURACRONPCGEnergyPulse::execMulticastOnTerritorialControlChanged },
		{ "MulticastTriggerPulse", &AAURACRONPCGEnergyPulse::execMulticastTriggerPulse },
		{ "OnPlayerEnterPulseRadius", &AAURACRONPCGEnergyPulse::execOnPlayerEnterPulseRadius },
		{ "SetPulseDuration", &AAURACRONPCGEnergyPulse::execSetPulseDuration },
		{ "SetPulseIntensity", &AAURACRONPCGEnergyPulse::execSetPulseIntensity },
		{ "SetPulseRadius", &AAURACRONPCGEnergyPulse::execSetPulseRadius },
		{ "SetQualityScale", &AAURACRONPCGEnergyPulse::execSetQualityScale },
		{ "TriggerPulse", &AAURACRONPCGEnergyPulse::execTriggerPulse },
		{ "UpdateForMapPhase", &AAURACRONPCGEnergyPulse::execUpdateForMapPhase },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAURACRONPCGEnergyPulse;
UClass* AAURACRONPCGEnergyPulse::GetPrivateStaticClass()
{
	using TClass = AAURACRONPCGEnergyPulse;
	if (!Z_Registration_Info_UClass_AAURACRONPCGEnergyPulse.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONPCGEnergyPulse"),
			Z_Registration_Info_UClass_AAURACRONPCGEnergyPulse.InnerSingleton,
			StaticRegisterNativesAAURACRONPCGEnergyPulse,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGEnergyPulse.InnerSingleton;
}
UClass* Z_Construct_UClass_AAURACRONPCGEnergyPulse_NoRegister()
{
	return AAURACRONPCGEnergyPulse::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Ator que representa um pulso de energia no mapa\n * Implementa o efeito especial EnergyPulses para a Fase 4 (Resolu\xc3\xa7\xc3\xa3o)\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGEnergyPulse.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ator que representa um pulso de energia no mapa\nImplementa o efeito especial EnergyPulses para a Fase 4 (Resolu\xc3\xa7\xc3\xa3o)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PulseEffect_MetaData[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de efeito de part\xc3\xad""culas */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de efeito de part\xc3\xad""culas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PulseLight_MetaData[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de luz */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de luz" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PulseSound_MetaData[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de \xc3\xa1udio */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de \xc3\xa1udio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PulseSphere_MetaData[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de colis\xc3\xa3o para detec\xc3\xa7\xc3\xa3o */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de colis\xc3\xa3o para detec\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PulseRadius_MetaData[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio do pulso */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio do pulso" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PulseDuration_MetaData[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\xa7\xc3\xa3o do pulso (0 = \xc3\xbanico) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o do pulso (0 = \xc3\xbanico)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PulseIntensity_MetaData[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade do pulso */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade do pulso" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PulseColor_MetaData[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cor do pulso */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cor do pulso" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnergyType_MetaData[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de energia do pulso */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de energia do pulso" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExpansionSpeed_MetaData[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Velocidade de expans\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade de expans\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_QualityScale_MetaData[] = {
		{ "Category", "AURACRON|EnergyPulse" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Escala de qualidade (para ajuste de performance) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escala de qualidade (para ajuste de performance)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentMapPhase_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Fase atual do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fase atual do mapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPulseActive_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se o pulso est\xc3\xa1 ativo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se o pulso est\xc3\xa1 ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentRadius_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio atual do pulso */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio atual do pulso" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GoldenEnergyNiagaraSystem_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sistemas Niagara carregados assincronamente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistemas Niagara carregados assincronamente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChaosEnergyNiagaraSystem_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VoidEnergyNiagaraSystem_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenericEnergyNiagaraSystem_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GoldenEnvironmentNiagaraSystem_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChaosEnvironmentNiagaraSystem_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GoldenEnergyGameplayEffect_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** GameplayEffects para integra\xc3\xa7\xc3\xa3o com AbilitySystem */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "GameplayEffects para integra\xc3\xa7\xc3\xa3o com AbilitySystem" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChaosEnergyGameplayEffect_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VoidEnergyGameplayEffect_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenericEnergyGameplayEffect_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ControllingTeam_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sistema de territorialidade */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de territorialidade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TerritorialInfluence_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnergyPulse.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PulseEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PulseLight;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PulseSound;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PulseSphere;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PulseRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PulseDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PulseIntensity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PulseColor;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EnergyType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EnergyType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExpansionSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_QualityScale;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentMapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentMapPhase;
	static void NewProp_bPulseActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPulseActive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentRadius;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_GoldenEnergyNiagaraSystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ChaosEnergyNiagaraSystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_VoidEnergyNiagaraSystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_GenericEnergyNiagaraSystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_GoldenEnvironmentNiagaraSystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ChaosEnvironmentNiagaraSystem;
	static const UECodeGen_Private::FClassPropertyParams NewProp_GoldenEnergyGameplayEffect;
	static const UECodeGen_Private::FClassPropertyParams NewProp_ChaosEnergyGameplayEffect;
	static const UECodeGen_Private::FClassPropertyParams NewProp_VoidEnergyGameplayEffect;
	static const UECodeGen_Private::FClassPropertyParams NewProp_GenericEnergyGameplayEffect;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ControllingTeam;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TerritorialInfluence;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAURACRONPCGEnergyPulse_ApplyPulseEffects, "ApplyPulseEffects" }, // 4148912496
		{ &Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateEnergyPulseForPortalType, "CreateEnergyPulseForPortalType" }, // 3677538082
		{ &Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateGoldenEnergyPulse, "CreateGoldenEnergyPulse" }, // 1726974687
		{ &Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateLunarEnergyPulse, "CreateLunarEnergyPulse" }, // 3544767100
		{ &Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSilverEnergyPulse, "CreateSilverEnergyPulse" }, // 265098458
		{ &Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateSolarEnergyPulse, "CreateSolarEnergyPulse" }, // 3294845339
		{ &Z_Construct_UFunction_AAURACRONPCGEnergyPulse_CreateVioletEnergyPulse, "CreateVioletEnergyPulse" }, // 342310940
		{ &Z_Construct_UFunction_AAURACRONPCGEnergyPulse_MulticastOnTerritorialControlChanged, "MulticastOnTerritorialControlChanged" }, // 2892597945
		{ &Z_Construct_UFunction_AAURACRONPCGEnergyPulse_MulticastTriggerPulse, "MulticastTriggerPulse" }, // 2071134949
		{ &Z_Construct_UFunction_AAURACRONPCGEnergyPulse_OnPlayerEnterPulseRadius, "OnPlayerEnterPulseRadius" }, // 1204967059
		{ &Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseDuration, "SetPulseDuration" }, // 4070704072
		{ &Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseIntensity, "SetPulseIntensity" }, // 1922932354
		{ &Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetPulseRadius, "SetPulseRadius" }, // 3422307786
		{ &Z_Construct_UFunction_AAURACRONPCGEnergyPulse_SetQualityScale, "SetQualityScale" }, // 3912224943
		{ &Z_Construct_UFunction_AAURACRONPCGEnergyPulse_TriggerPulse, "TriggerPulse" }, // 155446430
		{ &Z_Construct_UFunction_AAURACRONPCGEnergyPulse_UpdateForMapPhase, "UpdateForMapPhase" }, // 374909575
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAURACRONPCGEnergyPulse>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseEffect = { "PulseEffect", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, PulseEffect), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PulseEffect_MetaData), NewProp_PulseEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseLight = { "PulseLight", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, PulseLight), Z_Construct_UClass_UPointLightComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PulseLight_MetaData), NewProp_PulseLight_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseSound = { "PulseSound", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, PulseSound), Z_Construct_UClass_UAudioComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PulseSound_MetaData), NewProp_PulseSound_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseSphere = { "PulseSphere", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, PulseSphere), Z_Construct_UClass_USphereComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PulseSphere_MetaData), NewProp_PulseSphere_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseRadius = { "PulseRadius", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, PulseRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PulseRadius_MetaData), NewProp_PulseRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseDuration = { "PulseDuration", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, PulseDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PulseDuration_MetaData), NewProp_PulseDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseIntensity = { "PulseIntensity", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, PulseIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PulseIntensity_MetaData), NewProp_PulseIntensity_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseColor = { "PulseColor", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, PulseColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PulseColor_MetaData), NewProp_PulseColor_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_EnergyType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_EnergyType = { "EnergyType", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, EnergyType), Z_Construct_UEnum_AURACRON_EAURACRONEnergyType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnergyType_MetaData), NewProp_EnergyType_MetaData) }; // 81350420
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_ExpansionSpeed = { "ExpansionSpeed", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, ExpansionSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExpansionSpeed_MetaData), NewProp_ExpansionSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_QualityScale = { "QualityScale", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, QualityScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_QualityScale_MetaData), NewProp_QualityScale_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_CurrentMapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_CurrentMapPhase = { "CurrentMapPhase", nullptr, (EPropertyFlags)0x0020080000000020, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, CurrentMapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentMapPhase_MetaData), NewProp_CurrentMapPhase_MetaData) }; // 2541365769
void Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_bPulseActive_SetBit(void* Obj)
{
	((AAURACRONPCGEnergyPulse*)Obj)->bPulseActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_bPulseActive = { "bPulseActive", nullptr, (EPropertyFlags)0x0040000000000020, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnergyPulse), &Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_bPulseActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPulseActive_MetaData), NewProp_bPulseActive_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_CurrentRadius = { "CurrentRadius", nullptr, (EPropertyFlags)0x0040000000000020, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, CurrentRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentRadius_MetaData), NewProp_CurrentRadius_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_GoldenEnergyNiagaraSystem = { "GoldenEnergyNiagaraSystem", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, GoldenEnergyNiagaraSystem), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GoldenEnergyNiagaraSystem_MetaData), NewProp_GoldenEnergyNiagaraSystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_ChaosEnergyNiagaraSystem = { "ChaosEnergyNiagaraSystem", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, ChaosEnergyNiagaraSystem), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChaosEnergyNiagaraSystem_MetaData), NewProp_ChaosEnergyNiagaraSystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_VoidEnergyNiagaraSystem = { "VoidEnergyNiagaraSystem", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, VoidEnergyNiagaraSystem), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VoidEnergyNiagaraSystem_MetaData), NewProp_VoidEnergyNiagaraSystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_GenericEnergyNiagaraSystem = { "GenericEnergyNiagaraSystem", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, GenericEnergyNiagaraSystem), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenericEnergyNiagaraSystem_MetaData), NewProp_GenericEnergyNiagaraSystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_GoldenEnvironmentNiagaraSystem = { "GoldenEnvironmentNiagaraSystem", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, GoldenEnvironmentNiagaraSystem), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GoldenEnvironmentNiagaraSystem_MetaData), NewProp_GoldenEnvironmentNiagaraSystem_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_ChaosEnvironmentNiagaraSystem = { "ChaosEnvironmentNiagaraSystem", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, ChaosEnvironmentNiagaraSystem), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChaosEnvironmentNiagaraSystem_MetaData), NewProp_ChaosEnvironmentNiagaraSystem_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_GoldenEnergyGameplayEffect = { "GoldenEnergyGameplayEffect", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, GoldenEnergyGameplayEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GoldenEnergyGameplayEffect_MetaData), NewProp_GoldenEnergyGameplayEffect_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_ChaosEnergyGameplayEffect = { "ChaosEnergyGameplayEffect", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, ChaosEnergyGameplayEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChaosEnergyGameplayEffect_MetaData), NewProp_ChaosEnergyGameplayEffect_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_VoidEnergyGameplayEffect = { "VoidEnergyGameplayEffect", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, VoidEnergyGameplayEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VoidEnergyGameplayEffect_MetaData), NewProp_VoidEnergyGameplayEffect_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_GenericEnergyGameplayEffect = { "GenericEnergyGameplayEffect", nullptr, (EPropertyFlags)0x0044000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, GenericEnergyGameplayEffect), Z_Construct_UClass_UClass, Z_Construct_UClass_UGameplayEffect_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenericEnergyGameplayEffect_MetaData), NewProp_GenericEnergyGameplayEffect_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_ControllingTeam = { "ControllingTeam", nullptr, (EPropertyFlags)0x0040000000000020, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, ControllingTeam), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ControllingTeam_MetaData), NewProp_ControllingTeam_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_TerritorialInfluence = { "TerritorialInfluence", nullptr, (EPropertyFlags)0x0040000000000020, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnergyPulse, TerritorialInfluence), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TerritorialInfluence_MetaData), NewProp_TerritorialInfluence_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseLight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseSphere,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_PulseColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_EnergyType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_EnergyType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_ExpansionSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_QualityScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_CurrentMapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_CurrentMapPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_bPulseActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_CurrentRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_GoldenEnergyNiagaraSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_ChaosEnergyNiagaraSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_VoidEnergyNiagaraSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_GenericEnergyNiagaraSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_GoldenEnvironmentNiagaraSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_ChaosEnvironmentNiagaraSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_GoldenEnergyGameplayEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_ChaosEnergyGameplayEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_VoidEnergyGameplayEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_GenericEnergyGameplayEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_ControllingTeam,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::NewProp_TerritorialInfluence,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::ClassParams = {
	&AAURACRONPCGEnergyPulse::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::Class_MetaDataParams), Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAURACRONPCGEnergyPulse()
{
	if (!Z_Registration_Info_UClass_AAURACRONPCGEnergyPulse.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAURACRONPCGEnergyPulse.OuterSingleton, Z_Construct_UClass_AAURACRONPCGEnergyPulse_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGEnergyPulse.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void AAURACRONPCGEnergyPulse::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_CurrentMapPhase(TEXT("CurrentMapPhase"));
	static FName Name_bPulseActive(TEXT("bPulseActive"));
	static FName Name_CurrentRadius(TEXT("CurrentRadius"));
	static FName Name_ControllingTeam(TEXT("ControllingTeam"));
	static FName Name_TerritorialInfluence(TEXT("TerritorialInfluence"));
	const bool bIsValid = true
		&& Name_CurrentMapPhase == ClassReps[(int32)ENetFields_Private::CurrentMapPhase].Property->GetFName()
		&& Name_bPulseActive == ClassReps[(int32)ENetFields_Private::bPulseActive].Property->GetFName()
		&& Name_CurrentRadius == ClassReps[(int32)ENetFields_Private::CurrentRadius].Property->GetFName()
		&& Name_ControllingTeam == ClassReps[(int32)ENetFields_Private::ControllingTeam].Property->GetFName()
		&& Name_TerritorialInfluence == ClassReps[(int32)ENetFields_Private::TerritorialInfluence].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in AAURACRONPCGEnergyPulse"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(AAURACRONPCGEnergyPulse);
AAURACRONPCGEnergyPulse::~AAURACRONPCGEnergyPulse() {}
// ********** End Class AAURACRONPCGEnergyPulse ****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnergyPulse_h__Script_AURACRON_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAURACRONPCGEnergyPulse, AAURACRONPCGEnergyPulse::StaticClass, TEXT("AAURACRONPCGEnergyPulse"), &Z_Registration_Info_UClass_AAURACRONPCGEnergyPulse, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAURACRONPCGEnergyPulse), 1769488803U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnergyPulse_h__Script_AURACRON_3953403546(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnergyPulse_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnergyPulse_h__Script_AURACRON_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
