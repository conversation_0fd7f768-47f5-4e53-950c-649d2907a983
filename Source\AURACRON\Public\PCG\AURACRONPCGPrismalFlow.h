// AURACRONPCGPrismalFlow.h
// Sistema de Geração Procedural para AURACRON - UE 5.6
// Classe para gerenciar o Prismal Flow serpentino

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
// Forward declaration para evitar dependência direta
class UPCGComponent;
#include "PCGSettings.h"
#include "PCG/AURACRONPCGSubsystem.h"
#include "Components/SplineComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/BoxComponent.h"
#include "Components/SphereComponent.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "PCG/AURACRONPCGWorldPartitionIntegration.h"
#include "Data/AURACRONEnums.h"
#include "Data/AURACRONStructs.h"
#include "AURACRONPCGPrismalFlow.generated.h"

class UPCGComponent;
class USplineComponent;
class UNiagaraComponent;
struct FStreamableManager;

/**
 * Enumeração para definir os tipos de ilhas no Prismal Flow
 * Alinhado com EAURACRONIslandType em AURACRONPCGSubsystem.h
 */
UENUM(BlueprintType)
enum class EPrismalFlowIslandType : uint8
{
    None         UMETA(DisplayName = "None"),               // Nenhum tipo específico
    Nexus        UMETA(DisplayName = "Nexus Island"),       // Ilha central com poderes especiais
    Sanctuary    UMETA(DisplayName = "Sanctuary Island"),   // Ilha de refúgio e recuperação
    Arsenal      UMETA(DisplayName = "Arsenal Island"),     // Ilha com upgrades de armas e buffs temporários
    Chaos        UMETA(DisplayName = "Chaos Island"),       // Ilha com perigos ambientais e recompensas de alto risco
    Battlefield  UMETA(DisplayName = "Battlefield Island"), // Ilha para combates e confrontos
    Amplifier    UMETA(DisplayName = "Amplifier Island"),   // Ilha que amplifica poderes
    Gateway      UMETA(DisplayName = "Gateway Island"),     // Ilha que permite teleporte
    Corrupted    UMETA(DisplayName = "Corrupted Island")    // Ilha corrompida com efeitos negativos
};

// Usando FPrismalFlowSegment definido em Data/AURACRONStructs.h

/**
 * Classe base para todas as ilhas no Prismal Flow
 */
UCLASS()
class AURACRON_API APrismalFlowIsland : public AActor
{
    GENERATED_BODY()
    
public:
    APrismalFlowIsland();
    
    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;
    
    // Ativa/desativa a ilha
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Island")
    virtual void SetIslandActive(bool bActive);
    
    // Aplica efeito ao jogador quando está na ilha
    UFUNCTION()
    virtual void ApplyIslandEffect(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);
    
    // Configura o tipo de ilha
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Island")
    void SetIslandType(EPrismalFlowIslandType NewType);
    
    // Retorna o tipo de ilha
    UFUNCTION(BlueprintPure, Category = "Prismal Flow|Island")
    EPrismalFlowIslandType GetIslandType() const { return IslandType; }
    
    // Configura a posição da ilha no flow (0-1)
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Island")
    void SetFlowPosition(float InFlowPosition);
    
    // Retorna a posição da ilha no flow (0-1)
    UFUNCTION(BlueprintPure, Category = "Prismal Flow|Island")
    float GetFlowPosition() const { return FlowPosition; }

    // Estado de ativação da ilha
    UPROPERTY(BlueprintReadWrite, Category = "Prismal Flow|Island")
    bool bIsActive;

protected:
    // Malha da ilha
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Island")
    UStaticMeshComponent* IslandMesh;
    
    // Efeito visual da ilha
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Island")
    UNiagaraComponent* IslandEffect;
    
    // Área de interação
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Island")
    USphereComponent* InteractionArea;
    
    // Tipo de ilha
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Island")
    EPrismalFlowIslandType IslandType;
    
    // Posição no flow (0-1)
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Island")
    float FlowPosition;
    

    
    // Material dinâmico da ilha
    UPROPERTY()
    UMaterialInstanceDynamic* IslandMaterial;
    
    // Atualiza o visual da ilha baseado no tipo
    virtual void UpdateIslandVisuals();
};

// ANexusIsland está definida em AURACRONPCGNexusIsland.h

/**
 * Ator para gerenciar o Prismal Flow serpentino que conecta os três ambientes
 * O flow tem largura variável, curvas matemáticas precisas e ilhas estratégicas
 * Integra com World Partition e Data Layers para streaming eficiente
 */
UCLASS()
class AURACRON_API AAURACRONPCGPrismalFlow : public AActor
{
    GENERATED_BODY()

public:
    AAURACRONPCGPrismalFlow();

    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;

    // ========================================
    // FUNÇÕES PÚBLICAS DE CONFIGURAÇÃO
    // ========================================
    
    /** Gerar o Prismal Flow completo */
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow")
    void GeneratePrismalFlow();
    
    /** Atualizar o flow para uma fase específica do mapa */
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow")
    void UpdateForMapPhase(EAURACRONMapPhase MapPhase);
    
    /** Definir intensidade global do flow */
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow")
    void SetFlowIntensity(float NewIntensity);
    
    /** Obter posição no flow baseada em parâmetro T (0-1) */
    UFUNCTION(BlueprintPure, Category = "Prismal Flow")
    FVector GetFlowPositionAtT(float T) const;
    
    /** Obter largura do flow em uma posição específica */
    UFUNCTION(BlueprintPure, Category = "Prismal Flow")
    float GetFlowWidthAtT(float T) const;
    
    /** Verificar se uma posição está dentro do flow */
    UFUNCTION(BlueprintPure, Category = "Prismal Flow")
    bool IsPositionInFlow(const FVector& Position, float Tolerance = 100.0f) const;
    
    // Inicializar o sistema de fluxo prismal
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PrismalFlow")
    void InitializePrismalFlow();
    
    // Configurar para Fase 1: Despertar
    UFUNCTION(BlueprintCallable, Category = "PrismalFlow")
    void ConfigureForAwakeningPhase(bool bIsEntryDevice);
    
    // Configurar para Fase 2: Convergência (fortalecimento gradual)
    UFUNCTION(BlueprintCallable, Category = "PrismalFlow")
    void ConfigureForConvergencePhase(bool bIsEntryDevice, bool bIsMidDevice, bool bIsHighDevice);
    
    // Definir padrão predeterminado do fluxo prismal
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PrismalFlow")
    void SetPredeterminedPattern(bool bUsePredeterminedPattern);

    // Configurar qualidade dos efeitos
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|PrismalFlow")
    void SetEffectQuality(float EffectQuality);

    // ========================================
    // FUNÇÕES DE ILHAS ESTRATÉGICAS
    // ========================================
    
    /** Adicionar uma ilha estratégica em uma posição específica do flow */
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Islands")
    APrismalFlowIsland* AddIslandAtPosition(float FlowPosition, EPrismalFlowIslandType IslandType);
    
    /** Remover uma ilha específica */
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Islands")
    void RemoveIsland(APrismalFlowIsland* Island);
    
    /** Obter todas as ilhas do flow */
    UFUNCTION(BlueprintPure, Category = "Prismal Flow|Islands")
    TArray<APrismalFlowIsland*> GetAllIslands() const;
    
    /** Obter ilhas de um tipo específico */
    UFUNCTION(BlueprintPure, Category = "Prismal Flow|Islands")
    TArray<APrismalFlowIsland*> GetIslandsByType(EPrismalFlowIslandType IslandType) const;
    
    // ========================================
    // FUNÇÕES DE INTEGRAÇÃO COM WORLD PARTITION
    // ========================================
    
    /** Configurar streaming para o Prismal Flow */
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|WorldPartition")
    void ConfigureWorldPartitionStreaming(const FAURACRONPCGStreamingConfig& StreamingConfig);
    
    /** Associar o flow a uma Data Layer específica */
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|DataLayers")
    void AssociateWithDataLayer(const FName& DataLayerName);

    /** Obter pontos de controle do fluxo para navegação */
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Navigation")
    TArray<FVector> GetFlowControlPoints() const;


protected:
    // ========================================
    // COMPONENTES
    // ========================================
    
    /** Componente PCG principal */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Components")
    TObjectPtr<UPCGComponent> PCGComponent;
    
    /** Spline que define o caminho do flow */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Components")
    TObjectPtr<USplineComponent> FlowSpline;
    
    /** Componente de efeitos visuais principais */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Components")
    TObjectPtr<UNiagaraComponent> MainFlowEffect;

    /** Mesh do flow (alias para compatibilidade) */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Components")
    TObjectPtr<UStaticMeshComponent> FlowMesh;

    /** Efeito do flow (alias para compatibilidade) */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Components")
    TObjectPtr<UNiagaraComponent> FlowEffect;
    
    /** Componente de colisão para o flow */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Components")
    TObjectPtr<UBoxComponent> FlowCollision;

    // ========================================
    // PROPRIEDADES CONFIGURÁVEIS
    // ========================================
    
    /** Intensidade global do flow */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Settings", meta = (ClampMin = "0.0", ClampMax = "2.0"))
    float GlobalFlowIntensity;
    
    /** Velocidade base do flow */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Settings", meta = (ClampMin = "0.1", ClampMax = "5.0"))
    float BaseFlowSpeed;
    
    /** Equipe controladora atual do flow (0 = neutro, 1 = Equipe A, 2 = Equipe B) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Control")
    int32 ControllingTeam;
    
    /** Número de pontos de controle para a curva serpentina */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Settings", meta = (ClampMin = "10", ClampMax = "50"))
    int32 NumControlPoints;
    
    /** Amplitude das curvas serpentinas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Settings", meta = (ClampMin = "500.0", ClampMax = "3000.0"))
    float SerpentineAmplitude;
    
    /** Frequência das curvas serpentinas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Settings", meta = (ClampMin = "1.0", ClampMax = "6.0"))
    float SerpentineFrequency;

    /** Escala de atividade do flow */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Settings", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float ActivityScale;
    
    // ========================================
    // ILHAS ESTRATÉGICAS
    // ========================================
    
    /** Ilhas estratégicas no flow */
    UPROPERTY()
    TArray<APrismalFlowIsland*> Islands;
    
    /** Configurações para geração de ilhas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Islands")
    int32 MaxIslandCount;
    
    /** Distância mínima entre ilhas (em unidades de flow T 0-1) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Islands", meta = (ClampMin = "0.05", ClampMax = "0.5"))
    float MinIslandSpacing;
    
    /** Classes de ilhas para cada tipo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Islands")
    TMap<EPrismalFlowIslandType, TSubclassOf<APrismalFlowIsland>> IslandClasses;
    
    // ========================================
    // INTEGRAÇÃO COM WORLD PARTITION
    // ========================================
    
    /** Configuração de streaming para World Partition */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|WorldPartition")
    FAURACRONPCGStreamingConfig StreamingConfiguration;
    
    /** Nome da Data Layer associada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|DataLayers")
    FName AssociatedDataLayer;

private:
    // ========================================
    // DADOS INTERNOS
    // ========================================
    
    /** Segmentos do flow gerados */
    UPROPERTY()
    TArray<FPrismalFlowSegment> FlowSegments;
    
    /** Componentes gerados dinamicamente */
    UPROPERTY()
    TArray<UActorComponent*> GeneratedComponents;
    
    /** Fase atual do mapa */
    UPROPERTY()
    EAURACRONMapPhase CurrentMapPhase;
    
    /** Tempo acumulado para animações */
    UPROPERTY()
    float AccumulatedTime;
    
    /** Entradas de streaming para World Partition */
    UPROPERTY()
    TArray<FAURACRONPCGStreamingEntry> StreamingEntries;
    
    /** Material dinâmico para o flow */
    UPROPERTY()
    UMaterialInstanceDynamic* FlowMaterialInstance;

    /** Componentes de efeitos Niagara do flow */
    UPROPERTY()
    TArray<class UNiagaraComponent*> FlowEffects;

    /** Se deve usar padrão predeterminado */
    UPROPERTY()
    bool bUsePredeterminedPattern;

    /** Se streaming está habilitado */
    UPROPERTY()
    bool bStreamingEnabled;

    /** Distância de streaming */
    UPROPERTY()
    float StreamingDistance;

    /** Se efeitos visuais estão habilitados */
    UPROPERTY()
    bool bVisualEffectsEnabled;

    /** Ilhas estratégicas registradas */
    UPROPERTY()
    TArray<APrismalFlowIsland*> StrategicIslands;

    /** Componente Niagara principal do flow */
    UPROPERTY()
    UNiagaraComponent* FlowNiagaraComponent;

    // ========================================
    // FUNÇÕES INTERNAS
    // ========================================

    /** Função auxiliar para configurar parâmetros PCG usando API moderna UE 5.6 */
    void SetPCGParameterModern(const FString& ParameterName, const FVector& Value, const FString& Context = TEXT(""));

    /** Gerar pontos da curva serpentina */
    TArray<FVector> GenerateSerpentineCurve();
    
    /** Calcular largura do flow baseada na posição */
    float CalculateFlowWidth(float T, EAURACRONEnvironmentType EnvironmentType);
    
    /** Calcular intensidade do flow baseada na posição */
    float CalculateFlowIntensity(float T, EAURACRONMapPhase MapPhase);
    
    /** Gerar segmentos do flow */
    void GenerateFlowSegments();
    
    /** Gerar efeitos visuais */
    void GenerateVisualEffects();
    
    /** Gerar ilhas estratégicas ao longo do flow */
    void GenerateStrategicIslands();
    
    /** Limpar elementos gerados anteriormente */
    void ClearGeneratedElements();
    
    /** Atualizar efeitos dinâmicos */
    void UpdateDynamicEffects(float DeltaTime);
    
    /** Determinar tipo de ambiente baseado na posição */
    EAURACRONEnvironmentType DetermineEnvironmentType(const FVector& Position);
    
    /** Aplicar efeitos específicos do ambiente */
    void ApplyEnvironmentEffects(FPrismalFlowSegment& Segment);
    
    /** Calcular volatilidade do flow baseada na fase */
    float CalculateFlowVolatility(EAURACRONMapPhase MapPhase);
    
    /** Gerar pontos de interseção com trilhas */
    TArray<FVector> GenerateTrailIntersections();
    
    /** Criar efeitos de transição entre ambientes */
    void CreateEnvironmentTransitions();
    
    /** Atualizar parâmetros baseados no tempo */
    void UpdateTimeBasedParameters();
    
    /** Gerar obstáculos e características especiais */
    void GenerateFlowObstacles();
    
    /** Calcular direção do flow em uma posição */
    FVector CalculateFlowDirection(float T) const;
    
    /** Aplicar efeitos de fase do mapa */
    void ApplyMapPhaseEffects();

    /** Obter cor do ambiente */
    FLinearColor GetEnvironmentColor(EAURACRONEnvironmentType EnvironmentType);

    /** Obter raio da ilha baseado no tipo */
    float GetIslandRadius(EPrismalFlowIslandType IslandType);
    
    /** Configurar streaming para World Partition */
    void ConfigureWorldPartitionStreaming();
    

    
    /** Atualizar as ilhas estratégicas para a fase atual do mapa */
    void UpdateIslandsForMapPhase(EAURACRONMapPhase NewPhase);
    
    /** Atualizar os efeitos visuais para a fase atual do mapa */
    void UpdateVisualEffectsForMapPhase(EAURACRONMapPhase NewPhase);
    
    /** Atualizar a configuração de streaming para a fase atual do mapa */
    void UpdateStreamingForMapPhase(EAURACRONMapPhase NewPhase);
    
    /** Criar uma ilha estratégica de um tipo específico */
    APrismalFlowIsland* CreateIsland(EPrismalFlowIslandType IslandType, float FlowPosition);
    
    /** Verificar se uma posição no flow está disponível para uma ilha */
    bool IsFlowPositionAvailableForIsland(float FlowPosition, float MinSpacing = 0.05f) const;

public:
    /** Obter componente PCG para acesso externo */
    UFUNCTION(BlueprintPure, Category = "Prismal Flow|Components")
    UPCGComponent* GetPCGComponent() const { return PCGComponent; }

    /** Definir escala de atividade */
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Settings")
    void SetActivityScale(float NewActivityScale) { ActivityScale = NewActivityScale; }

    /** Replicação de propriedades */
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

    /** Callback quando flow é ativado */
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow")
    void OnFlowActivated();

    /** Callback quando flow é desativado */
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow")
    void OnFlowDeactivated();

    /** Atualizar flow para nova fase */
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow")
    void UpdateFlowPhase(EAURACRONMapPhase NewPhase);

    /** Obter cor do flow para fase */
    UFUNCTION(BlueprintPure, Category = "Prismal Flow")
    FLinearColor GetFlowColorForPhase(EAURACRONMapPhase Phase) const;

    /** Obter cor do flow baseada na equipe controladora */
    UFUNCTION(BlueprintPure, Category = "Prismal Flow|Control")
    FLinearColor GetFlowColorForTeam() const;
    
    /** Definir equipe controladora do flow */
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Control")
    void SetControllingTeam(int32 TeamID);
    
    /** Obter equipe controladora atual */
    UFUNCTION(BlueprintPure, Category = "Prismal Flow|Control")
    int32 GetControllingTeam() const { return ControllingTeam; }

    /** Gerar caminho do flow */
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow")
    void GenerateFlowPath();

    /** Aplicar efeitos visuais da fase */
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow")
    void ApplyPhaseVisualEffects(const FLinearColor& PhaseColor, float PhaseIntensity, const FString& EffectName);
    
    /** Obter a configuração de streaming atual */
    UFUNCTION(BlueprintPure, Category = "Prismal Flow|WorldPartition")
    FAURACRONPCGStreamingConfig GetStreamingConfiguration() const { return StreamingConfiguration; }
    
    /** Obter a Data Layer associada */
    UFUNCTION(BlueprintPure, Category = "Prismal Flow|DataLayers")
    FName GetAssociatedDataLayer() const { return AssociatedDataLayer; }
    
    /** Obter a fase atual do mapa */
    UFUNCTION(BlueprintPure, Category = "Prismal Flow")
    EAURACRONMapPhase GetCurrentMapPhase() const { return CurrentMapPhase; }
    
    // Funções públicas para configuração de streaming
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Streaming")
    void ConfigureStreamingSettings(const FAURACRONPCGStreamingConfig& StreamingConfig);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Streaming")
    void SetStreamingEnabled(bool bEnabled);

    // Funções para efeitos visuais
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Visual Effects")
    void SetVisualEffectsEnabled(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Visual Effects")
    void UpdateVisualEffectsForPhase(EAURACRONMapPhase Phase);

    // Funções para gerenciamento de ilhas
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Islands")
    void RegisterStrategicIsland(APrismalFlowIsland* Island);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Islands")
    void UnregisterStrategicIsland(APrismalFlowIsland* Island);

    // Funções para volatilidade adaptada ao hardware
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Hardware")
    void UpdateVolatilityForHardware();

    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Hardware")
    float GetHardwareVolatilityMultiplier() const;

    /** Aplicar contração do mapa ao Prismal Flow */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PrismalFlow")
    void OnMapContraction(float ContractionFactor);

    // ========================================
    // ✅ DECLARAÇÕES DAS NOVAS FUNÇÕES - UE 5.6
    // ========================================

    /** Destructor para limpeza adequada */
    virtual ~AAURACRONPCGPrismalFlow();

    /** EndPlay para limpeza de timers */
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

    // ✅ DECLARAÇÃO DUPLICADA REMOVIDA - GetFlowColorForTeam() já declarada na linha 489

    /** Funções de Timer otimizadas para UE 5.6 */
    UFUNCTION()
    void UpdateDynamicEffectsTimer();

    UFUNCTION()
    void UpdateTimeBasedParametersTimer();

    /** Sistema de streaming inteligente */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PrismalFlow|Streaming")
    void UpdateIntelligentStreaming(float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|PrismalFlow|Streaming")
    void PreloadAssetsForPosition(const FVector& Position);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|PrismalFlow|Streaming")
    void UnloadUnusedAssets();

    /** Validação anti-cheat server-side */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PrismalFlow|AntiCheat")
    void ValidateFlowIntegrityServerSide();

    /** Orçamento de partículas escalável */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PrismalFlow|Performance")
    void UpdateParticlesBudgetForHardware();

    UFUNCTION(BlueprintCallable, Category = "AURACRON|PrismalFlow|Performance")
    void ApplyParticlesBudgetToComponents();

    /** Integração com Trilhos dinâmicos */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PrismalFlow|Rails")
    void IntegrateWithDynamicRails();

    UFUNCTION(BlueprintCallable, Category = "AURACRON|PrismalFlow|Rails")
    void IntegrateWithSolarRail(AActor* SolarRail);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|PrismalFlow|Rails")
    void IntegrateWithAxisRail(AActor* AxisRail);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|PrismalFlow|Rails")
    void IntegrateWithLunarRail(AActor* LunarRail);

    /** Funções auxiliares para trilhos */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PrismalFlow|Rails")
    TArray<FVector> FindIntersectionPoints(AActor* RailActor);

    UFUNCTION(BlueprintPure, Category = "AURACRON|PrismalFlow|Rails")
    bool FindLineIntersection(const FVector& Line1Start, const FVector& Line1End,
                             const FVector& Line2Start, const FVector& Line2End,
                             FVector& OutIntersection);

    /** Efeitos de intersecção com trilhos */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PrismalFlow|Rails")
    void CreateSolarIntersectionEffect(const FVector& IntersectionPoint);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|PrismalFlow|Rails")
    void CreateAxisIntersectionEffect(const FVector& IntersectionPoint);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|PrismalFlow|Rails")
    void CreateLunarIntersectionEffect(const FVector& IntersectionPoint);

    /** Aplicação de efeitos dos trilhos */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PrismalFlow|Rails")
    void ApplySolarEnergyBoost(const FVector& IntersectionPoint);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|PrismalFlow|Rails")
    void ApplyAxisGravitationalStabilization(const FVector& IntersectionPoint);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|PrismalFlow|Rails")
    void ApplyLunarPhaseShift(const FVector& IntersectionPoint);

    /** Callbacks de streaming assíncrono */
    UFUNCTION()
    void OnEnvironmentAssetsLoaded(EAURACRONEnvironmentType EnvironmentType);

    UFUNCTION()
    void OnTransitionMaterialLoaded(UStaticMeshComponent* TargetComponent);

    /** Aplicação de assets de ambiente */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PrismalFlow|Environment")
    void ApplyEnvironmentAssetsToComponents(EAURACRONEnvironmentType EnvironmentType);

    /** RPCs para multiplayer */
    UFUNCTION(Server, Reliable, WithValidation, Category = "AURACRON|PrismalFlow|Multiplayer")
    void ServerUpdateFlowIntensity(float NewIntensity);

    UFUNCTION(NetMulticast, Reliable, Category = "AURACRON|PrismalFlow|Multiplayer")
    void MulticastUpdateFlowIntensity(float NewIntensity);

    UFUNCTION(Server, Reliable, WithValidation, Category = "AURACRON|PrismalFlow|Multiplayer")
    void ServerUpdateControllingTeam(int32 NewTeam);

    UFUNCTION(NetMulticast, Reliable, Category = "AURACRON|PrismalFlow|Multiplayer")
    void MulticastUpdateControllingTeam(int32 NewTeam, const FLinearColor& NewColor);

protected:
    // ========================================
    // ✅ PROPRIEDADES ADICIONAIS - UE 5.6
    // ========================================

    /** StreamableManager para carregamento assíncrono - UE 5.6: usar TSharedPtr */
    TSharedPtr<FStreamableManager> StreamableManager;

    /** Orçamentos de partículas por nível de hardware */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 ParticlesBudgetEntry;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 ParticlesBudgetMid;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 ParticlesBudgetHigh;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    int32 CurrentParticlesBudget;

    /** Controle de Timer otimizado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bTimerBasedUpdatesEnabled;

    /** Handles de Timer */
    UPROPERTY()
    FTimerHandle DynamicEffectsTimerHandle;

    UPROPERTY()
    FTimerHandle TimeBasedParametersTimerHandle;

    /** Handles de streaming ativo - UE 5.6: TSharedPtr não pode ser UPROPERTY */
    TArray<TSharedPtr<FStreamableHandle>> ActiveStreamingHandles;
};
