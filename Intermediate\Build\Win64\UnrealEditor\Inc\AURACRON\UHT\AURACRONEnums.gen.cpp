// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "AURACRONEnums.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONEnums() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONBuffType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONDeviceType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONEnergyType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONHardwareQuality();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONIslandSector();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONIslandType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONMapPhase();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONNetworkState();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONObjectiveCategory();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONPerformanceLevel();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONSigilType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONStreamingQualityProfile();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONTemporalEffectType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONTrailType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EEnvironmentBlurType();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAURACRONMapPhase *********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONMapPhase;
static UEnum* EAURACRONMapPhase_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONMapPhase.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONMapPhase.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONMapPhase"));
	}
	return Z_Registration_Info_UEnum_EAURACRONMapPhase.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONMapPhase>()
{
	return EAURACRONMapPhase_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONMapPhase_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Awakening.DisplayName", "Despertar" },
		{ "Awakening.Name", "EAURACRONMapPhase::Awakening" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Fases do mapa durante a partida - Sistema de Altern\xc3\xa2ncia de Mapas\n * Baseado na documenta\xc3\xa7\xc3\xa3o: FASE 1: DESPERTAR (0-15 min), FASE 2: CONVERG\xc3\x8aNCIA (15-25 min), etc.\n */" },
#endif
		{ "Convergence.Comment", "// 15-20 minutos (fase intermedi\xc3\xa1ria)\n" },
		{ "Convergence.DisplayName", "Converg\xc3\xaancia" },
		{ "Convergence.Name", "EAURACRONMapPhase::Convergence" },
		{ "Convergence.ToolTip", "15-20 minutos (fase intermedi\xc3\xa1ria)" },
		{ "Expansion.Comment", "// 0-15 minutos\n" },
		{ "Expansion.DisplayName", "Expans\xc3\xa3o" },
		{ "Expansion.Name", "EAURACRONMapPhase::Expansion" },
		{ "Expansion.ToolTip", "0-15 minutos" },
		{ "Intensification.Comment", "// 15-25 minutos\n" },
		{ "Intensification.DisplayName", "Intensifica\xc3\xa7\xc3\xa3o" },
		{ "Intensification.Name", "EAURACRONMapPhase::Intensification" },
		{ "Intensification.ToolTip", "15-25 minutos" },
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
		{ "Resolution.Comment", "// 25-35 minutos\n" },
		{ "Resolution.DisplayName", "Resolu\xc3\xa7\xc3\xa3o" },
		{ "Resolution.Name", "EAURACRONMapPhase::Resolution" },
		{ "Resolution.ToolTip", "25-35 minutos" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fases do mapa durante a partida - Sistema de Altern\xc3\xa2ncia de Mapas\nBaseado na documenta\xc3\xa7\xc3\xa3o: FASE 1: DESPERTAR (0-15 min), FASE 2: CONVERG\xc3\x8aNCIA (15-25 min), etc." },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONMapPhase::Awakening", (int64)EAURACRONMapPhase::Awakening },
		{ "EAURACRONMapPhase::Expansion", (int64)EAURACRONMapPhase::Expansion },
		{ "EAURACRONMapPhase::Convergence", (int64)EAURACRONMapPhase::Convergence },
		{ "EAURACRONMapPhase::Intensification", (int64)EAURACRONMapPhase::Intensification },
		{ "EAURACRONMapPhase::Resolution", (int64)EAURACRONMapPhase::Resolution },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONMapPhase_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONMapPhase",
	"EAURACRONMapPhase",
	Z_Construct_UEnum_AURACRON_EAURACRONMapPhase_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONMapPhase_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONMapPhase_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONMapPhase()
{
	if (!Z_Registration_Info_UEnum_EAURACRONMapPhase.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONMapPhase.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONMapPhase_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONMapPhase.InnerSingleton;
}
// ********** End Enum EAURACRONMapPhase ***********************************************************

// ********** Begin Enum EAURACRONEnvironmentType **************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONEnvironmentType;
static UEnum* EAURACRONEnvironmentType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONEnvironmentType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONEnvironmentType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONEnvironmentType"));
	}
	return Z_Registration_Info_UEnum_EAURACRONEnvironmentType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONEnvironmentType>()
{
	return EAURACRONEnvironmentType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "CelestialBasin.Comment", "// Nexus prism\xc3\xa1tico\n" },
		{ "CelestialBasin.DisplayName", "Bacia Celestial" },
		{ "CelestialBasin.Name", "EAURACRONEnvironmentType::CelestialBasin" },
		{ "CelestialBasin.ToolTip", "Nexus prism\xc3\xa1tico" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Tipos de ambiente no sistema de altern\xc3\xa2ncia de mapas\n * Baseado na documenta\xc3\xa7\xc3\xa3o: Plan\xc3\xad""cie Radiante, Firmamento Zephyr, Reino Purgat\xc3\xb3rio\n */" },
#endif
		{ "CrystalCaverns.Comment", "// Bacia celestial\n" },
		{ "CrystalCaverns.DisplayName", "Cavernas de Cristal" },
		{ "CrystalCaverns.Name", "EAURACRONEnvironmentType::CrystalCaverns" },
		{ "CrystalCaverns.ToolTip", "Bacia celestial" },
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
		{ "PrismaticNexus.Comment", "// Dimens\xc3\xa3o espelhada\n" },
		{ "PrismaticNexus.DisplayName", "Nexus Prism\xc3\xa1tico" },
		{ "PrismaticNexus.Name", "EAURACRONEnvironmentType::PrismaticNexus" },
		{ "PrismaticNexus.ToolTip", "Dimens\xc3\xa3o espelhada" },
		{ "PurgatoryRealm.Comment", "// Plataformas celestiais\n" },
		{ "PurgatoryRealm.DisplayName", "Reino Purgat\xc3\xb3rio" },
		{ "PurgatoryRealm.Name", "EAURACRONEnvironmentType::PurgatoryRealm" },
		{ "PurgatoryRealm.ToolTip", "Plataformas celestiais" },
		{ "RadiantPlains.DisplayName", "Plan\xc3\xad""cie Radiante" },
		{ "RadiantPlains.Name", "EAURACRONEnvironmentType::RadiantPlains" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipos de ambiente no sistema de altern\xc3\xa2ncia de mapas\nBaseado na documenta\xc3\xa7\xc3\xa3o: Plan\xc3\xad""cie Radiante, Firmamento Zephyr, Reino Purgat\xc3\xb3rio" },
#endif
		{ "ZephyrFirmament.Comment", "// Mapa base terrestre\n" },
		{ "ZephyrFirmament.DisplayName", "Firmamento Zephyr" },
		{ "ZephyrFirmament.Name", "EAURACRONEnvironmentType::ZephyrFirmament" },
		{ "ZephyrFirmament.ToolTip", "Mapa base terrestre" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONEnvironmentType::RadiantPlains", (int64)EAURACRONEnvironmentType::RadiantPlains },
		{ "EAURACRONEnvironmentType::ZephyrFirmament", (int64)EAURACRONEnvironmentType::ZephyrFirmament },
		{ "EAURACRONEnvironmentType::PurgatoryRealm", (int64)EAURACRONEnvironmentType::PurgatoryRealm },
		{ "EAURACRONEnvironmentType::PrismaticNexus", (int64)EAURACRONEnvironmentType::PrismaticNexus },
		{ "EAURACRONEnvironmentType::CelestialBasin", (int64)EAURACRONEnvironmentType::CelestialBasin },
		{ "EAURACRONEnvironmentType::CrystalCaverns", (int64)EAURACRONEnvironmentType::CrystalCaverns },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONEnvironmentType",
	"EAURACRONEnvironmentType",
	Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType()
{
	if (!Z_Registration_Info_UEnum_EAURACRONEnvironmentType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONEnvironmentType.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONEnvironmentType.InnerSingleton;
}
// ********** End Enum EAURACRONEnvironmentType ****************************************************

// ********** Begin Enum EAURACRONObjectiveType ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONObjectiveType;
static UEnum* EAURACRONObjectiveType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONObjectiveType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONObjectiveType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONObjectiveType"));
	}
	return Z_Registration_Info_UEnum_EAURACRONObjectiveType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONObjectiveType>()
{
	return EAURACRONObjectiveType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AncientRelic.Comment", "// N\xc3\xb3 de recursos\n" },
		{ "AncientRelic.DisplayName", "Rel\xc3\xadquia Antiga" },
		{ "AncientRelic.Name", "EAURACRONObjectiveType::AncientRelic" },
		{ "AncientRelic.ToolTip", "N\xc3\xb3 de recursos" },
		{ "ArsenalIsland.Comment", "// Ilha com santu\xc3\xa1rio\n" },
		{ "ArsenalIsland.DisplayName", "Ilha Arsenal" },
		{ "ArsenalIsland.Name", "EAURACRONObjectiveType::ArsenalIsland" },
		{ "ArsenalIsland.ToolTip", "Ilha com santu\xc3\xa1rio" },
		{ "BlueprintType", "true" },
		{ "CapturePoint.Comment", "// Ilha com energia ca\xc3\xb3tica\n" },
		{ "CapturePoint.DisplayName", "Ponto de Captura" },
		{ "CapturePoint.Name", "EAURACRONObjectiveType::CapturePoint" },
		{ "CapturePoint.ToolTip", "Ilha com energia ca\xc3\xb3tica" },
		{ "ChaosIsland.Comment", "// Ilha com arsenal\n" },
		{ "ChaosIsland.DisplayName", "Ilha do Caos" },
		{ "ChaosIsland.Name", "EAURACRONObjectiveType::ChaosIsland" },
		{ "ChaosIsland.ToolTip", "Ilha com arsenal" },
		{ "ChaosRift.Comment", "// Santu\xc3\xa1rio de energia sombria\n" },
		{ "ChaosRift.DisplayName", "Fenda do Caos" },
		{ "ChaosRift.Name", "EAURACRONObjectiveType::ChaosRift" },
		{ "ChaosRift.ToolTip", "Santu\xc3\xa1rio de energia sombria" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Tipos de objetivos procedurais\n * Sistema de gera\xc3\xa7\xc3\xa3o din\xc3\xa2mica baseado no estado da partida\n */" },
#endif
		{ "DefenseTower.Comment", "// Condutor de energia\n" },
		{ "DefenseTower.DisplayName", "Torre de Defesa" },
		{ "DefenseTower.Name", "EAURACRONObjectiveType::DefenseTower" },
		{ "DefenseTower.ToolTip", "Condutor de energia" },
		{ "EnergyConduit.Comment", "// Rel\xc3\xadquia antiga\n" },
		{ "EnergyConduit.DisplayName", "Condutor de Energia" },
		{ "EnergyConduit.Name", "EAURACRONObjectiveType::EnergyConduit" },
		{ "EnergyConduit.ToolTip", "Rel\xc3\xadquia antiga" },
		{ "EnvironmentAnchor.Comment", "// Permite rewind de 10 segundos\n" },
		{ "EnvironmentAnchor.DisplayName", "\xc3\x82ncora de Ambiente" },
		{ "EnvironmentAnchor.Name", "EAURACRONObjectiveType::EnvironmentAnchor" },
		{ "EnvironmentAnchor.ToolTip", "Permite rewind de 10 segundos" },
		{ "FragmentAuracron.Comment", "// Valor padr\xc3\xa3o/inv\xc3\xa1lido\n" },
		{ "FragmentAuracron.DisplayName", "Fragmento Auracron" },
		{ "FragmentAuracron.Name", "EAURACRONObjectiveType::FragmentAuracron" },
		{ "FragmentAuracron.ToolTip", "Valor padr\xc3\xa3o/inv\xc3\xa1lido" },
		{ "FusionCatalyst.Comment", "// Controla qual mapa est\xc3\xa1 ativo\n" },
		{ "FusionCatalyst.DisplayName", "Catalisador de Fus\xc3\xa3o" },
		{ "FusionCatalyst.Name", "EAURACRONObjectiveType::FusionCatalyst" },
		{ "FusionCatalyst.ToolTip", "Controla qual mapa est\xc3\xa1 ativo" },
		{ "MapAnchor.Comment", "// Controla ambiente ativo\n" },
		{ "MapAnchor.DisplayName", "\xc3\x82ncora de Mapa" },
		{ "MapAnchor.Name", "EAURACRONObjectiveType::MapAnchor" },
		{ "MapAnchor.ToolTip", "Controla ambiente ativo" },
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
		{ "NexusFragment.Comment", "// Mini-objetivos que constroem buff maior\n" },
		{ "NexusFragment.DisplayName", "Fragmento de Nexus" },
		{ "NexusFragment.Name", "EAURACRONObjectiveType::NexusFragment" },
		{ "NexusFragment.ToolTip", "Mini-objetivos que constroem buff maior" },
		{ "NexusIsland.Comment", "// \xc3\x82ncora de energia sombria\n" },
		{ "NexusIsland.DisplayName", "Ilha Nexus" },
		{ "NexusIsland.Name", "EAURACRONObjectiveType::NexusIsland" },
		{ "NexusIsland.ToolTip", "\xc3\x82ncora de energia sombria" },
		{ "None.DisplayName", "Nenhum" },
		{ "None.Name", "EAURACRONObjectiveType::None" },
		{ "PowerCore.Comment", "// Ponto de captura estrat\xc3\xa9gico\n" },
		{ "PowerCore.DisplayName", "N\xc3\xba""cleo de Energia" },
		{ "PowerCore.Name", "EAURACRONObjectiveType::PowerCore" },
		{ "PowerCore.ToolTip", "Ponto de captura estrat\xc3\xa9gico" },
		{ "PrismalGuardian.Comment", "// Ativa transi\xc3\xa7\xc3\xb5""es entre ambientes\n" },
		{ "PrismalGuardian.DisplayName", "Guardi\xc3\xa3o Prismal" },
		{ "PrismalGuardian.Name", "EAURACRONObjectiveType::PrismalGuardian" },
		{ "PrismalGuardian.ToolTip", "Ativa transi\xc3\xa7\xc3\xb5""es entre ambientes" },
		{ "PrismalNexus.Comment", "// Objetivo principal terrestre\n" },
		{ "PrismalNexus.DisplayName", "Nexus Prismal" },
		{ "PrismalNexus.Name", "EAURACRONObjectiveType::PrismalNexus" },
		{ "PrismalNexus.ToolTip", "Objetivo principal terrestre" },
		{ "PurgatoryAnchor.Comment", "// \xc3\x82ncora de energia e\xc3\xb3lica\n" },
		{ "PurgatoryAnchor.DisplayName", "\xc3\x82ncora do Purgat\xc3\xb3rio" },
		{ "PurgatoryAnchor.Name", "EAURACRONObjectiveType::PurgatoryAnchor" },
		{ "PurgatoryAnchor.ToolTip", "\xc3\x82ncora de energia e\xc3\xb3lica" },
		{ "PurgatoryShrine.Comment", "// Santu\xc3\xa1rio de energia e\xc3\xb3lica\n" },
		{ "PurgatoryShrine.DisplayName", "Santu\xc3\xa1rio do Purgat\xc3\xb3rio" },
		{ "PurgatoryShrine.Name", "EAURACRONObjectiveType::PurgatoryShrine" },
		{ "PurgatoryShrine.ToolTip", "Santu\xc3\xa1rio de energia e\xc3\xb3lica" },
		{ "RadiantAnchor.Comment", "// Fenda de energia ca\xc3\xb3tica\n" },
		{ "RadiantAnchor.DisplayName", "\xc3\x82ncora Radiante" },
		{ "RadiantAnchor.Name", "EAURACRONObjectiveType::RadiantAnchor" },
		{ "RadiantAnchor.ToolTip", "Fenda de energia ca\xc3\xb3tica" },
		{ "RadiantShrine.Comment", "// Objetivo especial do Reino Purgat\xc3\xb3rio\n" },
		{ "RadiantShrine.DisplayName", "Santu\xc3\xa1rio Radiante" },
		{ "RadiantShrine.Name", "EAURACRONObjectiveType::RadiantShrine" },
		{ "RadiantShrine.ToolTip", "Objetivo especial do Reino Purgat\xc3\xb3rio" },
		{ "ResourceNode.Comment", "// N\xc3\xba""cleo de energia\n" },
		{ "ResourceNode.DisplayName", "N\xc3\xb3 de Recursos" },
		{ "ResourceNode.Name", "EAURACRONObjectiveType::ResourceNode" },
		{ "ResourceNode.ToolTip", "N\xc3\xba""cleo de energia" },
		{ "SanctuaryIsland.Comment", "// Ilha com nexus de energia\n" },
		{ "SanctuaryIsland.DisplayName", "Ilha Santu\xc3\xa1rio" },
		{ "SanctuaryIsland.Name", "EAURACRONObjectiveType::SanctuaryIsland" },
		{ "SanctuaryIsland.ToolTip", "Ilha com nexus de energia" },
		{ "SpectralGuardian.Comment", "// Objetivo principal celestial\n" },
		{ "SpectralGuardian.DisplayName", "Guardi\xc3\xa3o Espectral" },
		{ "SpectralGuardian.Name", "EAURACRONObjectiveType::SpectralGuardian" },
		{ "SpectralGuardian.ToolTip", "Objetivo principal celestial" },
		{ "StormCore.Comment", "// Nexus de energia prism\xc3\xa1tica\n" },
		{ "StormCore.DisplayName", "N\xc3\xba""cleo de Tempestade" },
		{ "StormCore.Name", "EAURACRONObjectiveType::StormCore" },
		{ "StormCore.ToolTip", "Nexus de energia prism\xc3\xa1tica" },
		{ "TemporalRift.Comment", "// Fragmentos de nexus para construir buffs\n" },
		{ "TemporalRift.DisplayName", "Fenda Temporal" },
		{ "TemporalRift.Name", "EAURACRONObjectiveType::TemporalRift" },
		{ "TemporalRift.ToolTip", "Fragmentos de nexus para construir buffs" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipos de objetivos procedurais\nSistema de gera\xc3\xa7\xc3\xa3o din\xc3\xa2mica baseado no estado da partida" },
#endif
		{ "TransitionPortal.Comment", "// Reduz cooldown de S\xc3\xadgilos\n" },
		{ "TransitionPortal.DisplayName", "Portal de Transi\xc3\xa7\xc3\xa3o" },
		{ "TransitionPortal.Name", "EAURACRONObjectiveType::TransitionPortal" },
		{ "TransitionPortal.ToolTip", "Reduz cooldown de S\xc3\xadgilos" },
		{ "UmbraticLeviathan.Comment", "// Objetivo principal espectral\n" },
		{ "UmbraticLeviathan.DisplayName", "Leviat\xc3\xa3 Umbr\xc3\xa1tico" },
		{ "UmbraticLeviathan.Name", "EAURACRONObjectiveType::UmbraticLeviathan" },
		{ "UmbraticLeviathan.ToolTip", "Objetivo principal espectral" },
		{ "WindSanctuary.Comment", "// Santu\xc3\xa1rio de energia radiante\n" },
		{ "WindSanctuary.DisplayName", "Santu\xc3\xa1rio do Vento" },
		{ "WindSanctuary.Name", "EAURACRONObjectiveType::WindSanctuary" },
		{ "WindSanctuary.ToolTip", "Santu\xc3\xa1rio de energia radiante" },
		{ "ZephyrAnchor.Comment", "// \xc3\x82ncora de energia radiante\n" },
		{ "ZephyrAnchor.DisplayName", "\xc3\x82ncora Z\xc3\xa9""firo" },
		{ "ZephyrAnchor.Name", "EAURACRONObjectiveType::ZephyrAnchor" },
		{ "ZephyrAnchor.ToolTip", "\xc3\x82ncora de energia radiante" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONObjectiveType::None", (int64)EAURACRONObjectiveType::None },
		{ "EAURACRONObjectiveType::FragmentAuracron", (int64)EAURACRONObjectiveType::FragmentAuracron },
		{ "EAURACRONObjectiveType::NexusFragment", (int64)EAURACRONObjectiveType::NexusFragment },
		{ "EAURACRONObjectiveType::TemporalRift", (int64)EAURACRONObjectiveType::TemporalRift },
		{ "EAURACRONObjectiveType::EnvironmentAnchor", (int64)EAURACRONObjectiveType::EnvironmentAnchor },
		{ "EAURACRONObjectiveType::MapAnchor", (int64)EAURACRONObjectiveType::MapAnchor },
		{ "EAURACRONObjectiveType::FusionCatalyst", (int64)EAURACRONObjectiveType::FusionCatalyst },
		{ "EAURACRONObjectiveType::TransitionPortal", (int64)EAURACRONObjectiveType::TransitionPortal },
		{ "EAURACRONObjectiveType::PrismalGuardian", (int64)EAURACRONObjectiveType::PrismalGuardian },
		{ "EAURACRONObjectiveType::PrismalNexus", (int64)EAURACRONObjectiveType::PrismalNexus },
		{ "EAURACRONObjectiveType::StormCore", (int64)EAURACRONObjectiveType::StormCore },
		{ "EAURACRONObjectiveType::SpectralGuardian", (int64)EAURACRONObjectiveType::SpectralGuardian },
		{ "EAURACRONObjectiveType::UmbraticLeviathan", (int64)EAURACRONObjectiveType::UmbraticLeviathan },
		{ "EAURACRONObjectiveType::RadiantShrine", (int64)EAURACRONObjectiveType::RadiantShrine },
		{ "EAURACRONObjectiveType::WindSanctuary", (int64)EAURACRONObjectiveType::WindSanctuary },
		{ "EAURACRONObjectiveType::PurgatoryShrine", (int64)EAURACRONObjectiveType::PurgatoryShrine },
		{ "EAURACRONObjectiveType::ChaosRift", (int64)EAURACRONObjectiveType::ChaosRift },
		{ "EAURACRONObjectiveType::RadiantAnchor", (int64)EAURACRONObjectiveType::RadiantAnchor },
		{ "EAURACRONObjectiveType::ZephyrAnchor", (int64)EAURACRONObjectiveType::ZephyrAnchor },
		{ "EAURACRONObjectiveType::PurgatoryAnchor", (int64)EAURACRONObjectiveType::PurgatoryAnchor },
		{ "EAURACRONObjectiveType::NexusIsland", (int64)EAURACRONObjectiveType::NexusIsland },
		{ "EAURACRONObjectiveType::SanctuaryIsland", (int64)EAURACRONObjectiveType::SanctuaryIsland },
		{ "EAURACRONObjectiveType::ArsenalIsland", (int64)EAURACRONObjectiveType::ArsenalIsland },
		{ "EAURACRONObjectiveType::ChaosIsland", (int64)EAURACRONObjectiveType::ChaosIsland },
		{ "EAURACRONObjectiveType::CapturePoint", (int64)EAURACRONObjectiveType::CapturePoint },
		{ "EAURACRONObjectiveType::PowerCore", (int64)EAURACRONObjectiveType::PowerCore },
		{ "EAURACRONObjectiveType::ResourceNode", (int64)EAURACRONObjectiveType::ResourceNode },
		{ "EAURACRONObjectiveType::AncientRelic", (int64)EAURACRONObjectiveType::AncientRelic },
		{ "EAURACRONObjectiveType::EnergyConduit", (int64)EAURACRONObjectiveType::EnergyConduit },
		{ "EAURACRONObjectiveType::DefenseTower", (int64)EAURACRONObjectiveType::DefenseTower },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONObjectiveType",
	"EAURACRONObjectiveType",
	Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType()
{
	if (!Z_Registration_Info_UEnum_EAURACRONObjectiveType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONObjectiveType.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONObjectiveType.InnerSingleton;
}
// ********** End Enum EAURACRONObjectiveType ******************************************************

// ********** Begin Enum EAURACRONBuffType *********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONBuffType;
static UEnum* EAURACRONBuffType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONBuffType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONBuffType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONBuffType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONBuffType"));
	}
	return Z_Registration_Info_UEnum_EAURACRONBuffType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONBuffType>()
{
	return EAURACRONBuffType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONBuffType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Armor.DisplayName", "Armadura" },
		{ "Armor.Name", "EAURACRONBuffType::Armor" },
		{ "AttackSpeed.DisplayName", "Velocidade de Ataque" },
		{ "AttackSpeed.Name", "EAURACRONBuffType::AttackSpeed" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Tipos de buff aplicados por objetivos\n * Sistema robusto de buffs para equipes e \xc3\xa1reas\n */" },
#endif
		{ "CooldownReduction.DisplayName", "Redu\xc3\xa7\xc3\xa3o de Recarga" },
		{ "CooldownReduction.Name", "EAURACRONBuffType::CooldownReduction" },
		{ "CriticalChance.DisplayName", "Chance Cr\xc3\xadtica" },
		{ "CriticalChance.Name", "EAURACRONBuffType::CriticalChance" },
		{ "DamageBoost.DisplayName", "Aumento de Dano" },
		{ "DamageBoost.Name", "EAURACRONBuffType::DamageBoost" },
		{ "DefenseBoost.DisplayName", "Aumento de Defesa" },
		{ "DefenseBoost.Name", "EAURACRONBuffType::DefenseBoost" },
		{ "HealthRegeneration.DisplayName", "Regenera\xc3\xa7\xc3\xa3o de Vida" },
		{ "HealthRegeneration.Name", "EAURACRONBuffType::HealthRegeneration" },
		{ "Lifesteal.DisplayName", "Roubo de Vida" },
		{ "Lifesteal.Name", "EAURACRONBuffType::Lifesteal" },
		{ "MagicResistance.DisplayName", "Resist\xc3\xaancia M\xc3\xa1gica" },
		{ "MagicResistance.Name", "EAURACRONBuffType::MagicResistance" },
		{ "ManaRegeneration.DisplayName", "Regenera\xc3\xa7\xc3\xa3o de Mana" },
		{ "ManaRegeneration.Name", "EAURACRONBuffType::ManaRegeneration" },
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
		{ "MovementSpeed.DisplayName", "Velocidade de Movimento" },
		{ "MovementSpeed.Name", "EAURACRONBuffType::MovementSpeed" },
		{ "SpellPower.DisplayName", "Poder M\xc3\xa1gico" },
		{ "SpellPower.Name", "EAURACRONBuffType::SpellPower" },
		{ "Tenacity.DisplayName", "Tenacidade" },
		{ "Tenacity.Name", "EAURACRONBuffType::Tenacity" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipos de buff aplicados por objetivos\nSistema robusto de buffs para equipes e \xc3\xa1reas" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONBuffType::MovementSpeed", (int64)EAURACRONBuffType::MovementSpeed },
		{ "EAURACRONBuffType::DamageBoost", (int64)EAURACRONBuffType::DamageBoost },
		{ "EAURACRONBuffType::DefenseBoost", (int64)EAURACRONBuffType::DefenseBoost },
		{ "EAURACRONBuffType::CooldownReduction", (int64)EAURACRONBuffType::CooldownReduction },
		{ "EAURACRONBuffType::HealthRegeneration", (int64)EAURACRONBuffType::HealthRegeneration },
		{ "EAURACRONBuffType::ManaRegeneration", (int64)EAURACRONBuffType::ManaRegeneration },
		{ "EAURACRONBuffType::CriticalChance", (int64)EAURACRONBuffType::CriticalChance },
		{ "EAURACRONBuffType::AttackSpeed", (int64)EAURACRONBuffType::AttackSpeed },
		{ "EAURACRONBuffType::SpellPower", (int64)EAURACRONBuffType::SpellPower },
		{ "EAURACRONBuffType::Armor", (int64)EAURACRONBuffType::Armor },
		{ "EAURACRONBuffType::MagicResistance", (int64)EAURACRONBuffType::MagicResistance },
		{ "EAURACRONBuffType::Lifesteal", (int64)EAURACRONBuffType::Lifesteal },
		{ "EAURACRONBuffType::Tenacity", (int64)EAURACRONBuffType::Tenacity },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONBuffType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONBuffType",
	"EAURACRONBuffType",
	Z_Construct_UEnum_AURACRON_EAURACRONBuffType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONBuffType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONBuffType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONBuffType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONBuffType()
{
	if (!Z_Registration_Info_UEnum_EAURACRONBuffType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONBuffType.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONBuffType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONBuffType.InnerSingleton;
}
// ********** End Enum EAURACRONBuffType ***********************************************************

// ********** Begin Enum EAURACRONTemporalEffectType ***********************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONTemporalEffectType;
static UEnum* EAURACRONTemporalEffectType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONTemporalEffectType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONTemporalEffectType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONTemporalEffectType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONTemporalEffectType"));
	}
	return Z_Registration_Info_UEnum_EAURACRONTemporalEffectType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONTemporalEffectType>()
{
	return EAURACRONTemporalEffectType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONTemporalEffectType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Accelerate.Comment", "// Reduz velocidade temporal\n" },
		{ "Accelerate.DisplayName", "Acelera\xc3\xa7\xc3\xa3o" },
		{ "Accelerate.Name", "EAURACRONTemporalEffectType::Accelerate" },
		{ "Accelerate.ToolTip", "Reduz velocidade temporal" },
		{ "BlueprintType", "true" },
		{ "ChronoShield.Comment", "// Desacelera inimigos\n" },
		{ "ChronoShield.DisplayName", "Escudo Temporal" },
		{ "ChronoShield.Name", "EAURACRONTemporalEffectType::ChronoShield" },
		{ "ChronoShield.ToolTip", "Desacelera inimigos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Tipos de efeitos temporais\n * Sistema de manipula\xc3\xa7\xc3\xa3o temporal para Fendas Temporais\n */" },
#endif
		{ "Freeze.Comment", "// Aumenta velocidade temporal\n" },
		{ "Freeze.DisplayName", "Congelamento" },
		{ "Freeze.Name", "EAURACRONTemporalEffectType::Freeze" },
		{ "Freeze.ToolTip", "Aumenta velocidade temporal" },
		{ "Loop.Comment", "// Congela no tempo\n" },
		{ "Loop.DisplayName", "Loop" },
		{ "Loop.Name", "EAURACRONTemporalEffectType::Loop" },
		{ "Loop.ToolTip", "Congela no tempo" },
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
		{ "None.DisplayName", "Nenhum" },
		{ "None.Name", "EAURACRONTemporalEffectType::None" },
		{ "Rewind.Comment", "// Sem efeito temporal\n" },
		{ "Rewind.DisplayName", "Retrocesso" },
		{ "Rewind.Name", "EAURACRONTemporalEffectType::Rewind" },
		{ "Rewind.ToolTip", "Sem efeito temporal" },
		{ "Slow.Comment", "// Volta posi\xc3\xa7\xc3\xa3o/vida 10 segundos\n" },
		{ "Slow.DisplayName", "Lentid\xc3\xa3o" },
		{ "Slow.Name", "EAURACRONTemporalEffectType::Slow" },
		{ "Slow.ToolTip", "Volta posi\xc3\xa7\xc3\xa3o/vida 10 segundos" },
		{ "TemporalEcho.Comment", "// Imunidade temporal breve\n" },
		{ "TemporalEcho.DisplayName", "Eco Temporal" },
		{ "TemporalEcho.Name", "EAURACRONTemporalEffectType::TemporalEcho" },
		{ "TemporalEcho.ToolTip", "Imunidade temporal breve" },
		{ "TimeAcceleration.Comment", "// Cria loop temporal\n" },
		{ "TimeAcceleration.DisplayName", "Acelera\xc3\xa7\xc3\xa3o Temporal" },
		{ "TimeAcceleration.Name", "EAURACRONTemporalEffectType::TimeAcceleration" },
		{ "TimeAcceleration.ToolTip", "Cria loop temporal" },
		{ "TimeDeceleration.Comment", "// Acelera cooldowns\n" },
		{ "TimeDeceleration.DisplayName", "Desacelera\xc3\xa7\xc3\xa3o Temporal" },
		{ "TimeDeceleration.Name", "EAURACRONTemporalEffectType::TimeDeceleration" },
		{ "TimeDeceleration.ToolTip", "Acelera cooldowns" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipos de efeitos temporais\nSistema de manipula\xc3\xa7\xc3\xa3o temporal para Fendas Temporais" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONTemporalEffectType::None", (int64)EAURACRONTemporalEffectType::None },
		{ "EAURACRONTemporalEffectType::Rewind", (int64)EAURACRONTemporalEffectType::Rewind },
		{ "EAURACRONTemporalEffectType::Slow", (int64)EAURACRONTemporalEffectType::Slow },
		{ "EAURACRONTemporalEffectType::Accelerate", (int64)EAURACRONTemporalEffectType::Accelerate },
		{ "EAURACRONTemporalEffectType::Freeze", (int64)EAURACRONTemporalEffectType::Freeze },
		{ "EAURACRONTemporalEffectType::Loop", (int64)EAURACRONTemporalEffectType::Loop },
		{ "EAURACRONTemporalEffectType::TimeAcceleration", (int64)EAURACRONTemporalEffectType::TimeAcceleration },
		{ "EAURACRONTemporalEffectType::TimeDeceleration", (int64)EAURACRONTemporalEffectType::TimeDeceleration },
		{ "EAURACRONTemporalEffectType::ChronoShield", (int64)EAURACRONTemporalEffectType::ChronoShield },
		{ "EAURACRONTemporalEffectType::TemporalEcho", (int64)EAURACRONTemporalEffectType::TemporalEcho },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONTemporalEffectType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONTemporalEffectType",
	"EAURACRONTemporalEffectType",
	Z_Construct_UEnum_AURACRON_EAURACRONTemporalEffectType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONTemporalEffectType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONTemporalEffectType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONTemporalEffectType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONTemporalEffectType()
{
	if (!Z_Registration_Info_UEnum_EAURACRONTemporalEffectType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONTemporalEffectType.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONTemporalEffectType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONTemporalEffectType.InnerSingleton;
}
// ********** End Enum EAURACRONTemporalEffectType *************************************************

// ********** Begin Enum EAURACRONTrailType ********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONTrailType;
static UEnum* EAURACRONTrailType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONTrailType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONTrailType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONTrailType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONTrailType"));
	}
	return Z_Registration_Info_UEnum_EAURACRONTrailType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONTrailType>()
{
	return EAURACRONTrailType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONTrailType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Axis.Comment", "// Energia dourada, boost velocidade\n" },
		{ "Axis.DisplayName", "Trilho Axis" },
		{ "Axis.Name", "EAURACRONTrailType::Axis" },
		{ "Axis.ToolTip", "Energia dourada, boost velocidade" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Tipos de trilhos din\xc3\xa2micos\n * Sistema de trilhos que mudam baseado em condi\xc3\xa7\xc3\xb5""es\n */" },
#endif
		{ "EtherealPath.Comment", "// Trilho do fluxo prismal\n" },
		{ "EtherealPath.DisplayName", "Caminho Et\xc3\xa9reo" },
		{ "EtherealPath.Name", "EAURACRONTrailType::EtherealPath" },
		{ "EtherealPath.ToolTip", "Trilho do fluxo prismal" },
		{ "Lunar.Comment", "// Canais neutros, transi\xc3\xa7\xc3\xa3o instant\xc3\xa2nea\n" },
		{ "Lunar.DisplayName", "Trilho Lunar" },
		{ "Lunar.Name", "EAURACRONTrailType::Lunar" },
		{ "Lunar.ToolTip", "Canais neutros, transi\xc3\xa7\xc3\xa3o instant\xc3\xa2nea" },
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
		{ "NexusConnection.Comment", "// Trilho et\xc3\xa9reo\n" },
		{ "NexusConnection.DisplayName", "Conex\xc3\xa3o Nexus" },
		{ "NexusConnection.Name", "EAURACRONTrailType::NexusConnection" },
		{ "NexusConnection.ToolTip", "Trilho et\xc3\xa9reo" },
		{ "None.DisplayName", "Nenhum" },
		{ "None.Name", "EAURACRONTrailType::None" },
		{ "PrismalFlow.Comment", "// Caminhos et\xc3\xa9reos, furtividade noturna\n" },
		{ "PrismalFlow.DisplayName", "Fluxo Prismal" },
		{ "PrismalFlow.Name", "EAURACRONTrailType::PrismalFlow" },
		{ "PrismalFlow.ToolTip", "Caminhos et\xc3\xa9reos, furtividade noturna" },
		{ "Solar.Comment", "// Valor padr\xc3\xa3o/inv\xc3\xa1lido\n" },
		{ "Solar.DisplayName", "Trilho Solar" },
		{ "Solar.Name", "EAURACRONTrailType::Solar" },
		{ "Solar.ToolTip", "Valor padr\xc3\xa3o/inv\xc3\xa1lido" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipos de trilhos din\xc3\xa2micos\nSistema de trilhos que mudam baseado em condi\xc3\xa7\xc3\xb5""es" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONTrailType::None", (int64)EAURACRONTrailType::None },
		{ "EAURACRONTrailType::Solar", (int64)EAURACRONTrailType::Solar },
		{ "EAURACRONTrailType::Axis", (int64)EAURACRONTrailType::Axis },
		{ "EAURACRONTrailType::Lunar", (int64)EAURACRONTrailType::Lunar },
		{ "EAURACRONTrailType::PrismalFlow", (int64)EAURACRONTrailType::PrismalFlow },
		{ "EAURACRONTrailType::EtherealPath", (int64)EAURACRONTrailType::EtherealPath },
		{ "EAURACRONTrailType::NexusConnection", (int64)EAURACRONTrailType::NexusConnection },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONTrailType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONTrailType",
	"EAURACRONTrailType",
	Z_Construct_UEnum_AURACRON_EAURACRONTrailType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONTrailType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONTrailType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONTrailType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONTrailType()
{
	if (!Z_Registration_Info_UEnum_EAURACRONTrailType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONTrailType.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONTrailType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONTrailType.InnerSingleton;
}
// ********** End Enum EAURACRONTrailType **********************************************************

// ********** Begin Enum EAURACRONHardwareQuality **************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONHardwareQuality;
static UEnum* EAURACRONHardwareQuality_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONHardwareQuality.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONHardwareQuality.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONHardwareQuality, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONHardwareQuality"));
	}
	return Z_Registration_Info_UEnum_EAURACRONHardwareQuality.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONHardwareQuality>()
{
	return EAURACRONHardwareQuality_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONHardwareQuality_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estados de qualidade de hardware para otimiza\xc3\xa7\xc3\xa3o adaptativa\n * Sistema de detec\xc3\xa7\xc3\xa3o autom\xc3\xa1tica de performance\n */" },
#endif
		{ "Entry.DisplayName", "Entry Level" },
		{ "Entry.Name", "EAURACRONHardwareQuality::Entry" },
		{ "HighEnd.Comment", "// 3-4GB RAM, GPU intermedi\xc3\xa1ria  \n" },
		{ "HighEnd.DisplayName", "High End" },
		{ "HighEnd.Name", "EAURACRONHardwareQuality::HighEnd" },
		{ "HighEnd.ToolTip", "3-4GB RAM, GPU intermedi\xc3\xa1ria" },
		{ "MidRange.Comment", "// 2-3GB RAM, GPU b\xc3\xa1sica\n" },
		{ "MidRange.DisplayName", "Mid Range" },
		{ "MidRange.Name", "EAURACRONHardwareQuality::MidRange" },
		{ "MidRange.ToolTip", "2-3GB RAM, GPU b\xc3\xa1sica" },
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estados de qualidade de hardware para otimiza\xc3\xa7\xc3\xa3o adaptativa\nSistema de detec\xc3\xa7\xc3\xa3o autom\xc3\xa1tica de performance" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONHardwareQuality::Entry", (int64)EAURACRONHardwareQuality::Entry },
		{ "EAURACRONHardwareQuality::MidRange", (int64)EAURACRONHardwareQuality::MidRange },
		{ "EAURACRONHardwareQuality::HighEnd", (int64)EAURACRONHardwareQuality::HighEnd },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONHardwareQuality_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONHardwareQuality",
	"EAURACRONHardwareQuality",
	Z_Construct_UEnum_AURACRON_EAURACRONHardwareQuality_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONHardwareQuality_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONHardwareQuality_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONHardwareQuality_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONHardwareQuality()
{
	if (!Z_Registration_Info_UEnum_EAURACRONHardwareQuality.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONHardwareQuality.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONHardwareQuality_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONHardwareQuality.InnerSingleton;
}
// ********** End Enum EAURACRONHardwareQuality ****************************************************

// ********** Begin Enum EAURACRONSigilType ********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONSigilType;
static UEnum* EAURACRONSigilType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONSigilType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONSigilType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONSigilType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONSigilType"));
	}
	return Z_Registration_Info_UEnum_EAURACRONSigilType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONSigilType>()
{
	return EAURACRONSigilType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONSigilType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Aegis.DisplayName", "Aegis" },
		{ "Aegis.Name", "EAURACRONSigilType::Aegis" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Tipos de s\xc3\xadgilos do sistema de fus\xc3\xa3o\n * Sistema de S\xc3\xadgilos Auracron (Fusion 2.0)\n */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
		{ "Ruin.Comment", "// Tanque - +15% HP, Armadura adaptativa\n" },
		{ "Ruin.DisplayName", "Ruin" },
		{ "Ruin.Name", "EAURACRONSigilType::Ruin" },
		{ "Ruin.ToolTip", "Tanque - +15% HP, Armadura adaptativa" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipos de s\xc3\xadgilos do sistema de fus\xc3\xa3o\nSistema de S\xc3\xadgilos Auracron (Fusion 2.0)" },
#endif
		{ "Vesper.Comment", "// Dano - +12% ATK/AP adaptativo\n" },
		{ "Vesper.DisplayName", "Vesper" },
		{ "Vesper.Name", "EAURACRONSigilType::Vesper" },
		{ "Vesper.ToolTip", "Dano - +12% ATK/AP adaptativo" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONSigilType::Aegis", (int64)EAURACRONSigilType::Aegis },
		{ "EAURACRONSigilType::Ruin", (int64)EAURACRONSigilType::Ruin },
		{ "EAURACRONSigilType::Vesper", (int64)EAURACRONSigilType::Vesper },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONSigilType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONSigilType",
	"EAURACRONSigilType",
	Z_Construct_UEnum_AURACRON_EAURACRONSigilType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONSigilType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONSigilType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONSigilType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONSigilType()
{
	if (!Z_Registration_Info_UEnum_EAURACRONSigilType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONSigilType.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONSigilType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONSigilType.InnerSingleton;
}
// ********** End Enum EAURACRONSigilType **********************************************************

// ********** Begin Enum EAURACRONNetworkState *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONNetworkState;
static UEnum* EAURACRONNetworkState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONNetworkState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONNetworkState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONNetworkState, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONNetworkState"));
	}
	return Z_Registration_Info_UEnum_EAURACRONNetworkState.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONNetworkState>()
{
	return EAURACRONNetworkState_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONNetworkState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estados de replica\xc3\xa7\xc3\xa3o de rede\n * Sistema robusto para multiplayer\n */" },
#endif
		{ "Connected.DisplayName", "Conectado" },
		{ "Connected.Name", "EAURACRONNetworkState::Connected" },
		{ "Connecting.DisplayName", "Conectando" },
		{ "Connecting.Name", "EAURACRONNetworkState::Connecting" },
		{ "Disconnected.DisplayName", "Desconectado" },
		{ "Disconnected.Name", "EAURACRONNetworkState::Disconnected" },
		{ "Error.DisplayName", "Erro" },
		{ "Error.Name", "EAURACRONNetworkState::Error" },
		{ "InGame.DisplayName", "Em Jogo" },
		{ "InGame.Name", "EAURACRONNetworkState::InGame" },
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
		{ "Ready.DisplayName", "Pronto" },
		{ "Ready.Name", "EAURACRONNetworkState::Ready" },
		{ "Reconnecting.DisplayName", "Reconectando" },
		{ "Reconnecting.Name", "EAURACRONNetworkState::Reconnecting" },
		{ "Synchronizing.DisplayName", "Sincronizando" },
		{ "Synchronizing.Name", "EAURACRONNetworkState::Synchronizing" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estados de replica\xc3\xa7\xc3\xa3o de rede\nSistema robusto para multiplayer" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONNetworkState::Disconnected", (int64)EAURACRONNetworkState::Disconnected },
		{ "EAURACRONNetworkState::Connecting", (int64)EAURACRONNetworkState::Connecting },
		{ "EAURACRONNetworkState::Connected", (int64)EAURACRONNetworkState::Connected },
		{ "EAURACRONNetworkState::Synchronizing", (int64)EAURACRONNetworkState::Synchronizing },
		{ "EAURACRONNetworkState::Ready", (int64)EAURACRONNetworkState::Ready },
		{ "EAURACRONNetworkState::InGame", (int64)EAURACRONNetworkState::InGame },
		{ "EAURACRONNetworkState::Reconnecting", (int64)EAURACRONNetworkState::Reconnecting },
		{ "EAURACRONNetworkState::Error", (int64)EAURACRONNetworkState::Error },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONNetworkState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONNetworkState",
	"EAURACRONNetworkState",
	Z_Construct_UEnum_AURACRON_EAURACRONNetworkState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONNetworkState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONNetworkState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONNetworkState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONNetworkState()
{
	if (!Z_Registration_Info_UEnum_EAURACRONNetworkState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONNetworkState.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONNetworkState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONNetworkState.InnerSingleton;
}
// ********** End Enum EAURACRONNetworkState *******************************************************

// ********** Begin Enum EAURACRONObjectiveCategory ************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONObjectiveCategory;
static UEnum* EAURACRONObjectiveCategory_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONObjectiveCategory.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONObjectiveCategory.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONObjectiveCategory, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONObjectiveCategory"));
	}
	return Z_Registration_Info_UEnum_EAURACRONObjectiveCategory.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONObjectiveCategory>()
{
	return EAURACRONObjectiveCategory_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONObjectiveCategory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Bonus.Comment", "// Objetivos de catch-up\n" },
		{ "Bonus.DisplayName", "B\xc3\xb4nus" },
		{ "Bonus.Name", "EAURACRONObjectiveCategory::Bonus" },
		{ "Bonus.ToolTip", "Objetivos de catch-up" },
		{ "CatchUp.Comment", "// Objetivos principais\n" },
		{ "CatchUp.DisplayName", "Recupera\xc3\xa7\xc3\xa3o" },
		{ "CatchUp.Name", "EAURACRONObjectiveCategory::CatchUp" },
		{ "CatchUp.ToolTip", "Objetivos principais" },
		{ "Combat.Comment", "// Objetivos de explora\xc3\xa7\xc3\xa3o\n" },
		{ "Combat.DisplayName", "Combate" },
		{ "Combat.Name", "EAURACRONObjectiveCategory::Combat" },
		{ "Combat.ToolTip", "Objetivos de explora\xc3\xa7\xc3\xa3o" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Categoria de objetivos procedurais\n * Sistema de classifica\xc3\xa7\xc3\xa3o para balanceamento\n */" },
#endif
		{ "Core.DisplayName", "Principal" },
		{ "Core.Name", "EAURACRONObjectiveCategory::Core" },
		{ "Elite.Comment", "// Objetivos estrat\xc3\xa9gicos\n" },
		{ "Elite.DisplayName", "Elite" },
		{ "Elite.Name", "EAURACRONObjectiveCategory::Elite" },
		{ "Elite.ToolTip", "Objetivos estrat\xc3\xa9gicos" },
		{ "Environment.Comment", "// Objetivos padr\xc3\xa3o\n" },
		{ "Environment.DisplayName", "Ambiente" },
		{ "Environment.Name", "EAURACRONObjectiveCategory::Environment" },
		{ "Environment.ToolTip", "Objetivos padr\xc3\xa3o" },
		{ "Event.Comment", "// Objetivos extras\n" },
		{ "Event.DisplayName", "Evento" },
		{ "Event.Name", "EAURACRONObjectiveCategory::Event" },
		{ "Event.ToolTip", "Objetivos extras" },
		{ "Exploration.Comment", "// Objetivos de eventos especiais\n" },
		{ "Exploration.DisplayName", "Explora\xc3\xa7\xc3\xa3o" },
		{ "Exploration.Name", "EAURACRONObjectiveCategory::Exploration" },
		{ "Exploration.ToolTip", "Objetivos de eventos especiais" },
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
		{ "Standard.Comment", "// Objetivos elite\n" },
		{ "Standard.DisplayName", "Padr\xc3\xa3o" },
		{ "Standard.Name", "EAURACRONObjectiveCategory::Standard" },
		{ "Standard.ToolTip", "Objetivos elite" },
		{ "Strategic.Comment", "// Objetivos de combate\n" },
		{ "Strategic.DisplayName", "Estrat\xc3\xa9gico" },
		{ "Strategic.Name", "EAURACRONObjectiveCategory::Strategic" },
		{ "Strategic.ToolTip", "Objetivos de combate" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Categoria de objetivos procedurais\nSistema de classifica\xc3\xa7\xc3\xa3o para balanceamento" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONObjectiveCategory::Core", (int64)EAURACRONObjectiveCategory::Core },
		{ "EAURACRONObjectiveCategory::CatchUp", (int64)EAURACRONObjectiveCategory::CatchUp },
		{ "EAURACRONObjectiveCategory::Bonus", (int64)EAURACRONObjectiveCategory::Bonus },
		{ "EAURACRONObjectiveCategory::Event", (int64)EAURACRONObjectiveCategory::Event },
		{ "EAURACRONObjectiveCategory::Exploration", (int64)EAURACRONObjectiveCategory::Exploration },
		{ "EAURACRONObjectiveCategory::Combat", (int64)EAURACRONObjectiveCategory::Combat },
		{ "EAURACRONObjectiveCategory::Strategic", (int64)EAURACRONObjectiveCategory::Strategic },
		{ "EAURACRONObjectiveCategory::Elite", (int64)EAURACRONObjectiveCategory::Elite },
		{ "EAURACRONObjectiveCategory::Standard", (int64)EAURACRONObjectiveCategory::Standard },
		{ "EAURACRONObjectiveCategory::Environment", (int64)EAURACRONObjectiveCategory::Environment },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONObjectiveCategory_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONObjectiveCategory",
	"EAURACRONObjectiveCategory",
	Z_Construct_UEnum_AURACRON_EAURACRONObjectiveCategory_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONObjectiveCategory_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONObjectiveCategory_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONObjectiveCategory_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONObjectiveCategory()
{
	if (!Z_Registration_Info_UEnum_EAURACRONObjectiveCategory.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONObjectiveCategory.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONObjectiveCategory_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONObjectiveCategory.InnerSingleton;
}
// ********** End Enum EAURACRONObjectiveCategory **************************************************

// ********** Begin Enum EAURACRONObjectiveState ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONObjectiveState;
static UEnum* EAURACRONObjectiveState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONObjectiveState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONObjectiveState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONObjectiveState"));
	}
	return Z_Registration_Info_UEnum_EAURACRONObjectiveState.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONObjectiveState>()
{
	return EAURACRONObjectiveState_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Active.Comment", "// Objetivo n\xc3\xa3o ativo\n" },
		{ "Active.DisplayName", "Ativo" },
		{ "Active.Name", "EAURACRONObjectiveState::Active" },
		{ "Active.ToolTip", "Objetivo n\xc3\xa3o ativo" },
		{ "Available.Comment", "// Objetivo ativo e dispon\xc3\xadvel\n" },
		{ "Available.DisplayName", "Dispon\xc3\xadvel" },
		{ "Available.Name", "EAURACRONObjectiveState::Available" },
		{ "Available.ToolTip", "Objetivo ativo e dispon\xc3\xadvel" },
		{ "BlueprintType", "true" },
		{ "Captured.Comment", "// Objetivo sendo disputado por m\xc3\xbaltiplas equipes\n" },
		{ "Captured.DisplayName", "Capturado" },
		{ "Captured.Name", "EAURACRONObjectiveState::Captured" },
		{ "Captured.ToolTip", "Objetivo sendo disputado por m\xc3\xbaltiplas equipes" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estados de objetivos procedurais\n * Sistema de controle de estado dos objetivos\n */" },
#endif
		{ "Completed.Comment", "// Objetivo capturado por uma equipe\n" },
		{ "Completed.DisplayName", "Completado" },
		{ "Completed.Name", "EAURACRONObjectiveState::Completed" },
		{ "Completed.ToolTip", "Objetivo capturado por uma equipe" },
		{ "Contested.Comment", "// Objetivo em combate\n" },
		{ "Contested.DisplayName", "Contestado" },
		{ "Contested.Name", "EAURACRONObjectiveState::Contested" },
		{ "Contested.ToolTip", "Objetivo em combate" },
		{ "Expired.Comment", "// Objetivo renascendo\n" },
		{ "Expired.DisplayName", "Expirado" },
		{ "Expired.Name", "EAURACRONObjectiveState::Expired" },
		{ "Expired.ToolTip", "Objetivo renascendo" },
		{ "Inactive.DisplayName", "Inativo" },
		{ "Inactive.Name", "EAURACRONObjectiveState::Inactive" },
		{ "InCombat.Comment", "// Objetivo sendo capturado\n" },
		{ "InCombat.DisplayName", "Em Combate" },
		{ "InCombat.Name", "EAURACRONObjectiveState::InCombat" },
		{ "InCombat.ToolTip", "Objetivo sendo capturado" },
		{ "InProgress.Comment", "// Objetivo dispon\xc3\xadvel para captura\n" },
		{ "InProgress.DisplayName", "Em Progresso" },
		{ "InProgress.Name", "EAURACRONObjectiveState::InProgress" },
		{ "InProgress.ToolTip", "Objetivo dispon\xc3\xadvel para captura" },
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
		{ "Respawning.Comment", "// Objetivo completado\n" },
		{ "Respawning.DisplayName", "Renascendo" },
		{ "Respawning.Name", "EAURACRONObjectiveState::Respawning" },
		{ "Respawning.ToolTip", "Objetivo completado" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estados de objetivos procedurais\nSistema de controle de estado dos objetivos" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONObjectiveState::Inactive", (int64)EAURACRONObjectiveState::Inactive },
		{ "EAURACRONObjectiveState::Active", (int64)EAURACRONObjectiveState::Active },
		{ "EAURACRONObjectiveState::Available", (int64)EAURACRONObjectiveState::Available },
		{ "EAURACRONObjectiveState::InProgress", (int64)EAURACRONObjectiveState::InProgress },
		{ "EAURACRONObjectiveState::InCombat", (int64)EAURACRONObjectiveState::InCombat },
		{ "EAURACRONObjectiveState::Contested", (int64)EAURACRONObjectiveState::Contested },
		{ "EAURACRONObjectiveState::Captured", (int64)EAURACRONObjectiveState::Captured },
		{ "EAURACRONObjectiveState::Completed", (int64)EAURACRONObjectiveState::Completed },
		{ "EAURACRONObjectiveState::Respawning", (int64)EAURACRONObjectiveState::Respawning },
		{ "EAURACRONObjectiveState::Expired", (int64)EAURACRONObjectiveState::Expired },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONObjectiveState",
	"EAURACRONObjectiveState",
	Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState()
{
	if (!Z_Registration_Info_UEnum_EAURACRONObjectiveState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONObjectiveState.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONObjectiveState.InnerSingleton;
}
// ********** End Enum EAURACRONObjectiveState *****************************************************

// ********** Begin Enum EAURACRONEnergyType *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONEnergyType;
static UEnum* EAURACRONEnergyType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONEnergyType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONEnergyType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONEnergyType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONEnergyType"));
	}
	return Z_Registration_Info_UEnum_EAURACRONEnergyType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONEnergyType>()
{
	return EAURACRONEnergyType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONEnergyType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Chaos.Comment", "// Energia prism\xc3\xa1tica multicolor\n" },
		{ "Chaos.DisplayName", "Caos" },
		{ "Chaos.Name", "EAURACRONEnergyType::Chaos" },
		{ "Chaos.ToolTip", "Energia prism\xc3\xa1tica multicolor" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Tipos de energia para efeitos especiais\n * Sistema de classifica\xc3\xa7\xc3\xa3o de energia\n */" },
#endif
		{ "Golden.DisplayName", "Energia Dourada" },
		{ "Golden.Name", "EAURACRONEnergyType::Golden" },
		{ "Lunar.Comment", "// Energia solar dourada\n" },
		{ "Lunar.DisplayName", "Lunar" },
		{ "Lunar.Name", "EAURACRONEnergyType::Lunar" },
		{ "Lunar.ToolTip", "Energia solar dourada" },
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
		{ "Prismal.Comment", "// Energia lunar prateada\n" },
		{ "Prismal.DisplayName", "Prismal" },
		{ "Prismal.Name", "EAURACRONEnergyType::Prismal" },
		{ "Prismal.ToolTip", "Energia lunar prateada" },
		{ "Silver.Comment", "// Energia dos Portais Radiantes\n" },
		{ "Silver.DisplayName", "Energia Prateada" },
		{ "Silver.Name", "EAURACRONEnergyType::Silver" },
		{ "Silver.ToolTip", "Energia dos Portais Radiantes" },
		{ "Solar.Comment", "// Energia dos Portais Umbrais\n" },
		{ "Solar.DisplayName", "Solar" },
		{ "Solar.Name", "EAURACRONEnergyType::Solar" },
		{ "Solar.ToolTip", "Energia dos Portais Umbrais" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipos de energia para efeitos especiais\nSistema de classifica\xc3\xa7\xc3\xa3o de energia" },
#endif
		{ "Violet.Comment", "// Energia dos Portais Zephyr\n" },
		{ "Violet.DisplayName", "Energia Violeta" },
		{ "Violet.Name", "EAURACRONEnergyType::Violet" },
		{ "Violet.ToolTip", "Energia dos Portais Zephyr" },
		{ "Void.Comment", "// Energia ca\xc3\xb3tica vermelha\n" },
		{ "Void.DisplayName", "Vazio" },
		{ "Void.Name", "EAURACRONEnergyType::Void" },
		{ "Void.ToolTip", "Energia ca\xc3\xb3tica vermelha" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONEnergyType::Golden", (int64)EAURACRONEnergyType::Golden },
		{ "EAURACRONEnergyType::Silver", (int64)EAURACRONEnergyType::Silver },
		{ "EAURACRONEnergyType::Violet", (int64)EAURACRONEnergyType::Violet },
		{ "EAURACRONEnergyType::Solar", (int64)EAURACRONEnergyType::Solar },
		{ "EAURACRONEnergyType::Lunar", (int64)EAURACRONEnergyType::Lunar },
		{ "EAURACRONEnergyType::Prismal", (int64)EAURACRONEnergyType::Prismal },
		{ "EAURACRONEnergyType::Chaos", (int64)EAURACRONEnergyType::Chaos },
		{ "EAURACRONEnergyType::Void", (int64)EAURACRONEnergyType::Void },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONEnergyType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONEnergyType",
	"EAURACRONEnergyType",
	Z_Construct_UEnum_AURACRON_EAURACRONEnergyType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONEnergyType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONEnergyType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONEnergyType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONEnergyType()
{
	if (!Z_Registration_Info_UEnum_EAURACRONEnergyType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONEnergyType.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONEnergyType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONEnergyType.InnerSingleton;
}
// ********** End Enum EAURACRONEnergyType *********************************************************

// ********** Begin Enum EAURACRONIslandType *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONIslandType;
static UEnum* EAURACRONIslandType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONIslandType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONIslandType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONIslandType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONIslandType"));
	}
	return Z_Registration_Info_UEnum_EAURACRONIslandType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONIslandType>()
{
	return EAURACRONIslandType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONIslandType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Arsenal.DisplayName", "Arsenal Island" },
		{ "Arsenal.Name", "EAURACRONIslandType::Arsenal" },
		{ "Battlefield.DisplayName", "Battlefield Island" },
		{ "Battlefield.Name", "EAURACRONIslandType::Battlefield" },
		{ "BlueprintType", "true" },
		{ "Chaos.DisplayName", "Chaos Island" },
		{ "Chaos.Name", "EAURACRONIslandType::Chaos" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Tipos de ilhas no sistema PCG\n * Sistema de classifica\xc3\xa7\xc3\xa3o de ilhas procedurais\n */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
		{ "Nexus.DisplayName", "Nexus Island" },
		{ "Nexus.Name", "EAURACRONIslandType::Nexus" },
		{ "None.DisplayName", "Nenhum" },
		{ "None.Name", "EAURACRONIslandType::None" },
		{ "Sanctuary.DisplayName", "Sanctuary Island" },
		{ "Sanctuary.Name", "EAURACRONIslandType::Sanctuary" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipos de ilhas no sistema PCG\nSistema de classifica\xc3\xa7\xc3\xa3o de ilhas procedurais" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONIslandType::None", (int64)EAURACRONIslandType::None },
		{ "EAURACRONIslandType::Nexus", (int64)EAURACRONIslandType::Nexus },
		{ "EAURACRONIslandType::Sanctuary", (int64)EAURACRONIslandType::Sanctuary },
		{ "EAURACRONIslandType::Arsenal", (int64)EAURACRONIslandType::Arsenal },
		{ "EAURACRONIslandType::Chaos", (int64)EAURACRONIslandType::Chaos },
		{ "EAURACRONIslandType::Battlefield", (int64)EAURACRONIslandType::Battlefield },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONIslandType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONIslandType",
	"EAURACRONIslandType",
	Z_Construct_UEnum_AURACRON_EAURACRONIslandType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONIslandType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONIslandType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONIslandType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONIslandType()
{
	if (!Z_Registration_Info_UEnum_EAURACRONIslandType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONIslandType.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONIslandType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONIslandType.InnerSingleton;
}
// ********** End Enum EAURACRONIslandType *********************************************************

// ********** Begin Enum EAURACRONJungleCampType ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONJungleCampType;
static UEnum* EAURACRONJungleCampType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONJungleCampType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONJungleCampType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONJungleCampType"));
	}
	return Z_Registration_Info_UEnum_EAURACRONJungleCampType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONJungleCampType>()
{
	return EAURACRONJungleCampType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AncientGuardian.Comment", "// Dragon equivalent\n" },
		{ "AncientGuardian.DisplayName", "Ancient Guardian" },
		{ "AncientGuardian.Name", "EAURACRONJungleCampType::AncientGuardian" },
		{ "AncientGuardian.ToolTip", "Dragon equivalent" },
		{ "BlueprintType", "true" },
		{ "ChaosEssence.Comment", "// Blue Buff equivalent (mana regen + CDR)\n" },
		{ "ChaosEssence.DisplayName", "Chaos Essence" },
		{ "ChaosEssence.Name", "EAURACRONJungleCampType::ChaosEssence" },
		{ "ChaosEssence.ToolTip", "Blue Buff equivalent (mana regen + CDR)" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Tipos de acampamentos da selva baseados no LoL\n * Sistema de classifica\xc3\xa7\xc3\xa3o de acampamentos procedurais\n */" },
#endif
		{ "CrystalGolem.Comment", "// Shadow wolf camp\n" },
		{ "CrystalGolem.DisplayName", "Crystal Golem" },
		{ "CrystalGolem.Name", "EAURACRONJungleCampType::CrystalGolem" },
		{ "CrystalGolem.ToolTip", "Shadow wolf camp" },
		{ "CrystalWolves.Comment", "// Raptors equivalent\n" },
		{ "CrystalWolves.DisplayName", "Crystal Wolves" },
		{ "CrystalWolves.Name", "EAURACRONJungleCampType::CrystalWolves" },
		{ "CrystalWolves.ToolTip", "Raptors equivalent" },
		{ "Epic.DisplayName", "\xc3\x89pico" },
		{ "Epic.Name", "EAURACRONJungleCampType::Epic" },
		{ "EtherealGrove.Comment", "// Krugs equivalent\n" },
		{ "EtherealGrove.DisplayName", "Ethereal Grove" },
		{ "EtherealGrove.Name", "EAURACRONJungleCampType::EtherealGrove" },
		{ "EtherealGrove.ToolTip", "Krugs equivalent" },
		{ "FluxCrawler.Comment", "// Wind spirit camp\n" },
		{ "FluxCrawler.DisplayName", "Flux Crawler" },
		{ "FluxCrawler.Name", "EAURACRONJungleCampType::FluxCrawler" },
		{ "FluxCrawler.ToolTip", "Wind spirit camp" },
		{ "Large.DisplayName", "Grande" },
		{ "Large.Name", "EAURACRONJungleCampType::Large" },
		{ "MAX.Hidden", "" },
		{ "MAX.Name", "EAURACRONJungleCampType::MAX" },
		{ "Medium.DisplayName", "M\xc3\xa9""dio" },
		{ "Medium.Name", "EAURACRONJungleCampType::Medium" },
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
		{ "None.DisplayName", "Nenhum" },
		{ "None.Name", "EAURACRONJungleCampType::None" },
		{ "PrismalDragon.Comment", "// EPIC MONSTERS (equivalentes ao Dragon/Baron)\n" },
		{ "PrismalDragon.DisplayName", "Prismal Dragon" },
		{ "PrismalDragon.Name", "EAURACRONJungleCampType::PrismalDragon" },
		{ "PrismalDragon.ToolTip", "EPIC MONSTERS (equivalentes ao Dragon/Baron)" },
		{ "PrismalToad.Comment", "// Stone guardian camp\n" },
		{ "PrismalToad.DisplayName", "Prismal Toad" },
		{ "PrismalToad.Name", "EAURACRONJungleCampType::PrismalToad" },
		{ "PrismalToad.ToolTip", "Stone guardian camp" },
		{ "RadiantEssence.Comment", "// BUFF CAMPS (equivalentes ao Blue/Red Buff)\n" },
		{ "RadiantEssence.DisplayName", "Radiant Essence" },
		{ "RadiantEssence.Name", "EAURACRONJungleCampType::RadiantEssence" },
		{ "RadiantEssence.ToolTip", "BUFF CAMPS (equivalentes ao Blue/Red Buff)" },
		{ "ShadowWolf.Comment", "// Void harpy camp\n" },
		{ "ShadowWolf.DisplayName", "Shadow Wolf" },
		{ "ShadowWolf.Name", "EAURACRONJungleCampType::ShadowWolf" },
		{ "ShadowWolf.ToolTip", "Void harpy camp" },
		{ "Small.Comment", "// Legacy values for compatibility\n" },
		{ "Small.DisplayName", "Pequeno" },
		{ "Small.Name", "EAURACRONJungleCampType::Small" },
		{ "Small.ToolTip", "Legacy values for compatibility" },
		{ "SpectralPack.Comment", "// REGULAR CAMPS\n" },
		{ "SpectralPack.DisplayName", "Spectral Pack" },
		{ "SpectralPack.Name", "EAURACRONJungleCampType::SpectralPack" },
		{ "SpectralPack.ToolTip", "REGULAR CAMPS" },
		{ "StoneGuardians.Comment", "// Wolves equivalent\n" },
		{ "StoneGuardians.DisplayName", "Stone Guardians" },
		{ "StoneGuardians.Name", "EAURACRONJungleCampType::StoneGuardians" },
		{ "StoneGuardians.ToolTip", "Wolves equivalent" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipos de acampamentos da selva baseados no LoL\nSistema de classifica\xc3\xa7\xc3\xa3o de acampamentos procedurais" },
#endif
		{ "VoidHarpy.Comment", "// Additional camp types\n" },
		{ "VoidHarpy.DisplayName", "Void Harpy" },
		{ "VoidHarpy.Name", "EAURACRONJungleCampType::VoidHarpy" },
		{ "VoidHarpy.ToolTip", "Additional camp types" },
		{ "VoidRaptors.Comment", "// Gromp equivalent\n" },
		{ "VoidRaptors.DisplayName", "Void Raptors" },
		{ "VoidRaptors.Name", "EAURACRONJungleCampType::VoidRaptors" },
		{ "VoidRaptors.ToolTip", "Gromp equivalent" },
		{ "WindSpirits.Comment", "// Prismal toad camp\n" },
		{ "WindSpirits.DisplayName", "Wind Spirits" },
		{ "WindSpirits.Name", "EAURACRONJungleCampType::WindSpirits" },
		{ "WindSpirits.ToolTip", "Prismal toad camp" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONJungleCampType::None", (int64)EAURACRONJungleCampType::None },
		{ "EAURACRONJungleCampType::RadiantEssence", (int64)EAURACRONJungleCampType::RadiantEssence },
		{ "EAURACRONJungleCampType::ChaosEssence", (int64)EAURACRONJungleCampType::ChaosEssence },
		{ "EAURACRONJungleCampType::SpectralPack", (int64)EAURACRONJungleCampType::SpectralPack },
		{ "EAURACRONJungleCampType::EtherealGrove", (int64)EAURACRONJungleCampType::EtherealGrove },
		{ "EAURACRONJungleCampType::VoidRaptors", (int64)EAURACRONJungleCampType::VoidRaptors },
		{ "EAURACRONJungleCampType::CrystalWolves", (int64)EAURACRONJungleCampType::CrystalWolves },
		{ "EAURACRONJungleCampType::StoneGuardians", (int64)EAURACRONJungleCampType::StoneGuardians },
		{ "EAURACRONJungleCampType::PrismalToad", (int64)EAURACRONJungleCampType::PrismalToad },
		{ "EAURACRONJungleCampType::WindSpirits", (int64)EAURACRONJungleCampType::WindSpirits },
		{ "EAURACRONJungleCampType::FluxCrawler", (int64)EAURACRONJungleCampType::FluxCrawler },
		{ "EAURACRONJungleCampType::PrismalDragon", (int64)EAURACRONJungleCampType::PrismalDragon },
		{ "EAURACRONJungleCampType::AncientGuardian", (int64)EAURACRONJungleCampType::AncientGuardian },
		{ "EAURACRONJungleCampType::VoidHarpy", (int64)EAURACRONJungleCampType::VoidHarpy },
		{ "EAURACRONJungleCampType::ShadowWolf", (int64)EAURACRONJungleCampType::ShadowWolf },
		{ "EAURACRONJungleCampType::CrystalGolem", (int64)EAURACRONJungleCampType::CrystalGolem },
		{ "EAURACRONJungleCampType::Small", (int64)EAURACRONJungleCampType::Small },
		{ "EAURACRONJungleCampType::Medium", (int64)EAURACRONJungleCampType::Medium },
		{ "EAURACRONJungleCampType::Large", (int64)EAURACRONJungleCampType::Large },
		{ "EAURACRONJungleCampType::Epic", (int64)EAURACRONJungleCampType::Epic },
		{ "EAURACRONJungleCampType::MAX", (int64)EAURACRONJungleCampType::MAX },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONJungleCampType",
	"EAURACRONJungleCampType",
	Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType()
{
	if (!Z_Registration_Info_UEnum_EAURACRONJungleCampType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONJungleCampType.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONJungleCampType.InnerSingleton;
}
// ********** End Enum EAURACRONJungleCampType *****************************************************

// ********** Begin Enum EAURACRONDeviceType *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONDeviceType;
static UEnum* EAURACRONDeviceType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONDeviceType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONDeviceType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONDeviceType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONDeviceType"));
	}
	return Z_Registration_Info_UEnum_EAURACRONDeviceType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONDeviceType>()
{
	return EAURACRONDeviceType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONDeviceType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Amplifier.DisplayName", "Amplificador" },
		{ "Amplifier.Name", "EAURACRONDeviceType::Amplifier" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Tipos de dispositivos para adapta\xc3\xa7\xc3\xa3o de performance\n * Sistema de classifica\xc3\xa7\xc3\xa3o de dispositivos procedurais e performance\n */" },
#endif
		{ "Entry.Comment", "// Performance device types\n" },
		{ "Entry.DisplayName", "Entry Device - Low Performance" },
		{ "Entry.Name", "EAURACRONDeviceType::Entry" },
		{ "Entry.ToolTip", "Performance device types" },
		{ "Generator.Comment", "// Legacy PCG device types\n" },
		{ "Generator.DisplayName", "Gerador" },
		{ "Generator.Name", "EAURACRONDeviceType::Generator" },
		{ "Generator.ToolTip", "Legacy PCG device types" },
		{ "High.DisplayName", "High Device - High Performance" },
		{ "High.Name", "EAURACRONDeviceType::High" },
		{ "Mid.DisplayName", "Mid Device - Medium Performance" },
		{ "Mid.Name", "EAURACRONDeviceType::Mid" },
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
		{ "None.DisplayName", "Nenhum" },
		{ "None.Name", "EAURACRONDeviceType::None" },
		{ "Resonator.DisplayName", "Ressonador" },
		{ "Resonator.Name", "EAURACRONDeviceType::Resonator" },
		{ "Stabilizer.DisplayName", "Estabilizador" },
		{ "Stabilizer.Name", "EAURACRONDeviceType::Stabilizer" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipos de dispositivos para adapta\xc3\xa7\xc3\xa3o de performance\nSistema de classifica\xc3\xa7\xc3\xa3o de dispositivos procedurais e performance" },
#endif
		{ "Unknown.DisplayName", "Unknown Device Type" },
		{ "Unknown.Name", "EAURACRONDeviceType::Unknown" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONDeviceType::None", (int64)EAURACRONDeviceType::None },
		{ "EAURACRONDeviceType::Entry", (int64)EAURACRONDeviceType::Entry },
		{ "EAURACRONDeviceType::Mid", (int64)EAURACRONDeviceType::Mid },
		{ "EAURACRONDeviceType::High", (int64)EAURACRONDeviceType::High },
		{ "EAURACRONDeviceType::Unknown", (int64)EAURACRONDeviceType::Unknown },
		{ "EAURACRONDeviceType::Generator", (int64)EAURACRONDeviceType::Generator },
		{ "EAURACRONDeviceType::Amplifier", (int64)EAURACRONDeviceType::Amplifier },
		{ "EAURACRONDeviceType::Stabilizer", (int64)EAURACRONDeviceType::Stabilizer },
		{ "EAURACRONDeviceType::Resonator", (int64)EAURACRONDeviceType::Resonator },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONDeviceType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONDeviceType",
	"EAURACRONDeviceType",
	Z_Construct_UEnum_AURACRON_EAURACRONDeviceType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONDeviceType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONDeviceType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONDeviceType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONDeviceType()
{
	if (!Z_Registration_Info_UEnum_EAURACRONDeviceType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONDeviceType.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONDeviceType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONDeviceType.InnerSingleton;
}
// ********** End Enum EAURACRONDeviceType *********************************************************

// ********** Begin Enum EAURACRONStreamingQualityProfile ******************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONStreamingQualityProfile;
static UEnum* EAURACRONStreamingQualityProfile_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONStreamingQualityProfile.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONStreamingQualityProfile.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONStreamingQualityProfile, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONStreamingQualityProfile"));
	}
	return Z_Registration_Info_UEnum_EAURACRONStreamingQualityProfile.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONStreamingQualityProfile>()
{
	return EAURACRONStreamingQualityProfile_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONStreamingQualityProfile_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Perfis de qualidade de streaming\n * Sistema de otimiza\xc3\xa7\xc3\xa3o de streaming baseado em hardware\n */" },
#endif
		{ "Custom.DisplayName", "Personalizado" },
		{ "Custom.Name", "EAURACRONStreamingQualityProfile::Custom" },
		{ "High.DisplayName", "Alta Qualidade" },
		{ "High.Name", "EAURACRONStreamingQualityProfile::High" },
		{ "Low.DisplayName", "Baixa Qualidade" },
		{ "Low.Name", "EAURACRONStreamingQualityProfile::Low" },
		{ "Medium.DisplayName", "M\xc3\xa9""dia Qualidade" },
		{ "Medium.Name", "EAURACRONStreamingQualityProfile::Medium" },
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Perfis de qualidade de streaming\nSistema de otimiza\xc3\xa7\xc3\xa3o de streaming baseado em hardware" },
#endif
		{ "Ultra.DisplayName", "Ultra Qualidade" },
		{ "Ultra.Name", "EAURACRONStreamingQualityProfile::Ultra" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONStreamingQualityProfile::Low", (int64)EAURACRONStreamingQualityProfile::Low },
		{ "EAURACRONStreamingQualityProfile::Medium", (int64)EAURACRONStreamingQualityProfile::Medium },
		{ "EAURACRONStreamingQualityProfile::High", (int64)EAURACRONStreamingQualityProfile::High },
		{ "EAURACRONStreamingQualityProfile::Ultra", (int64)EAURACRONStreamingQualityProfile::Ultra },
		{ "EAURACRONStreamingQualityProfile::Custom", (int64)EAURACRONStreamingQualityProfile::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONStreamingQualityProfile_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONStreamingQualityProfile",
	"EAURACRONStreamingQualityProfile",
	Z_Construct_UEnum_AURACRON_EAURACRONStreamingQualityProfile_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONStreamingQualityProfile_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONStreamingQualityProfile_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONStreamingQualityProfile_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONStreamingQualityProfile()
{
	if (!Z_Registration_Info_UEnum_EAURACRONStreamingQualityProfile.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONStreamingQualityProfile.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONStreamingQualityProfile_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONStreamingQualityProfile.InnerSingleton;
}
// ********** End Enum EAURACRONStreamingQualityProfile ********************************************

// ********** Begin Enum EEnvironmentBlurType ******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EEnvironmentBlurType;
static UEnum* EEnvironmentBlurType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EEnvironmentBlurType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EEnvironmentBlurType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EEnvironmentBlurType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EEnvironmentBlurType"));
	}
	return Z_Registration_Info_UEnum_EEnvironmentBlurType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EEnvironmentBlurType>()
{
	return EEnvironmentBlurType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EEnvironmentBlurType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Atmospheric.Comment", "// Blur gaussiano suave\n" },
		{ "Atmospheric.DisplayName", "Atmosf\xc3\xa9rico" },
		{ "Atmospheric.Name", "EEnvironmentBlurType::Atmospheric" },
		{ "Atmospheric.ToolTip", "Blur gaussiano suave" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Tipos de blur para bordas de ambiente\n * Sistema de efeitos visuais para transi\xc3\xa7\xc3\xb5""es\n */" },
#endif
		{ "Depth.Comment", "// Blur de movimento\n" },
		{ "Depth.DisplayName", "Profundidade" },
		{ "Depth.Name", "EEnvironmentBlurType::Depth" },
		{ "Depth.ToolTip", "Blur de movimento" },
		{ "Gaussian.DisplayName", "Gaussiano" },
		{ "Gaussian.Name", "EEnvironmentBlurType::Gaussian" },
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
		{ "Motion.Comment", "// Blur radial\n" },
		{ "Motion.DisplayName", "Movimento" },
		{ "Motion.Name", "EEnvironmentBlurType::Motion" },
		{ "Motion.ToolTip", "Blur radial" },
		{ "Radial.Comment", "// Blur espectral sombrio\n" },
		{ "Radial.DisplayName", "Radial" },
		{ "Radial.Name", "EEnvironmentBlurType::Radial" },
		{ "Radial.ToolTip", "Blur espectral sombrio" },
		{ "Spectral.Comment", "// Blur atmosf\xc3\xa9rico et\xc3\xa9reo\n" },
		{ "Spectral.DisplayName", "Espectral" },
		{ "Spectral.Name", "EEnvironmentBlurType::Spectral" },
		{ "Spectral.ToolTip", "Blur atmosf\xc3\xa9rico et\xc3\xa9reo" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipos de blur para bordas de ambiente\nSistema de efeitos visuais para transi\xc3\xa7\xc3\xb5""es" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EEnvironmentBlurType::Gaussian", (int64)EEnvironmentBlurType::Gaussian },
		{ "EEnvironmentBlurType::Atmospheric", (int64)EEnvironmentBlurType::Atmospheric },
		{ "EEnvironmentBlurType::Spectral", (int64)EEnvironmentBlurType::Spectral },
		{ "EEnvironmentBlurType::Radial", (int64)EEnvironmentBlurType::Radial },
		{ "EEnvironmentBlurType::Motion", (int64)EEnvironmentBlurType::Motion },
		{ "EEnvironmentBlurType::Depth", (int64)EEnvironmentBlurType::Depth },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EEnvironmentBlurType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EEnvironmentBlurType",
	"EEnvironmentBlurType",
	Z_Construct_UEnum_AURACRON_EEnvironmentBlurType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EEnvironmentBlurType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EEnvironmentBlurType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EEnvironmentBlurType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EEnvironmentBlurType()
{
	if (!Z_Registration_Info_UEnum_EEnvironmentBlurType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EEnvironmentBlurType.InnerSingleton, Z_Construct_UEnum_AURACRON_EEnvironmentBlurType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EEnvironmentBlurType.InnerSingleton;
}
// ********** End Enum EEnvironmentBlurType ********************************************************

// ********** Begin Enum EAURACRONPerformanceLevel *************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONPerformanceLevel;
static UEnum* EAURACRONPerformanceLevel_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONPerformanceLevel.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONPerformanceLevel.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONPerformanceLevel, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONPerformanceLevel"));
	}
	return Z_Registration_Info_UEnum_EAURACRONPerformanceLevel.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONPerformanceLevel>()
{
	return EAURACRONPerformanceLevel_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONPerformanceLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * N\xc3\xadveis de performance para otimiza\xc3\xa7\xc3\xa3o de hardware\n * Sistema de classifica\xc3\xa7\xc3\xa3o de performance baseado na documenta\xc3\xa7\xc3\xa3o AURACRON\n */" },
#endif
		{ "Custom.Comment", "// Ultra - 3000+ part\xc3\xad""culas\n" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAURACRONPerformanceLevel::Custom" },
		{ "Custom.ToolTip", "Ultra - 3000+ part\xc3\xad""culas" },
		{ "Entry.DisplayName", "Entry Level" },
		{ "Entry.Name", "EAURACRONPerformanceLevel::Entry" },
		{ "High.Comment", "// Mid-range - 800 part\xc3\xad""culas\n" },
		{ "High.DisplayName", "High End" },
		{ "High.Name", "EAURACRONPerformanceLevel::High" },
		{ "High.ToolTip", "Mid-range - 800 part\xc3\xad""culas" },
		{ "Mid.Comment", "// Entry Level - 300 part\xc3\xad""culas\n" },
		{ "Mid.DisplayName", "Mid Range" },
		{ "Mid.Name", "EAURACRONPerformanceLevel::Mid" },
		{ "Mid.ToolTip", "Entry Level - 300 part\xc3\xad""culas" },
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xadveis de performance para otimiza\xc3\xa7\xc3\xa3o de hardware\nSistema de classifica\xc3\xa7\xc3\xa3o de performance baseado na documenta\xc3\xa7\xc3\xa3o AURACRON" },
#endif
		{ "Ultra.Comment", "// High-end - 2000 part\xc3\xad""culas\n" },
		{ "Ultra.DisplayName", "Ultra" },
		{ "Ultra.Name", "EAURACRONPerformanceLevel::Ultra" },
		{ "Ultra.ToolTip", "High-end - 2000 part\xc3\xad""culas" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONPerformanceLevel::Entry", (int64)EAURACRONPerformanceLevel::Entry },
		{ "EAURACRONPerformanceLevel::Mid", (int64)EAURACRONPerformanceLevel::Mid },
		{ "EAURACRONPerformanceLevel::High", (int64)EAURACRONPerformanceLevel::High },
		{ "EAURACRONPerformanceLevel::Ultra", (int64)EAURACRONPerformanceLevel::Ultra },
		{ "EAURACRONPerformanceLevel::Custom", (int64)EAURACRONPerformanceLevel::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONPerformanceLevel_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONPerformanceLevel",
	"EAURACRONPerformanceLevel",
	Z_Construct_UEnum_AURACRON_EAURACRONPerformanceLevel_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONPerformanceLevel_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONPerformanceLevel_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONPerformanceLevel_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONPerformanceLevel()
{
	if (!Z_Registration_Info_UEnum_EAURACRONPerformanceLevel.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONPerformanceLevel.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONPerformanceLevel_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONPerformanceLevel.InnerSingleton;
}
// ********** End Enum EAURACRONPerformanceLevel ***************************************************

// ********** Begin Enum EAURACRONIslandSector *****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONIslandSector;
static UEnum* EAURACRONIslandSector_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONIslandSector.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONIslandSector.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONIslandSector, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONIslandSector"));
	}
	return Z_Registration_Info_UEnum_EAURACRONIslandSector.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONIslandSector>()
{
	return EAURACRONIslandSector_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONIslandSector_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Arsenal.Comment", "// Fontes de cura e amplificadores de vis\xc3\xa3o\n" },
		{ "Arsenal.DisplayName", "Setor Arsenal" },
		{ "Arsenal.Name", "EAURACRONIslandSector::Arsenal" },
		{ "Arsenal.ToolTip", "Fontes de cura e amplificadores de vis\xc3\xa3o" },
		{ "BlueprintType", "true" },
		{ "Chaos.Comment", "// Upgrades de armas e potencializadores de habilidades\n" },
		{ "Chaos.DisplayName", "Setor Caos" },
		{ "Chaos.Name", "EAURACRONIslandSector::Chaos" },
		{ "Chaos.ToolTip", "Upgrades de armas e potencializadores de habilidades" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Setores da Ilha Central Auracron\n * Sistema de classifica\xc3\xa7\xc3\xa3o dos 4 setores baseado na documenta\xc3\xa7\xc3\xa3o\n */" },
#endif
		{ "ModuleRelativePath", "Public/Data/AURACRONEnums.h" },
		{ "Nexus.Comment", "// Valor padr\xc3\xa3o\n" },
		{ "Nexus.DisplayName", "Setor Nexus" },
		{ "Nexus.Name", "EAURACRONIslandSector::Nexus" },
		{ "Nexus.ToolTip", "Valor padr\xc3\xa3o" },
		{ "None.DisplayName", "Nenhum" },
		{ "None.Name", "EAURACRONIslandSector::None" },
		{ "Sanctuary.Comment", "// Geradores de recursos e manipula\xc3\xa7\xc3\xa3o do Fluxo\n" },
		{ "Sanctuary.DisplayName", "Setor Santu\xc3\xa1rio" },
		{ "Sanctuary.Name", "EAURACRONIslandSector::Sanctuary" },
		{ "Sanctuary.ToolTip", "Geradores de recursos e manipula\xc3\xa7\xc3\xa3o do Fluxo" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Setores da Ilha Central Auracron\nSistema de classifica\xc3\xa7\xc3\xa3o dos 4 setores baseado na documenta\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONIslandSector::None", (int64)EAURACRONIslandSector::None },
		{ "EAURACRONIslandSector::Nexus", (int64)EAURACRONIslandSector::Nexus },
		{ "EAURACRONIslandSector::Sanctuary", (int64)EAURACRONIslandSector::Sanctuary },
		{ "EAURACRONIslandSector::Arsenal", (int64)EAURACRONIslandSector::Arsenal },
		{ "EAURACRONIslandSector::Chaos", (int64)EAURACRONIslandSector::Chaos },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONIslandSector_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONIslandSector",
	"EAURACRONIslandSector",
	Z_Construct_UEnum_AURACRON_EAURACRONIslandSector_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONIslandSector_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONIslandSector_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONIslandSector_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONIslandSector()
{
	if (!Z_Registration_Info_UEnum_EAURACRONIslandSector.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONIslandSector.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONIslandSector_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONIslandSector.InnerSingleton;
}
// ********** End Enum EAURACRONIslandSector *******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Data_AURACRONEnums_h__Script_AURACRON_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAURACRONMapPhase_StaticEnum, TEXT("EAURACRONMapPhase"), &Z_Registration_Info_UEnum_EAURACRONMapPhase, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2541365769U) },
		{ EAURACRONEnvironmentType_StaticEnum, TEXT("EAURACRONEnvironmentType"), &Z_Registration_Info_UEnum_EAURACRONEnvironmentType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2509470107U) },
		{ EAURACRONObjectiveType_StaticEnum, TEXT("EAURACRONObjectiveType"), &Z_Registration_Info_UEnum_EAURACRONObjectiveType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2271266485U) },
		{ EAURACRONBuffType_StaticEnum, TEXT("EAURACRONBuffType"), &Z_Registration_Info_UEnum_EAURACRONBuffType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 362549284U) },
		{ EAURACRONTemporalEffectType_StaticEnum, TEXT("EAURACRONTemporalEffectType"), &Z_Registration_Info_UEnum_EAURACRONTemporalEffectType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3331782527U) },
		{ EAURACRONTrailType_StaticEnum, TEXT("EAURACRONTrailType"), &Z_Registration_Info_UEnum_EAURACRONTrailType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2049964576U) },
		{ EAURACRONHardwareQuality_StaticEnum, TEXT("EAURACRONHardwareQuality"), &Z_Registration_Info_UEnum_EAURACRONHardwareQuality, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 919796832U) },
		{ EAURACRONSigilType_StaticEnum, TEXT("EAURACRONSigilType"), &Z_Registration_Info_UEnum_EAURACRONSigilType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1798462891U) },
		{ EAURACRONNetworkState_StaticEnum, TEXT("EAURACRONNetworkState"), &Z_Registration_Info_UEnum_EAURACRONNetworkState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2714070138U) },
		{ EAURACRONObjectiveCategory_StaticEnum, TEXT("EAURACRONObjectiveCategory"), &Z_Registration_Info_UEnum_EAURACRONObjectiveCategory, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 842757909U) },
		{ EAURACRONObjectiveState_StaticEnum, TEXT("EAURACRONObjectiveState"), &Z_Registration_Info_UEnum_EAURACRONObjectiveState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1111886678U) },
		{ EAURACRONEnergyType_StaticEnum, TEXT("EAURACRONEnergyType"), &Z_Registration_Info_UEnum_EAURACRONEnergyType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 81350420U) },
		{ EAURACRONIslandType_StaticEnum, TEXT("EAURACRONIslandType"), &Z_Registration_Info_UEnum_EAURACRONIslandType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3618671819U) },
		{ EAURACRONJungleCampType_StaticEnum, TEXT("EAURACRONJungleCampType"), &Z_Registration_Info_UEnum_EAURACRONJungleCampType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 44161181U) },
		{ EAURACRONDeviceType_StaticEnum, TEXT("EAURACRONDeviceType"), &Z_Registration_Info_UEnum_EAURACRONDeviceType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2389933137U) },
		{ EAURACRONStreamingQualityProfile_StaticEnum, TEXT("EAURACRONStreamingQualityProfile"), &Z_Registration_Info_UEnum_EAURACRONStreamingQualityProfile, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1148837425U) },
		{ EEnvironmentBlurType_StaticEnum, TEXT("EEnvironmentBlurType"), &Z_Registration_Info_UEnum_EEnvironmentBlurType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 920242184U) },
		{ EAURACRONPerformanceLevel_StaticEnum, TEXT("EAURACRONPerformanceLevel"), &Z_Registration_Info_UEnum_EAURACRONPerformanceLevel, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1200720964U) },
		{ EAURACRONIslandSector_StaticEnum, TEXT("EAURACRONIslandSector"), &Z_Registration_Info_UEnum_EAURACRONIslandSector, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2158473133U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Data_AURACRONEnums_h__Script_AURACRON_799745646(TEXT("/Script/AURACRON"),
	nullptr, 0,
	nullptr, 0,
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Data_AURACRONEnums_h__Script_AURACRON_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_Data_AURACRONEnums_h__Script_AURACRON_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
