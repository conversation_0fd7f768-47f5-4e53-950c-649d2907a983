{"Version": "1.2", "Data": {"Source": "c:\\auracron\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\auracron\\module.auracron.9.cpp", "ProvidedModule": "", "PCH": "c:\\auracron\\intermediate\\build\\win64\\x64\\auracroneditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\auracron\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\auracron\\definitions.auracron.h", "c:\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracron\\uht\\auracronpcgtypes.gen.cpp", "c:\\auracron\\source\\auracron\\public\\pcg\\auracronpcgtypes.h", "c:\\auracron\\source\\auracron\\public\\data\\auracronenums.h", "c:\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracron\\uht\\auracronenums.generated.h", "c:\\auracron\\source\\auracron\\public\\data\\auracronstructs.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayabilityspec.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\attributeset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\attributeset.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayabilityspechandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayabilityspechandle.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayeffecttypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\activegameplayeffecthandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\activegameplayeffecthandle.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayeffectattributecapturedefinition.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayeffectattributecapturedefinition.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagcontainer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\comparisonutility.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagcontainer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayeffecttypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\gameplayprediction.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\classes\\net\\serialization\\fastarrayserializer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\public\\net\\core\\misc\\guidreferences.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\public\\net\\core\\pushmodel\\pushmodel.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\public\\net\\core\\netcoremodule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\netcore\\uht\\fastarrayserializer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayprediction.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\source\\gameplayabilities\\public\\scalablefloat.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\dataregistry\\source\\dataregistry\\public\\dataregistryid.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\dataregistry\\intermediate\\build\\win64\\unrealeditor\\inc\\dataregistry\\uht\\dataregistryid.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\scalablefloat.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\gameplayabilities\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplayabilities\\uht\\gameplayabilityspec.generated.h", "c:\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracron\\uht\\auracronstructs.generated.h", "c:\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracron\\uht\\auracronpcgtypes.generated.h", "c:\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracron\\uht\\auracronpcgutility.gen.cpp", "c:\\auracron\\source\\auracron\\public\\pcg\\auracronpcgutility.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\noexporttypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\propertyaccessutil.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\serialization\\testundeclaredscriptstructobjectreferences.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\math\\unitconversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\math\\unitconversion.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\polyglottextdata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystatics.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetsystemlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\kismetsystemlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\dialoguetypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\dialoguetypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystaticstypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystaticstypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystatics.generated.h", "c:\\auracron\\source\\auracron\\public\\pcg\\auracronmapmeasurements.h", "c:\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracron\\uht\\auracronmapmeasurements.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\streamablemanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\iostore\\ondemandcore\\public\\io\\iostoreondemand.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\sharedstring.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\iostore\\ondemandcore\\public\\io\\ondemandhostgroup.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\iostore\\ondemandcore\\public\\io\\ondemandtoc.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\io\\iocontainerid.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\misc\\packageaccesstracking.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\sourcelocationutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\experimental\\streamablemanagererror.h", "c:\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracron\\uht\\auracronpcgutility.generated.h", "c:\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracron\\uht\\auracronpcgworldpartitionintegration.gen.cpp", "c:\\auracron\\source\\auracron\\public\\pcg\\auracronpcgworldpartitionintegration.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartition.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionlog.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionactordesc.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionactorcontainerid.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitionactorcontainerid.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\filter\\worldpartitionactorfilter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitionactorfilter.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionhandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\actordesccontainerinstancecollection.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\actordesccontainerinstance.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionactordescinstance.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayerinstancenames.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\datalayerinstancenames.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\actordesclist.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\actordesccontainerinstance.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\cook\\worldpartitioncookpackagegenerator.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionstreaminggeneration.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\actordesccontainercollection.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\actordesccontainer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\actordesccontainerinitparams.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\actordesccontainer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionactordescinstanceview.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionactordescinstanceviewinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionactorloaderinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitionactorloaderinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitioneditorloaderadapter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitioneditorloaderadapter.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionruntimecelltransformer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitionruntimecelltransformer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\actorreferencesutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\externaldirtyactorstracker.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\packagesourcecontrolhelper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\sourcecontrol\\public\\sourcecontrolhelpers.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\sourcecontrol\\public\\isourcecontrolprovider.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\sourcecontrol\\public\\isourcecontrolchangelist.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\sourcecontrol\\public\\isourcecontrolchangeliststate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\sourcecontrol\\public\\isourcecontrolstate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\sourcecontrol\\public\\isourcecontroloperation.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\sourcecontrol\\public\\sourcecontrolresultinfo.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\sourcecontrol\\public\\isourcecontrolrevision.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\sourcecontrol\\uht\\sourcecontrolhelpers.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\cookpackagesplitter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\assetregistry\\public\\assetregistry\\iassetregistry.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\misc\\assetregistryinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\assetregistry\\uht\\iassetregistry.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartition.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionsubsystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\streaming\\streamingworldsubsysteminterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\streamingworldsubsysteminterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitionsubsystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayersubsystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\actordatalayer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayerinstance.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayertype.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\datalayertype.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\errorhandling\\worldpartitionstreaminggenerationerrorhandler.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\datalayerinstance.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\actordatalayer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayermanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionhelpers.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\worldpartitionruntimecellinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worldpartitionruntimecellinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayereditorcontext.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\worlddatalayers.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\datalayer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayerinstanceproviderinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\datalayerinstanceproviderinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\externaldatalayerasset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayerasset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\datalayerasset.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\externaldatalayeruid.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\externaldatalayeruid.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\externaldatalayerasset.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\externalpackagehelper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\assetregistry\\public\\assetregistry\\assetregistrymodule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\archivemd5.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\externaldatalayerinstance.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayerinstancewithasset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\datalayerinstancewithasset.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\externaldatalayerinstance.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\externaldatalayerhelper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\coreredirects.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\internal\\uobject\\coreredirects\\pm-k.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\worlddatalayers.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\datalayermanager.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\datalayersubsystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\loaderadapter\\loaderadaptershape.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\loaderadapter\\loaderadapterspatial.h", "c:\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracron\\uht\\auracronpcgworldpartitionintegration.generated.h", "c:\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracron\\uht\\auracronsigilcomponent.gen.cpp", "c:\\auracron\\source\\auracron\\public\\components\\auracronsigilcomponent.h", "c:\\auracron\\intermediate\\build\\win64\\unrealeditor\\inc\\auracron\\uht\\auracronsigilcomponent.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}