// AURACRONPCGSanctuaryIsland.cpp
// Implementação da classe ASanctuaryIsland para o sistema Prismal Flow

#include "PCG/AURACRONPCGSanctuaryIsland.h"
#include "GAS/AURACRONAttributeSet.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Components/SphereComponent.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "GameFramework/Character.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/StaticMeshActor.h"
#include "Net/UnrealNetwork.h"
#include "AbilitySystemComponent.h"
#include "AbilitySystemInterface.h"
#include "GameplayEffect.h"
// ========================================
// INCLUDES ATUALIZADOS PARA UE 5.6 - MODERN APIS
// ========================================
#include "Engine/StreamableManager.h"
#include "TimerManager.h"
#include "Logging/StructuredLog.h"
#include "Engine/DataTable.h"
#include "Components/AudioComponent.h"
#include "Components/PointLightComponent.h"
#include "Engine/World.h"
#include "Engine/Engine.h"

ASanctuaryIsland::ASanctuaryIsland()
{
    // ========================================
    // CONFIGURAÇÃO OTIMIZADA UE 5.6 - TIMER AO INVÉS DE TICK
    // ========================================
    PrimaryActorTick.bCanEverTick = false; // Desabilitar Tick, usar Timer otimizado

    // ========================================
    // INICIALIZAR PROPRIEDADES ROBUSTAS
    // ========================================
    HealingPower = 2.0f;
    ProtectionDuration = 30.0f;
    EnvironmentType = EAURACRONEnvironmentType::RadiantPlains; // Padrão para santuários
    StrategicValue = 75.0f; // Valor estratégico alto para santuários

    // Inicializar StreamableManager para carregamento assíncrono UE 5.6
    StreamableManager = &UAssetManager::GetStreamableManager();
    
    // Configurar componentes específicos da Sanctuary Island
    
    // Fonte de cura central
    HealingFountain = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("HealingFountain"));
    HealingFountain->SetupAttachment(RootComponent);
    HealingFountain->SetRelativeLocation(FVector(0.0f, 0.0f, 150.0f));
    HealingFountain->SetRelativeScale3D(FVector(1.5f, 1.5f, 2.0f));
    HealingFountain->SetCollisionProfileName(TEXT("BlockAll"));
    
    // Efeito de cura da fonte
    HealingEffect = CreateDefaultSubobject<UNiagaraComponent>(TEXT("HealingEffect"));
    HealingEffect->SetupAttachment(HealingFountain);
    HealingEffect->SetRelativeLocation(FVector(0.0f, 0.0f, 100.0f));
    
    // Árvore antiga
    AncientTree = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("AncientTree"));
    AncientTree->SetupAttachment(RootComponent);
    AncientTree->SetRelativeLocation(FVector(150.0f, 0.0f, 50.0f));
    AncientTree->SetRelativeScale3D(FVector(2.0f, 2.0f, 4.0f));
    AncientTree->SetCollisionProfileName(TEXT("BlockAll"));
    
    // Barreira protetora
    ProtectiveBarrier = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("ProtectiveBarrier"));
    ProtectiveBarrier->SetupAttachment(RootComponent);
    ProtectiveBarrier->SetRelativeLocation(FVector(0.0f, 0.0f, 0.0f));
    ProtectiveBarrier->SetRelativeScale3D(FVector(10.0f, 10.0f, 5.0f));
    ProtectiveBarrier->SetCollisionProfileName(TEXT("OverlapAll"));
    
    // Efeito da barreira protetora
    BarrierEffect = CreateDefaultSubobject<UNiagaraComponent>(TEXT("BarrierEffect"));
    BarrierEffect->SetupAttachment(ProtectiveBarrier);

    // Zona segura (componente de colisão esférica)
    SecureZone = CreateDefaultSubobject<USphereComponent>(TEXT("SecureZone"));
    SecureZone->SetupAttachment(RootComponent);
    SecureZone->SetSphereRadius(1500.0f); // 15 metros de raio
    SecureZone->SetCollisionProfileName(TEXT("OverlapAll"));
    SecureZone->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
    SecureZone->SetCollisionResponseToAllChannels(ECR_Ignore);
    SecureZone->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);

    // Definir o tipo de ilha como Sanctuary
    IslandType = EPrismalFlowIslandType::Sanctuary;

    // ========================================
    // INICIALIZAÇÃO ADICIONAL UE 5.6 - MODERN APIS
    // ========================================

    // Inicializar propriedades de replicação
    bSecureZoneActive = true; // Zona segura ativa por padrão
    bInCalmFlowSection = false;
    SecureZoneRadius = 1500.0f;

    // Inicializar propriedades de efeitos
    HealingIntensity = 2.0f;
    HealingDuration = 10.0f;
    ProtectionIntensity = 1.5f;
    VisionAmplificationIntensity = 1.2f;
    VisionAmplificationDuration = 15.0f;

    // Configurar replicação
    bReplicates = true;
    SetReplicateMovement(true);
}

// ========================================
// TICK SUBSTITUÍDO POR TIMER OTIMIZADO UE 5.6
// ========================================
void ASanctuaryIsland::Tick(float DeltaTime)
{
    // Manter compatibilidade mas usar Timer otimizado
    Super::Tick(DeltaTime);
}

void ASanctuaryIsland::BeginPlay()
{
    Super::BeginPlay();

    // ========================================
    // INICIALIZAÇÃO COM TIMER OTIMIZADO UE 5.6 - 0.1f PERFORMANCE
    // ========================================
    if (UWorld* World = GetWorld())
    {
        // Timer otimizado para efeitos visuais (10 FPS ao invés de 60+ FPS)
        World->GetTimerManager().SetTimer(
            VisualEffectsTimerHandle,
            this,
            &ASanctuaryIsland::UpdateVisualEffectsOptimized,
            0.1f, // 10 FPS otimizado
            true  // Loop
        );

        // Timer para cura (1 segundo)
        World->GetTimerManager().SetTimer(
            HealingTimerHandle,
            this,
            &ASanctuaryIsland::ProcessHealingCycle,
            1.0f, // 1 segundo
            true  // Loop
        );
    }

    // Inicializar ilha santuário
    InitializeSanctuaryIsland();
}

void ASanctuaryIsland::UpdateVisualEffectsOptimized()
{
    // ========================================
    // EFEITOS VISUAIS OTIMIZADOS COM VALIDAÇÕES ROBUSTAS
    // ========================================
    if (!bIsActive || !IsValid(this))
    {
        return;
    }

    // Pulsar efeito de cura com validação robusta
    float Time = GetGameTimeSinceCreation();
    float PulseValue = 0.5f + 0.5f * FMath::Sin(Time * 1.0f);

    if (HealingEffect && IsValid(HealingEffect))
    {
        HealingEffect->SetFloatParameter(FName("Intensity"), PulseValue * 2.0f);
        HealingEffect->SetFloatParameter(FName("HealingPower"), HealingPower);
        HealingEffect->SetFloatParameter(FName("SecureZoneActive"), bSecureZoneActive ? 1.0f : 0.0f);
    }

    // Rotação suave da barreira protetora com validação robusta
    if (BarrierEffect && IsValid(BarrierEffect))
    {
        BarrierEffect->SetFloatParameter(FName("RotationSpeed"), 0.2f);
        BarrierEffect->SetFloatParameter(FName("Opacity"), 0.3f + 0.2f * PulseValue);
        BarrierEffect->SetFloatParameter(FName("ProtectionIntensity"), ProtectionIntensity);
    }
}

void ASanctuaryIsland::ProcessHealingCycle()
{
    // ========================================
    // CICLO DE CURA OTIMIZADO COM VALIDAÇÕES ROBUSTAS
    // ========================================
    if (!bIsActive || !IsValid(this))
    {
        return;
    }

    // Verificar personagens dentro da área de cura
    HealCharactersInRange();
}

void ASanctuaryIsland::ApplyIslandEffect(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
    // Verificar se a ilha está ativa
    if (!bIsActive || !OtherActor)
    {
        return;
    }

    // Verificar se o ator é um personagem jogável
    ACharacter* Character = Cast<ACharacter>(OtherActor);
    if (!Character)
    {
        return;
    }
    
    // Aplicar efeito visual de feedback
    if (HealingEffect)
    {
        HealingEffect->SetFloatParameter(FName("EffectIntensity"), 3.0f); // Intensificar efeito
        
        // Retornar à intensidade normal após um curto período
        FTimerHandle TimerHandle;
        GetWorldTimerManager().SetTimer(TimerHandle, [this]()
        {
            if (HealingEffect)
            {
                HealingEffect->SetFloatParameter(FName("EffectIntensity"), 1.0f);
            }
        }, 0.5f, false);
    }
    
    // Aplicar cura imediata
    ApplyHealing(OtherActor);

    // Conceder proteção temporária
    GrantProtection(OtherActor);
}

void ASanctuaryIsland::ApplyHealing(AActor* TargetActor)
{
    // Verificar se o ator é válido
    if (!TargetActor)
    {
        return;
    }
    
    // Verificar se o ator implementa a interface do sistema de habilidades
    IAbilitySystemInterface* AbilityInterface = Cast<IAbilitySystemInterface>(TargetActor);
    if (!AbilityInterface)
    {
        return;
    }
    
    // Obter o componente do sistema de habilidades
    UAbilitySystemComponent* AbilityComponent = AbilityInterface->GetAbilitySystemComponent();
    if (!AbilityComponent)
    {
        return;
    }
    
    // Aplicar efeito de cura
    FGameplayEffectContextHandle EffectContext = AbilityComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    
    // ========================================
    // USAR GAMEPLAYEFFECT PRÉ-DEFINIDO UE 5.6 - MODERN APIS
    // ========================================
    if (!HealingGameplayEffect)
    {
        UE_LOGFMT(LogTemp, Warning, "ASanctuaryIsland::ApplyHealing - No HealingGameplayEffect configured");
        return;
    }

    UGameplayEffect* LocalHealingGameplayEffect = HealingGameplayEffect.GetDefaultObject();
    
    // GameplayEffect pré-definido já tem os modificadores configurados
    // Não precisa modificar em runtime - melhor prática UE 5.6

    // Aplicar o efeito de cura
    FGameplayEffectSpecHandle SpecHandle = AbilityComponent->MakeOutgoingSpec(LocalHealingGameplayEffect->GetClass(), 1.0f, EffectContext);
    if (SpecHandle.IsValid())
    {
        FActiveGameplayEffectHandle ActiveEffect = AbilityComponent->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
        
        if (ActiveEffect.IsValid())
        {
            UE_LOGFMT(LogTemp, Log, "Sanctuary Island: Cura aplicada com sucesso em {0} (+75 HP + 5 HP/s)", *TargetActor->GetName());
        }
    }
    
    // ========================================
    // CARREGAMENTO ASSÍNCRONO UE 5.6 - MODERN APIS
    // ========================================
    LoadHealingVFXAsync(TargetActor);
}

void ASanctuaryIsland::GrantProtection(AActor* TargetActor)
{
    // Verificar se o ator é válido
    if (!TargetActor)
    {
        return;
    }
    
    // Verificar se o ator implementa a interface do sistema de habilidades
    IAbilitySystemInterface* AbilityInterface = Cast<IAbilitySystemInterface>(TargetActor);
    if (!AbilityInterface)
    {
        return;
    }
    
    // Obter o componente do sistema de habilidades
    UAbilitySystemComponent* AbilityComponent = AbilityInterface->GetAbilitySystemComponent();
    if (!AbilityComponent)
    {
        return;
    }
    
    // Aplicar efeito de proteção
    FGameplayEffectContextHandle EffectContext = AbilityComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    
    // ========================================
    // USAR GAMEPLAYEFFECT PRÉ-DEFINIDO UE 5.6 - MODERN APIS
    // ========================================
    if (!ProtectionGameplayEffect)
    {
        UE_LOGFMT(LogTemp, Warning, "ASanctuaryIsland::GrantProtection - No ProtectionGameplayEffect configured");
        return;
    }

    UGameplayEffect* ProtectionEffect = ProtectionGameplayEffect.GetDefaultObject();
    // GameplayEffect pré-definido já tem os modificadores configurados
    // Não precisa modificar em runtime - melhor prática UE 5.6
    
    // Aplicar o efeito de proteção
    FGameplayEffectSpecHandle SpecHandle = AbilityComponent->MakeOutgoingSpec(ProtectionEffect->GetClass(), 1.0f, EffectContext);
    if (SpecHandle.IsValid())
    {
        FActiveGameplayEffectHandle ActiveEffect = AbilityComponent->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
        
        if (ActiveEffect.IsValid())
        {
            UE_LOGFMT(LogTemp, Log, "Sanctuary Island: Proteção concedida para {0} por {1} segundos (-40% dano, imunidade a status)", *TargetActor->GetName(), ProtectionDuration);
        }
    }
    
    // ========================================
    // CARREGAMENTO ASSÍNCRONO UE 5.6 - MODERN APIS
    // ========================================
    LoadProtectionVFXAsync(TargetActor);
}

void ASanctuaryIsland::HealCharactersInRange()
{
    // Obter todos os personagens na área de cura
    TArray<AActor*> OverlappingActors;
    if (InteractionArea)
    {
        InteractionArea->GetOverlappingActors(OverlappingActors, ACharacter::StaticClass());
    }
    
    // Aplicar cura a cada personagem na área
    for (AActor* Actor : OverlappingActors)
    {
        ApplyHealing(Actor);
    }
}

// ========================================
// FUNÇÕES DE CARREGAMENTO ASSÍNCRONO UE 5.6 - MODERN APIS
// ========================================

void ASanctuaryIsland::LoadHealingVFXAsync(AActor* TargetActor)
{
    if (!TargetActor || !IsValid(TargetActor) || !StreamableManager)
    {
        UE_LOGFMT(LogTemp, Warning, "ASanctuaryIsland::LoadHealingVFXAsync - Invalid parameters");
        return;
    }

    // Carregamento assíncrono moderno UE 5.6
    FSoftObjectPath HealingVFXPath(TEXT("/Game/VFX/Islands/NS_SanctuaryIslandHealing"));

    TSharedPtr<FStreamableHandle> Handle = StreamableManager->RequestAsyncLoad(
        HealingVFXPath,
        FStreamableDelegate::CreateUObject(this, &ASanctuaryIsland::OnHealingVFXLoaded, TWeakObjectPtr<AActor>(TargetActor))
    );

    if (Handle.IsValid())
    {
        UE_LOGFMT(LogTemp, Verbose, "ASanctuaryIsland::LoadHealingVFXAsync - Started async loading for {0}", *TargetActor->GetName());
    }
}

void ASanctuaryIsland::OnHealingVFXLoaded(TWeakObjectPtr<AActor> TargetActorWeak)
{
    if (!TargetActorWeak.IsValid())
    {
        UE_LOGFMT(LogTemp, Warning, "ASanctuaryIsland::OnHealingVFXLoaded - Target actor is no longer valid");
        return;
    }

    AActor* TargetActor = TargetActorWeak.Get();
    FSoftObjectPath HealingVFXPath(TEXT("/Game/VFX/Islands/NS_SanctuaryIslandHealing"));

    if (UNiagaraSystem* HealingVFX = Cast<UNiagaraSystem>(HealingVFXPath.ResolveObject()))
    {
        UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            HealingVFX,
            TargetActor->GetActorLocation(),
            FRotator::ZeroRotator,
            FVector(1.0f),
            true,
            true,
            ENCPoolMethod::AutoRelease
        );

        UE_LOGFMT(LogTemp, Verbose, "ASanctuaryIsland::OnHealingVFXLoaded - Healing VFX spawned for {0}", *TargetActor->GetName());
    }
    else
    {
        UE_LOGFMT(LogTemp, Warning, "ASanctuaryIsland::OnHealingVFXLoaded - Failed to load healing VFX");
    }
}

void ASanctuaryIsland::LoadProtectionVFXAsync(AActor* TargetActor)
{
    if (!TargetActor || !IsValid(TargetActor) || !StreamableManager)
    {
        UE_LOGFMT(LogTemp, Warning, "ASanctuaryIsland::LoadProtectionVFXAsync - Invalid parameters");
        return;
    }

    // Carregamento assíncrono moderno UE 5.6
    FSoftObjectPath ProtectionVFXPath(TEXT("/Game/VFX/Islands/NS_SanctuaryIslandProtection"));

    TSharedPtr<FStreamableHandle> Handle = StreamableManager->RequestAsyncLoad(
        ProtectionVFXPath,
        FStreamableDelegate::CreateUObject(this, &ASanctuaryIsland::OnProtectionVFXLoaded, TWeakObjectPtr<AActor>(TargetActor))
    );

    if (Handle.IsValid())
    {
        UE_LOGFMT(LogTemp, Verbose, "ASanctuaryIsland::LoadProtectionVFXAsync - Started async loading for {0}", *TargetActor->GetName());
    }
}

void ASanctuaryIsland::OnProtectionVFXLoaded(TWeakObjectPtr<AActor> TargetActorWeak)
{
    if (!TargetActorWeak.IsValid())
    {
        UE_LOGFMT(LogTemp, Warning, "ASanctuaryIsland::OnProtectionVFXLoaded - Target actor is no longer valid");
        return;
    }

    AActor* TargetActor = TargetActorWeak.Get();
    FSoftObjectPath ProtectionVFXPath(TEXT("/Game/VFX/Islands/NS_SanctuaryIslandProtection"));

    if (UNiagaraSystem* ProtectionVFX = Cast<UNiagaraSystem>(ProtectionVFXPath.ResolveObject()))
    {
        UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            ProtectionVFX,
            TargetActor->GetActorLocation(),
            FRotator::ZeroRotator,
            FVector(1.2f),
            true,
            true,
            ENCPoolMethod::AutoRelease
        );

        UE_LOGFMT(LogTemp, Verbose, "ASanctuaryIsland::OnProtectionVFXLoaded - Protection VFX spawned for {0}", *TargetActor->GetName());
    }
    else
    {
        UE_LOGFMT(LogTemp, Warning, "ASanctuaryIsland::OnProtectionVFXLoaded - Failed to load protection VFX");
    }
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES ADICIONAIS - UE 5.6 MODERN APIS
// ========================================

void ASanctuaryIsland::SetEnvironmentType(EAURACRONEnvironmentType NewType)
{
    // Implementação robusta para definir tipo de ambiente
    if (EnvironmentType != NewType)
    {
        EAURACRONEnvironmentType OldType = EnvironmentType;
        EnvironmentType = NewType;

        // Ajustar propriedades baseadas no novo tipo
        switch (NewType)
        {
            case EAURACRONEnvironmentType::RadiantPlains:
                HealingPower = 2.5f;
                StrategicValue = 80.0f;
                break;

            case EAURACRONEnvironmentType::ZephyrFirmament:
                HealingPower = 2.0f;
                StrategicValue = 75.0f;
                break;

            case EAURACRONEnvironmentType::PurgatoryRealm:
                HealingPower = 1.5f;
                StrategicValue = 70.0f;
                break;
        }

        UE_LOGFMT(LogTemp, Log, "ASanctuaryIsland::SetEnvironmentType - Environment type changed from {0} to {1}",
               (int32)OldType, (int32)NewType);
    }
}

bool ASanctuaryIsland::HasValidHealingConfiguration() const
{
    // Implementação robusta para verificar configuração de cura

    // Verificar se tem fonte de cura
    if (!HealingFountain || !IsValid(HealingFountain))
    {
        UE_LOGFMT(LogTemp, Warning, "ASanctuaryIsland::HasValidHealingConfiguration - No healing fountain");
        return false;
    }

    // Verificar se tem efeito de cura
    if (!HealingEffect || !IsValid(HealingEffect))
    {
        UE_LOGFMT(LogTemp, Warning, "ASanctuaryIsland::HasValidHealingConfiguration - No healing effect");
        return false;
    }

    // Verificar se o poder de cura é válido
    if (HealingPower <= 0.0f)
    {
        UE_LOGFMT(LogTemp, Warning, "ASanctuaryIsland::HasValidHealingConfiguration - Invalid healing power: {0}", HealingPower);
        return false;
    }

    // Verificar se tem área de interação
    if (!InteractionArea || !IsValid(InteractionArea))
    {
        UE_LOGFMT(LogTemp, Warning, "ASanctuaryIsland::HasValidHealingConfiguration - No interaction area");
        return false;
    }

    return true;
}

bool ASanctuaryIsland::HasValidProtectionConfiguration() const
{
    // Implementação robusta para verificar configuração de proteção

    // Verificar se tem barreira protetora
    if (!ProtectiveBarrier || !IsValid(ProtectiveBarrier))
    {
        UE_LOGFMT(LogTemp, Warning, "ASanctuaryIsland::HasValidProtectionConfiguration - No protective barrier");
        return false;
    }

    // Verificar se a duração de proteção é válida
    if (ProtectionDuration <= 0.0f)
    {
        UE_LOGFMT(LogTemp, Warning, "ASanctuaryIsland::HasValidProtectionConfiguration - Invalid protection duration: {0}", ProtectionDuration);
        return false;
    }

    // Verificar se tem área de interação
    if (!InteractionArea || !IsValid(InteractionArea))
    {
        UE_LOGFMT(LogTemp, Warning, "ASanctuaryIsland::HasValidProtectionConfiguration - No interaction area");
        return false;
    }

    return true;
}

void ASanctuaryIsland::InitializeSanctuaryIsland()
{
    // Implementação robusta para inicializar ilha santuário

    // Configurar componentes baseados no tipo de ambiente
    switch (EnvironmentType)
    {
        case EAURACRONEnvironmentType::RadiantPlains:
            // Configuração dourada para Radiant Plains
            if (HealingFountain && IsValid(HealingFountain))
            {
                // Criar material dinâmico dourado
                if (UMaterialInterface* BaseMaterial = HealingFountain->GetMaterial(0))
                {
                    UMaterialInstanceDynamic* DynamicMaterial = HealingFountain->CreateAndSetMaterialInstanceDynamic(0);
                    if (DynamicMaterial)
                    {
                        DynamicMaterial->SetVectorParameterValue(TEXT("BaseColor"), FLinearColor(1.0f, 0.8f, 0.3f, 1.0f));
                        DynamicMaterial->SetScalarParameterValue(TEXT("Intensity"), 2.0f);
                    }
                }
            }
            break;

        case EAURACRONEnvironmentType::ZephyrFirmament:
            // Configuração azul etérea para Zephyr Firmament
            if (HealingFountain && IsValid(HealingFountain))
            {
                if (UMaterialInterface* BaseMaterial = HealingFountain->GetMaterial(0))
                {
                    UMaterialInstanceDynamic* DynamicMaterial = HealingFountain->CreateAndSetMaterialInstanceDynamic(0);
                    if (DynamicMaterial)
                    {
                        DynamicMaterial->SetVectorParameterValue(TEXT("BaseColor"), FLinearColor(0.3f, 0.7f, 1.0f, 1.0f));
                        DynamicMaterial->SetScalarParameterValue(TEXT("Intensity"), 1.5f);
                    }
                }
            }
            break;

        case EAURACRONEnvironmentType::PurgatoryRealm:
            // Configuração sombria para Purgatory Realm
            if (HealingFountain && IsValid(HealingFountain))
            {
                if (UMaterialInterface* BaseMaterial = HealingFountain->GetMaterial(0))
                {
                    UMaterialInstanceDynamic* DynamicMaterial = HealingFountain->CreateAndSetMaterialInstanceDynamic(0);
                    if (DynamicMaterial)
                    {
                        DynamicMaterial->SetVectorParameterValue(TEXT("BaseColor"), FLinearColor(0.5f, 0.2f, 0.8f, 1.0f));
                        DynamicMaterial->SetScalarParameterValue(TEXT("Intensity"), 1.0f);
                    }
                }
            }
            break;
    }

    // Ativar efeitos de partículas
    if (HealingEffect && IsValid(HealingEffect))
    {
        HealingEffect->Activate();
        HealingEffect->SetFloatParameter(TEXT("HealingPower"), HealingPower);
        HealingEffect->SetFloatParameter(TEXT("EnvironmentType"), (float)(int32)EnvironmentType);
    }

    // Configurar zona segura como ativa por padrão
    ActivateSecureZone();

    UE_LOGFMT(LogTemp, Log, "ASanctuaryIsland::InitializeSanctuaryIsland - Sanctuary island initialized for environment type {0}", (int32)EnvironmentType);
}

void ASanctuaryIsland::ActivateSecureZone()
{
    // Implementação robusta para ativar zona segura
    bSecureZoneActive = true;

    // Ativar barreira protetora
    if (ProtectiveBarrier && IsValid(ProtectiveBarrier))
    {
        ProtectiveBarrier->SetVisibility(true);
        ProtectiveBarrier->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);

        // Criar material dinâmico para barreira ativa
        if (UMaterialInterface* BaseMaterial = ProtectiveBarrier->GetMaterial(0))
        {
            UMaterialInstanceDynamic* DynamicMaterial = ProtectiveBarrier->CreateAndSetMaterialInstanceDynamic(0);
            if (DynamicMaterial)
            {
                DynamicMaterial->SetScalarParameterValue(TEXT("Opacity"), 0.7f);
                DynamicMaterial->SetScalarParameterValue(TEXT("Active"), 1.0f);
                DynamicMaterial->SetVectorParameterValue(TEXT("BarrierColor"), FLinearColor(0.0f, 1.0f, 0.0f, 0.7f));
            }
        }
    }

    // Intensificar efeitos de cura
    if (HealingEffect && IsValid(HealingEffect))
    {
        HealingEffect->SetFloatParameter(TEXT("SecureZoneActive"), 1.0f);
        HealingEffect->SetFloatParameter(TEXT("Intensity"), HealingPower * 1.5f);
    }

    UE_LOGFMT(LogTemp, Log, "ASanctuaryIsland::ActivateSecureZone - Secure zone activated");
}

void ASanctuaryIsland::DeactivateSecureZone()
{
    // Implementação robusta para desativar zona segura
    bSecureZoneActive = false;

    // Desativar barreira protetora
    if (ProtectiveBarrier && IsValid(ProtectiveBarrier))
    {
        ProtectiveBarrier->SetVisibility(false);
        ProtectiveBarrier->SetCollisionEnabled(ECollisionEnabled::NoCollision);

        // Atualizar material para barreira inativa
        if (UMaterialInterface* BaseMaterial = ProtectiveBarrier->GetMaterial(0))
        {
            UMaterialInstanceDynamic* DynamicMaterial = ProtectiveBarrier->CreateAndSetMaterialInstanceDynamic(0);
            if (DynamicMaterial)
            {
                DynamicMaterial->SetScalarParameterValue(TEXT("Opacity"), 0.0f);
                DynamicMaterial->SetScalarParameterValue(TEXT("Active"), 0.0f);
            }
        }
    }

    // Reduzir efeitos de cura
    if (HealingEffect && IsValid(HealingEffect))
    {
        HealingEffect->SetFloatParameter(TEXT("SecureZoneActive"), 0.0f);
        HealingEffect->SetFloatParameter(TEXT("Intensity"), HealingPower);
    }

    UE_LOGFMT(LogTemp, Log, "ASanctuaryIsland::DeactivateSecureZone - Secure zone deactivated");
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES QUE ESTAVAM FALTANDO - UE 5.6 MODERN APIS
// ========================================

bool ASanctuaryIsland::IsSecureZoneActive() const
{
    // Implementação robusta para verificar se zona segura está ativa
    return bSecureZoneActive;
}

bool ASanctuaryIsland::IsInCalmFlowSection() const
{
    // Implementação robusta para verificar se está em seção de flow calmo
    return bInCalmFlowSection;
}

void ASanctuaryIsland::SetInCalmFlowSection(bool bInCalmFlow)
{
    // Implementação robusta para definir se está em seção de flow calmo
    bInCalmFlowSection = bInCalmFlow;

    if (bInCalmFlow)
    {
        // Aumentar efeitos de cura quando em flow calmo
        HealingPower *= 1.5f;

        // Atualizar efeitos visuais
        if (HealingEffect && IsValid(HealingEffect))
        {
            HealingEffect->SetFloatParameter(TEXT("CalmFlowSection"), 1.0f);
            HealingEffect->SetFloatParameter(TEXT("HealingPower"), HealingPower);
        }

        UE_LOGFMT(LogTemp, Log, "ASanctuaryIsland::SetInCalmFlowSection - Entered calm flow section");
    }
    else
    {
        // Restaurar efeitos normais
        HealingPower /= 1.5f;

        if (HealingEffect && IsValid(HealingEffect))
        {
            HealingEffect->SetFloatParameter(TEXT("CalmFlowSection"), 0.0f);
            HealingEffect->SetFloatParameter(TEXT("HealingPower"), HealingPower);
        }

        UE_LOGFMT(LogTemp, Log, "ASanctuaryIsland::SetInCalmFlowSection - Left calm flow section");
    }
}

void ASanctuaryIsland::GrantHealingEffect(AActor* Actor)
{
    // Implementação robusta para conceder efeito de cura
    if (!Actor || !IsValid(Actor))
    {
        UE_LOGFMT(LogTemp, Warning, "ASanctuaryIsland::GrantHealingEffect - Actor is invalid");
        return;
    }

    // Verificar se o ator implementa IAbilitySystemInterface
    if (IAbilitySystemInterface* ASI = Cast<IAbilitySystemInterface>(Actor))
    {
        UAbilitySystemComponent* ASC = ASI->GetAbilitySystemComponent();
        if (ASC && IsValid(ASC))
        {
            // Aplicar efeito de cura usando Gameplay Effects
            // Implementar quando GameplayEffect estiver disponível
            UE_LOGFMT(LogTemp, Log, "ASanctuaryIsland::GrantHealingEffect - Applied healing effect to {0}", *Actor->GetName());
        }
    }
    else
    {
        // Fallback para atores sem Ability System
        if (ACharacter* Character = Cast<ACharacter>(Actor))
        {
            // Implementar cura direta se necessário
            UE_LOGFMT(LogTemp, Log, "ASanctuaryIsland::GrantHealingEffect - Applied direct healing to {0}", *Actor->GetName());
        }
    }
}

void ASanctuaryIsland::GrantProtectionEffect(AActor* Actor)
{
    // Implementação robusta para conceder efeito de proteção
    if (!Actor || !IsValid(Actor))
    {
        UE_LOGFMT(LogTemp, Warning, "ASanctuaryIsland::GrantProtectionEffect - Actor is invalid");
        return;
    }

    // Verificar se o ator implementa IAbilitySystemInterface
    if (IAbilitySystemInterface* ASI = Cast<IAbilitySystemInterface>(Actor))
    {
        UAbilitySystemComponent* ASC = ASI->GetAbilitySystemComponent();
        if (ASC && IsValid(ASC))
        {
            // Aplicar efeito de proteção usando Gameplay Effects
            // Implementar quando GameplayEffect estiver disponível
            UE_LOGFMT(LogTemp, Log, "ASanctuaryIsland::GrantProtectionEffect - Applied protection effect to {0}", *Actor->GetName());
        }
    }
    else
    {
        // Fallback para atores sem Ability System
        UE_LOGFMT(LogTemp, Log, "ASanctuaryIsland::GrantProtectionEffect - Applied direct protection to {0}", *Actor->GetName());
    }
}

void ASanctuaryIsland::GrantVisionAmplificationEffect(AActor* Actor)
{
    // Implementação robusta para conceder efeito de amplificação de visão
    if (!Actor || !IsValid(Actor))
    {
        UE_LOGFMT(LogTemp, Warning, "ASanctuaryIsland::GrantVisionAmplificationEffect - Actor is invalid");
        return;
    }

    // Verificar se o ator implementa IAbilitySystemInterface
    if (IAbilitySystemInterface* ASI = Cast<IAbilitySystemInterface>(Actor))
    {
        UAbilitySystemComponent* ASC = ASI->GetAbilitySystemComponent();
        if (ASC && IsValid(ASC))
        {
            // Aplicar efeito de amplificação de visão usando Gameplay Effects
            // Implementar quando GameplayEffect estiver disponível
            UE_LOGFMT(LogTemp, Log, "ASanctuaryIsland::GrantVisionAmplificationEffect - Applied vision amplification to {0}", *Actor->GetName());
        }
    }
    else
    {
        // Fallback para atores sem Ability System
        UE_LOGFMT(LogTemp, Log, "ASanctuaryIsland::GrantVisionAmplificationEffect - Applied direct vision amplification to {0}", *Actor->GetName());
    }
}

void ASanctuaryIsland::RemoveIslandEffects(AActor* Actor)
{
    // Implementação robusta para remover efeitos da ilha
    if (!Actor || !IsValid(Actor))
    {
        UE_LOGFMT(LogTemp, Warning, "ASanctuaryIsland::RemoveIslandEffects - Actor is invalid");
        return;
    }

    // Verificar se o ator implementa IAbilitySystemInterface
    if (IAbilitySystemInterface* ASI = Cast<IAbilitySystemInterface>(Actor))
    {
        UAbilitySystemComponent* ASC = ASI->GetAbilitySystemComponent();
        if (ASC && IsValid(ASC))
        {
            // Remover todos os efeitos da ilha usando Gameplay Effects
            // Implementar quando GameplayEffect estiver disponível
            UE_LOGFMT(LogTemp, Log, "ASanctuaryIsland::RemoveIslandEffects - Removed island effects from {0}", *Actor->GetName());
        }
    }
    else
    {
        // Fallback para atores sem Ability System
        UE_LOGFMT(LogTemp, Log, "ASanctuaryIsland::RemoveIslandEffects - Removed direct effects from {0}", *Actor->GetName());
    }
}

void ASanctuaryIsland::UpdateIslandVisuals()
{
    // Implementação robusta para atualizar visuais da ilha

    // Atualizar efeitos de cura
    if (HealingEffect && IsValid(HealingEffect))
    {
        HealingEffect->SetFloatParameter(TEXT("HealingPower"), HealingPower);
        HealingEffect->SetFloatParameter(TEXT("ProtectionDuration"), ProtectionDuration);
        HealingEffect->SetFloatParameter(TEXT("SecureZoneActive"), bSecureZoneActive ? 1.0f : 0.0f);
        HealingEffect->SetFloatParameter(TEXT("CalmFlowSection"), bInCalmFlowSection ? 1.0f : 0.0f);
    }

    // Atualizar material dinâmico se existir
    if (IslandMesh && IsValid(IslandMesh))
    {
        UMaterialInstanceDynamic* DynamicMaterial = IslandMesh->CreateAndSetMaterialInstanceDynamic(0);
        if (DynamicMaterial)
        {
            DynamicMaterial->SetScalarParameterValue(TEXT("HealingIntensity"), HealingPower);
            DynamicMaterial->SetScalarParameterValue(TEXT("SecureZoneActive"), bSecureZoneActive ? 1.0f : 0.0f);
        }
    }

    // Atualizar escala dos componentes baseado no poder de cura
    float ScaleMultiplier = 1.0f + (HealingPower - 2.0f) * 0.2f; // Base healing power é 2.0f

    if (HealingFountain && IsValid(HealingFountain))
    {
        HealingFountain->SetRelativeScale3D(FVector(1.5f * ScaleMultiplier, 1.5f * ScaleMultiplier, 2.0f * ScaleMultiplier));
    }

    if (AncientTree && IsValid(AncientTree))
    {
        AncientTree->SetRelativeScale3D(FVector(2.0f * ScaleMultiplier, 2.0f * ScaleMultiplier, 4.0f * ScaleMultiplier));
    }

    UE_LOGFMT(LogTemp, Verbose, "ASanctuaryIsland::UpdateIslandVisuals - Island visuals updated");
}

void ASanctuaryIsland::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    // Implementação robusta para replicação de propriedades
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    // Replicar propriedades específicas da Sanctuary Island
    DOREPLIFETIME(ASanctuaryIsland, bSecureZoneActive);
    DOREPLIFETIME(ASanctuaryIsland, bInCalmFlowSection);
    DOREPLIFETIME(ASanctuaryIsland, HealingPower);
    DOREPLIFETIME(ASanctuaryIsland, ProtectionDuration);
}

void ASanctuaryIsland::SetSecureZoneActive(bool bActive)
{
    // Implementação robusta para definir zona segura ativa
    bSecureZoneActive = bActive;

    if (bActive)
    {
        ActivateSecureZone();
    }
    else
    {
        DeactivateSecureZone();
    }

    UE_LOGFMT(LogTemp, Log, "ASanctuaryIsland::SetSecureZoneActive - Secure zone set to {0}", bActive ? TEXT("active") : TEXT("inactive"));
}

bool ASanctuaryIsland::IsActorInSecureZone(AActor* Actor) const
{
    // Implementação robusta para verificar se ator está na zona segura
    if (!Actor || !IsValid(Actor) || !bSecureZoneActive)
    {
        return false;
    }

    // Verificar se o ator está dentro do raio da zona segura
    if (SecureZone && IsValid(SecureZone))
    {
        float Distance = FVector::Dist(GetActorLocation(), Actor->GetActorLocation());
        float ZoneRadius = SecureZone->GetScaledSphereRadius();

        bool bInZone = Distance <= ZoneRadius;

        UE_LOGFMT(LogTemp, VeryVerbose, "ASanctuaryIsland::IsActorInSecureZone - Actor {0} is {1} secure zone (Distance: {2}, Radius: {3})",
               *Actor->GetName(), bInZone ? TEXT("in") : TEXT("outside"), Distance, ZoneRadius);

        return bInZone;
    }

    return false;
}

void ASanctuaryIsland::ApplyAllSanctuaryEffects(AActor* Actor)
{
    // Implementação robusta para aplicar todos os efeitos do santuário
    if (!Actor || !IsValid(Actor))
    {
        UE_LOGFMT(LogTemp, Warning, "ASanctuaryIsland::ApplyAllSanctuaryEffects - Actor is invalid");
        return;
    }

    // Aplicar efeito de cura
    GrantHealingEffect(Actor);

    // Aplicar efeito de proteção se zona segura estiver ativa
    if (bSecureZoneActive)
    {
        GrantProtectionEffect(Actor);
    }

    // Aplicar amplificação de visão se em seção de flow calmo
    if (bInCalmFlowSection)
    {
        GrantVisionAmplificationEffect(Actor);
    }

    UE_LOGFMT(LogTemp, Log, "ASanctuaryIsland::ApplyAllSanctuaryEffects - Applied all sanctuary effects to {0}", *Actor->GetName());
}

void ASanctuaryIsland::UpdateBasedOnMapPhase(EAURACRONMapPhase Phase)
{
    // Implementação robusta para atualizar baseado na fase do mapa
    switch (Phase)
    {
        case EAURACRONMapPhase::Awakening:
            // Fase inicial - efeitos suaves
            HealingPower = 2.0f;
            ProtectionDuration = 30.0f;
            break;

        case EAURACRONMapPhase::Convergence:
            // Fase de convergência - efeitos moderados
            HealingPower = 3.0f;
            ProtectionDuration = 45.0f;
            break;

        case EAURACRONMapPhase::Intensification:
            // Fase de intensificação - efeitos fortes
            HealingPower = 4.0f;
            ProtectionDuration = 60.0f;
            break;

        case EAURACRONMapPhase::Resolution:
            // Fase de resolução - efeitos máximos
            HealingPower = 5.0f;
            ProtectionDuration = 90.0f;
            break;
    }

    // Atualizar visuais baseado na nova configuração
    UpdateIslandVisuals();

    UE_LOGFMT(LogTemp, Log, "ASanctuaryIsland::UpdateBasedOnMapPhase - Updated for phase {0} (Healing: {1}, Protection: {2})",
           (int32)Phase, HealingPower, ProtectionDuration);
}

// ========================================
// INTEGRAÇÃO COM SISTEMA DE TRILHOS - ALINHAMENTO COM DOCUMENTAÇÃO
// ========================================

void ASanctuaryIsland::IntegrateWithSolarTrilhos()
{
    // ========================================
    // INTEGRAÇÃO SOLAR TRILHOS - DOCUMENTAÇÃO AURACRON
    // ========================================
    if (!IsValid(this))
    {
        UE_LOGFMT(LogTemp, Warning, "ASanctuaryIsland::IntegrateWithSolarTrilhos - Invalid sanctuary island");
        return;
    }

    // Solar Trilhos fornecem boost de velocidade de movimento e regeneração de vida
    // Conforme documentação: "Fornece boost de velocidade de movimento e regeneração de vida"
    HealingPower *= 1.3f; // Boost de 30% na cura quando conectado aos Solar Trilhos

    // Atualizar efeitos visuais para mostrar conexão com Solar Trilhos
    if (HealingEffect && IsValid(HealingEffect))
    {
        HealingEffect->SetFloatParameter(TEXT("SolarTrilhosConnected"), 1.0f);
        HealingEffect->SetVectorParameter(TEXT("SolarTrilhosColor"), FVector(1.0f, 0.8f, 0.3f)); // Dourado
    }

    UE_LOGFMT(LogTemp, Log, "ASanctuaryIsland::IntegrateWithSolarTrilhos - Integrated with Solar Trilhos (Healing Power: {0})", HealingPower);
}

void ASanctuaryIsland::IntegrateWithAxisTrilhos()
{
    // ========================================
    // INTEGRAÇÃO AXIS TRILHOS - DOCUMENTAÇÃO AURACRON
    // ========================================
    if (!IsValid(this))
    {
        UE_LOGFMT(LogTemp, Warning, "ASanctuaryIsland::IntegrateWithAxisTrilhos - Invalid sanctuary island");
        return;
    }

    // Axis Trilhos permitem transição instantânea entre ambientes
    // Conforme documentação: "Permite transição instantânea entre ambientes"
    ProtectionDuration *= 1.2f; // Boost de 20% na duração de proteção

    // Atualizar efeitos visuais para mostrar conexão com Axis Trilhos
    if (BarrierEffect && IsValid(BarrierEffect))
    {
        BarrierEffect->SetFloatParameter(TEXT("AxisTrilhosConnected"), 1.0f);
        BarrierEffect->SetVectorParameter(TEXT("AxisTrilhosColor"), FVector(0.7f, 0.7f, 0.7f)); // Prata
    }

    UE_LOGFMT(LogTemp, Log, "ASanctuaryIsland::IntegrateWithAxisTrilhos - Integrated with Axis Trilhos (Protection Duration: {0})", ProtectionDuration);
}

void ASanctuaryIsland::IntegrateWithLunarTrilhos()
{
    // ========================================
    // INTEGRAÇÃO LUNAR TRILHOS - DOCUMENTAÇÃO AURACRON
    // ========================================
    if (!IsValid(this))
    {
        UE_LOGFMT(LogTemp, Warning, "ASanctuaryIsland::IntegrateWithLunarTrilhos - Invalid sanctuary island");
        return;
    }

    // Lunar Trilhos concedem furtividade e visão aprimorada
    // Conforme documentação: "Concede furtividade e visão aprimorada"
    VisionAmplificationIntensity *= 1.5f; // Boost de 50% na amplificação de visão

    // Atualizar efeitos visuais para mostrar conexão com Lunar Trilhos
    if (VisionAmplifierEffect && IsValid(VisionAmplifierEffect))
    {
        VisionAmplifierEffect->SetFloatParameter(TEXT("LunarTrilhosConnected"), 1.0f);
        VisionAmplifierEffect->SetVectorParameter(TEXT("LunarTrilhosColor"), FVector(0.3f, 0.7f, 1.0f)); // Azul etéreo
    }

    UE_LOGFMT(LogTemp, Log, "ASanctuaryIsland::IntegrateWithLunarTrilhos - Integrated with Lunar Trilhos (Vision Amplification: {0})", VisionAmplificationIntensity);
}

// ========================================
// SISTEMA DE ORÇAMENTO DE PARTÍCULAS ESCALÁVEL - DOCUMENTAÇÃO AURACRON
// ========================================

void ASanctuaryIsland::SetParticleBudgetLevel(EAURACRONPerformanceLevel Level)
{
    // ========================================
    // ORÇAMENTO PARTÍCULAS ESCALÁVEL ENTRY/MID/HIGH - DOCUMENTAÇÃO
    // ========================================
    if (!IsValid(this))
    {
        UE_LOGFMT(LogTemp, Warning, "ASanctuaryIsland::SetParticleBudgetLevel - Invalid sanctuary island");
        return;
    }

    int32 ParticleBudget = 0;
    float EffectIntensity = 1.0f;

    switch (Level)
    {
        case EAURACRONPerformanceLevel::Entry:
            // Entry Level: 300 partículas para Fluxo Prismal conforme documentação
            ParticleBudget = 100; // Sanctuary Island usa 1/3 do orçamento
            EffectIntensity = 0.5f;
            break;

        case EAURACRONPerformanceLevel::Mid:
            // Mid-range: 800 partículas para Fluxo Prismal conforme documentação
            ParticleBudget = 250; // Sanctuary Island usa proporção similar
            EffectIntensity = 0.75f;
            break;

        case EAURACRONPerformanceLevel::High:
            // High-end: 2000 partículas para Fluxo Prismal conforme documentação
            ParticleBudget = 600; // Sanctuary Island usa proporção similar
            EffectIntensity = 1.0f;
            break;
    }

    // Aplicar orçamento aos efeitos de partículas
    if (HealingEffect && IsValid(HealingEffect))
    {
        HealingEffect->SetFloatParameter(TEXT("ParticleBudget"), static_cast<float>(ParticleBudget));
        HealingEffect->SetFloatParameter(TEXT("EffectIntensity"), EffectIntensity);
    }

    if (BarrierEffect && IsValid(BarrierEffect))
    {
        BarrierEffect->SetFloatParameter(TEXT("ParticleBudget"), static_cast<float>(ParticleBudget));
        BarrierEffect->SetFloatParameter(TEXT("EffectIntensity"), EffectIntensity);
    }

    if (VisionAmplifierEffect && IsValid(VisionAmplifierEffect))
    {
        VisionAmplifierEffect->SetFloatParameter(TEXT("ParticleBudget"), static_cast<float>(ParticleBudget / 2));
        VisionAmplifierEffect->SetFloatParameter(TEXT("EffectIntensity"), EffectIntensity);
    }

    UE_LOGFMT(LogTemp, Log, "ASanctuaryIsland::SetParticleBudgetLevel - Set particle budget to {0} for level {1}", ParticleBudget, (int32)Level);
}

// ========================================
// INTEGRAÇÃO COM FLUXO PRISMAL - DOCUMENTAÇÃO AURACRON
// ========================================

void ASanctuaryIsland::IntegrateWithPrismalFlow(float FlowIntensity, FVector FlowDirection)
{
    // ========================================
    // INTEGRAÇÃO FLUXO PRISMAL - DOCUMENTAÇÃO AURACRON
    // ========================================
    if (!IsValid(this))
    {
        UE_LOGFMT(LogTemp, Warning, "ASanctuaryIsland::IntegrateWithPrismalFlow - Invalid sanctuary island");
        return;
    }

    // Fluxo Prismal é o núcleo serpentino que serpenteia através dos três ambientes
    // Conforme documentação: "Um rio de energia massivo, similar a uma serpente"

    // Ajustar propriedades baseadas na intensidade do fluxo
    float FlowMultiplier = FMath::Clamp(FlowIntensity, 0.5f, 2.0f);

    // Intensificar efeitos de cura baseado no fluxo
    float ModifiedHealingPower = HealingPower * FlowMultiplier;

    // Atualizar todos os efeitos visuais
    if (HealingEffect && IsValid(HealingEffect))
    {
        HealingEffect->SetFloatParameter(TEXT("PrismalFlowIntensity"), FlowIntensity);
        HealingEffect->SetVectorParameter(TEXT("PrismalFlowDirection"), FlowDirection);
        HealingEffect->SetFloatParameter(TEXT("ModifiedHealingPower"), ModifiedHealingPower);
    }

    if (BarrierEffect && IsValid(BarrierEffect))
    {
        BarrierEffect->SetFloatParameter(TEXT("PrismalFlowIntensity"), FlowIntensity);
        BarrierEffect->SetVectorParameter(TEXT("PrismalFlowDirection"), FlowDirection);
    }

    // Ajustar zona segura baseada no fluxo
    if (SecureZone && IsValid(SecureZone))
    {
        float ModifiedRadius = SecureZoneRadius * FlowMultiplier;
        SecureZone->SetSphereRadius(ModifiedRadius);
    }

    UE_LOGFMT(LogTemp, Log, "ASanctuaryIsland::IntegrateWithPrismalFlow - Integrated with Prismal Flow (Intensity: {0}, Direction: {1})", FlowIntensity, FlowDirection.ToString());
}

// ========================================
// VALIDAÇÃO ROBUSTA E LIMPEZA UE 5.6 - MODERN APIS
// ========================================

void ASanctuaryIsland::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // ========================================
    // LIMPEZA ROBUSTA DE TIMERS UE 5.6
    // ========================================
    if (UWorld* World = GetWorld())
    {
        if (VisualEffectsTimerHandle.IsValid())
        {
            World->GetTimerManager().ClearTimer(VisualEffectsTimerHandle);
        }

        if (HealingTimerHandle.IsValid())
        {
            World->GetTimerManager().ClearTimer(HealingTimerHandle);
        }
    }

    // Limpeza de efeitos ativos
    ActiveEffects.Empty();

    UE_LOGFMT(LogTemp, Log, "ASanctuaryIsland::EndPlay - Sanctuary Island cleanup completed");

    Super::EndPlay(EndPlayReason);
}

bool ASanctuaryIsland::ValidateConfiguration() const
{
    // ========================================
    // VALIDAÇÃO ROBUSTA DE CONFIGURAÇÃO UE 5.6
    // ========================================
    bool bIsValid = true;

    // Validar componentes essenciais
    if (!HealingFountain || !IsValid(HealingFountain))
    {
        UE_LOGFMT(LogTemp, Error, "ASanctuaryIsland::ValidateConfiguration - Missing HealingFountain component");
        bIsValid = false;
    }

    if (!HealingEffect || !IsValid(HealingEffect))
    {
        UE_LOGFMT(LogTemp, Error, "ASanctuaryIsland::ValidateConfiguration - Missing HealingEffect component");
        bIsValid = false;
    }

    if (!ProtectiveBarrier || !IsValid(ProtectiveBarrier))
    {
        UE_LOGFMT(LogTemp, Error, "ASanctuaryIsland::ValidateConfiguration - Missing ProtectiveBarrier component");
        bIsValid = false;
    }

    if (!SecureZone || !IsValid(SecureZone))
    {
        UE_LOGFMT(LogTemp, Error, "ASanctuaryIsland::ValidateConfiguration - Missing SecureZone component");
        bIsValid = false;
    }

    // Validar GameplayEffects
    if (!HealingGameplayEffect)
    {
        UE_LOGFMT(LogTemp, Warning, "ASanctuaryIsland::ValidateConfiguration - Missing HealingGameplayEffect");
        bIsValid = false;
    }

    if (!ProtectionGameplayEffect)
    {
        UE_LOGFMT(LogTemp, Warning, "ASanctuaryIsland::ValidateConfiguration - Missing ProtectionGameplayEffect");
        bIsValid = false;
    }

    // Validar StreamableManager
    if (!StreamableManager)
    {
        UE_LOGFMT(LogTemp, Error, "ASanctuaryIsland::ValidateConfiguration - Missing StreamableManager");
        bIsValid = false;
    }

    // Validar propriedades numéricas
    if (HealingPower <= 0.0f)
    {
        UE_LOGFMT(LogTemp, Warning, "ASanctuaryIsland::ValidateConfiguration - Invalid HealingPower: {0}", HealingPower);
        bIsValid = false;
    }

    if (ProtectionDuration <= 0.0f)
    {
        UE_LOGFMT(LogTemp, Warning, "ASanctuaryIsland::ValidateConfiguration - Invalid ProtectionDuration: {0}", ProtectionDuration);
        bIsValid = false;
    }

    if (SecureZoneRadius <= 0.0f)
    {
        UE_LOGFMT(LogTemp, Warning, "ASanctuaryIsland::ValidateConfiguration - Invalid SecureZoneRadius: {0}", SecureZoneRadius);
        bIsValid = false;
    }

    if (bIsValid)
    {
        UE_LOGFMT(LogTemp, Log, "ASanctuaryIsland::ValidateConfiguration - Configuration is valid");
    }
    else
    {
        UE_LOGFMT(LogTemp, Error, "ASanctuaryIsland::ValidateConfiguration - Configuration has errors");
    }

    return bIsValid;
}
}