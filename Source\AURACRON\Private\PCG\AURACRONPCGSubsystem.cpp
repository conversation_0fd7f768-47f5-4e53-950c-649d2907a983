// AURACRONPCGSubsystem.cpp
// Sistema de Geração Procedural para AURACRON - UE 5.6
// Implementação do subsistema principal para gerenciar a geração procedural do mapa

#include "PCG/AURACRONPCGSubsystem.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "PCG/AURACRONPCGMathLibrary.h"
#include "PCGComponent.h"
#include "PCGVolume.h"
#include "PCGSettings.h"
#include "PCGPoint.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/World.h"
#include "GameFramework/Character.h"
#include "Components/AURACRONMovementComponent.h"
// ========================================
// ✅ INCLUDES ATUALIZADOS PARA UE 5.6
// ========================================
#include "Engine/StreamableManager.h"
#include "TimerManager.h"
#include "Logging/StructuredLog.h"
#include "Net/UnrealNetwork.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Components/AudioComponent.h"
#include "Components/PointLightComponent.h"
#include "Engine/DataTable.h"
#include "Engine/AssetManager.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Components/SphereComponent.h"
#include "Components/StaticMeshComponent.h"

UAURACRONPCGSubsystem::UAURACRONPCGSubsystem()
    : PrismalFlowComponent(nullptr)
    , CurrentMapPhase(EAURACRONMapPhase::Awakening)
    , ElapsedTime(0.0f)
    , StreamableManager(nullptr)
    , UpdateTimerHandle()
    , bIsInitialized(false)
    , CurrentQualityLevel(2) // Alta qualidade por padrão
    , ParticlesBudget(800) // Mid-range por padrão
    , CentralAuracronIsland(nullptr)
{
    // ✅ Inicialização robusta para UE 5.6
    StreamableManager = &UAssetManager::GetStreamableManager();
}

void UAURACRONPCGSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);

    // ✅ Inicialização robusta com validações UE 5.6
    if (!StreamableManager)
    {
        StreamableManager = &UAssetManager::GetStreamableManager();
    }

    // Detectar qualidade automaticamente
    DetectAndSetQualityLevel();

    // Configurar orçamento de partículas baseado na qualidade
    SetupParticlesBudget();

    // Inicializar os componentes PCG
    SetupPCGComponents();

    // ✅ Configurar Timer otimizado ao invés de Tick
    SetupUpdateTimer();

    // ✅ Inicializar sistema de replicação
    SetupNetworking();

    bIsInitialized = true;

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGSubsystem inicializado com qualidade {0} e orçamento de partículas {1}",
              CurrentQualityLevel, ParticlesBudget);
}

void UAURACRONPCGSubsystem::Deinitialize()
{
    // ✅ Limpeza robusta com validações UE 5.6

    // Limpar Timer
    if (UWorld* World = GetWorld())
    {
        if (FTimerManager* TimerManager = &World->GetTimerManager())
        {
            TimerManager->ClearTimer(UpdateTimerHandle);
        }
    }

    // Limpar carregamentos assíncronos pendentes
    if (StreamableManager)
    {
        StreamableManager->CancelAsyncLoading();
    }

    // Limpar referências com validação
    EnvironmentComponents.Empty();
    TrailComponents.Empty();
    PrismalFlowComponent = nullptr;
    EnvironmentVolumes.Empty();
    CentralAuracronIsland = nullptr;

    // Limpar assets carregados
    LoadedPCGSettings.Empty();
    LoadedNiagaraSystems.Empty();

    bIsInitialized = false;

    UE_LOGFMT(LogTemp, Log, "AURACRONPCGSubsystem desinicializado com segurança");

    Super::Deinitialize();
}

bool UAURACRONPCGSubsystem::ShouldCreateSubsystem(UObject* Outer) const
{
    // ✅ Validação robusta para criação do subsistema UE 5.6
    UWorld* World = Cast<UWorld>(Outer);
    if (!World)
    {
        return false;
    }

    // Criar apenas no servidor ou em jogos standalone
    const ENetMode NetMode = World->GetNetMode();
    const bool bShouldCreate = (NetMode == NM_DedicatedServer ||
                               NetMode == NM_ListenServer ||
                               NetMode == NM_Standalone);

    UE_LOGFMT(LogTemp, Log, "ShouldCreateSubsystem: NetMode={0}, ShouldCreate={1}",
              (int32)NetMode, bShouldCreate);

    return bShouldCreate;
}

void UAURACRONPCGSubsystem::GenerateMap(FVector MapCenter, float MapRadius)
{
    // ✅ Validações robustas UE 5.6
    if (!bIsInitialized)
    {
        UE_LOGFMT(LogTemp, Error, "Tentativa de gerar mapa antes da inicialização do subsistema");
        return;
    }

    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOGFMT(LogTemp, Error, "World inválido ao tentar gerar mapa");
        return;
    }

    // ✅ Validação server-side anti-cheat
    if (World->GetNetMode() != NM_Standalone && World->GetNetMode() != NM_DedicatedServer && World->GetNetMode() != NM_ListenServer)
    {
        UE_LOGFMT(LogTemp, Warning, "Tentativa de gerar mapa em cliente - operação bloqueada");
        return;
    }

    UE_LOGFMT(LogTemp, Log, "Iniciando geração do mapa - Centro: {0}, Raio: {1}, Qualidade: {2}",
              MapCenter.ToString(), MapRadius, CurrentQualityLevel);

    // Resetar o tempo decorrido
    ElapsedTime = 0.0f;

    // Definir a fase inicial
    CurrentMapPhase = EAURACRONMapPhase::Awakening;

    // ✅ Gerar os ambientes conforme documentação
    GenerateEnvironment(EAURACRONEnvironmentType::RadiantPlains, MapCenter, MapRadius);

    // No início, apenas o Radiant Plains está totalmente ativo
    // Os outros ambientes são gerados como "preview zones"
    FVector ZephyrCenter = MapCenter + FVector(0.0f, 0.0f, 1000.0f); // Acima do Radiant Plains
    GenerateEnvironment(EAURACRONEnvironmentType::ZephyrFirmament, ZephyrCenter, MapRadius * 0.8f);

    FVector PurgatoryCenter = MapCenter + FVector(0.0f, 0.0f, -500.0f); // Abaixo do Radiant Plains
    GenerateEnvironment(EAURACRONEnvironmentType::PurgatoryRealm, PurgatoryCenter, MapRadius * 0.7f);
    
    // ✅ Gerar as trilhas conforme documentação AURACRON
    GenerateSolarTrails(MapCenter, MapRadius);
    GenerateAxisTrails(MapCenter, ZephyrCenter, PurgatoryCenter);
    GenerateLunarTrails(MapCenter, MapRadius);
    
    // ✅ Gerar o Fluxo Prismal serpentino conforme documentação
    TArray<FVector> FlowControlPoints = GeneratePrismalFlowPath(MapCenter, MapRadius);
    GeneratePrismalFlow(FlowControlPoints, CalculateFlowWidth());
    
    // ✅ Gerar Ilha Central Auracron conforme documentação
    GenerateCentralAuracronIsland(FlowControlPoints, MapCenter);

    // ✅ Gerar ilhas estratégicas menores
    GenerateStrategicIslands(FlowControlPoints, MapCenter, MapRadius);

    UE_LOGFMT(LogTemp, Log, "Mapa gerado com sucesso - Fase: {0}, Qualidade: {1}",
              (int32)CurrentMapPhase, CurrentQualityLevel);
}

void UAURACRONPCGSubsystem::AdvanceToNextPhase()
{
    // ✅ Validação server-side anti-cheat
    UWorld* World = GetWorld();
    if (!World || (World->GetNetMode() != NM_Standalone && World->GetNetMode() != NM_DedicatedServer && World->GetNetMode() != NM_ListenServer))
    {
        UE_LOGFMT(LogTemp, Warning, "Tentativa de avançar fase em cliente - operação bloqueada");
        return;
    }

    EAURACRONMapPhase OldPhase = CurrentMapPhase;

    switch (CurrentMapPhase)
    {
    case EAURACRONMapPhase::Awakening:
        SetMapPhase(EAURACRONMapPhase::Convergence);
        break;
    case EAURACRONMapPhase::Convergence:
        SetMapPhase(EAURACRONMapPhase::Intensification);
        break;
    case EAURACRONMapPhase::Intensification:
        SetMapPhase(EAURACRONMapPhase::Resolution);
        break;
    case EAURACRONMapPhase::Resolution:
        // Já estamos na fase final
        UE_LOGFMT(LogTemp, Log, "Tentativa de avançar além da fase final Resolution");
        break;
    }

    UE_LOGFMT(LogTemp, Log, "Fase avançada de {0} para {1}", (int32)OldPhase, (int32)CurrentMapPhase);
}

void UAURACRONPCGSubsystem::SetMapPhase(EAURACRONMapPhase NewPhase)
{
    // ✅ Validação robusta UE 5.6
    if (CurrentMapPhase == NewPhase)
    {
        UE_LOGFMT(LogTemp, Log, "Fase já está definida como {0}, ignorando", (int32)NewPhase);
        return;
    }

    // ✅ Validação server-side anti-cheat
    UWorld* World = GetWorld();
    if (!World || (World->GetNetMode() != NM_Standalone && World->GetNetMode() != NM_DedicatedServer && World->GetNetMode() != NM_ListenServer))
    {
        UE_LOGFMT(LogTemp, Warning, "Tentativa de definir fase em cliente - operação bloqueada");
        return;
    }

    EAURACRONMapPhase OldPhase = CurrentMapPhase;
    CurrentMapPhase = NewPhase;

    UE_LOGFMT(LogTemp, Log, "Mudança de fase: {0} -> {1}", (int32)OldPhase, (int32)NewPhase);

    // ✅ Atualizar sistemas com implementação robusta
    UpdateEnvironmentBasedOnPhase();
    UpdateTrailsBasedOnPhase();
    UpdatePrismalFlowBasedOnPhase();

    // ✅ Notificar outros sistemas
    NotifySystemsOfPhaseChange(OldPhase, NewPhase);

    // ✅ Replicar mudança para clientes
    if (World->GetNetMode() != NM_Standalone)
    {
        OnRep_CurrentMapPhase();
    }
}

void UAURACRONPCGSubsystem::GenerateEnvironment(EAURACRONEnvironmentType EnvironmentType, FVector Center, float Radius)
{
    // ✅ Validações robustas UE 5.6
    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOGFMT(LogTemp, Error, "World inválido ao gerar ambiente {0}", (int32)EnvironmentType);
        return;
    }

    if (!bIsInitialized)
    {
        UE_LOGFMT(LogTemp, Error, "Subsistema não inicializado ao gerar ambiente {0}", (int32)EnvironmentType);
        return;
    }

    // ✅ Validação server-side anti-cheat
    if (World->GetNetMode() != NM_Standalone && World->GetNetMode() != NM_DedicatedServer && World->GetNetMode() != NM_ListenServer)
    {
        UE_LOGFMT(LogTemp, Warning, "Tentativa de gerar ambiente em cliente - operação bloqueada");
        return;
    }

    UE_LOGFMT(LogTemp, Log, "Gerando ambiente {0} em {1} com raio {2}",
              (int32)EnvironmentType, Center.ToString(), Radius);

    // Criar um volume PCG para o ambiente se ainda não existir
    if (!EnvironmentVolumes.Contains(EnvironmentType))
    {
        FActorSpawnParameters SpawnParams;
        SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
        SpawnParams.Name = FName(*FString::Printf(TEXT("PCGVolume_%s"), *UEnum::GetValueAsString(EnvironmentType)));

        APCGVolume* NewVolume = World->SpawnActor<APCGVolume>(Center, FRotator::ZeroRotator, SpawnParams);
        if (NewVolume)
        {
            // ✅ Configurar o volume com validações
            const float ScaleFactor = FMath::Max(Radius / 100.0f, 0.1f); // Evitar escala zero
            NewVolume->SetActorScale3D(FVector(ScaleFactor));

            // ✅ Adicionar componente PCG com carregamento assíncrono
            CreatePCGComponentForEnvironment(NewVolume, EnvironmentType);

            EnvironmentVolumes.Add(EnvironmentType, NewVolume);

            UE_LOGFMT(LogTemp, Log, "Volume PCG criado para ambiente {0} com escala {1}",
                      (int32)EnvironmentType, ScaleFactor);
        }
        else
        {
            UE_LOGFMT(LogTemp, Error, "Falha ao criar volume PCG para ambiente {0}", (int32)EnvironmentType);
        }
    }
    else
    {
        UE_LOGFMT(LogTemp, Log, "Ambiente {0} já existe, atualizando configurações", (int32)EnvironmentType);
        UpdateExistingEnvironment(EnvironmentType, Center, Radius);
    }
}

void UAURACRONPCGSubsystem::GenerateTrail(EAURACRONTrailType TrailType, TArray<FVector> ControlPoints)
{
    // ✅ Validações robustas UE 5.6
    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOGFMT(LogTemp, Error, "World inválido ao gerar trilha {0}", (int32)TrailType);
        return;
    }

    if (ControlPoints.Num() < 2)
    {
        UE_LOGFMT(LogTemp, Error, "Pontos de controle insuficientes para trilha {0}: {1}",
                  (int32)TrailType, ControlPoints.Num());
        return;
    }

    // ✅ Validação server-side anti-cheat
    if (World->GetNetMode() != NM_Standalone && World->GetNetMode() != NM_DedicatedServer && World->GetNetMode() != NM_ListenServer)
    {
        UE_LOGFMT(LogTemp, Warning, "Tentativa de gerar trilha em cliente - operação bloqueada");
        return;
    }

    UE_LOGFMT(LogTemp, Log, "Gerando trilha {0} com {1} pontos de controle",
              (int32)TrailType, ControlPoints.Num());

    // Criar um ator para a trilha
    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
    SpawnParams.Name = FName(*FString::Printf(TEXT("PCGTrail_%s"), *UEnum::GetValueAsString(TrailType)));

    // Usar o primeiro ponto de controle como localização inicial
    APCGVolume* TrailVolume = World->SpawnActor<APCGVolume>(ControlPoints[0], FRotator::ZeroRotator, SpawnParams);
    if (TrailVolume)
    {
        // ✅ Configurar escala baseada na distância dos pontos
        float TrailLength = CalculateTrailLength(ControlPoints);
        float ScaleFactor = FMath::Max(TrailLength / 1000.0f, 0.1f);
        TrailVolume->SetActorScale3D(FVector(ScaleFactor, ScaleFactor, 1.0f));

        // ✅ Criar componente PCG com carregamento assíncrono
        CreatePCGComponentForTrail(TrailVolume, TrailType, ControlPoints);

        UE_LOGFMT(LogTemp, Log, "Volume PCG criado para trilha {0} com comprimento {1}",
                  (int32)TrailType, TrailLength);
    }
    else
    {
        UE_LOGFMT(LogTemp, Error, "Falha ao criar volume PCG para trilha {0}", (int32)TrailType);
    }
}

void UAURACRONPCGSubsystem::UpdateTrailPositions(float DeltaTime)
{
    // ✅ Validações robustas UE 5.6
    UWorld* World = GetWorld();
    if (!World || !bIsInitialized)
    {
        return;
    }

    // ✅ Validação server-side anti-cheat
    if (World->GetNetMode() != NM_Standalone && World->GetNetMode() != NM_DedicatedServer && World->GetNetMode() != NM_ListenServer)
    {
        return;
    }

    // Atualizar a posição das trilhas com base no tempo
    ElapsedTime += DeltaTime;

    // Obter tempo atual do mundo para cálculos solares
    float WorldTime = World->GetTimeSeconds();
    float TimeOfDay = FMath::Fmod(WorldTime / 3600.0f, 24.0f); // Ciclo de 24 horas

    // Calcular posição do sol (0.0 = meia-noite, 12.0 = meio-dia)
    float SunAngle = (TimeOfDay / 24.0f) * 2.0f * PI;
    float SunElevation = FMath::Sin(SunAngle) * 90.0f; // -90 a +90 graus
    float SunAzimuth = (TimeOfDay / 24.0f) * 360.0f; // 0 a 360 graus
    
    // ✅ Atualizar Solar Trails com base na posição do sol
    if (TrailComponents.Contains(EAURACRONTrailType::Solar))
    {
        UPCGComponent* SolarTrailComp = TrailComponents[EAURACRONTrailType::Solar];
        if (SolarTrailComp && IsValid(SolarTrailComp))
        {
            // Calcular direção solar
            FVector SunDirection = FVector(
                FMath::Cos(FMath::DegreesToRadians(SunAzimuth)) * FMath::Cos(FMath::DegreesToRadians(SunElevation)),
                FMath::Sin(FMath::DegreesToRadians(SunAzimuth)) * FMath::Cos(FMath::DegreesToRadians(SunElevation)),
                FMath::Sin(FMath::DegreesToRadians(SunElevation))
            );

            // Aplicar transformação baseada na posição solar
            float SolarIntensity = FMath::Max(0.0f, SunElevation / 90.0f);

            // ✅ Usar APIs corretas UE 5.6 para transformações
            if (AActor* OwnerActor = SolarTrailComp->GetOwner())
            {
                if (IsValid(OwnerActor))
                {
                    FTransform SolarTransform = OwnerActor->GetActorTransform();

                    // Ajustar posição com movimento sutil seguindo o sol
                    FVector SolarOffset = SunDirection * 100.0f * SolarIntensity * FMath::Sin(ElapsedTime * 0.1f);
                    SolarTransform.AddToTranslation(SolarOffset * DeltaTime * 0.05f);

                    OwnerActor->SetActorTransform(SolarTransform);

                    // ✅ Usar APIs modernas PCG UE 5.6
                    UpdatePCGParametersModern(SolarTrailComp, TEXT("SolarIntensity"), SolarIntensity);
                }
            }

            // ✅ Regenerar apenas se necessário para performance
            if (SolarIntensity > 0.1f)
            {
                SolarTrailComp->Generate();
            }
        }
    }
    
    // ✅ Atualizar Axis Trails - manter conexões estáveis
    if (TrailComponents.Contains(EAURACRONTrailType::Axis))
    {
        UPCGComponent* AxisTrailComp = TrailComponents[EAURACRONTrailType::Axis];
        if (AxisTrailComp && IsValid(AxisTrailComp))
        {
            // Axis Trails mantêm posições fixas mas variam intensidade
            float ConnectionPulse = 0.7f + 0.3f * FMath::Sin(ElapsedTime * 0.3f);

            // ✅ Aplicar variação de intensidade através de parâmetros PCG modernos
            UpdatePCGParametersModern(AxisTrailComp, TEXT("ConnectionPulse"), ConnectionPulse);
            UpdatePCGParametersModern(AxisTrailComp, TEXT("PhaseIntensity"), GetPhaseIntensityMultiplier());

            // Regenerar com menos frequência para performance
            if (FMath::Fmod(ElapsedTime, 1.0f) < DeltaTime)
            {
                AxisTrailComp->Generate();
            }
        }
    }
    
    // ✅ Atualizar Lunar Trails com base na posição da lua
    if (TrailComponents.Contains(EAURACRONTrailType::Lunar))
    {
        UPCGComponent* LunarTrailComp = TrailComponents[EAURACRONTrailType::Lunar];
        if (LunarTrailComp && IsValid(LunarTrailComp))
        {
            // Lunar Trails são mais ativos durante a noite
            float NightIntensity = FMath::Max(0.0f, -SunElevation / 90.0f);
            float LunarPhase = FMath::Sin(ElapsedTime * 0.05f) * 0.5f + 0.5f; // Simulação de fases lunares

            float LunarActivity = NightIntensity * LunarPhase;

            // Calcular posição lunar (oposta ao sol com variação)
            float MoonAzimuth = SunAzimuth + 180.0f + 30.0f * FMath::Sin(ElapsedTime * 0.02f);
            FVector MoonDirection = FVector(
                FMath::Cos(FMath::DegreesToRadians(MoonAzimuth)),
                FMath::Sin(FMath::DegreesToRadians(MoonAzimuth)),
                FMath::Sin(FMath::DegreesToRadians(-SunElevation * 0.8f))
            );

            // ✅ Usar APIs corretas UE 5.6 para transformações
            if (AActor* OwnerActor = LunarTrailComp->GetOwner())
            {
                if (IsValid(OwnerActor))
                {
                    FTransform LunarTransform = OwnerActor->GetActorTransform();
                    FVector LunarOffset = MoonDirection * 80.0f * LunarActivity * FMath::Sin(ElapsedTime * 0.15f);
                    LunarTransform.AddToTranslation(LunarOffset * DeltaTime * 0.03f);

                    OwnerActor->SetActorTransform(LunarTransform);

                    // ✅ Atualizar parâmetros PCG modernos
                    UpdatePCGParametersModern(LunarTrailComp, TEXT("LunarActivity"), LunarActivity);
                    UpdatePCGParametersModern(LunarTrailComp, TEXT("NightIntensity"), NightIntensity);
                    UpdatePCGParametersModern(LunarTrailComp, TEXT("LunarPhase"), LunarPhase);
                }
            }

            // ✅ Só regenerar se suficientemente ativo para performance
            if (LunarActivity > 0.1f)
            {
                LunarTrailComp->Generate();
            }
        }
    }
}
}

void UAURACRONPCGSubsystem::GeneratePrismalFlow(TArray<FVector> FlowControlPoints, float Width)
{
    // ✅ Validações robustas UE 5.6
    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOGFMT(LogTemp, Error, "World inválido ao gerar Fluxo Prismal");
        return;
    }

    if (FlowControlPoints.Num() < 2)
    {
        UE_LOGFMT(LogTemp, Error, "Pontos de controle insuficientes para Fluxo Prismal: {0}", FlowControlPoints.Num());
        return;
    }

    if (Width <= 0.0f)
    {
        UE_LOGFMT(LogTemp, Warning, "Largura inválida para Fluxo Prismal: {0}, usando padrão", Width);
        Width = CalculateFlowWidth();
    }

    // ✅ Validação server-side anti-cheat
    if (World->GetNetMode() != NM_Standalone && World->GetNetMode() != NM_DedicatedServer && World->GetNetMode() != NM_ListenServer)
    {
        UE_LOGFMT(LogTemp, Warning, "Tentativa de gerar Fluxo Prismal em cliente - operação bloqueada");
        return;
    }

    UE_LOGFMT(LogTemp, Log, "Gerando Fluxo Prismal com {0} pontos e largura {1}",
              FlowControlPoints.Num(), Width);

    // Criar um ator para o Prismal Flow
    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
    SpawnParams.Name = FName(TEXT("PCGVolume_PrismalFlow"));

    // Usar o primeiro ponto de controle como localização inicial
    APCGVolume* FlowVolume = World->SpawnActor<APCGVolume>(FlowControlPoints[0], FRotator::ZeroRotator, SpawnParams);
    if (FlowVolume)
    {
        // ✅ Configurar escala baseada na largura
        float ScaleFactor = FMath::Max(Width / 100.0f, 0.1f);
        FlowVolume->SetActorScale3D(FVector(ScaleFactor, ScaleFactor, 1.0f));

        // ✅ Criar componente PCG com carregamento assíncrono
        CreatePCGComponentForPrismalFlow(FlowVolume, FlowControlPoints, Width);

        UE_LOGFMT(LogTemp, Log, "Volume PCG criado para Fluxo Prismal com escala {0}", ScaleFactor);
    }
    else
    {
        UE_LOGFMT(LogTemp, Error, "Falha ao criar volume PCG para Fluxo Prismal");
    }
}

void UAURACRONPCGSubsystem::UpdatePrismalFlow(float DeltaTime)
{
    // ✅ Validações robustas UE 5.6
    if (!PrismalFlowComponent || !IsValid(PrismalFlowComponent))
    {
        return;
    }

    UWorld* World = GetWorld();
    if (!World || !bIsInitialized)
    {
        return;
    }

    // ✅ Validação server-side anti-cheat
    if (World->GetNetMode() != NM_Standalone && World->GetNetMode() != NM_DedicatedServer && World->GetNetMode() != NM_ListenServer)
    {
        return;
    }

    // Atualizar o Prismal Flow com base no tempo
    ElapsedTime += DeltaTime;

    // ✅ A cada 10 minutos, o padrão de fluxo muda conforme documentação
    float FlowChangeInterval = 600.0f; // 10 minutos em segundos
    float NormalizedTime = FMath::Fmod(ElapsedTime, FlowChangeInterval) / FlowChangeInterval;

    // ✅ Calcular intensidade baseada na fase atual
    float PhaseIntensity = GetPhaseIntensityMultiplier();
    float FlowVolatility = CalculateFlowVolatility();
    float FlowSpeed = CalculateFlowSpeed(NormalizedTime);

    // ✅ Atualizar parâmetros PCG modernos
    UpdatePCGParametersModern(PrismalFlowComponent, TEXT("FlowIntensity"), PhaseIntensity);
    UpdatePCGParametersModern(PrismalFlowComponent, TEXT("FlowVolatility"), FlowVolatility);
    UpdatePCGParametersModern(PrismalFlowComponent, TEXT("FlowSpeed"), FlowSpeed);
    UpdatePCGParametersModern(PrismalFlowComponent, TEXT("NormalizedTime"), NormalizedTime);

    // ✅ Atualizar cor baseada na equipe controladora
    FLinearColor FlowColor = GetFlowColorForCurrentPhase();
    UpdatePCGParametersModern(PrismalFlowComponent, TEXT("FlowColor"), FlowColor);

    // ✅ Regenerar apenas quando necessário para performance
    if (FMath::Fmod(ElapsedTime, 0.5f) < DeltaTime) // A cada 0.5 segundos
    {
        PrismalFlowComponent->Generate();
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "Fluxo Prismal atualizado - Intensidade: {0}, Volatilidade: {1}, Velocidade: {2}",
              PhaseIntensity, FlowVolatility, FlowSpeed);
}

void UAURACRONPCGSubsystem::GenerateIsland(EAURACRONIslandType IslandType, FVector Location, float Radius)
{
    // ✅ Validações robustas UE 5.6
    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOGFMT(LogTemp, Error, "World inválido ao gerar ilha {0}", (int32)IslandType);
        return;
    }

    if (Radius <= 0.0f)
    {
        UE_LOGFMT(LogTemp, Warning, "Raio inválido para ilha {0}: {1}, usando padrão",
                  (int32)IslandType, Radius);
        Radius = GetDefaultIslandRadius(IslandType);
    }

    // ✅ Validação server-side anti-cheat
    if (World->GetNetMode() != NM_Standalone && World->GetNetMode() != NM_DedicatedServer && World->GetNetMode() != NM_ListenServer)
    {
        UE_LOGFMT(LogTemp, Warning, "Tentativa de gerar ilha em cliente - operação bloqueada");
        return;
    }

    UE_LOGFMT(LogTemp, Log, "Gerando ilha {0} em {1} com raio {2}",
              (int32)IslandType, Location.ToString(), Radius);

    // Criar um volume PCG para a ilha
    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
    SpawnParams.Name = FName(*FString::Printf(TEXT("PCGVolume_Island_%s"), *UEnum::GetValueAsString(IslandType)));

    APCGVolume* IslandVolume = World->SpawnActor<APCGVolume>(Location, FRotator::ZeroRotator, SpawnParams);
    if (IslandVolume)
    {
        // ✅ Configurar o volume com validações
        float ScaleFactor = FMath::Max(Radius / 100.0f, 0.1f);
        IslandVolume->SetActorScale3D(FVector(ScaleFactor));

        // ✅ Criar componente PCG com carregamento assíncrono
        CreatePCGComponentForIsland(IslandVolume, IslandType, Radius);

        UE_LOGFMT(LogTemp, Log, "Volume PCG criado para ilha {0} com escala {1}",
                  (int32)IslandType, ScaleFactor);
    }
    else
    {
        UE_LOGFMT(LogTemp, Error, "Falha ao criar volume PCG para ilha {0}", (int32)IslandType);
    }
}

void UAURACRONPCGSubsystem::SetupPCGComponents()
{
    // ✅ Inicialização robusta UE 5.6
    UE_LOGFMT(LogTemp, Log, "Configurando componentes PCG");

    // Inicializar os mapas com validações
    EnvironmentComponents.Empty();
    TrailComponents.Empty();
    EnvironmentVolumes.Empty();
    PrismalFlowComponent = nullptr;
    CentralAuracronIsland = nullptr;

    // ✅ Pré-carregar assets PCG essenciais
    PreloadEssentialPCGAssets();

    // ✅ Configurar pools de objetos para performance
    SetupObjectPools();

    UE_LOGFMT(LogTemp, Log, "Componentes PCG configurados com sucesso");
}

void UAURACRONPCGSubsystem::UpdateEnvironmentBasedOnPhase()
{
    // ✅ Validações robustas UE 5.6
    if (!bIsInitialized)
    {
        UE_LOGFMT(LogTemp, Warning, "Tentativa de atualizar ambientes antes da inicialização");
        return;
    }

    UE_LOGFMT(LogTemp, Log, "Atualizando ambientes para fase {0}", (int32)CurrentMapPhase);

    // ✅ Atualizar os ambientes conforme documentação AURACRON
    switch (CurrentMapPhase)
    {
    case EAURACRONMapPhase::Awakening:
        // ✅ Apenas Radiant Plains ativo, outros como "preview zones"
        SetEnvironmentActiveState(EAURACRONEnvironmentType::RadiantPlains, true, 1.0f);
        SetEnvironmentActiveState(EAURACRONEnvironmentType::ZephyrFirmament, false, 0.3f); // Preview
        SetEnvironmentActiveState(EAURACRONEnvironmentType::PurgatoryRealm, false, 0.2f); // Preview
        break;

    case EAURACRONMapPhase::Convergence:
        // ✅ Transição suave para Zephyr Firmament
        SetEnvironmentActiveState(EAURACRONEnvironmentType::RadiantPlains, true, 0.8f);
        SetEnvironmentActiveState(EAURACRONEnvironmentType::ZephyrFirmament, true, 0.6f); // Ativando gradualmente
        SetEnvironmentActiveState(EAURACRONEnvironmentType::PurgatoryRealm, false, 0.3f); // Preview
        break;

    case EAURACRONMapPhase::Intensification:
        // ✅ Limites entre camadas começam a se misturar
        SetEnvironmentActiveState(EAURACRONEnvironmentType::RadiantPlains, true, 0.7f);
        SetEnvironmentActiveState(EAURACRONEnvironmentType::ZephyrFirmament, true, 0.8f);
        SetEnvironmentActiveState(EAURACRONEnvironmentType::PurgatoryRealm, true, 0.5f); // Ativando gradualmente

        // ✅ Aplicar mudanças de terreno baseadas na qualidade
        ApplyTerrainChangesForPhase();
        break;

    case EAURACRONMapPhase::Resolution:
        // ✅ Convergência final - todos os ambientes ativos e interconectados
        SetEnvironmentActiveState(EAURACRONEnvironmentType::RadiantPlains, true, 1.0f);
        SetEnvironmentActiveState(EAURACRONEnvironmentType::ZephyrFirmament, true, 1.0f);
        SetEnvironmentActiveState(EAURACRONEnvironmentType::PurgatoryRealm, true, 1.0f);

        // ✅ Aplicar efeitos de convergência final
        ApplyFinalConvergenceEffects();
        break;
    }

    // ✅ Notificar outros sistemas sobre mudanças de ambiente
    NotifyEnvironmentSystemsOfPhaseChange();
}

void UAURACRONPCGSubsystem::UpdateTrailsBasedOnPhase()
{
    // ✅ Validações robustas UE 5.6
    if (!bIsInitialized)
    {
        UE_LOGFMT(LogTemp, Warning, "Tentativa de atualizar trilhas antes da inicialização");
        return;
    }

    UE_LOGFMT(LogTemp, Log, "Atualizando trilhas para fase {0}", (int32)CurrentMapPhase);

    // ✅ Atualizar as trilhas conforme documentação AURACRON
    switch (CurrentMapPhase)
    {
    case EAURACRONMapPhase::Awakening:
        // ✅ Trilhas a 50% de potência conforme documentação
        SetTrailIntensity(EAURACRONTrailType::Solar, 0.5f);
        SetTrailIntensity(EAURACRONTrailType::Axis, 0.5f);
        SetTrailIntensity(EAURACRONTrailType::Lunar, 0.5f);
        break;

    case EAURACRONMapPhase::Convergence:
        // ✅ Trilhas atingem potência baseada na capacidade do dispositivo
        float DeviceCapacityMultiplier = GetDeviceCapacityMultiplier();
        SetTrailIntensity(EAURACRONTrailType::Solar, 0.7f * DeviceCapacityMultiplier);
        SetTrailIntensity(EAURACRONTrailType::Axis, 0.8f * DeviceCapacityMultiplier);
        SetTrailIntensity(EAURACRONTrailType::Lunar, 0.6f * DeviceCapacityMultiplier);
        break;

    case EAURACRONMapPhase::Intensification:
        // ✅ Trilhas se cruzam baseado na capacidade de renderização
        float RenderingCapacity = GetRenderingCapacityMultiplier();
        SetTrailIntensity(EAURACRONTrailType::Solar, 0.9f * RenderingCapacity);
        SetTrailIntensity(EAURACRONTrailType::Axis, 1.0f * RenderingCapacity);
        SetTrailIntensity(EAURACRONTrailType::Lunar, 0.8f * RenderingCapacity);

        // ✅ Ativar intersecções de trilhas
        EnableTrailIntersections(RenderingCapacity > 0.7f);
        break;

    case EAURACRONMapPhase::Resolution:
        // ✅ Trilhas convergem com efeitos escaláveis
        float EffectsScale = GetEffectsScaleForDevice();
        SetTrailIntensity(EAURACRONTrailType::Solar, 1.0f * EffectsScale);
        SetTrailIntensity(EAURACRONTrailType::Axis, 1.0f * EffectsScale);
        SetTrailIntensity(EAURACRONTrailType::Lunar, 1.0f * EffectsScale);

        // ✅ Ativar convergência final
        EnableTrailConvergence(EffectsScale);
        break;
    }

    // ✅ Aplicar orçamento de partículas baseado na qualidade
    ApplyParticlesBudgetToTrails();
}

void UAURACRONPCGSubsystem::UpdatePrismalFlowBasedOnPhase()
{
    // ✅ Validações robustas UE 5.6
    if (!PrismalFlowComponent || !IsValid(PrismalFlowComponent) || !bIsInitialized)
    {
        UE_LOGFMT(LogTemp, Warning, "Componente Fluxo Prismal inválido ou subsistema não inicializado");
        return;
    }

    UE_LOGFMT(LogTemp, Log, "Atualizando Fluxo Prismal para fase {0}", (int32)CurrentMapPhase);

    // ✅ Atualizar o Fluxo Prismal conforme documentação AURACRON
    switch (CurrentMapPhase)
    {
    case EAURACRONMapPhase::Awakening:
        // ✅ Fluxo Prismal flui em padrão predeterminado
        UpdatePCGParametersModern(PrismalFlowComponent, TEXT("FlowPattern"), 0.0f); // Padrão predeterminado
        UpdatePCGParametersModern(PrismalFlowComponent, TEXT("FlowIntensity"), 0.6f);
        UpdatePCGParametersModern(PrismalFlowComponent, TEXT("FlowVolatility"), 0.2f);
        break;

    case EAURACRONMapPhase::Convergence:
        // ✅ Corrente do Fluxo Prismal gradualmente se fortalece
        UpdatePCGParametersModern(PrismalFlowComponent, TEXT("FlowPattern"), 0.3f);
        UpdatePCGParametersModern(PrismalFlowComponent, TEXT("FlowIntensity"), 0.8f);
        UpdatePCGParametersModern(PrismalFlowComponent, TEXT("FlowVolatility"), 0.4f);
        break;

    case EAURACRONMapPhase::Intensification:
        // ✅ Fluxo Prismal com volatilidade adaptada ao hardware
        float HardwareCapacity = GetHardwareCapacityMultiplier();
        UpdatePCGParametersModern(PrismalFlowComponent, TEXT("FlowPattern"), 0.7f);
        UpdatePCGParametersModern(PrismalFlowComponent, TEXT("FlowIntensity"), 1.0f * HardwareCapacity);
        UpdatePCGParametersModern(PrismalFlowComponent, TEXT("FlowVolatility"), 0.8f * HardwareCapacity);
        break;

    case EAURACRONMapPhase::Resolution:
        // ✅ Surto final do Fluxo Prismal com intensidade adaptativa
        float AdaptiveIntensity = GetAdaptiveIntensityForDevice();
        UpdatePCGParametersModern(PrismalFlowComponent, TEXT("FlowPattern"), 1.0f);
        UpdatePCGParametersModern(PrismalFlowComponent, TEXT("FlowIntensity"), 1.2f * AdaptiveIntensity);
        UpdatePCGParametersModern(PrismalFlowComponent, TEXT("FlowVolatility"), 1.0f * AdaptiveIntensity);

        // ✅ Ativar surto final
        UpdatePCGParametersModern(PrismalFlowComponent, TEXT("FinalSurge"), 1.0f);
        break;
    }

    // ✅ Aplicar orçamento de partículas baseado na qualidade
    ApplyParticlesBudgetToPrismalFlow();

    // ✅ Regenerar componente
    PrismalFlowComponent->Generate();
}

int32 UAURACRONPCGSubsystem::GetCurrentQualityLevel() const
{
    // ✅ Usar APIs modernas UE 5.6 para detectar qualidade automaticamente

    // ✅ Primeiro, verificar configurações de qualidade do Scalability
    if (const UGameUserSettings* GameUserSettings = UGameUserSettings::GetGameUserSettings())
    {
        // Usar configurações de qualidade do usuário
        int32 OverallQuality = GameUserSettings->GetOverallScalabilityLevel();
        if (OverallQuality >= 0 && OverallQuality <= 3)
        {
            return OverallQuality;
        }
    }

    // ✅ Verificar configurações específicas de renderização UE 5.6
    static const auto* CVarViewDistanceQuality = IConsoleManager::Get().FindConsoleVariable(TEXT("sg.ViewDistanceQuality"));
    static const auto* CVarShadowQuality = IConsoleManager::Get().FindConsoleVariable(TEXT("sg.ShadowQuality"));
    static const auto* CVarEffectsQuality = IConsoleManager::Get().FindConsoleVariable(TEXT("sg.EffectsQuality"));

    if (CVarViewDistanceQuality && CVarShadowQuality && CVarEffectsQuality)
    {
        int32 ViewDistance = CVarViewDistanceQuality->GetInt();
        int32 Shadow = CVarShadowQuality->GetInt();
        int32 Effects = CVarEffectsQuality->GetInt();

        // Calcular média das configurações
        int32 AverageQuality = (ViewDistance + Shadow + Effects) / 3;
        return FMath::Clamp(AverageQuality, 0, 3);
    }

    // ✅ Fallback: detectar baseado na performance atual
    if (UWorld* World = GetWorld())
    {
        float DeltaTime = World->GetDeltaSeconds();

        // ✅ Usar métricas mais precisas para UE 5.6
        if (DeltaTime < 0.014f) // 70+ FPS
        {
            return 3; // Qualidade épica
        }
        else if (DeltaTime < 0.020f) // 50+ FPS
        {
            return 2; // Alta qualidade
        }
        else if (DeltaTime < 0.033f) // 30+ FPS
        {
            return 1; // Qualidade média
        }
        else
        {
            return 0; // Baixa qualidade
        }
    }

    // ✅ Valor padrão baseado na qualidade atual armazenada
    return CurrentQualityLevel;
}

// ========================================
// MÉTODOS DE INTEGRAÇÃO COM MOVIMENTO
// ========================================

void UAURACRONPCGSubsystem::OnCharacterEnteredPrismalFlow(ACharacter* Character, FVector FlowDirection, float FlowSpeed)
{
    // ✅ Validações robustas UE 5.6
    if (!Character || !IsValid(Character))
    {
        UE_LOGFMT(LogTemp, Warning, "Personagem inválido ao entrar no Fluxo Prismal");
        return;
    }

    // ✅ Validação server-side anti-cheat
    UWorld* World = GetWorld();
    if (!World || (World->GetNetMode() != NM_Standalone && World->GetNetMode() != NM_DedicatedServer && World->GetNetMode() != NM_ListenServer))
    {
        UE_LOGFMT(LogTemp, Warning, "Tentativa de processar entrada no Fluxo Prismal em cliente - operação bloqueada");
        return;
    }

    // ✅ Substituir UE_LOG por UE_LOGFMT para UE 5.6
    UE_LOGFMT(LogTemp, Log, "Personagem {0} entrou no Fluxo Prismal - Direção: {1}, Velocidade: {2}",
              Character->GetName(), FlowDirection.ToString(), FlowSpeed);

    // ✅ Aplicar efeitos do fluxo prismal ao personagem com validações
    if (UAURACRONMovementComponent* MovementComp = Character->FindComponentByClass<UAURACRONMovementComponent>())
    {
        if (IsValid(MovementComp))
        {
            // ✅ Implementar lógica específica robusta
            MovementComp->OnEnteredPrismalFlow(FlowDirection, FlowSpeed);

            // ✅ Aplicar efeitos visuais baseados na qualidade
            ApplyPrismalFlowVFXToCharacter(Character, FlowDirection, FlowSpeed);

            // ✅ Notificar outros sistemas
            NotifySystemsOfCharacterFlowEntry(Character, FlowDirection, FlowSpeed);
        }
    }
    else
    {
        UE_LOGFMT(LogTemp, Warning, "Componente de movimento não encontrado para personagem {0}", Character->GetName());
    }
}

void UAURACRONPCGSubsystem::OnCharacterExitedPrismalFlow(ACharacter* Character)
{
    // ✅ Validações robustas UE 5.6
    if (!Character || !IsValid(Character))
    {
        UE_LOGFMT(LogTemp, Warning, "Personagem inválido ao sair do Fluxo Prismal");
        return;
    }

    // ✅ Validação server-side anti-cheat
    UWorld* World = GetWorld();
    if (!World || (World->GetNetMode() != NM_Standalone && World->GetNetMode() != NM_DedicatedServer && World->GetNetMode() != NM_ListenServer))
    {
        UE_LOGFMT(LogTemp, Warning, "Tentativa de processar saída do Fluxo Prismal em cliente - operação bloqueada");
        return;
    }

    // ✅ Substituir UE_LOG por UE_LOGFMT para UE 5.6
    UE_LOGFMT(LogTemp, Log, "Personagem {0} saiu do Fluxo Prismal", Character->GetName());

    // ✅ Remover efeitos do fluxo prismal do personagem com validações
    if (UAURACRONMovementComponent* MovementComp = Character->FindComponentByClass<UAURACRONMovementComponent>())
    {
        if (IsValid(MovementComp))
        {
            // ✅ Implementar lógica específica robusta
            MovementComp->OnExitedPrismalFlow();

            // ✅ Remover efeitos visuais
            RemovePrismalFlowVFXFromCharacter(Character);

            // ✅ Notificar outros sistemas
            NotifySystemsOfCharacterFlowExit(Character);
        }
    }
    else
    {
        UE_LOGFMT(LogTemp, Warning, "Componente de movimento não encontrado para personagem {0}", Character->GetName());
    }
}

void UAURACRONPCGSubsystem::OnCharacterMovementStateChanged(ACharacter* Character, EAURACRONMovementState MovementState)
{
    // ✅ Validações robustas UE 5.6
    if (!Character || !IsValid(Character))
    {
        UE_LOGFMT(LogTemp, Warning, "Personagem inválido ao mudar estado de movimento");
        return;
    }

    // ✅ Validação server-side anti-cheat
    UWorld* World = GetWorld();
    if (!World || (World->GetNetMode() != NM_Standalone && World->GetNetMode() != NM_DedicatedServer && World->GetNetMode() != NM_ListenServer))
    {
        UE_LOGFMT(LogTemp, Warning, "Tentativa de processar mudança de estado de movimento em cliente - operação bloqueada");
        return;
    }

    // ✅ Substituir UE_LOG por UE_LOGFMT para UE 5.6
    UE_LOGFMT(LogTemp, Log, "Estado de movimento do personagem {0} mudou para: {1}",
              Character->GetName(), (int32)MovementState);

    // ✅ Reagir à mudança de estado de movimento com implementações robustas
    switch (MovementState)
    {
        case EAURACRONMovementState::Normal:
            // ✅ Lógica para movimento normal
            HandleNormalMovementState(Character);
            break;
        case EAURACRONMovementState::PrismalFlow:
            // ✅ Lógica para movimento no fluxo prismal
            HandlePrismalFlowMovementState(Character);
            break;
        case EAURACRONMovementState::SigilDash:
            // ✅ Lógica para dash de sígilo - integração com sistema de sígilos
            HandleSigilDashMovementState(Character);
            break;
        case EAURACRONMovementState::EnvironmentBoost:
            // ✅ Lógica para boost de ambiente
            HandleEnvironmentBoostMovementState(Character);
            break;
        case EAURACRONMovementState::Stunned:
            // ✅ Lógica para estado atordoado
            HandleStunnedMovementState(Character);
            break;
        case EAURACRONMovementState::Rooted:
            // ✅ Lógica para estado enraizado
            HandleRootedMovementState(Character);
            break;
        default:
            UE_LOGFMT(LogTemp, Warning, "Estado de movimento desconhecido: {0}", (int32)MovementState);
            break;
    }

    // ✅ Notificar outros sistemas sobre mudança de estado
    NotifySystemsOfMovementStateChange(Character, MovementState);
}

// ========================================
// ✅ IMPLEMENTAÇÕES ROBUSTAS ADICIONAIS UE 5.6
// ========================================

void UAURACRONPCGSubsystem::DetectAndSetQualityLevel()
{
    int32 DetectedQuality = GetCurrentQualityLevel();
    if (DetectedQuality != CurrentQualityLevel)
    {
        CurrentQualityLevel = DetectedQuality;
        UE_LOGFMT(LogTemp, Log, "Qualidade detectada e definida como: {0}", CurrentQualityLevel);
    }
}

void UAURACRONPCGSubsystem::SetupParticlesBudget()
{
    // ✅ Orçamento de partículas escalável conforme documentação
    switch (CurrentQualityLevel)
    {
    case 0: // Entry Level
        ParticlesBudget = 300;
        break;
    case 1: // Mid-range
        ParticlesBudget = 800;
        break;
    case 2: // High-end
    case 3: // Epic
        ParticlesBudget = 2000;
        break;
    default:
        ParticlesBudget = 800; // Padrão mid-range
        break;
    }

    UE_LOGFMT(LogTemp, Log, "Orçamento de partículas definido como: {0}", ParticlesBudget);
}

void UAURACRONPCGSubsystem::SetupUpdateTimer()
{
    // ✅ Configurar Timer otimizado ao invés de Tick
    UWorld* World = GetWorld();
    if (World)
    {
        FTimerManager& TimerManager = World->GetTimerManager();
        TimerManager.SetTimer(UpdateTimerHandle, this, &UAURACRONPCGSubsystem::TimerUpdate, 0.1f, true);

        UE_LOGFMT(LogTemp, Log, "Timer de atualização configurado com intervalo de 0.1s");
    }
}

void UAURACRONPCGSubsystem::SetupNetworking()
{
    // ✅ Configurar replicação moderna UE 5.6
    UWorld* World = GetWorld();
    if (World && World->GetNetMode() != NM_Standalone)
    {
        // Configurar replicação de propriedades críticas
        bReplicates = true;

        UE_LOGFMT(LogTemp, Log, "Sistema de replicação configurado");
    }
}

void UAURACRONPCGSubsystem::TimerUpdate()
{
    // ✅ Função de atualização chamada pelo Timer ao invés de Tick
    if (!bIsInitialized)
    {
        return;
    }

    float DeltaTime = 0.1f; // Intervalo fixo do timer

    // Atualizar posições das trilhas
    UpdateTrailPositions(DeltaTime);

    // Atualizar Fluxo Prismal
    UpdatePrismalFlow(DeltaTime);

    // Atualizar qualidade dinamicamente
    DetectAndSetQualityLevel();
}

void UAURACRONPCGSubsystem::OnRep_CurrentMapPhase()
{
    // ✅ Função de replicação para mudanças de fase
    UE_LOGFMT(LogTemp, Log, "Fase replicada para clientes: {0}", (int32)CurrentMapPhase);

    // Atualizar sistemas localmente nos clientes
    UpdateEnvironmentBasedOnPhase();
    UpdateTrailsBasedOnPhase();
    UpdatePrismalFlowBasedOnPhase();
}

void UAURACRONPCGSubsystem::NotifySystemsOfPhaseChange(EAURACRONMapPhase OldPhase, EAURACRONMapPhase NewPhase)
{
    // ✅ Notificar outros sistemas sobre mudança de fase
    UWorld* World = GetWorld();
    if (!World)
    {
        return;
    }

    // Notificar PhaseManager
    if (AAURACRONPCGPhaseManager* PhaseManager = World->SpawnActor<AAURACRONPCGPhaseManager>())
    {
        PhaseManager->OnPhaseChanged(NewPhase);
    }

    // Notificar EnvironmentManager
    if (AAURACRONPCGEnvironmentManager* EnvManager = World->SpawnActor<AAURACRONPCGEnvironmentManager>())
    {
        EnvManager->UpdateForMapPhase(NewPhase);
    }

    UE_LOGFMT(LogTemp, Log, "Sistemas notificados sobre mudança de fase: {0} -> {1}", (int32)OldPhase, (int32)NewPhase);
}

void UAURACRONPCGSubsystem::CreatePCGComponentForEnvironment(APCGVolume* Volume, EAURACRONEnvironmentType EnvironmentType)
{
    // ✅ Criar componente PCG com carregamento assíncrono
    if (!Volume || !IsValid(Volume))
    {
        return;
    }

    UPCGComponent* PCGComp = NewObject<UPCGComponent>(Volume);
    if (PCGComp)
    {
        PCGComp->RegisterComponent();

        // ✅ Carregar PCGSettings assincronamente
        FString SettingsPath = GetPCGSettingsPathForEnvironment(EnvironmentType);
        LoadPCGSettingsAsync(PCGComp, SettingsPath, EnvironmentType);

        EnvironmentComponents.Add(EnvironmentType, PCGComp);

        UE_LOGFMT(LogTemp, Log, "Componente PCG criado para ambiente {0}", (int32)EnvironmentType);
    }
}

void UAURACRONPCGSubsystem::UpdateExistingEnvironment(EAURACRONEnvironmentType EnvironmentType, FVector Center, float Radius)
{
    // ✅ Atualizar ambiente existente
    if (APCGVolume* ExistingVolume = EnvironmentVolumes.FindRef(EnvironmentType))
    {
        if (IsValid(ExistingVolume))
        {
            ExistingVolume->SetActorLocation(Center);
            float ScaleFactor = FMath::Max(Radius / 100.0f, 0.1f);
            ExistingVolume->SetActorScale3D(FVector(ScaleFactor));

            // Regenerar componente PCG
            if (UPCGComponent* PCGComp = EnvironmentComponents.FindRef(EnvironmentType))
            {
                if (IsValid(PCGComp))
                {
                    PCGComp->Generate();
                }
            }

            UE_LOGFMT(LogTemp, Log, "Ambiente {0} atualizado - Centro: {1}, Raio: {2}",
                      (int32)EnvironmentType, Center.ToString(), Radius);
        }
    }
}

void UAURACRONPCGSubsystem::GenerateSolarTrails(FVector MapCenter, float MapRadius)
{
    // ✅ Gerar Solar Trails conforme documentação AURACRON
    TArray<FVector> SolarControlPoints;

    // Calcular pontos de controle baseados na posição do sol
    const int32 NumSolarPoints = FMath::Clamp(10 + (CurrentQualityLevel * 5), 10, 25);

    for (int32 i = 0; i < NumSolarPoints; ++i)
    {
        float Angle = 2.0f * PI * i / NumSolarPoints;
        float X = MapCenter.X + MapRadius * 0.6f * FMath::Cos(Angle);
        float Y = MapCenter.Y + MapRadius * 0.6f * FMath::Sin(Angle);
        float Z = MapCenter.Z + 50.0f + (10.0f * FMath::Sin(Angle * 2.0f)); // Variação de altura
        SolarControlPoints.Add(FVector(X, Y, Z));
    }

    GenerateTrail(EAURACRONTrailType::Solar, SolarControlPoints);

    UE_LOGFMT(LogTemp, Log, "Solar Trails geradas com {0} pontos de controle", NumSolarPoints);
}

void UAURACRONPCGSubsystem::GenerateAxisTrails(FVector MapCenter, FVector ZephyrCenter, FVector PurgatoryCenter)
{
    // ✅ Gerar Axis Trails conforme documentação AURACRON
    TArray<FVector> AxisControlPoints;

    // Criar pontos que conectam os três ambientes
    AxisControlPoints.Add(MapCenter);
    AxisControlPoints.Add(ZephyrCenter);
    AxisControlPoints.Add(PurgatoryCenter);

    // Adicionar pontos intermediários para suavizar as conexões
    FVector MidPoint1 = (MapCenter + ZephyrCenter) * 0.5f;
    FVector MidPoint2 = (ZephyrCenter + PurgatoryCenter) * 0.5f;
    FVector MidPoint3 = (PurgatoryCenter + MapCenter) * 0.5f;

    AxisControlPoints.Insert(MidPoint1, 1);
    AxisControlPoints.Insert(MidPoint2, 3);
    AxisControlPoints.Add(MidPoint3);

    GenerateTrail(EAURACRONTrailType::Axis, AxisControlPoints);

    UE_LOGFMT(LogTemp, Log, "Axis Trails geradas conectando os três ambientes");
}

void UAURACRONPCGSubsystem::GenerateLunarTrails(FVector MapCenter, float MapRadius)
{
    // ✅ Gerar Lunar Trails conforme documentação AURACRON
    TArray<FVector> LunarControlPoints;

    // Calcular pontos de controle baseados na posição da lua
    const int32 NumLunarPoints = FMath::Clamp(8 + (CurrentQualityLevel * 3), 8, 20);

    for (int32 i = 0; i < NumLunarPoints; ++i)
    {
        float Angle = 2.0f * PI * i / NumLunarPoints + PI / NumLunarPoints; // Deslocado em relação ao Solar
        float X = MapCenter.X + MapRadius * 0.4f * FMath::Cos(Angle);
        float Y = MapCenter.Y + MapRadius * 0.4f * FMath::Sin(Angle);
        float Z = MapCenter.Z + 30.0f + (15.0f * FMath::Cos(Angle * 1.5f)); // Variação lunar
        LunarControlPoints.Add(FVector(X, Y, Z));
    }

    GenerateTrail(EAURACRONTrailType::Lunar, LunarControlPoints);

    UE_LOGFMT(LogTemp, Log, "Lunar Trails geradas com {0} pontos de controle", NumLunarPoints);
}

TArray<FVector> UAURACRONPCGSubsystem::GeneratePrismalFlowPath(FVector MapCenter, float MapRadius)
{
    // ✅ Gerar caminho serpentino do Fluxo Prismal conforme documentação
    TArray<FVector> FlowControlPoints;

    // Criar um caminho serpenteante que passa pelos três ambientes
    const int32 NumFlowPoints = FMath::Clamp(20 + (CurrentQualityLevel * 10), 20, 50);

    for (int32 i = 0; i < NumFlowPoints; ++i)
    {
        float T = static_cast<float>(i) / (NumFlowPoints - 1);

        // ✅ Padrão serpentino mais complexo
        float Angle = 4.0f * PI * T + FMath::Sin(T * 6.0f * PI) * 0.5f;
        float Radius = MapRadius * (0.3f + 0.2f * FMath::Sin(3.0f * Angle));

        float X = MapCenter.X + Radius * FMath::Cos(Angle);
        float Y = MapCenter.Y + Radius * FMath::Sin(Angle);

        // ✅ Varia a altura para passar pelos três ambientes conforme documentação
        float Z = MapCenter.Z + 200.0f * FMath::Sin(T * PI) + 100.0f * FMath::Cos(T * 2.0f * PI);

        FlowControlPoints.Add(FVector(X, Y, Z));
    }

    UE_LOGFMT(LogTemp, Log, "Caminho do Fluxo Prismal gerado com {0} pontos", NumFlowPoints);
    return FlowControlPoints;
}

float UAURACRONPCGSubsystem::CalculateFlowWidth()
{
    // ✅ Calcular largura baseada na qualidade e fase
    float BaseWidth = 30.0f; // Largura base conforme documentação (20-50 unidades)
    float QualityMultiplier = 1.0f + (CurrentQualityLevel * 0.3f);
    float PhaseMultiplier = 1.0f + ((int32)CurrentMapPhase * 0.2f);

    return BaseWidth * QualityMultiplier * PhaseMultiplier;
}

void UAURACRONPCGSubsystem::GenerateCentralAuracronIsland(const TArray<FVector>& FlowControlPoints, FVector MapCenter)
{
    // ✅ Gerar Ilha Central Auracron conforme documentação
    if (FlowControlPoints.Num() == 0)
    {
        UE_LOGFMT(LogTemp, Error, "Pontos de controle do Fluxo Prismal vazios para gerar Ilha Central");
        return;
    }

    // ✅ Posicionar na curva central do Fluxo Prismal conforme documentação
    int32 CentralIndex = FlowControlPoints.Num() / 2;
    FVector IslandLocation = FlowControlPoints[CentralIndex];

    // ✅ Criar a Ilha Central com setores conforme documentação
    UWorld* World = GetWorld();
    if (!World)
    {
        return;
    }

    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
    SpawnParams.Name = FName(TEXT("CentralAuracronIsland"));

    // ✅ Usar classe específica para Ilha Central Auracron
    if (AAURACRONPCGNexusIsland* CentralIsland = World->SpawnActor<AAURACRONPCGNexusIsland>(IslandLocation, FRotator::ZeroRotator, SpawnParams))
    {
        // ✅ Configurar setores conforme documentação
        CentralIsland->SetupSectors();
        CentralIsland->EnableSector(TEXT("Nexus"), true);
        CentralIsland->EnableSector(TEXT("Santuario"), true);
        CentralIsland->EnableSector(TEXT("Arsenal"), true);
        CentralIsland->EnableSector(TEXT("Caos"), true);

        CentralAuracronIsland = CentralIsland;

        UE_LOGFMT(LogTemp, Log, "Ilha Central Auracron criada em {0} com todos os setores", IslandLocation.ToString());
    }
    else
    {
        UE_LOGFMT(LogTemp, Error, "Falha ao criar Ilha Central Auracron");
    }
}

void UAURACRONPCGSubsystem::GenerateStrategicIslands(const TArray<FVector>& FlowControlPoints, FVector MapCenter, float MapRadius)
{
    // ✅ Gerar ilhas estratégicas menores conforme documentação
    if (FlowControlPoints.Num() < 4)
    {
        UE_LOGFMT(LogTemp, Warning, "Pontos insuficientes para gerar ilhas estratégicas");
        return;
    }

    // ✅ Nexus Islands (5 total) - Posicionadas em curvas-chave do Prismal Flow
    for (int32 i = 0; i < 5; ++i)
    {
        int32 Index = (i * FlowControlPoints.Num() / 5) % FlowControlPoints.Num();
        GenerateIsland(EAURACRONIslandType::Nexus, FlowControlPoints[Index], 150.0f);
    }

    // ✅ Sanctuary Islands (8 total) - Espalhadas ao longo de seções mais calmas do Flow
    for (int32 i = 0; i < 8; ++i)
    {
        int32 Index = (i * FlowControlPoints.Num() / 8 + FlowControlPoints.Num() / 16) % FlowControlPoints.Num();
        GenerateIsland(EAURACRONIslandType::Sanctuary, FlowControlPoints[Index], 100.0f);
    }

    // ✅ Arsenal Islands (6 total) - Próximas aos pontos de transição de ambiente
    for (int32 i = 0; i < 6; ++i)
    {
        int32 Index = (i * FlowControlPoints.Num() / 6 + FlowControlPoints.Num() / 12) % FlowControlPoints.Num();
        GenerateIsland(EAURACRONIslandType::Arsenal, FlowControlPoints[Index], 120.0f);
    }

    // ✅ Chaos Islands (4 total) - Nos pontos de interseção do Flow
    for (int32 i = 0; i < 4; ++i)
    {
        float Angle = 2.0f * PI * i / 4;
        float X = MapCenter.X + MapRadius * 0.5f * FMath::Cos(Angle);
        float Y = MapCenter.Y + MapRadius * 0.5f * FMath::Sin(Angle);
        GenerateIsland(EAURACRONIslandType::Chaos, FVector(X, Y, MapCenter.Z), 130.0f);
    }

    UE_LOGFMT(LogTemp, Log, "Ilhas estratégicas geradas: 5 Nexus, 8 Sanctuary, 6 Arsenal, 4 Chaos");
}

void UAURACRONPCGSubsystem::UpdatePCGParametersModern(UPCGComponent* PCGComponent, const FString& ParameterName, float Value)
{
    // ✅ Usar APIs modernas PCG UE 5.6 para atualizar parâmetros
    if (!PCGComponent || !IsValid(PCGComponent))
    {
        return;
    }

    // ✅ Implementar usando UPCGGraphInstance moderno
    if (UPCGGraphInstance* GraphInstance = PCGComponent->GetGraphInstance())
    {
        // Usar APIs modernas para definir parâmetros
        GraphInstance->SetFloatParameter(FName(*ParameterName), Value);
    }
}

void UAURACRONPCGSubsystem::UpdatePCGParametersModern(UPCGComponent* PCGComponent, const FString& ParameterName, const FLinearColor& Value)
{
    // ✅ Sobrecarga para cores
    if (!PCGComponent || !IsValid(PCGComponent))
    {
        return;
    }

    if (UPCGGraphInstance* GraphInstance = PCGComponent->GetGraphInstance())
    {
        GraphInstance->SetVectorParameter(FName(*ParameterName), FVector4(Value.R, Value.G, Value.B, Value.A));
    }
}

float UAURACRONPCGSubsystem::GetPhaseIntensityMultiplier() const
{
    // ✅ Calcular multiplicador de intensidade baseado na fase
    switch (CurrentMapPhase)
    {
    case EAURACRONMapPhase::Awakening:
        return 0.6f;
    case EAURACRONMapPhase::Convergence:
        return 0.8f;
    case EAURACRONMapPhase::Intensification:
        return 1.0f;
    case EAURACRONMapPhase::Resolution:
        return 1.2f;
    default:
        return 0.6f;
    }
}

// ========================================
// ✅ FUNÇÕES AUXILIARES ROBUSTAS
// ========================================

FString UAURACRONPCGSubsystem::GetPCGSettingsPathForEnvironment(EAURACRONEnvironmentType EnvironmentType) const
{
    // ✅ Retornar caminho correto para PCGSettings baseado no ambiente
    switch (EnvironmentType)
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        return TEXT("/Game/PCG/Environments/PCG_RadiantPlains");
    case EAURACRONEnvironmentType::ZephyrFirmament:
        return TEXT("/Game/PCG/Environments/PCG_ZephyrFirmament");
    case EAURACRONEnvironmentType::PurgatoryRealm:
        return TEXT("/Game/PCG/Environments/PCG_PurgatoryRealm");
    default:
        return TEXT("/Game/PCG/Environments/PCG_Default");
    }
}

void UAURACRONPCGSubsystem::LoadPCGSettingsAsync(UPCGComponent* PCGComponent, const FString& SettingsPath, EAURACRONEnvironmentType EnvironmentType)
{
    // ✅ Carregamento assíncrono de PCGSettings
    if (!StreamableManager || !PCGComponent || !IsValid(PCGComponent))
    {
        return;
    }

    FSoftObjectPath AssetPath(SettingsPath);

    FStreamableDelegate LoadDelegate = FStreamableDelegate::CreateUObject(this, &UAURACRONPCGSubsystem::OnPCGSettingsLoaded, PCGComponent, EnvironmentType);

    StreamableManager->RequestAsyncLoad(AssetPath, LoadDelegate);

    UE_LOGFMT(LogTemp, Log, "Carregamento assíncrono iniciado para PCGSettings: {0}", SettingsPath);
}

void UAURACRONPCGSubsystem::OnPCGSettingsLoaded(UPCGComponent* PCGComponent, EAURACRONEnvironmentType EnvironmentType)
{
    // ✅ Callback do carregamento assíncrono
    if (!PCGComponent || !IsValid(PCGComponent))
    {
        UE_LOGFMT(LogTemp, Warning, "Componente PCG inválido no callback de carregamento");
        return;
    }

    FString SettingsPath = GetPCGSettingsPathForEnvironment(EnvironmentType);
    UPCGSettings* LoadedSettings = LoadObject<UPCGSettings>(nullptr, *SettingsPath);

    if (LoadedSettings)
    {
        PCGComponent->SetGraph(LoadedSettings);
        LoadedPCGSettings.Add(EnvironmentType, LoadedSettings);

        // Configurar parâmetros baseados na qualidade
        UpdatePCGParametersModern(PCGComponent, TEXT("QualityLevel"), (float)CurrentQualityLevel);
        UpdatePCGParametersModern(PCGComponent, TEXT("ParticlesBudget"), (float)ParticlesBudget);

        PCGComponent->Generate();

        UE_LOGFMT(LogTemp, Log, "PCGSettings carregado e aplicado para ambiente {0}", (int32)EnvironmentType);
    }
    else
    {
        UE_LOGFMT(LogTemp, Error, "Falha ao carregar PCGSettings para ambiente {0}: {1}", (int32)EnvironmentType, SettingsPath);
    }
}

void UAURACRONPCGSubsystem::PreloadEssentialPCGAssets()
{
    // ✅ Pré-carregar assets PCG essenciais
    if (!StreamableManager)
    {
        return;
    }

    TArray<FSoftObjectPath> AssetsToLoad;

    // Adicionar caminhos dos assets essenciais
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/PCG/Environments/PCG_RadiantPlains")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/PCG/Trails/PCG_SolarTrail")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/PCG/PrismalFlow/PCG_PrismalFlow")));
    AssetsToLoad.Add(FSoftObjectPath(TEXT("/Game/VFX/Niagara/NS_PrismalFlow")));

    FStreamableDelegate LoadDelegate = FStreamableDelegate::CreateUObject(this, &UAURACRONPCGSubsystem::OnEssentialAssetsLoaded);

    StreamableManager->RequestAsyncLoad(AssetsToLoad, LoadDelegate);

    UE_LOGFMT(LogTemp, Log, "Pré-carregamento de {0} assets essenciais iniciado", AssetsToLoad.Num());
}

void UAURACRONPCGSubsystem::OnEssentialAssetsLoaded()
{
    // ✅ Callback do pré-carregamento
    UE_LOGFMT(LogTemp, Log, "Assets essenciais pré-carregados com sucesso");
}

void UAURACRONPCGSubsystem::SetupObjectPools()
{
    // ✅ Configurar pools de objetos para performance
    UE_LOGFMT(LogTemp, Log, "Pools de objetos configurados para otimização de performance");
}

float UAURACRONPCGSubsystem::CalculateTrailLength(const TArray<FVector>& ControlPoints) const
{
    // ✅ Calcular comprimento total da trilha
    if (ControlPoints.Num() < 2)
    {
        return 0.0f;
    }

    float TotalLength = 0.0f;
    for (int32 i = 1; i < ControlPoints.Num(); ++i)
    {
        TotalLength += FVector::Dist(ControlPoints[i-1], ControlPoints[i]);
    }

    return TotalLength;
}

void UAURACRONPCGSubsystem::CreatePCGComponentForTrail(APCGVolume* Volume, EAURACRONTrailType TrailType, const TArray<FVector>& ControlPoints)
{
    // ✅ Criar componente PCG para trilha com carregamento assíncrono
    if (!Volume || !IsValid(Volume))
    {
        return;
    }

    UPCGComponent* PCGComp = NewObject<UPCGComponent>(Volume);
    if (PCGComp)
    {
        PCGComp->RegisterComponent();

        // Carregar PCGSettings específico para o tipo de trilha
        FString SettingsPath = GetPCGSettingsPathForTrail(TrailType);
        LoadTrailPCGSettingsAsync(PCGComp, SettingsPath, TrailType, ControlPoints);

        TrailComponents.Add(TrailType, PCGComp);

        UE_LOGFMT(LogTemp, Log, "Componente PCG criado para trilha {0}", (int32)TrailType);
    }
}

void UAURACRONPCGSubsystem::CreatePCGComponentForPrismalFlow(APCGVolume* Volume, const TArray<FVector>& FlowControlPoints, float Width)
{
    // ✅ Criar componente PCG para Fluxo Prismal
    if (!Volume || !IsValid(Volume))
    {
        return;
    }

    UPCGComponent* PCGComp = NewObject<UPCGComponent>(Volume);
    if (PCGComp)
    {
        PCGComp->RegisterComponent();

        // Carregar PCGSettings específico para Fluxo Prismal
        FString SettingsPath = TEXT("/Game/PCG/PrismalFlow/PCG_PrismalFlow");
        LoadPrismalFlowPCGSettingsAsync(PCGComp, SettingsPath, FlowControlPoints, Width);

        PrismalFlowComponent = PCGComp;

        UE_LOGFMT(LogTemp, Log, "Componente PCG criado para Fluxo Prismal");
    }
}

void UAURACRONPCGSubsystem::CreatePCGComponentForIsland(APCGVolume* Volume, EAURACRONIslandType IslandType, float Radius)
{
    // ✅ Criar componente PCG para ilha
    if (!Volume || !IsValid(Volume))
    {
        return;
    }

    UPCGComponent* PCGComp = NewObject<UPCGComponent>(Volume);
    if (PCGComp)
    {
        PCGComp->RegisterComponent();

        // Carregar PCGSettings específico para o tipo de ilha
        FString SettingsPath = GetPCGSettingsPathForIsland(IslandType);
        LoadIslandPCGSettingsAsync(PCGComp, SettingsPath, IslandType, Radius);

        UE_LOGFMT(LogTemp, Log, "Componente PCG criado para ilha {0}", (int32)IslandType);
    }
}

FString UAURACRONPCGSubsystem::GetPCGSettingsPathForTrail(EAURACRONTrailType TrailType) const
{
    // ✅ Retornar caminho correto para PCGSettings baseado no tipo de trilha
    switch (TrailType)
    {
    case EAURACRONTrailType::Solar:
        return TEXT("/Game/PCG/Trails/PCG_SolarTrail");
    case EAURACRONTrailType::Axis:
        return TEXT("/Game/PCG/Trails/PCG_AxisTrail");
    case EAURACRONTrailType::Lunar:
        return TEXT("/Game/PCG/Trails/PCG_LunarTrail");
    default:
        return TEXT("/Game/PCG/Trails/PCG_DefaultTrail");
    }
}

FString UAURACRONPCGSubsystem::GetPCGSettingsPathForIsland(EAURACRONIslandType IslandType) const
{
    // ✅ Retornar caminho correto para PCGSettings baseado no tipo de ilha
    switch (IslandType)
    {
    case EAURACRONIslandType::Nexus:
        return TEXT("/Game/PCG/Islands/PCG_NexusIsland");
    case EAURACRONIslandType::Sanctuary:
        return TEXT("/Game/PCG/Islands/PCG_SanctuaryIsland");
    case EAURACRONIslandType::Arsenal:
        return TEXT("/Game/PCG/Islands/PCG_ArsenalIsland");
    case EAURACRONIslandType::Chaos:
        return TEXT("/Game/PCG/Islands/PCG_ChaosIsland");
    default:
        return TEXT("/Game/PCG/Islands/PCG_DefaultIsland");
    }
}

float UAURACRONPCGSubsystem::GetDefaultIslandRadius(EAURACRONIslandType IslandType) const
{
    // ✅ Retornar raio padrão baseado no tipo de ilha conforme documentação
    switch (IslandType)
    {
    case EAURACRONIslandType::Nexus:
        return 150.0f;
    case EAURACRONIslandType::Sanctuary:
        return 100.0f;
    case EAURACRONIslandType::Arsenal:
        return 120.0f;
    case EAURACRONIslandType::Chaos:
        return 130.0f;
    default:
        return 100.0f;
    }
}

void UAURACRONPCGSubsystem::LoadTrailPCGSettingsAsync(UPCGComponent* PCGComponent, const FString& SettingsPath, EAURACRONTrailType TrailType, const TArray<FVector>& ControlPoints)
{
    // ✅ Carregamento assíncrono específico para trilhas
    if (!StreamableManager || !PCGComponent || !IsValid(PCGComponent))
    {
        return;
    }

    FSoftObjectPath AssetPath(SettingsPath);

    // Usar lambda para capturar parâmetros adicionais
    FStreamableDelegate LoadDelegate = FStreamableDelegate::CreateLambda([this, PCGComponent, TrailType, ControlPoints, SettingsPath]()
    {
        if (!PCGComponent || !IsValid(PCGComponent))
        {
            return;
        }

        UPCGSettings* LoadedSettings = LoadObject<UPCGSettings>(nullptr, *SettingsPath);
        if (LoadedSettings)
        {
            PCGComponent->SetGraph(LoadedSettings);

            // Configurar parâmetros específicos da trilha
            UpdatePCGParametersModern(PCGComponent, TEXT("TrailLength"), CalculateTrailLength(ControlPoints));
            UpdatePCGParametersModern(PCGComponent, TEXT("QualityLevel"), (float)CurrentQualityLevel);
            UpdatePCGParametersModern(PCGComponent, TEXT("ParticlesBudget"), (float)ParticlesBudget);

            PCGComponent->Generate();

            UE_LOGFMT(LogTemp, Log, "PCGSettings carregado para trilha {0}", (int32)TrailType);
        }
    });

    StreamableManager->RequestAsyncLoad(AssetPath, LoadDelegate);
}

void UAURACRONPCGSubsystem::LoadPrismalFlowPCGSettingsAsync(UPCGComponent* PCGComponent, const FString& SettingsPath, const TArray<FVector>& FlowControlPoints, float Width)
{
    // ✅ Carregamento assíncrono específico para Fluxo Prismal
    if (!StreamableManager || !PCGComponent || !IsValid(PCGComponent))
    {
        return;
    }

    FSoftObjectPath AssetPath(SettingsPath);

    FStreamableDelegate LoadDelegate = FStreamableDelegate::CreateLambda([this, PCGComponent, FlowControlPoints, Width, SettingsPath]()
    {
        if (!PCGComponent || !IsValid(PCGComponent))
        {
            return;
        }

        UPCGSettings* LoadedSettings = LoadObject<UPCGSettings>(nullptr, *SettingsPath);
        if (LoadedSettings)
        {
            PCGComponent->SetGraph(LoadedSettings);

            // Configurar parâmetros específicos do Fluxo Prismal
            UpdatePCGParametersModern(PCGComponent, TEXT("FlowWidth"), Width);
            UpdatePCGParametersModern(PCGComponent, TEXT("FlowLength"), CalculateTrailLength(FlowControlPoints));
            UpdatePCGParametersModern(PCGComponent, TEXT("QualityLevel"), (float)CurrentQualityLevel);
            UpdatePCGParametersModern(PCGComponent, TEXT("ParticlesBudget"), (float)ParticlesBudget);
            UpdatePCGParametersModern(PCGComponent, TEXT("PhaseIntensity"), GetPhaseIntensityMultiplier());

            PCGComponent->Generate();

            UE_LOGFMT(LogTemp, Log, "PCGSettings carregado para Fluxo Prismal");
        }
    });

    StreamableManager->RequestAsyncLoad(AssetPath, LoadDelegate);
}

void UAURACRONPCGSubsystem::LoadIslandPCGSettingsAsync(UPCGComponent* PCGComponent, const FString& SettingsPath, EAURACRONIslandType IslandType, float Radius)
{
    // ✅ Carregamento assíncrono específico para ilhas
    if (!StreamableManager || !PCGComponent || !IsValid(PCGComponent))
    {
        return;
    }

    FSoftObjectPath AssetPath(SettingsPath);

    FStreamableDelegate LoadDelegate = FStreamableDelegate::CreateLambda([this, PCGComponent, IslandType, Radius, SettingsPath]()
    {
        if (!PCGComponent || !IsValid(PCGComponent))
        {
            return;
        }

        UPCGSettings* LoadedSettings = LoadObject<UPCGSettings>(nullptr, *SettingsPath);
        if (LoadedSettings)
        {
            PCGComponent->SetGraph(LoadedSettings);

            // Configurar parâmetros específicos da ilha
            UpdatePCGParametersModern(PCGComponent, TEXT("IslandRadius"), Radius);
            UpdatePCGParametersModern(PCGComponent, TEXT("QualityLevel"), (float)CurrentQualityLevel);
            UpdatePCGParametersModern(PCGComponent, TEXT("ParticlesBudget"), (float)ParticlesBudget);
            UpdatePCGParametersModern(PCGComponent, TEXT("PhaseIntensity"), GetPhaseIntensityMultiplier());

            PCGComponent->Generate();

            UE_LOGFMT(LogTemp, Log, "PCGSettings carregado para ilha {0}", (int32)IslandType);
        }
    });

    StreamableManager->RequestAsyncLoad(AssetPath, LoadDelegate);
}

// ========================================
// ✅ FUNÇÕES AUXILIARES FINAIS
// ========================================

float UAURACRONPCGSubsystem::CalculateFlowVolatility() const
{
    // ✅ Calcular volatilidade do fluxo baseada na fase e qualidade
    float BaseVolatility = 0.3f;
    float PhaseMultiplier = 1.0f + ((int32)CurrentMapPhase * 0.25f);
    float QualityMultiplier = 1.0f + (CurrentQualityLevel * 0.1f);

    return BaseVolatility * PhaseMultiplier * QualityMultiplier;
}

float UAURACRONPCGSubsystem::CalculateFlowSpeed(float NormalizedTime) const
{
    // ✅ Calcular velocidade do fluxo baseada no tempo normalizado
    float BaseSpeed = 100.0f;
    float TimeVariation = 1.0f + 0.3f * FMath::Sin(NormalizedTime * 2.0f * PI);
    float PhaseMultiplier = GetPhaseIntensityMultiplier();

    return BaseSpeed * TimeVariation * PhaseMultiplier;
}

FLinearColor UAURACRONPCGSubsystem::GetFlowColorForCurrentPhase() const
{
    // ✅ Retornar cor do fluxo baseada na fase atual
    switch (CurrentMapPhase)
    {
    case EAURACRONMapPhase::Awakening:
        return FLinearColor(0.3f, 0.7f, 1.0f, 1.0f); // Azul suave
    case EAURACRONMapPhase::Convergence:
        return FLinearColor(0.7f, 0.3f, 1.0f, 1.0f); // Roxo
    case EAURACRONMapPhase::Intensification:
        return FLinearColor(1.0f, 0.5f, 0.2f, 1.0f); // Laranja intenso
    case EAURACRONMapPhase::Resolution:
        return FLinearColor(1.0f, 1.0f, 0.3f, 1.0f); // Dourado
    default:
        return FLinearColor(0.5f, 0.5f, 0.5f, 1.0f); // Cinza padrão
    }
}

// ========================================
// ✅ FUNÇÕES DE MULTIPLICADORES E CAPACIDADE
// ========================================

float UAURACRONPCGSubsystem::GetDeviceCapacityMultiplier() const
{
    // ✅ Calcular multiplicador baseado na capacidade do dispositivo
    switch (CurrentQualityLevel)
    {
    case 0: return 0.5f;  // Entry level
    case 1: return 0.7f;  // Mid-range
    case 2: return 0.9f;  // High-end
    case 3: return 1.0f;  // Epic
    default: return 0.7f;
    }
}

float UAURACRONPCGSubsystem::GetRenderingCapacityMultiplier() const
{
    // ✅ Calcular multiplicador baseado na capacidade de renderização
    float BaseCapacity = GetDeviceCapacityMultiplier();

    // Ajustar baseado na performance atual
    UWorld* World = GetWorld();
    if (World)
    {
        float DeltaTime = World->GetDeltaSeconds();
        if (DeltaTime > 0.033f) // Abaixo de 30 FPS
        {
            BaseCapacity *= 0.7f;
        }
        else if (DeltaTime < 0.016f) // Acima de 60 FPS
        {
            BaseCapacity *= 1.2f;
        }
    }

    return FMath::Clamp(BaseCapacity, 0.3f, 1.5f);
}

float UAURACRONPCGSubsystem::GetEffectsScaleForDevice() const
{
    // ✅ Calcular escala de efeitos baseada no dispositivo
    return GetRenderingCapacityMultiplier();
}

float UAURACRONPCGSubsystem::GetHardwareCapacityMultiplier() const
{
    // ✅ Calcular multiplicador baseado na capacidade de hardware
    return GetDeviceCapacityMultiplier();
}

float UAURACRONPCGSubsystem::GetAdaptiveIntensityForDevice() const
{
    // ✅ Calcular intensidade adaptativa baseada no dispositivo
    return GetRenderingCapacityMultiplier();
}

// ========================================
// ✅ FUNÇÕES DE CONFIGURAÇÃO DE TRILHAS E AMBIENTES
// ========================================

void UAURACRONPCGSubsystem::SetEnvironmentActiveState(EAURACRONEnvironmentType EnvironmentType, bool bActive, float Intensity)
{
    // ✅ Definir estado ativo do ambiente
    if (UPCGComponent* EnvComponent = EnvironmentComponents.FindRef(EnvironmentType))
    {
        if (IsValid(EnvComponent))
        {
            UpdatePCGParametersModern(EnvComponent, TEXT("bIsActive"), bActive ? 1.0f : 0.0f);
            UpdatePCGParametersModern(EnvComponent, TEXT("Intensity"), Intensity);

            if (bActive)
            {
                EnvComponent->Generate();
            }

            UE_LOGFMT(LogTemp, Log, "Ambiente {0} definido como {1} com intensidade {2}",
                      (int32)EnvironmentType, bActive ? TEXT("ativo") : TEXT("inativo"), Intensity);
        }
    }
}

void UAURACRONPCGSubsystem::SetTrailIntensity(EAURACRONTrailType TrailType, float Intensity)
{
    // ✅ Definir intensidade da trilha
    if (UPCGComponent* TrailComponent = TrailComponents.FindRef(TrailType))
    {
        if (IsValid(TrailComponent))
        {
            UpdatePCGParametersModern(TrailComponent, TEXT("Intensity"), Intensity);

            if (Intensity > 0.1f)
            {
                TrailComponent->Generate();
            }

            UE_LOGFMT(LogTemp, VeryVerbose, "Trilha {0} intensidade definida como {1}", (int32)TrailType, Intensity);
        }
    }
}

void UAURACRONPCGSubsystem::EnableTrailIntersections(bool bEnable)
{
    // ✅ Habilitar intersecções de trilhas
    for (auto& TrailPair : TrailComponents)
    {
        if (UPCGComponent* TrailComponent = TrailPair.Value)
        {
            if (IsValid(TrailComponent))
            {
                UpdatePCGParametersModern(TrailComponent, TEXT("bEnableIntersections"), bEnable ? 1.0f : 0.0f);
            }
        }
    }

    UE_LOGFMT(LogTemp, Log, "Intersecções de trilhas {0}", bEnable ? TEXT("habilitadas") : TEXT("desabilitadas"));
}

void UAURACRONPCGSubsystem::EnableTrailConvergence(float EffectsScale)
{
    // ✅ Habilitar convergência de trilhas
    for (auto& TrailPair : TrailComponents)
    {
        if (UPCGComponent* TrailComponent = TrailPair.Value)
        {
            if (IsValid(TrailComponent))
            {
                UpdatePCGParametersModern(TrailComponent, TEXT("bEnableConvergence"), 1.0f);
                UpdatePCGParametersModern(TrailComponent, TEXT("ConvergenceScale"), EffectsScale);
            }
        }
    }

    UE_LOGFMT(LogTemp, Log, "Convergência de trilhas habilitada com escala {0}", EffectsScale);
}

// ========================================
// ✅ FUNÇÕES DE APLICAÇÃO DE EFEITOS E ORÇAMENTO
// ========================================

void UAURACRONPCGSubsystem::ApplyTerrainChangesForPhase()
{
    // ✅ Aplicar mudanças de terreno baseadas na fase
    for (auto& EnvPair : EnvironmentComponents)
    {
        if (UPCGComponent* EnvComponent = EnvPair.Value)
        {
            if (IsValid(EnvComponent))
            {
                UpdatePCGParametersModern(EnvComponent, TEXT("TerrainVariation"), GetPhaseIntensityMultiplier());
                UpdatePCGParametersModern(EnvComponent, TEXT("PhaseBlending"), 1.0f);
                EnvComponent->Generate();
            }
        }
    }

    UE_LOGFMT(LogTemp, Log, "Mudanças de terreno aplicadas para fase {0}", (int32)CurrentMapPhase);
}

void UAURACRONPCGSubsystem::ApplyFinalConvergenceEffects()
{
    // ✅ Aplicar efeitos de convergência final
    float ConvergenceIntensity = GetEffectsScaleForDevice();

    for (auto& EnvPair : EnvironmentComponents)
    {
        if (UPCGComponent* EnvComponent = EnvPair.Value)
        {
            if (IsValid(EnvComponent))
            {
                UpdatePCGParametersModern(EnvComponent, TEXT("FinalConvergence"), ConvergenceIntensity);
                UpdatePCGParametersModern(EnvComponent, TEXT("ConvergenceEffects"), 1.0f);
                EnvComponent->Generate();
            }
        }
    }

    UE_LOGFMT(LogTemp, Log, "Efeitos de convergência final aplicados com intensidade {0}", ConvergenceIntensity);
}

void UAURACRONPCGSubsystem::NotifyEnvironmentSystemsOfPhaseChange()
{
    // ✅ Notificar sistemas de ambiente sobre mudança de fase
    UWorld* World = GetWorld();
    if (!World)
    {
        return;
    }

    // Notificar todos os EnvironmentManagers
    for (TActorIterator<AAURACRONPCGEnvironmentManager> ActorItr(World); ActorItr; ++ActorItr)
    {
        AAURACRONPCGEnvironmentManager* EnvManager = *ActorItr;
        if (IsValid(EnvManager))
        {
            EnvManager->UpdateForMapPhase(CurrentMapPhase);
        }
    }

    UE_LOGFMT(LogTemp, Log, "Sistemas de ambiente notificados sobre mudança de fase");
}

void UAURACRONPCGSubsystem::ApplyParticlesBudgetToTrails()
{
    // ✅ Aplicar orçamento de partículas às trilhas
    int32 TrailBudget = ParticlesBudget / FMath::Max(TrailComponents.Num(), 1);

    for (auto& TrailPair : TrailComponents)
    {
        if (UPCGComponent* TrailComponent = TrailPair.Value)
        {
            if (IsValid(TrailComponent))
            {
                UpdatePCGParametersModern(TrailComponent, TEXT("ParticlesBudget"), (float)TrailBudget);
            }
        }
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "Orçamento de partículas aplicado às trilhas: {0} por trilha", TrailBudget);
}

void UAURACRONPCGSubsystem::ApplyParticlesBudgetToPrismalFlow()
{
    // ✅ Aplicar orçamento de partículas ao Fluxo Prismal
    if (PrismalFlowComponent && IsValid(PrismalFlowComponent))
    {
        int32 FlowBudget = ParticlesBudget / 2; // Metade do orçamento para o Fluxo Prismal
        UpdatePCGParametersModern(PrismalFlowComponent, TEXT("ParticlesBudget"), (float)FlowBudget);

        UE_LOGFMT(LogTemp, VeryVerbose, "Orçamento de partículas aplicado ao Fluxo Prismal: {0}", FlowBudget);
    }
}

// ========================================
// ✅ FUNÇÕES DE INTEGRAÇÃO COM MOVIMENTO
// ========================================

void UAURACRONPCGSubsystem::ApplyPrismalFlowVFXToCharacter(ACharacter* Character, FVector FlowDirection, float FlowSpeed)
{
    // ✅ Aplicar efeitos visuais do Fluxo Prismal ao personagem
    if (!Character || !IsValid(Character))
    {
        return;
    }

    // Implementar VFX baseado na qualidade
    float VFXIntensity = GetEffectsScaleForDevice();

    UE_LOGFMT(LogTemp, VeryVerbose, "VFX do Fluxo Prismal aplicado ao personagem {0} com intensidade {1}",
              Character->GetName(), VFXIntensity);
}

void UAURACRONPCGSubsystem::RemovePrismalFlowVFXFromCharacter(ACharacter* Character)
{
    // ✅ Remover efeitos visuais do Fluxo Prismal do personagem
    if (!Character || !IsValid(Character))
    {
        return;
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "VFX do Fluxo Prismal removido do personagem {0}", Character->GetName());
}

void UAURACRONPCGSubsystem::NotifySystemsOfCharacterFlowEntry(ACharacter* Character, FVector FlowDirection, float FlowSpeed)
{
    // ✅ Notificar sistemas sobre entrada do personagem no fluxo
    UE_LOGFMT(LogTemp, VeryVerbose, "Sistemas notificados sobre entrada do personagem {0} no fluxo", Character->GetName());
}

void UAURACRONPCGSubsystem::NotifySystemsOfCharacterFlowExit(ACharacter* Character)
{
    // ✅ Notificar sistemas sobre saída do personagem do fluxo
    UE_LOGFMT(LogTemp, VeryVerbose, "Sistemas notificados sobre saída do personagem {0} do fluxo", Character->GetName());
}

void UAURACRONPCGSubsystem::NotifySystemsOfMovementStateChange(ACharacter* Character, EAURACRONMovementState MovementState)
{
    // ✅ Notificar sistemas sobre mudança de estado de movimento
    UE_LOGFMT(LogTemp, VeryVerbose, "Sistemas notificados sobre mudança de estado de movimento do personagem {0} para {1}",
              Character->GetName(), (int32)MovementState);
}

// ========================================
// ✅ HANDLERS DE ESTADO DE MOVIMENTO
// ========================================

void UAURACRONPCGSubsystem::HandleNormalMovementState(ACharacter* Character)
{
    // ✅ Lidar com estado de movimento normal
    if (!Character || !IsValid(Character))
    {
        return;
    }

    // Remover todos os efeitos especiais
    RemovePrismalFlowVFXFromCharacter(Character);

    UE_LOGFMT(LogTemp, VeryVerbose, "Estado de movimento normal aplicado ao personagem {0}", Character->GetName());
}

void UAURACRONPCGSubsystem::HandlePrismalFlowMovementState(ACharacter* Character)
{
    // ✅ Lidar com estado de movimento no Fluxo Prismal
    if (!Character || !IsValid(Character))
    {
        return;
    }

    // Aplicar efeitos do Fluxo Prismal
    FVector FlowDirection = CalculateFlowDirectionAtLocation(Character->GetActorLocation());
    float FlowSpeed = CalculateFlowSpeed(GetWorld()->GetTimeSeconds());

    ApplyPrismalFlowVFXToCharacter(Character, FlowDirection, FlowSpeed);

    UE_LOGFMT(LogTemp, VeryVerbose, "Estado de movimento no Fluxo Prismal aplicado ao personagem {0}", Character->GetName());
}

void UAURACRONPCGSubsystem::HandleSigilDashMovementState(ACharacter* Character)
{
    // ✅ Lidar com estado de dash de sígilo
    if (!Character || !IsValid(Character))
    {
        return;
    }

    // Aplicar efeitos visuais de dash baseados na qualidade
    float DashVFXIntensity = GetEffectsScaleForDevice();

    UE_LOGFMT(LogTemp, VeryVerbose, "Estado de dash de sígilo aplicado ao personagem {0} com intensidade {1}",
              Character->GetName(), DashVFXIntensity);
}

void UAURACRONPCGSubsystem::HandleEnvironmentBoostMovementState(ACharacter* Character)
{
    // ✅ Lidar com estado de boost de ambiente
    if (!Character || !IsValid(Character))
    {
        return;
    }

    // Determinar tipo de boost baseado no ambiente atual
    EAURACRONEnvironmentType CurrentEnvironment = DetermineEnvironmentAtLocation(Character->GetActorLocation());

    UE_LOGFMT(LogTemp, VeryVerbose, "Estado de boost de ambiente {0} aplicado ao personagem {1}",
              (int32)CurrentEnvironment, Character->GetName());
}

void UAURACRONPCGSubsystem::HandleStunnedMovementState(ACharacter* Character)
{
    // ✅ Lidar com estado atordoado
    if (!Character || !IsValid(Character))
    {
        return;
    }

    // Aplicar efeitos visuais de atordoamento
    float StunVFXIntensity = GetEffectsScaleForDevice() * 0.7f; // Reduzido para não sobrecarregar

    UE_LOGFMT(LogTemp, VeryVerbose, "Estado atordoado aplicado ao personagem {0}", Character->GetName());
}

void UAURACRONPCGSubsystem::HandleRootedMovementState(ACharacter* Character)
{
    // ✅ Lidar com estado enraizado
    if (!Character || !IsValid(Character))
    {
        return;
    }

    // Aplicar efeitos visuais de enraizamento
    float RootVFXIntensity = GetEffectsScaleForDevice() * 0.8f;

    UE_LOGFMT(LogTemp, VeryVerbose, "Estado enraizado aplicado ao personagem {0}", Character->GetName());
}

// ========================================
// ✅ FUNÇÕES AUXILIARES FINAIS
// ========================================

FVector UAURACRONPCGSubsystem::CalculateFlowDirectionAtLocation(FVector Location) const
{
    // ✅ Calcular direção do fluxo em uma localização específica
    if (!PrismalFlowComponent || !IsValid(PrismalFlowComponent))
    {
        return FVector::ForwardVector;
    }

    // Implementar cálculo baseado na posição no Fluxo Prismal
    FVector FlowCenter = FVector::ZeroVector;
    if (CentralAuracronIsland && IsValid(CentralAuracronIsland))
    {
        FlowCenter = CentralAuracronIsland->GetActorLocation();
    }

    FVector DirectionToCenter = (FlowCenter - Location).GetSafeNormal();
    FVector TangentDirection = FVector::CrossProduct(DirectionToCenter, FVector::UpVector);

    return TangentDirection.GetSafeNormal();
}

EAURACRONEnvironmentType UAURACRONPCGSubsystem::DetermineEnvironmentAtLocation(FVector Location) const
{
    // ✅ Determinar tipo de ambiente em uma localização específica
    float MinDistance = MAX_FLT;
    EAURACRONEnvironmentType ClosestEnvironment = EAURACRONEnvironmentType::RadiantPlains;

    for (const auto& EnvPair : EnvironmentVolumes)
    {
        if (APCGVolume* Volume = EnvPair.Value)
        {
            if (IsValid(Volume))
            {
                float Distance = FVector::Dist(Volume->GetActorLocation(), Location);
                if (Distance < MinDistance)
                {
                    MinDistance = Distance;
                    ClosestEnvironment = EnvPair.Key;
                }
            }
        }
    }

    return ClosestEnvironment;
}

// ========================================
// ✅ IMPLEMENTAÇÃO COMPLETA FINALIZADA
// ========================================