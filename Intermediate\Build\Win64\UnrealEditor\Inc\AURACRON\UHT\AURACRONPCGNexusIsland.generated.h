// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PCG/AURACRONPCGNexusIsland.h"

#ifdef AURACRON_AURACRONPCGNexusIsland_generated_h
#error "AURACRONPCGNexusIsland.generated.h already included, missing '#pragma once' in AURACRONPCGNexusIsland.h"
#endif
#define AURACRON_AURACRONPCGNexusIsland_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
enum class EIslandSectorType : uint8;

// ********** Begin Class ANexusIsland *************************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGNexusIsland_h_36_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execUpdateVisualEffects); \
	DECLARE_FUNCTION(execRemoveFlowManipulationAbility); \
	DECLARE_FUNCTION(execReleaseSector); \
	DECLARE_FUNCTION(execCaptureSector); \
	DECLARE_FUNCTION(execGrantFlowManipulationAbility);


AURACRON_API UClass* Z_Construct_UClass_ANexusIsland_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGNexusIsland_h_36_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesANexusIsland(); \
	friend struct Z_Construct_UClass_ANexusIsland_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_ANexusIsland_NoRegister(); \
public: \
	DECLARE_CLASS2(ANexusIsland, APrismalFlowIsland, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_ANexusIsland_NoRegister) \
	DECLARE_SERIALIZER(ANexusIsland) \
	enum class ENetFields_Private : uint16 \
	{ \
		NETFIELD_REP_START=(uint16)((int32)Super::ENetFields_Private::NETFIELD_REP_END + (int32)1), \
		PowerIntensity=NETFIELD_REP_START, \
		PowerDuration, \
		FlowManipulationEffect, \
		NexusSectorComponent, \
		SanctuarySectorComponent, \
		ArsenalSectorComponent, \
		ChaosSectorComponent, \
		NETFIELD_REP_END=ChaosSectorComponent	}; \
	DECLARE_VALIDATE_GENERATED_REP_ENUMS(NO_API)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGNexusIsland_h_36_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	ANexusIsland(ANexusIsland&&) = delete; \
	ANexusIsland(const ANexusIsland&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ANexusIsland); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ANexusIsland); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(ANexusIsland) \
	NO_API virtual ~ANexusIsland();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGNexusIsland_h_33_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGNexusIsland_h_36_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGNexusIsland_h_36_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGNexusIsland_h_36_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGNexusIsland_h_36_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class ANexusIsland;

// ********** End Class ANexusIsland ***************************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGNexusIsland_h

// ********** Begin Enum EIslandSectorType *********************************************************
#define FOREACH_ENUM_EISLANDSECTORTYPE(op) \
	op(EIslandSectorType::Nexus) \
	op(EIslandSectorType::Sanctuary) \
	op(EIslandSectorType::Arsenal) \
	op(EIslandSectorType::Chaos) 

enum class EIslandSectorType : uint8;
template<> struct TIsUEnumClass<EIslandSectorType> { enum { Value = true }; };
template<> AURACRON_API UEnum* StaticEnum<EIslandSectorType>();
// ********** End Enum EIslandSectorType ***********************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
