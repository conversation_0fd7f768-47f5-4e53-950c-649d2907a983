// AURACRONPCGNexusIsland.h
// Definição da classe ANexusIsland para o sistema Prismal Flow

#pragma once

#include "CoreMinimal.h"
#include "PCG/AURACRONPCGPrismalFlow.h"
#include "GameplayEffect.h"
#include "ActiveGameplayEffectHandle.h"
#include "Engine/StreamableManager.h"
#include "AURACRONPCGNexusIsland.generated.h"

class UNiagaraSystem;
class UNiagaraComponent;
class UAudioComponent;
class UPointLightComponent;
struct FStreamableManager;

// Enum para tipos de setores da Ilha Central Auracron
UENUM(BlueprintType)
enum class EIslandSectorType : uint8
{
    Nexus       UMETA(DisplayName = "Setor Nexus"),
    Sanctuary   UMETA(DisplayName = "Setor Santuário"),
    Arsenal     UMETA(DisplayName = "Setor Arsenal"),
    Chaos       UMETA(DisplayName = "Setor Caos")
};

/**
 * Implementação específica da Nexus Island
 * Ilha central com poderes especiais que concede habilidades de manipulação do flow
 */
UCLASS()
class AURACRON_API ANexusIsland : public APrismalFlowIsland
{
    GENERATED_BODY()
    
public:
    ANexusIsland();

    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void Tick(float DeltaTime) override;
    virtual void ApplyIslandEffect(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult) override;

    // Concede habilidade de manipulação do flow ao jogador
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Nexus Island")
    void GrantFlowManipulationAbility(AActor* TargetActor);

    // Funções de controle de setores
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Nexus Island|Sectors")
    void CaptureSector(EIslandSectorType SectorType, AActor* ControllingTeam);

    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Nexus Island|Sectors")
    void ReleaseSector(EIslandSectorType SectorType);
    
protected:
    // Remove a habilidade de manipulação do flow
    UFUNCTION()
    void RemoveFlowManipulationAbility(AActor* TargetActor);

    // Funções de atualização otimizada
    UFUNCTION()
    void UpdateVisualEffects();

    // Funções dos setores da Ilha Central Auracron
    void InitializeIslandSectors();
    void UpdateIslandSectors();
    void ApplySectorSpecificBenefits(EIslandSectorType SectorType, AActor* ControllingTeam);
    void RemoveSectorSpecificBenefits(EIslandSectorType SectorType, AActor* PreviousController);
    void CheckTotalIslandControl(AActor* ControllingTeam);
    void ApplyTotalControlBenefits(AActor* ControllingTeam);
    void ApplySectorBenefits(AActor* TargetActor);
    void RemoveSectorBenefits(AActor* TargetActor);

    // Funções de carregamento assíncrono
    void LoadIslandAssetsAsync();
    void OnAssetsLoaded();
    void ApplyLoadedAssets();
    
    // Intensidade do poder concedido
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Nexus Island")
    float PowerIntensity;
    
    // Duração do poder concedido
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Nexus Island")
    float PowerDuration;
    
    // Efeito visual do poder
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Nexus Island")
    UNiagaraSystem* PowerEffect;
    
    // Efeito de gameplay para manipulação de fluxo
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Nexus Island")
    TSubclassOf<UGameplayEffect> FlowManipulationEffect;
    
    // Mapa de efeitos ativos de manipulação de fluxo por ator
    UPROPERTY()
    TMap<AActor*, FActiveGameplayEffectHandle> ActiveFlowManipulationEffects;
    
    // Torre de controle central
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Nexus Island")
    UStaticMeshComponent* CentralTower;
    
    // Efeito de energia da torre
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Nexus Island")
    UNiagaraComponent* TowerEnergyEffect;
    
    // Plataformas defensivas em múltiplos níveis
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Nexus Island")
    TArray<UStaticMeshComponent*> DefensivePlatforms;
    
    // Gerador de recursos
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Nexus Island")
    UStaticMeshComponent* ResourceGenerator;
    
    // Efeito do gerador de recursos
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Nexus Island")
    UNiagaraComponent* ResourceEffect;
    
    // Tempo acumulado para efeitos
    UPROPERTY()
    float AccumulatedTime;

    // ========================================
    // COMPONENTES DOS SETORES DA ILHA CENTRAL AURACRON
    // ========================================

    // Setor Nexus: Geradores de recursos e manipulação do Fluxo
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Replicated, Category = "Prismal Flow|Nexus Island|Sectors")
    UStaticMeshComponent* NexusSectorComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Nexus Island|Sectors")
    UNiagaraComponent* NexusSectorEffect;

    // Setor Santuário: Fontes de cura e amplificadores de visão
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Replicated, Category = "Prismal Flow|Nexus Island|Sectors")
    UStaticMeshComponent* SanctuarySectorComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Nexus Island|Sectors")
    UNiagaraComponent* SanctuarySectorEffect;

    // Setor Arsenal: Upgrades de armas e potencializadores de habilidades
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Replicated, Category = "Prismal Flow|Nexus Island|Sectors")
    UStaticMeshComponent* ArsenalSectorComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Nexus Island|Sectors")
    UNiagaraComponent* ArsenalSectorEffect;

    // Setor Caos: Perigos ambientais com recompensas de alto risco
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Replicated, Category = "Prismal Flow|Nexus Island|Sectors")
    UStaticMeshComponent* ChaosSectorComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Nexus Island|Sectors")
    UNiagaraComponent* ChaosSectorEffect;

    // Mapa de controle dos setores - UE 5.6: TMap não suporta replicação direta
    UPROPERTY(BlueprintReadOnly, Category = "Prismal Flow|Nexus Island|Sectors")
    TMap<EIslandSectorType, AActor*> ControlledSectors;

    // ========================================
    // COMPONENTES DE ÁUDIO E ILUMINAÇÃO
    // ========================================

    // Sistema de áudio da torre
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Nexus Island|Audio")
    UAudioComponent* TowerAudioComponent;

    // Iluminação da torre
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Nexus Island|Lighting")
    UPointLightComponent* TowerLightComponent;

    // ========================================
    // SISTEMA DE CARREGAMENTO ASSÍNCRONO
    // ========================================

    // Referência ao StreamableManager para carregamento assíncrono - UE 5.6: usar TSharedPtr
    TSharedPtr<FStreamableManager> StreamableManager;

    // Handle para carregamento assíncrono de assets
    TSharedPtr<FStreamableHandle> AssetLoadHandle;

    // Timer para atualização otimizada de efeitos visuais
    FTimerHandle VisualUpdateTimerHandle;

    virtual void UpdateIslandVisuals() override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;
};