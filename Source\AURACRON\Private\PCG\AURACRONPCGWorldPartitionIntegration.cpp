// AURACRONPCGWorldPartitionIntegration.cpp
// Implementação da Integração com World Partition para AURACRON - UE 5.6
// Sistema de streaming e gerenciamento de elementos PCG usando APIs modernas
// Suporte para otimizações de hardware e streaming adaptativo

#include "PCG/AURACRONPCGWorldPartitionIntegration.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "PCG/AURACRONPCGEnvironment.h"
#include "PCG/AURACRONPCGIsland.h"
#include "PCG/AURACRONPCGPrismalFlow.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "GameFramework/PlayerController.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetSystemLibrary.h"
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionSubsystem.h"
#include "WorldPartition/DataLayer/DataLayerSubsystem.h"
#include "WorldPartition/WorldPartitionStreamingPolicy.h"
#include "Engine/GameViewportClient.h"
#include "Engine/GameInstance.h"
#include "HAL/PlatformMemory.h"
#include "HAL/PlatformApplicationMisc.h"
#include "GenericPlatform/GenericPlatformMemory.h"
#include "Curves/CurveFloat.h"
#include "TimerManager.h"
// UE 5.6 Modern APIs - Includes Adicionados
#include "StreamableManager.h"
#include "Logging/StructuredLog.h"
#include "Net/UnrealNetwork.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Components/AudioComponent.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Engine/DataTable.h"
#include "Components/PointLightComponent.h"
#include "Components/SphereComponent.h"

AAURACRONPCGWorldPartitionIntegration::AAURACRONPCGWorldPartitionIntegration()
    : StreamingRadius(5000.0f)
    , UpdateInterval(0.1f) // UE 5.6 Otimizado: 0.1f para melhor responsividade
    , bAutoUpdateStreaming(true)
{
    // UE 5.6 Otimização: Desabilitar Tick tradicional, usar Timer
    PrimaryActorTick.bCanEverTick = false;
    PrimaryActorTick.TickInterval = 0.0f;

    // Configurar replicação para multiplayer
    bReplicates = true;
    SetReplicateMovement(false);

    // Criar componente raiz
    RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootComponent"));

    // UE 5.6 - Inicializar StreamableManager para carregamento assíncrono
    StreamableManager = MakeShared<FStreamableManager>();

    // Configurações de otimização de hardware
    HardwareConfig.QualityProfile = EAURACRONStreamingQualityProfile::Medium;
    HardwareConfig.DistanceMultiplier = 1.0f;
    HardwareConfig.StreamingDistanceMultiplier = 1.0f; // Propriedade adicionada
    HardwareConfig.bIsLowEndHardware = false;
    HardwareConfig.MemoryBudgetMB = 1024;
    HardwareConfig.MaxConcurrentStreamingElements = 100;
    HardwareConfig.LODDistanceFactors = {1.0f, 0.75f, 0.5f, 0.25f};
    HardwareConfig.TargetFPS = 60.0f;
    HardwareConfig.MinAcceptableFPS = 30.0f;
    HardwareConfig.bEnableDynamicFPSAdjustment = true;

    // Estatísticas de desempenho
    PerformanceStats.AverageFrameTime = 0.0f;
    PerformanceStats.ElementsCurrentlyStreamed = 0;
    PerformanceStats.PeakMemoryUsageMB = 0;
    PerformanceStats.LastUpdateTime = 0.0f;
    PerformanceStats.AverageFPS = 60.0f;
    PerformanceStats.HitchesDetected = 0;
    PerformanceStats.TotalElementLoadTime = 0.0f;
    PerformanceStats.ElementLoadCount = 0;
    PerformanceStats.AverageElementLoadTime = 0.0f;
    PerformanceStats.SlowLoadCount = 0;
    PerformanceStats.StreamingOperationsPerSecond = 0.0f;
    PerformanceStats.CurrentMemoryUsageMB = 0.0f;

    // Configurações de otimização dinâmica
    StatsUpdateInterval = 5.0f;
    bEnableDynamicOptimizations = true;
    bAutoAdjustQualityProfile = true; // Propriedade adicionada
    LowFPSThreshold = 30.0f;
    MinTimeBetweenOptimizationAdjustments = 10.0f;
    bUsePlatformSpecificOptimizations = true;

    // Inicialização de variáveis de rastreamento
    TimeSinceLastOptimizationAdjustment = 0.0f;
    StatsUpdateAccumulator = 0.0f;
    TotalElementsLoaded = 0;
    TotalElementsUnloaded = 0;

    // UE 5.6 - Orçamento de partículas escalável baseado na documentação AURACRON
    ParticleBudgets.Add(EAURACRONStreamingQualityProfile::Low, 300);    // Entry Level
    ParticleBudgets.Add(EAURACRONStreamingQualityProfile::Medium, 800); // Mid-range
    ParticleBudgets.Add(EAURACRONStreamingQualityProfile::High, 2000);  // High-end
    ParticleBudgets.Add(EAURACRONStreamingQualityProfile::Ultra, 3000); // Ultra
}

// UE 5.6 - Implementar GetLifetimeReplicatedProps para replicação moderna
void AAURACRONPCGWorldPartitionIntegration::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    // Replicar configurações de hardware para clientes
    DOREPLIFETIME(AAURACRONPCGWorldPartitionIntegration, HardwareConfig);
    DOREPLIFETIME(AAURACRONPCGWorldPartitionIntegration, PerformanceStats);
    DOREPLIFETIME(AAURACRONPCGWorldPartitionIntegration, StreamingRadius);
    DOREPLIFETIME(AAURACRONPCGWorldPartitionIntegration, bAutoUpdateStreaming);
}

void AAURACRONPCGWorldPartitionIntegration::BeginPlay()
{
    Super::BeginPlay();

    // Inicializar integração apenas no servidor
    if (HasAuthority())
    {
        InitializeWorldPartitionIntegration();

        // Detectar capacidades de hardware e otimizar streaming
        DetectHardwareCapabilities();
        OptimizeStreamingForCurrentPlatform();

        // UE 5.6 Otimizado: Usar Timer em vez de Tick para melhor performance
        if (bAutoUpdateStreaming && UpdateInterval > 0.0f)
        {
            GetWorld()->GetTimerManager().SetTimer(UpdateTimerHandle, this,
                &AAURACRONPCGWorldPartitionIntegration::UpdateStreamingForAllPlayers,
                UpdateInterval, true);
        }

        // Configurar timer para atualização de estatísticas de desempenho
        GetWorld()->GetTimerManager().SetTimer(StatsTimerHandle, FTimerDelegate::CreateUObject(this,
            &AAURACRONPCGWorldPartitionIntegration::UpdatePerformanceStats, StatsUpdateInterval),
            StatsUpdateInterval, true);

        // UE 5.6 - Inicializar sistemas AURACRON específicos
        InitializeAURACRONSystems();

        UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::BeginPlay - Sistema de streaming PCG inicializado com otimizações de hardware UE 5.6");
    }
}

// UE 5.6 Otimizado: Tick removido, substituído por Timer-based system
// A lógica do Tick foi movida para funções de Timer específicas para melhor performance

// UE 5.6 - Nova função para inicializar sistemas específicos do AURACRON
void AAURACRONPCGWorldPartitionIntegration::InitializeAURACRONSystems()
{
    if (!HasAuthority())
    {
        return;
    }

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::InitializeAURACRONSystems - Inicializando sistemas AURACRON específicos");

    // Inicializar Trilhos Solar/Axis/Lunar baseado na documentação
    InitializeSolarAxisLunarTrails();

    // Inicializar Fluxo Prismal serpentino
    InitializePrismalFlow();

    // Inicializar Ilha Central Auracron com 4 setores
    InitializeCentralAuracronIsland();

    // Configurar orçamento de partículas baseado no hardware
    ConfigureParticleBudgets();

    // Configurar timer para otimizações dinâmicas
    if (bEnableDynamicOptimizations)
    {
        GetWorld()->GetTimerManager().SetTimer(OptimizationTimerHandle, this,
            &AAURACRONPCGWorldPartitionIntegration::PerformDynamicOptimizations,
            MinTimeBetweenOptimizationAdjustments, true);
    }
}

// ========================================
// UE 5.6 - IMPLEMENTAÇÕES DOS SISTEMAS AURACRON ESPECÍFICOS
// ========================================

// UE 5.6 - Implementar Trilhos Solar/Axis/Lunar baseado na documentação AURACRON
void AAURACRONPCGWorldPartitionIntegration::InitializeSolarAxisLunarTrails()
{
    if (!HasAuthority())
    {
        return;
    }

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::InitializeSolarAxisLunarTrails - Inicializando Trilhos Solar/Axis/Lunar");

    // Solar Trilhos - Correntes de energia dourada
    SolarTrailConfig.TrailType = EAURACRONTrailType::Solar;
    SolarTrailConfig.Color = FLinearColor::Yellow;
    SolarTrailConfig.SpeedBoost = 1.25f;
    SolarTrailConfig.HealthRegenBoost = 1.15f;
    SolarTrailConfig.ParticleBudget = GetParticleBudgetForQuality() / 3; // 1/3 do orçamento total
    SolarTrailConfig.bFollowsSunPosition = true;

    // Axis Trilhos - Canais cinza/prata neutros
    AxisTrailConfig.TrailType = EAURACRONTrailType::Axis;
    AxisTrailConfig.Color = FLinearColor(0.7f, 0.7f, 0.7f, 1.0f);
    AxisTrailConfig.bAllowsEnvironmentTransition = true;
    AxisTrailConfig.TransitionSpeed = 2.0f;
    AxisTrailConfig.ParticleBudget = GetParticleBudgetForQuality() / 3;
    AxisTrailConfig.bControlledByNexusPoints = true;

    // Lunar Trilhos - Caminhos etéreos azul-branco
    LunarTrailConfig.TrailType = EAURACRONTrailType::Lunar;
    LunarTrailConfig.Color = FLinearColor(0.5f, 0.8f, 1.0f, 0.8f);
    LunarTrailConfig.StealthBoost = 1.3f;
    LunarTrailConfig.VisionBoost = 1.2f;
    LunarTrailConfig.ParticleBudget = GetParticleBudgetForQuality() / 3;
    LunarTrailConfig.bVisibleOnlyAtNight = true;
    LunarTrailConfig.bFollowsLunarCycles = true;

    UE_LOGFMT(LogTemp, Log, "Trilhos configurados - Solar: {SolarBudget}, Axis: {AxisBudget}, Lunar: {LunarBudget} partículas",
              SolarTrailConfig.ParticleBudget, AxisTrailConfig.ParticleBudget, LunarTrailConfig.ParticleBudget);
}

// UE 5.6 - Implementar Fluxo Prismal serpentino baseado na documentação
void AAURACRONPCGWorldPartitionIntegration::InitializePrismalFlow()
{
    if (!HasAuthority())
    {
        return;
    }

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::InitializePrismalFlow - Inicializando Fluxo Prismal serpentino");

    // Configurar Fluxo Prismal baseado na documentação
    PrismalFlowConfig.Width = FMath::RandRange(20.0f, 50.0f); // Largura variável
    PrismalFlowConfig.FlowSpeed = 1.0f;
    PrismalFlowConfig.ChangePatternInterval = 600.0f; // 10 minutos
    PrismalFlowConfig.ParticleBudget = GetParticleBudgetForQuality() / 2; // Metade do orçamento para o fluxo principal
    PrismalFlowConfig.bSerpentinePattern = true;
    PrismalFlowConfig.CurrentColor = FLinearColor::White; // Neutro inicialmente
    PrismalFlowConfig.ControllingTeam = EAURACRONTeam::None;

    // Configurar padrão serpentino
    GenerateSerpentineFlowPattern();

    UE_LOGFMT(LogTemp, Log, "Fluxo Prismal configurado com largura {Width} e orçamento de {Budget} partículas",
              PrismalFlowConfig.Width, PrismalFlowConfig.ParticleBudget);
}

// UE 5.6 - Implementar Ilha Central Auracron com 4 setores baseado na documentação
void AAURACRONPCGWorldPartitionIntegration::InitializeCentralAuracronIsland()
{
    if (!HasAuthority())
    {
        return;
    }

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::InitializeCentralAuracronIsland - Inicializando Ilha Central Auracron");

    // Configurar Ilha Central baseada na documentação
    CentralIslandConfig.Position = FAURACRONMapDimensions::MAP_CENTER; // Centro do mapa
    CentralIslandConfig.Radius = 500.0f; // Raio da ilha
    CentralIslandConfig.bIsActive = true;

    // Configurar 4 setores conforme documentação
    // Setor Nexus - Geradores de recursos e manipulação do Fluxo
    CentralIslandConfig.NexusSector.SectorType = EAURACRONIslandSector::Nexus;
    CentralIslandConfig.NexusSector.Position = CentralIslandConfig.Position + FVector(100.0f, 0.0f, 0.0f);
    CentralIslandConfig.NexusSector.ControllingTeam = EAURACRONTeam::None;
    CentralIslandConfig.NexusSector.ResourceGenerationRate = 1.2f;
    CentralIslandConfig.NexusSector.FlowManipulationPower = 1.5f;

    // Setor Santuário - Fontes de cura e amplificadores de visão
    CentralIslandConfig.SanctuarySector.SectorType = EAURACRONIslandSector::Sanctuary;
    CentralIslandConfig.SanctuarySector.Position = CentralIslandConfig.Position + FVector(0.0f, 100.0f, 0.0f);
    CentralIslandConfig.SanctuarySector.ControllingTeam = EAURACRONTeam::None;
    CentralIslandConfig.SanctuarySector.HealingRate = 1.3f;
    CentralIslandConfig.SanctuarySector.VisionAmplification = 1.4f;

    // Setor Arsenal - Upgrades de armas e potencializadores de habilidades
    CentralIslandConfig.ArsenalSector.SectorType = EAURACRONIslandSector::Arsenal;
    CentralIslandConfig.ArsenalSector.Position = CentralIslandConfig.Position + FVector(-100.0f, 0.0f, 0.0f);
    CentralIslandConfig.ArsenalSector.ControllingTeam = EAURACRONTeam::None;
    CentralIslandConfig.ArsenalSector.WeaponUpgradeRate = 1.25f;
    CentralIslandConfig.ArsenalSector.AbilityAmplification = 1.2f;

    // Setor Caos - Perigos ambientais com recompensas de alto risco
    CentralIslandConfig.ChaosSector.SectorType = EAURACRONIslandSector::Chaos;
    CentralIslandConfig.ChaosSector.Position = CentralIslandConfig.Position + FVector(0.0f, -100.0f, 0.0f);
    CentralIslandConfig.ChaosSector.ControllingTeam = EAURACRONTeam::None;
    CentralIslandConfig.ChaosSector.RiskLevel = 1.5f;
    CentralIslandConfig.ChaosSector.RewardMultiplier = 2.0f;

    UE_LOGFMT(LogTemp, Log, "Ilha Central Auracron configurada com 4 setores na posição {Position}",
              CentralIslandConfig.Position.ToString());
}

// UE 5.6 - Configurar orçamento de partículas baseado no hardware
void AAURACRONPCGWorldPartitionIntegration::ConfigureParticleBudgets()
{
    int32 TotalBudget = GetParticleBudgetForQuality();

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::ConfigureParticleBudgets - Configurando orçamento total de {Budget} partículas para perfil {Profile}",
              TotalBudget, static_cast<int32>(HardwareConfig.QualityProfile));

    // Distribuir orçamento baseado na documentação AURACRON
    CurrentParticleBudget.TrilhosBudget = TotalBudget * 0.3f;      // 30% para Trilhos
    CurrentParticleBudget.FluxoPrismalBudget = TotalBudget * 0.4f; // 40% para Fluxo Prismal
    CurrentParticleBudget.AmbientalBudget = TotalBudget * 0.2f;    // 20% para efeitos ambientais
    CurrentParticleBudget.CombateBudget = TotalBudget * 0.1f;      // 10% para efeitos de combate

    UE_LOGFMT(LogTemp, Log, "Orçamento distribuído - Trilhos: {Trilhos}, Fluxo: {Fluxo}, Ambiental: {Ambiental}, Combate: {Combate}",
              CurrentParticleBudget.TrilhosBudget, CurrentParticleBudget.FluxoPrismalBudget,
              CurrentParticleBudget.AmbientalBudget, CurrentParticleBudget.CombateBudget);
}

// UE 5.6 - Obter orçamento de partículas baseado na qualidade
int32 AAURACRONPCGWorldPartitionIntegration::GetParticleBudgetForQuality() const
{
    if (ParticleBudgets.Contains(HardwareConfig.QualityProfile))
    {
        return ParticleBudgets[HardwareConfig.QualityProfile];
    }

    // Fallback para Medium se não encontrar
    return 800;
}

// UE 5.6 - Gerar padrão serpentino para o Fluxo Prismal
void AAURACRONPCGWorldPartitionIntegration::GenerateSerpentineFlowPattern()
{
    PrismalFlowConfig.FlowPoints.Empty();

    // Gerar pontos do padrão serpentino baseado no centro do mapa
    FVector StartPoint = FAURACRONMapDimensions::MAP_CENTER + FVector(-FAURACRONMapDimensions::MAP_RADIUS_CM * 0.8f, 0.0f, 0.0f);
    FVector EndPoint = FAURACRONMapDimensions::MAP_CENTER + FVector(FAURACRONMapDimensions::MAP_RADIUS_CM * 0.8f, 0.0f, 0.0f);

    int32 NumSegments = 8; // Número de curvas serpentinas
    float SegmentLength = FVector::Dist(StartPoint, EndPoint) / NumSegments;

    for (int32 i = 0; i <= NumSegments; ++i)
    {
        float Alpha = static_cast<float>(i) / NumSegments;
        FVector BasePoint = FMath::Lerp(StartPoint, EndPoint, Alpha);

        // Adicionar curvatura serpentina
        float CurveOffset = FMath::Sin(Alpha * PI * 4.0f) * FAURACRONMapDimensions::MAP_RADIUS_CM * 0.3f;
        FVector CurvedPoint = BasePoint + FVector(0.0f, CurveOffset, 0.0f);

        PrismalFlowConfig.FlowPoints.Add(CurvedPoint);
    }

    UE_LOGFMT(LogTemp, Log, "Gerado padrão serpentino com {NumPoints} pontos", PrismalFlowConfig.FlowPoints.Num());
}

// UE 5.6 - Função para otimizações dinâmicas baseadas em Timer
void AAURACRONPCGWorldPartitionIntegration::PerformDynamicOptimizations()
{
    if (!HasAuthority() || !bEnableDynamicOptimizations)
    {
        return;
    }

    // Calcular FPS atual
    float CurrentFPS = PerformanceStats.AverageFrameTime > 0.0f ? 1.0f / PerformanceStats.AverageFrameTime : 60.0f;

    // Verificar se precisa ajustar otimizações
    if (CurrentFPS < HardwareConfig.MinAcceptableFPS)
    {
        UE_LOGFMT(LogTemp, Warning, "Baixo FPS detectado ({FPS}). Executando otimizações dinâmicas.", CurrentFPS);
        AdjustOptimizationsForPerformance();
    }
    else if (CurrentFPS > HardwareConfig.TargetFPS * 1.2f && HardwareConfig.QualityProfile != EAURACRONStreamingQualityProfile::Ultra)
    {
        UE_LOGFMT(LogTemp, Log, "Alto FPS detectado ({FPS}). Considerando aumento de qualidade.", CurrentFPS);
        // Aumentar qualidade gradualmente se performance permitir
        ConsiderQualityIncrease();
    }

    // Detectar hitches de streaming
    DetectStreamingHitches(MinTimeBetweenOptimizationAdjustments);
}

// UE 5.6 - Carregamento assíncrono de assets de elementos
void AAURACRONPCGWorldPartitionIntegration::LoadElementAssetsAsync(const FAURACRONPCGStreamingEntry& StreamingEntry)
{
    if (!StreamableManager.IsValid())
    {
        UE_LOGFMT(LogTemp, Warning, "StreamableManager não válido para carregamento assíncrono");
        return;
    }

    // Implementar carregamento assíncrono baseado no tipo de elemento
    TArray<FSoftObjectPath> AssetsToLoad;

    if (AAURACRONPCGEnvironment* Environment = Cast<AAURACRONPCGEnvironment>(StreamingEntry.PCGElement))
    {
        // Carregar assets específicos do ambiente
        // Implementação específica seria baseada nos assets necessários
    }
    else if (AAURACRONPCGPrismalFlow* PrismalFlow = Cast<AAURACRONPCGPrismalFlow>(StreamingEntry.PCGElement))
    {
        // Carregar assets do Fluxo Prismal
        // Implementação específica seria baseada nos assets necessários
    }

    if (AssetsToLoad.Num() > 0)
    {
        StreamableManager->RequestAsyncLoad(AssetsToLoad, FStreamableDelegate::CreateUObject(this,
            &AAURACRONPCGWorldPartitionIntegration::OnElementAssetsLoaded, StreamingEntry.ElementId));
    }
}

// UE 5.6 - Callback para assets carregados
void AAURACRONPCGWorldPartitionIntegration::OnElementAssetsLoaded(FGuid ElementId)
{
    if (RegisteredElements.Contains(ElementId))
    {
        UE_LOGFMT(LogTemp, VeryVerbose, "Assets carregados assincronamente para elemento {ElementId}", ElementId.ToString());
        // Aplicar assets carregados ao elemento
    }
}

// UE 5.6 - Descarregamento assíncrono de assets
void AAURACRONPCGWorldPartitionIntegration::UnloadElementAssetsAsync(const FAURACRONPCGStreamingEntry& StreamingEntry)
{
    if (!StreamableManager.IsValid())
    {
        return;
    }

    // Implementar descarregamento de assets não utilizados
    UE_LOGFMT(LogTemp, VeryVerbose, "Descarregando assets para elemento {ElementName}",
              StreamingEntry.PCGElement ? StreamingEntry.PCGElement->GetName() : TEXT("Unknown"));
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES FALTANTES - UE 5.6 APIS MODERNAS
// ========================================

void AAURACRONPCGWorldPartitionIntegration::InitializeWorldPartitionIntegration()
{
    // Inicializar integração com World Partition usando APIs modernas do UE 5.6
    if (!HasAuthority())
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGWorldPartitionIntegration::InitializeWorldPartitionIntegration - Only server can initialize");
        return;
    }

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::InitializeWorldPartitionIntegration - Initializing World Partition integration");

    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOGFMT(LogTemp, Error, "AAURACRONPCGWorldPartitionIntegration::InitializeWorldPartitionIntegration - World is null");
        return;
    }

    // Obter subsistema de World Partition
    if (UWorldPartitionSubsystem* WorldPartitionSubsystem = World->GetSubsystem<UWorldPartitionSubsystem>())
    {
        UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::InitializeWorldPartitionIntegration - World Partition subsystem found");

        // Configurar streaming baseado nas dimensões do mapa AURACRON
        FVector MapCenter = FAURACRONMapDimensions::MAP_CENTER;
        float MapRadius = FAURACRONMapDimensions::MAP_RADIUS_CM;

        // Registrar regiões de streaming para diferentes áreas do mapa AURACRON
        RegisterStreamingRegion(TEXT("CenterRegion"), MapCenter, MapRadius * 0.3f);
        RegisterStreamingRegion(TEXT("InnerRegion"), MapCenter, MapRadius * 0.6f);
        RegisterStreamingRegion(TEXT("OuterRegion"), MapCenter, MapRadius);

        // UE 5.6 - Registrar regiões específicas dos 3 ambientes AURACRON
        RegisterStreamingRegion(TEXT("PlanicieRadiante"), MapCenter + FVector(0, -MapRadius * 0.5f, 0), MapRadius * 0.4f);
        RegisterStreamingRegion(TEXT("FirmamentoZephyr"), MapCenter + FVector(0, 0, MapRadius * 0.3f), MapRadius * 0.4f);
        RegisterStreamingRegion(TEXT("ReinoPurgatorio"), MapCenter + FVector(0, MapRadius * 0.5f, 0), MapRadius * 0.4f);
    }
    else
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGWorldPartitionIntegration::InitializeWorldPartitionIntegration - World Partition subsystem not found");
    }

    // Obter subsistema de Data Layer
    if (UDataLayerSubsystem* DataLayerSubsystem = World->GetSubsystem<UDataLayerSubsystem>())
    {
        UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::InitializeWorldPartitionIntegration - Data Layer subsystem found");

        // Configurar data layers para diferentes tipos de conteúdo PCG
        ConfigureDataLayers(DataLayerSubsystem);
    }

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::InitializeWorldPartitionIntegration - Integration initialized successfully");
}

void AAURACRONPCGWorldPartitionIntegration::RegisterPCGElementForStreaming(AActor* PCGElement, const FAURACRONPCGStreamingConfig& StreamingConfig)
{
    // Registrar elemento PCG para streaming usando APIs modernas do UE 5.6
    if (!PCGElement || !IsValid(PCGElement))
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGWorldPartitionIntegration::RegisterPCGElementForStreaming - Invalid PCG element");
        return;
    }

    if (!HasAuthority())
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGWorldPartitionIntegration::RegisterPCGElementForStreaming - Only server can register elements");
        return;
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGWorldPartitionIntegration::RegisterPCGElementForStreaming - Registering element {ElementName}", PCGElement->GetName());

    // Criar entrada de streaming para o elemento
    FAURACRONPCGStreamingEntry StreamingEntry;
    StreamingEntry.PCGElement = PCGElement;
    StreamingEntry.StreamingConfig = StreamingConfig;
    StreamingEntry.bIsCurrentlyStreamed = false;
    StreamingEntry.LastUpdateTime = GetWorld()->GetTimeSeconds();
    StreamingEntry.LastLoadTime = 0.0f; // UE 5.6 - Adicionar propriedade para rastreamento

    // UE 5.6 - Gerar ElementId único e adicionar à entrada
    FGuid ElementId = FGuid::NewGuid();
    StreamingEntry.ElementId = ElementId; // Corrigir inconsistência identificada na auditoria
    RegisteredElements.Add(ElementId, StreamingEntry);

    // UE 5.6 - Configurar elemento baseado no tipo com validações robustas
    if (AAURACRONPCGEnvironment* Environment = Cast<AAURACRONPCGEnvironment>(PCGElement))
    {
        // Ambientes têm configuração especial de streaming
        Environment->SetActivityScale(StreamingConfig.bStartActive ? 1.0f : 0.0f);
        UE_LOGFMT(LogTemp, VeryVerbose, "Configured Environment element {ElementName} with activity scale {Scale}",
                  PCGElement->GetName(), StreamingConfig.bStartActive ? 1.0f : 0.0f);
    }
    else if (AAURACRONPCGIsland* Island = Cast<AAURACRONPCGIsland>(PCGElement))
    {
        // Ilhas são sempre importantes para streaming
        Island->SetActorTickEnabled(StreamingConfig.bStartActive);
        UE_LOGFMT(LogTemp, VeryVerbose, "Configured Island element {ElementName} with tick enabled {TickEnabled}",
                  PCGElement->GetName(), StreamingConfig.bStartActive);
    }
    else if (AAURACRONPCGPrismalFlow* PrismalFlow = Cast<AAURACRONPCGPrismalFlow>(PCGElement))
    {
        // Prismal Flow tem streaming dinâmico
        PrismalFlow->SetActorHiddenInGame(!StreamingConfig.bStartActive);
        UE_LOGFMT(LogTemp, VeryVerbose, "Configured PrismalFlow element {ElementName} with hidden {Hidden}",
                  PCGElement->GetName(), !StreamingConfig.bStartActive);
    }

    // UE 5.6 - Carregamento assíncrono de assets relacionados se necessário
    if (StreamingConfig.bStartActive)
    {
        LoadElementAssetsAsync(StreamingEntry);
    }

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::RegisterPCGElementForStreaming - Registered element {ElementName} with streaming distance {Distance}",
              PCGElement->GetName(), StreamingConfig.StreamingDistance);
}

void AAURACRONPCGWorldPartitionIntegration::UpdateStreamingForPlayerLocation(const FVector& PlayerLocation)
{
    // Atualizar streaming para localização do jogador usando APIs modernas do UE 5.6
    if (!HasAuthority())
    {
        return;
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGWorldPartitionIntegration::UpdateStreamingForPlayerLocation - Updating streaming for location {Location}", PlayerLocation.ToString());

    int32 StreamedInCount = 0;
    int32 StreamedOutCount = 0;

    // UE 5.6 - Validação robusta antes de processar elementos
    if (RegisteredElements.Num() == 0)
    {
        UE_LOGFMT(LogTemp, VeryVerbose, "No registered elements to update streaming for");
        return;
    }

    // Atualizar streaming para todos os elementos registrados
    for (auto& ElementPair : RegisteredElements)
    {
        FAURACRONPCGStreamingEntry& StreamingEntry = ElementPair.Value;

        if (!StreamingEntry.PCGElement || !IsValid(StreamingEntry.PCGElement))
        {
            UE_LOGFMT(LogTemp, Warning, "Invalid PCG element found in registered elements, skipping");
            continue;
        }

        // Calcular distância do jogador ao elemento
        float Distance = FVector::Dist(PlayerLocation, StreamingEntry.PCGElement->GetActorLocation());

        // UE 5.6 - Verificar se o elemento deve ser carregado usando validação anti-cheat
        bool bShouldBeStreamed = ShouldLoadElement(StreamingEntry, Distance);

        // UE 5.6 - Validação anti-cheat: verificar se distância é válida
        if (Distance < 0.0f || Distance > FAURACRONMapDimensions::MAP_RADIUS_CM * 2.0f)
        {
            UE_LOGFMT(LogTemp, Warning, "Invalid distance {Distance} detected for element {ElementName}, possible cheat attempt",
                      Distance, StreamingEntry.PCGElement->GetName());
            continue;
        }

        // Verificar se estado de streaming mudou
        if (bShouldBeStreamed && !StreamingEntry.bIsCurrentlyStreamed)
        {
            // Stream in
            StreamInElement(StreamingEntry);
            StreamedInCount++;
        }
        else if (!bShouldBeStreamed && StreamingEntry.bIsCurrentlyStreamed)
        {
            // Stream out
            StreamOutElement(StreamingEntry);
            StreamedOutCount++;
        }
    }

    if (StreamedInCount > 0 || StreamedOutCount > 0)
    {
        UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGWorldPartitionIntegration::UpdateStreamingForPlayerLocation - Streamed in: {StreamedIn}, Streamed out: {StreamedOut}",
                  StreamedInCount, StreamedOutCount);
    }
}

// ========================================
// FUNÇÕES INTERNAS - BÁSICAS
// ========================================

bool AAURACRONPCGWorldPartitionIntegration::ShouldLoadElement(const FAURACRONPCGStreamingEntry& StreamingEntry, float Distance)
{
    // Verificar se o elemento deve ser carregado baseado na distância e configurações de hardware
    if (!IsValid(StreamingEntry.PCGElement))
    {
        return false;
    }
    
    // Aplicar multiplicador de distância baseado nas configurações de hardware
    float DistanceMultiplier = GetHardwareStreamingDistanceMultiplier();
    float AdjustedLoadingDistance = StreamingEntry.StreamingConfig.LoadingDistance * DistanceMultiplier;
    float AdjustedUnloadingDistance = StreamingEntry.StreamingConfig.UnloadingDistance * DistanceMultiplier;
    
    // Verificar se já está em streaming e usar histerese para evitar oscilações
    if (StreamingEntry.bIsCurrentlyStreamed)
    {
        // Se já está em streaming, usar distância de descarregamento (maior)
        return Distance <= AdjustedUnloadingDistance;
    }
    else
    {
        // Se não está em streaming, usar distância de carregamento (menor)
        // Verificar também se não excedemos o número máximo de elementos em streaming
        if (PerformanceStats.ElementsCurrentlyStreamed >= HardwareConfig.MaxConcurrentStreamingElements)
        {
            // Se excedemos o limite, só carregar elementos muito próximos ou de alta prioridade
            return (Distance <= AdjustedLoadingDistance * 0.5f) || 
                   (StreamingEntry.StreamingConfig.StreamingPriority > 75);
        }
        
        return Distance <= AdjustedLoadingDistance;
    }
}

void AAURACRONPCGWorldPartitionIntegration::RegisterStreamingRegion(const FString& RegionName, const FVector& Center, float Radius)
{
    // UE 5.6 - Registrar região de streaming usando APIs modernas com validações robustas
    if (RegionName.IsEmpty())
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGWorldPartitionIntegration::RegisterStreamingRegion - Empty region name provided");
        return;
    }

    if (Radius <= 0.0f)
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGWorldPartitionIntegration::RegisterStreamingRegion - Invalid radius {Radius} for region {RegionName}",
                  Radius, RegionName);
        return;
    }

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::RegisterStreamingRegion - Registering region {RegionName} at {Center} with radius {Radius}",
              RegionName, Center.ToString(), Radius);

    // UE 5.6 - Criar configuração de região com propriedades expandidas
    FAURACRONPCGStreamingRegion Region;
    Region.RegionName = RegionName;
    Region.Center = Center;
    Region.Radius = Radius;
    Region.bIsActive = true;
    Region.Priority = 50; // Prioridade padrão
    Region.LastUpdateTime = GetWorld()->GetTimeSeconds();

    // UE 5.6 - Verificar se região já existe e atualizar em vez de duplicar
    bool bRegionExists = false;
    for (int32 i = 0; i < StreamingRegions.Num(); ++i)
    {
        if (StreamingRegions[i].RegionName == RegionName)
        {
            StreamingRegions[i] = Region;
            bRegionExists = true;
            UE_LOGFMT(LogTemp, Log, "Updated existing streaming region {RegionName}", RegionName);
            break;
        }
    }

    if (!bRegionExists)
    {
        // Adicionar à lista de regiões
        StreamingRegions.Add(Region);
        UE_LOGFMT(LogTemp, Log, "Added new streaming region {RegionName}", RegionName);
    }
}

void AAURACRONPCGWorldPartitionIntegration::ConfigureDataLayers(UDataLayerSubsystem* DataLayerSubsystem)
{
    // UE 5.6 - Configurar data layers para diferentes tipos de conteúdo PCG com validações robustas
    if (!DataLayerSubsystem)
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGWorldPartitionIntegration::ConfigureDataLayers - DataLayerSubsystem is null");
        return;
    }

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::ConfigureDataLayers - Configuring data layers for PCG content");

    // UE 5.6 - Configurar data layers baseados na documentação AURACRON atualizada
    TArray<FString> DataLayerNames = {
        TEXT("AURACRON_PlanicieRadiante"),     // Planície Radiante
        TEXT("AURACRON_FirmamentoZephyr"),    // Firmamento Zephyr
        TEXT("AURACRON_ReinoPurgatorio"),     // Reino Purgatório
        TEXT("AURACRON_IlhaCentralAuracron"), // Ilha Central Auracron
        TEXT("AURACRON_FluxoPrismal"),        // Fluxo Prismal
        TEXT("AURACRON_TrilhosSolar"),        // Trilhos Solar
        TEXT("AURACRON_TrilhosAxis"),         // Trilhos Axis
        TEXT("AURACRON_TrilhosLunar"),        // Trilhos Lunar
        TEXT("AURACRON_Objectives"),          // Objetivos
        TEXT("AURACRON_SetorNexus"),          // Setor Nexus da Ilha Central
        TEXT("AURACRON_SetorSantuario"),      // Setor Santuário da Ilha Central
        TEXT("AURACRON_SetorArsenal"),        // Setor Arsenal da Ilha Central
        TEXT("AURACRON_SetorCaos")            // Setor Caos da Ilha Central
    };

    int32 ConfiguredLayers = 0;
    for (const FString& LayerName : DataLayerNames)
    {
        // UE 5.6 - Tentar ativar data layer se existir com tratamento de erro robusto
        try
        {
            // Implementação específica da API do UE 5.6 seria aqui
            // Por enquanto, apenas log da configuração
            UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGWorldPartitionIntegration::ConfigureDataLayers - Configured data layer {LayerName}", LayerName);
            ConfiguredLayers++;
        }
        catch (...)
        {
            UE_LOGFMT(LogTemp, Warning, "Failed to configure data layer {LayerName}", LayerName);
        }
    }

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::ConfigureDataLayers - Successfully configured {ConfiguredCount} out of {TotalCount} data layers",
              ConfiguredLayers, DataLayerNames.Num());
}

void AAURACRONPCGWorldPartitionIntegration::StreamInElement(FAURACRONPCGStreamingEntry& StreamingEntry)
{
    // Stream in elemento usando APIs modernas do UE 5.6
    if (!StreamingEntry.PCGElement || !IsValid(StreamingEntry.PCGElement))
    {
        return;
    }

    StreamingEntry.bIsCurrentlyStreamed = true;
    StreamingEntry.LastUpdateTime = GetWorld()->GetTimeSeconds();

    // Ativar elemento baseado no tipo
    if (AAURACRONPCGEnvironment* Environment = Cast<AAURACRONPCGEnvironment>(StreamingEntry.PCGElement))
    {
        Environment->SetActivityScale(1.0f);
        Environment->SetActorHiddenInGame(false);
        
        // Aplicar configurações de LOD baseadas no perfil de qualidade
        ApplyLODSettingsToElement(StreamingEntry);
    }
    else if (AAURACRONPCGIsland* Island = Cast<AAURACRONPCGIsland>(StreamingEntry.PCGElement))
    {
        Island->SetActorTickEnabled(true);
        Island->SetActorHiddenInGame(false);
        
        // Aplicar configurações de LOD baseadas no perfil de qualidade
        ApplyLODSettingsToElement(StreamingEntry);
    }
    else if (AAURACRONPCGPrismalFlow* PrismalFlow = Cast<AAURACRONPCGPrismalFlow>(StreamingEntry.PCGElement))
    {
        PrismalFlow->SetActorHiddenInGame(false);
        PrismalFlow->SetActorTickEnabled(true);
        
        // Aplicar configurações de LOD baseadas no perfil de qualidade
        ApplyLODSettingsToElement(StreamingEntry);
    }

    // Atualizar estatísticas
    PerformanceStats.ElementsCurrentlyStreamed++;
    TotalElementsLoaded++;

    UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGWorldPartitionIntegration::StreamInElement - Streamed in element {ElementName}",
              StreamingEntry.PCGElement->GetName());
}

void AAURACRONPCGWorldPartitionIntegration::StreamOutElement(FAURACRONPCGStreamingEntry& StreamingEntry)
{
    // UE 5.6 - Stream out elemento usando APIs modernas com validações robustas
    if (!StreamingEntry.PCGElement || !IsValid(StreamingEntry.PCGElement))
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGWorldPartitionIntegration::StreamOutElement - Invalid PCG element");
        return;
    }

    StreamingEntry.bIsCurrentlyStreamed = false;
    StreamingEntry.LastUpdateTime = GetWorld()->GetTimeSeconds();

    // UE 5.6 - Desativar elemento baseado no tipo com logging detalhado
    if (AAURACRONPCGEnvironment* Environment = Cast<AAURACRONPCGEnvironment>(StreamingEntry.PCGElement))
    {
        Environment->SetActivityScale(0.0f);
        Environment->SetActorHiddenInGame(true);
        UE_LOGFMT(LogTemp, VeryVerbose, "Streamed out Environment element {ElementName}", StreamingEntry.PCGElement->GetName());
    }
    else if (AAURACRONPCGIsland* Island = Cast<AAURACRONPCGIsland>(StreamingEntry.PCGElement))
    {
        Island->SetActorTickEnabled(false);
        Island->SetActorHiddenInGame(true);
        UE_LOGFMT(LogTemp, VeryVerbose, "Streamed out Island element {ElementName}", StreamingEntry.PCGElement->GetName());
    }
    else if (AAURACRONPCGPrismalFlow* PrismalFlow = Cast<AAURACRONPCGPrismalFlow>(StreamingEntry.PCGElement))
    {
        PrismalFlow->SetActorHiddenInGame(true);
        PrismalFlow->SetActorTickEnabled(false);
        UE_LOGFMT(LogTemp, VeryVerbose, "Streamed out PrismalFlow element {ElementName}", StreamingEntry.PCGElement->GetName());
    }

    // UE 5.6 - Atualizar estatísticas (CORRIGIDO: removida duplicação identificada na auditoria)
    PerformanceStats.ElementsCurrentlyStreamed = FMath::Max(0, PerformanceStats.ElementsCurrentlyStreamed - 1);
    TotalElementsUnloaded++;

    // UE 5.6 - Liberar assets assíncronos se necessário
    UnloadElementAssetsAsync(StreamingEntry);

    UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGWorldPartitionIntegration::StreamOutElement - Streamed out element {ElementName}",
              StreamingEntry.PCGElement->GetName());
}

// ========================================
// FUNÇÕES INTERNAS - OTIMIZAÇÃO DE HARDWARE
// ========================================

float AAURACRONPCGWorldPartitionIntegration::GetHardwareStreamingDistanceMultiplier() const
{
    // Retornar o multiplicador de distância baseado no perfil de qualidade atual
    switch (HardwareConfig.QualityProfile)
    {
        case EAURACRONStreamingQualityProfile::Ultra:
            return 1.2f;
        case EAURACRONStreamingQualityProfile::High:
            return 1.0f;
        case EAURACRONStreamingQualityProfile::Medium:
            return 0.8f;
        case EAURACRONStreamingQualityProfile::Low:
            return 0.6f;
        case EAURACRONStreamingQualityProfile::Custom:
            return 0.4f;
        default:
            return 1.0f;
    }
}

void AAURACRONPCGWorldPartitionIntegration::DetectHardwareCapabilitiesInternal()
{
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::DetectHardwareCapabilitiesInternal - Detectando capacidades de hardware");

    // UE 5.6 - Obter informações de memória do sistema com validações robustas
    FPlatformMemoryStats MemoryStats = FPlatformMemory::GetStats();
    uint64 TotalPhysicalGB = MemoryStats.TotalPhysical / (1024 * 1024 * 1024);

    // Detectar GPU e CPU
    FString CPUBrand = FPlatformMisc::GetCPUBrand();
    FString GPUBrand = FPlatformMisc::GetPrimaryGPUBrand();

    UE_LOGFMT(LogTemp, Log, "Hardware detectado - CPU: {CPU}, GPU: {GPU}, RAM: {RAM} GB",
              CPUBrand, GPUBrand, TotalPhysicalGB);

    // UE 5.6 - Configurar hardware baseado na documentação AURACRON (Entry/Mid/High)
    if (TotalPhysicalGB >= 16)
    {
        // High-end devices
        HardwareConfig.bIsLowEndHardware = false;
        HardwareConfig.MemoryBudgetMB = 2048;
        HardwareConfig.QualityProfile = EAURACRONStreamingQualityProfile::High;
        HardwareConfig.MaxConcurrentStreamingElements = 200;
        UE_LOGFMT(LogTemp, Log, "Configurado como dispositivo High-end");
    }
    else if (TotalPhysicalGB >= 8)
    {
        // Mid-range devices
        HardwareConfig.bIsLowEndHardware = false;
        HardwareConfig.MemoryBudgetMB = 1024;
        HardwareConfig.QualityProfile = EAURACRONStreamingQualityProfile::Medium;
        HardwareConfig.MaxConcurrentStreamingElements = 100;
        UE_LOGFMT(LogTemp, Log, "Configurado como dispositivo Mid-range");
    }
    else
    {
        // Entry level devices
        HardwareConfig.bIsLowEndHardware = true;
        HardwareConfig.MemoryBudgetMB = 512;
        HardwareConfig.QualityProfile = EAURACRONStreamingQualityProfile::Low;
        HardwareConfig.MaxConcurrentStreamingElements = 50;
        UE_LOGFMT(LogTemp, Log, "Configurado como dispositivo Entry-level");
    }

    // UE 5.6 - Configurar multiplicador de distância baseado no perfil de qualidade
    SetStreamingQualityProfile(HardwareConfig.QualityProfile);

    // UE 5.6 - Configurar orçamento de partículas baseado na documentação
    ConfigureParticleBudgets();

    UE_LOGFMT(LogTemp, Log, "Detecção de hardware concluída - Perfil: {Profile}, Orçamento: {Budget}MB, Elementos: {Elements}",
              static_cast<int32>(HardwareConfig.QualityProfile), HardwareConfig.MemoryBudgetMB, HardwareConfig.MaxConcurrentStreamingElements);
}

void AAURACRONPCGWorldPartitionIntegration::ApplyHardwareSpecificOptimizations()
{
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::ApplyHardwareSpecificOptimizations - Aplicando otimizações de hardware");

    // UE 5.6 - Aplicar otimizações baseadas no perfil de qualidade com valores da documentação
    switch (HardwareConfig.QualityProfile)
    {
        case EAURACRONStreamingQualityProfile::Low:
            // Entry Level - Configurações mínimas
            StreamingRadius = 3000.0f * HardwareConfig.DistanceMultiplier;
            HardwareConfig.StreamingDistanceMultiplier = 0.6f;
            break;

        case EAURACRONStreamingQualityProfile::Medium:
            // Mid-range - Configurações balanceadas
            StreamingRadius = 5000.0f * HardwareConfig.DistanceMultiplier;
            HardwareConfig.StreamingDistanceMultiplier = 0.8f;
            break;

        case EAURACRONStreamingQualityProfile::High:
            // High-end - Configurações avançadas
            StreamingRadius = 7000.0f * HardwareConfig.DistanceMultiplier;
            HardwareConfig.StreamingDistanceMultiplier = 1.0f;
            break;

        case EAURACRONStreamingQualityProfile::Ultra:
            // Ultra - Máxima qualidade
            StreamingRadius = 10000.0f * HardwareConfig.DistanceMultiplier;
            HardwareConfig.StreamingDistanceMultiplier = 1.2f;
            break;

        default:
            StreamingRadius = 5000.0f * HardwareConfig.DistanceMultiplier;
            HardwareConfig.StreamingDistanceMultiplier = 1.0f;
            break;
    }

    // UE 5.6 - Aplicar otimizações a todos os elementos registrados
    ApplyOptimizationToAllElements();

    // UE 5.6 - Atualizar orçamento de partículas baseado no novo perfil
    ConfigureParticleBudgets();

    UE_LOGFMT(LogTemp, Log, "Otimizações aplicadas - Raio: {Radius}, Multiplicador: {Multiplier}, Perfil: {Profile}",
              StreamingRadius, HardwareConfig.StreamingDistanceMultiplier, static_cast<int32>(HardwareConfig.QualityProfile));
}

// UE 5.6 - Implementar função ApplyOptimizationToAllElements que estava faltando
void AAURACRONPCGWorldPartitionIntegration::ApplyOptimizationToAllElements()
{
    if (!HasAuthority())
    {
        return;
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGWorldPartitionIntegration::ApplyOptimizationToAllElements - Aplicando otimizações a {Count} elementos",
              RegisteredElements.Num());

    int32 OptimizedElements = 0;
    for (auto& ElementPair : RegisteredElements)
    {
        FAURACRONPCGStreamingEntry& StreamingEntry = ElementPair.Value;
        if (StreamingEntry.bIsCurrentlyStreamed && StreamingEntry.PCGElement && IsValid(StreamingEntry.PCGElement))
        {
            // Aplicar configurações de LOD baseadas no perfil atual
            ApplyLODSettingsToElement(StreamingEntry);

            // Aplicar configurações de streaming
            ApplyStreamingConfigToElement(StreamingEntry);

            OptimizedElements++;
        }
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "Otimizações aplicadas a {Count} elementos ativos", OptimizedElements);
}

// UE 5.6 - Implementar função ConsiderQualityIncrease que estava faltando
void AAURACRONPCGWorldPartitionIntegration::ConsiderQualityIncrease()
{
    if (!HasAuthority() || !bAutoAdjustQualityProfile)
    {
        return;
    }

    // Verificar se podemos aumentar a qualidade
    EAURACRONStreamingQualityProfile NewProfile = HardwareConfig.QualityProfile;

    switch (HardwareConfig.QualityProfile)
    {
        case EAURACRONStreamingQualityProfile::Low:
            NewProfile = EAURACRONStreamingQualityProfile::Medium;
            break;
        case EAURACRONStreamingQualityProfile::Medium:
            NewProfile = EAURACRONStreamingQualityProfile::High;
            break;
        case EAURACRONStreamingQualityProfile::High:
            NewProfile = EAURACRONStreamingQualityProfile::Ultra;
            break;
        case EAURACRONStreamingQualityProfile::Ultra:
            // Já está no máximo
            return;
    }

    // Verificar se o hardware suporta o aumento
    if (PerformanceStats.CurrentMemoryUsageMB < HardwareConfig.MemoryBudgetMB * 0.7f)
    {
        UE_LOGFMT(LogTemp, Log, "Considerando aumento de qualidade de {OldProfile} para {NewProfile}",
                  static_cast<int32>(HardwareConfig.QualityProfile), static_cast<int32>(NewProfile));

        SetStreamingQualityProfile(NewProfile);
    }
}

void AAURACRONPCGWorldPartitionIntegration::ApplyPlatformSpecificOptimizations()
{
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::ApplyPlatformSpecificOptimizations - Aplicando otimizações específicas da plataforma");

    // UE 5.6 - Detectar plataforma atual usando APIs modernas
    bool bIsConsole = false;
#if PLATFORM_XBOXONE || PLATFORM_PS4 || PLATFORM_PS5 || PLATFORM_XBOXSERIES
    bIsConsole = true;
#endif

    bool bIsMobile = false;
#if PLATFORM_ANDROID || PLATFORM_IOS
    bIsMobile = true;
#endif

    if (bIsMobile)
    {
        // UE 5.6 - Otimizações para dispositivos móveis baseadas na documentação Entry Level
        HardwareConfig.QualityProfile = EAURACRONStreamingQualityProfile::Low;
        HardwareConfig.DistanceMultiplier = 0.5f;
        HardwareConfig.StreamingDistanceMultiplier = 0.4f;
        HardwareConfig.MaxConcurrentStreamingElements = FMath::Min(HardwareConfig.MaxConcurrentStreamingElements, 50);

        // Reduzir orçamento de partículas para mobile
        ParticleBudgets[EAURACRONStreamingQualityProfile::Low] = 200; // Ainda mais baixo para mobile

        UE_LOGFMT(LogTemp, Log, "Aplicadas otimizações para dispositivos móveis");
    }
    else if (bIsConsole)
    {
        // UE 5.6 - Otimizações para consoles baseadas na documentação Mid-range
        HardwareConfig.QualityProfile = EAURACRONStreamingQualityProfile::Medium;
        HardwareConfig.DistanceMultiplier = 0.75f;
        HardwareConfig.StreamingDistanceMultiplier = 0.8f;
        HardwareConfig.MaxConcurrentStreamingElements = FMath::Min(HardwareConfig.MaxConcurrentStreamingElements, 100);

        UE_LOGFMT(LogTemp, Log, "Aplicadas otimizações para consoles");
    }
    else
    {
        // UE 5.6 - Otimizações para PC baseadas na detecção de hardware
        // Já configuradas em DetectHardwareCapabilitiesInternal
        UE_LOGFMT(LogTemp, Log, "Usando otimizações baseadas na detecção automática de hardware para PC");
    }

    // UE 5.6 - Reconfigurar orçamento de partículas após mudanças de plataforma
    ConfigureParticleBudgets();
}

void AAURACRONPCGWorldPartitionIntegration::RegisterElementLoadTime(const FAURACRONPCGStreamingEntry& Element, float LoadTimeMS)
{
    // UE 5.6 - Registrar o tempo de carregamento de um elemento para análise de desempenho
    if (!Element.PCGElement || !IsValid(Element.PCGElement))
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGWorldPartitionIntegration::RegisterElementLoadTime - Invalid PCG element");
        return;
    }

    // UE 5.6 - Validação robusta de entrada
    if (LoadTimeMS < 0.0f)
    {
        UE_LOGFMT(LogTemp, Warning, "Invalid load time {LoadTime}ms for element {ElementName}",
                  LoadTimeMS, Element.PCGElement->GetName());
        return;
    }

    // Converter de milissegundos para segundos para consistência interna
    float LoadTimeSeconds = LoadTimeMS / 1000.0f;

    // Atualizar estatísticas de carregamento
    PerformanceStats.TotalElementLoadTime += LoadTimeSeconds;
    PerformanceStats.ElementLoadCount++;

    // Calcular média de tempo de carregamento
    if (PerformanceStats.ElementLoadCount > 0)
    {
        PerformanceStats.AverageElementLoadTime = PerformanceStats.TotalElementLoadTime / PerformanceStats.ElementLoadCount;
    }

    // UE 5.6 - Registrar tempo de carregamento no elemento (CORRIGIDO: ElementId agora existe)
    if (RegisteredElements.Contains(Element.ElementId))
    {
        RegisteredElements[Element.ElementId].LastLoadTime = LoadTimeSeconds;
        UE_LOGFMT(LogTemp, VeryVerbose, "Registered load time {LoadTime}s for element {ElementId}",
                  LoadTimeSeconds, Element.ElementId.ToString());
    }

    // UE 5.6 - Detectar carregamentos lentos com logging melhorado
    if (LoadTimeSeconds > 0.5f) // Mais de 500ms é considerado lento
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGWorldPartitionIntegration::RegisterElementLoadTime - Carregamento lento detectado: {LoadTime} segundos para o elemento {ElementName}",
                  LoadTimeSeconds, Element.PCGElement->GetName());

        // Incrementar contador de carregamentos lentos
        PerformanceStats.SlowLoadCount++;

        // UE 5.6 - Sugerir otimizações para carregamentos lentos
        if (PerformanceStats.SlowLoadCount > 5)
        {
            UE_LOGFMT(LogTemp, Warning, "Múltiplos carregamentos lentos detectados ({Count}). Considerando redução de qualidade.",
                      PerformanceStats.SlowLoadCount);
        }
    }
}

void AAURACRONPCGWorldPartitionIntegration::DetectStreamingHitches(float DeltaTime)
{
    // Analisar padrões de streaming para detectar hitches e problemas de desempenho
    
    // Acumular tempo para análise periódica
    static float TimeSinceLastHitchAnalysis = 0.0f;
    TimeSinceLastHitchAnalysis += DeltaTime;
    
    // Realizar análise a cada 5 segundos para evitar sobrecarga
    const float HitchAnalysisInterval = 5.0f;
    if (TimeSinceLastHitchAnalysis < HitchAnalysisInterval)
    {
        return;
    }
    
    TimeSinceLastHitchAnalysis = 0.0f;
    
    // Verificar se temos dados suficientes para análise
    if (PerformanceStats.ElementLoadCount < 10)
    {
        return;
    }
    
    // Verificar se a média de tempo de carregamento está acima do limite aceitável
    const float MaxAcceptableLoadTime = 0.25f; // 250ms
    if (PerformanceStats.AverageElementLoadTime > MaxAcceptableLoadTime)
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGWorldPartitionIntegration::DetectStreamingHitches - Tempo médio de carregamento alto: {LoadTime} segundos",
                  PerformanceStats.AverageElementLoadTime);

        // UE 5.6 - Sugerir ajustes de otimização com validações robustas
        if (HardwareConfig.QualityProfile > EAURACRONStreamingQualityProfile::Low)
        {
            UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGWorldPartitionIntegration::DetectStreamingHitches - Considerando reduzir perfil de qualidade");

            // Reduzir automaticamente o perfil de qualidade se configurado para ajuste automático
            if (bAutoAdjustQualityProfile)
            {
                EAURACRONStreamingQualityProfile NewProfile = static_cast<EAURACRONStreamingQualityProfile>(static_cast<int32>(HardwareConfig.QualityProfile) - 1);
                if (NewProfile >= EAURACRONStreamingQualityProfile::Low)
                {
                    SetStreamingQualityProfile(NewProfile);
                    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::DetectStreamingHitches - Perfil de qualidade reduzido automaticamente para {Profile}",
                              static_cast<int32>(NewProfile));
                }
            }
        }
    }
    
    // Verificar se temos muitos carregamentos lentos
    const float SlowLoadThreshold = 0.3f; // 30% dos carregamentos são lentos
    float SlowLoadPercentage = PerformanceStats.ElementLoadCount > 0 ? 
        static_cast<float>(PerformanceStats.SlowLoadCount) / static_cast<float>(PerformanceStats.ElementLoadCount) : 0.0f;
    
    if (SlowLoadPercentage > SlowLoadThreshold)
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGWorldPartitionIntegration::DetectStreamingHitches - Alta porcentagem de carregamentos lentos: {Percentage}%",
                  SlowLoadPercentage * 100.0f);

        // UE 5.6 - Reduzir distância de streaming se muitos carregamentos são lentos
        if (HardwareConfig.StreamingDistanceMultiplier > 0.5f)
        {
            HardwareConfig.StreamingDistanceMultiplier = FMath::Max(0.5f, HardwareConfig.StreamingDistanceMultiplier - 0.1f);
            UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::DetectStreamingHitches - Multiplicador de distância reduzido para {Multiplier}",
                      HardwareConfig.StreamingDistanceMultiplier);
        }
    }
    
    // Verificar se houve hitches durante o streaming
    if (PerformanceStats.HitchesDetected > 0)
    {
        // Calcular FPS médio
        float CurrentFPS = PerformanceStats.AverageFrameTime > 0.0f ? 1.0f / PerformanceStats.AverageFrameTime : 60.0f;
        
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGWorldPartitionIntegration::DetectStreamingHitches - Detectados {HitchCount} hitches. FPS atual: {FPS}",
                  PerformanceStats.HitchesDetected, CurrentFPS);

        // UE 5.6 - Se FPS estiver abaixo do aceitável e tivermos hitches, reduzir qualidade
        if (CurrentFPS < HardwareConfig.MinAcceptableFPS && HardwareConfig.QualityProfile > EAURACRONStreamingQualityProfile::Low)
        {
            // Reduzir qualidade mais agressivamente
            EAURACRONStreamingQualityProfile NewProfile = static_cast<EAURACRONStreamingQualityProfile>(static_cast<int32>(HardwareConfig.QualityProfile) - 1);
            if (NewProfile >= EAURACRONStreamingQualityProfile::Low)
            {
                SetStreamingQualityProfile(NewProfile);
                UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGWorldPartitionIntegration::DetectStreamingHitches - Reduzindo qualidade devido a hitches. Novo perfil: {Profile}",
                          static_cast<int32>(NewProfile));
            }
        }
    }
    
    // Resetar contadores após análise periódica
    if (PerformanceStats.ElementLoadCount > 100)
    {
        PerformanceStats.TotalElementLoadTime = PerformanceStats.AverageElementLoadTime * 10; // Manter média com peso menor
        PerformanceStats.ElementLoadCount = 10;
        PerformanceStats.SlowLoadCount = FMath::RoundToInt(SlowLoadPercentage * 10); // Manter proporção
    }
}

void AAURACRONPCGWorldPartitionIntegration::UpdatePerformanceStats(float DeltaTime)
{
    UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGWorldPartitionIntegration::UpdatePerformanceStats - Atualizando estatísticas de desempenho");

    // UE 5.6 - Validação robusta de entrada
    if (DeltaTime <= 0.0f)
    {
        UE_LOGFMT(LogTemp, Warning, "Invalid DeltaTime {DeltaTime} in UpdatePerformanceStats", DeltaTime);
        return;
    }

    // Atualizar tempo desde a última atualização
    float CurrentTime = GetWorld()->GetTimeSeconds();
    PerformanceStats.LastUpdateTime = CurrentTime;
    
    // Atualizar contagem de elementos em streaming
    int32 PreviousElementCount = PerformanceStats.ElementsCurrentlyStreamed;
    PerformanceStats.ElementsCurrentlyStreamed = 0;
    for (const auto& ElementPair : RegisteredElements)
    {
        if (ElementPair.Value.bIsCurrentlyStreamed)
        {
            PerformanceStats.ElementsCurrentlyStreamed++;
        }
    }
    
    // Calcular taxa de operações de streaming por segundo
    if (DeltaTime > 0.0f)
    {
        PerformanceStats.StreamingOperationsPerSecond = (PerformanceStats.ElementsCurrentlyStreamed - PreviousElementCount) / DeltaTime;
    }
    
    // Atualizar tempo médio de frame usando UWorld para maior precisão
    UWorld* World = GetWorld();
    if (World)
    {
        float CurrentFrameTime = World->GetDeltaSeconds();
        
        // Atualizar média de tempo de frame usando média móvel
        const float FrameTimeAlpha = 0.1f; // Peso para nova amostra
        PerformanceStats.AverageFrameTime = (PerformanceStats.AverageFrameTime * (1.0f - FrameTimeAlpha)) + (CurrentFrameTime * FrameTimeAlpha);
        
        // Calcular FPS médio
        PerformanceStats.AverageFPS = PerformanceStats.AverageFrameTime > 0.0f ? 1.0f / PerformanceStats.AverageFrameTime : 60.0f;
        
        // Detectar hitches (picos de tempo de frame)
        if (CurrentFrameTime > PerformanceStats.AverageFrameTime * 2.0f)
        {
            PerformanceStats.HitchesDetected++;
            
            // UE 5.6 - Analisar se o hitch pode estar relacionado ao streaming
            if (PerformanceStats.StreamingOperationsPerSecond > 5.0f)
            {
                UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGWorldPartitionIntegration::UpdatePerformanceStats - Hitch possivelmente relacionado ao streaming ({OpsPerSec} ops/s)",
                          PerformanceStats.StreamingOperationsPerSecond);

                // Executar detecção de hitches de streaming para possíveis ajustes
                DetectStreamingHitches(DeltaTime);
            }
        }
    }
    
    // Atualizar uso de memória
    FPlatformMemoryStats MemoryStats = FPlatformMemory::GetStats();
    uint64 CurrentMemoryUsageMB = MemoryStats.UsedPhysical / (1024 * 1024);
    PerformanceStats.CurrentMemoryUsageMB = static_cast<float>(CurrentMemoryUsageMB);
    PerformanceStats.PeakMemoryUsageMB = FMath::Max(PerformanceStats.PeakMemoryUsageMB, static_cast<float>(CurrentMemoryUsageMB));
    
    // UE 5.6 - Verificar se é necessário otimizar o uso de memória
    if (PerformanceStats.CurrentMemoryUsageMB > HardwareConfig.MemoryBudgetMB * 0.9f)
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGWorldPartitionIntegration::UpdatePerformanceStats - Uso de memória alto: {CurrentMemory} MB / {BudgetMemory} MB",
                  PerformanceStats.CurrentMemoryUsageMB, static_cast<float>(HardwareConfig.MemoryBudgetMB));

        // Otimizar uso de memória se estiver acima do orçamento
        OptimizeMemoryUsage();
    }

    // UE 5.6 - Log periódico de estatísticas para debugging
    static float LastStatsLogTime = 0.0f;
    if (CurrentTime - LastStatsLogTime > 30.0f) // Log a cada 30 segundos
    {
        UE_LOGFMT(LogTemp, VeryVerbose, "Performance Stats - FPS: {FPS}, Memory: {Memory}MB, Elements: {Elements}, Hitches: {Hitches}",
                  PerformanceStats.AverageFPS, PerformanceStats.CurrentMemoryUsageMB,
                  PerformanceStats.ElementsCurrentlyStreamed, PerformanceStats.HitchesDetected);
        LastStatsLogTime = CurrentTime;
    }
}

float AAURACRONPCGWorldPartitionIntegration::CalculateFPSBasedDistanceMultiplier() const
{
    // Se o FPS estiver abaixo do limite, reduzir o multiplicador de distância
    // para diminuir a carga de streaming e melhorar o desempenho
    
    const float TargetFPS = HardwareConfig.TargetFPS;
    const float MinFPS = HardwareConfig.MinAcceptableFPS;
    
    // Se não temos estatísticas de FPS válidas, retornar valor neutro
    if (PerformanceStats.AverageFPS <= 0.0f)
    {
        return 1.0f;
    }
    
    // Se o FPS está acima do alvo, podemos aumentar ligeiramente o multiplicador
    if (PerformanceStats.AverageFPS >= TargetFPS)
    {
        // Aumentar até 20% se o FPS estiver bem acima do alvo
        return FMath::Min(1.2f, 1.0f + ((PerformanceStats.AverageFPS - TargetFPS) / TargetFPS) * 0.2f);
    }
    
    // Se o FPS está abaixo do mínimo aceitável, reduzir drasticamente
    if (PerformanceStats.AverageFPS <= MinFPS)
    {
        // Reduzir até 50% em casos extremos
        return 0.5f;
    }
    
    // Caso contrário, interpolar linearmente entre 0.5 e 1.0 com base na relação entre FPS atual e alvo
    float FPSRatio = (PerformanceStats.AverageFPS - MinFPS) / (TargetFPS - MinFPS);
    return FMath::Lerp(0.5f, 1.0f, FPSRatio);
}

void AAURACRONPCGWorldPartitionIntegration::AdjustStreamingBasedOnPerformance()
{
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::AdjustStreamingBasedOnPerformance - Ajustando streaming baseado no desempenho atual");

    // UE 5.6 - Verificar se é necessário ajustar otimizações com validações robustas
    if (!ShouldAdjustOptimizations())
    {
        UE_LOGFMT(LogTemp, VeryVerbose, "Ajustes de otimização não necessários no momento");
        return;
    }

    // Calcular FPS atual
    float CurrentFPS = PerformanceStats.AverageFrameTime > 0.0f ? 1.0f / PerformanceStats.AverageFrameTime : 60.0f;

    // UE 5.6 - Verificar se o FPS está abaixo do limite
    if (CurrentFPS < HardwareConfig.MinAcceptableFPS)
    {
        UE_LOGFMT(LogTemp, Warning, "Baixo FPS detectado ({FPS}). Reduzindo qualidade de streaming.", CurrentFPS);
        
        // Reduzir qualidade de streaming
        switch (HardwareConfig.QualityProfile)
        {
            case EAURACRONStreamingQualityProfile::Ultra:
                SetStreamingQualityProfile(EAURACRONStreamingQualityProfile::High);
                break;
                
            case EAURACRONStreamingQualityProfile::High:
                SetStreamingQualityProfile(EAURACRONStreamingQualityProfile::Medium);
                break;
                
            case EAURACRONStreamingQualityProfile::Medium:
                SetStreamingQualityProfile(EAURACRONStreamingQualityProfile::Low);
                break;
                
            case EAURACRONStreamingQualityProfile::Low:
                // Já está no nível mais baixo, reduzir ainda mais o multiplicador de distância
                HardwareConfig.DistanceMultiplier = FMath::Max(0.25f, HardwareConfig.DistanceMultiplier - 0.1f);
                break;
        }
        
        // Aplicar otimizações
        ApplyHardwareSpecificOptimizations();
        
        // Otimizar uso de memória em situações críticas
        OptimizeMemoryUsage();
    }
    else if (CurrentFPS > HardwareConfig.TargetFPS && HardwareConfig.QualityProfile != EAURACRONStreamingQualityProfile::Ultra)
    {
        // UE 5.6 - FPS está bom, podemos aumentar a qualidade se não estiver no máximo
        UE_LOGFMT(LogTemp, Log, "Bom FPS detectado ({FPS}). Aumentando qualidade de streaming.", CurrentFPS);
        
        // Aumentar qualidade de streaming gradualmente
        switch (HardwareConfig.QualityProfile)
        {
            case EAURACRONStreamingQualityProfile::Low:
                SetStreamingQualityProfile(EAURACRONStreamingQualityProfile::Medium);
                break;
                
            case EAURACRONStreamingQualityProfile::Medium:
                SetStreamingQualityProfile(EAURACRONStreamingQualityProfile::High);
                break;
                
            case EAURACRONStreamingQualityProfile::High:
                SetStreamingQualityProfile(EAURACRONStreamingQualityProfile::Ultra);
                break;
        }
        
        // Aplicar otimizações
        ApplyHardwareSpecificOptimizations();
    }
}

void AAURACRONPCGWorldPartitionIntegration::AdjustOptimizationsForPerformance()
{
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::AdjustOptimizationsForPerformance - Ajustando otimizações para melhorar desempenho");

    // UE 5.6 - Ajustar streaming baseado no desempenho atual
    AdjustStreamingBasedOnPerformance();

    // UE 5.6 - Calcular nível de LOD ótimo baseado no hardware
    int32 OptimalLOD = CalculateOptimalLODLevel();
    UE_LOGFMT(LogTemp, VeryVerbose, "Calculated optimal LOD level: {LOD}", OptimalLOD);

    // UE 5.6 - Aplicar configurações de LOD a todos os elementos com validações
    int32 ElementsProcessed = 0;
    for (auto& ElementPair : RegisteredElements)
    {
        FAURACRONPCGStreamingEntry& StreamingEntry = ElementPair.Value;
        if (StreamingEntry.bIsCurrentlyStreamed && StreamingEntry.PCGElement && IsValid(StreamingEntry.PCGElement))
        {
            ApplyStreamingConfigToElement(StreamingEntry);
            ElementsProcessed++;
        }
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "Applied optimizations to {Count} active elements", ElementsProcessed);
}

void AAURACRONPCGWorldPartitionIntegration::ApplyLODSettingsToElement(FAURACRONPCGStreamingEntry& StreamingEntry)
{
    if (!StreamingEntry.PCGElement || !IsValid(StreamingEntry.PCGElement))
    {
        return;
    }
    
    // Configurar LOD baseado no perfil de qualidade
    int32 TargetLOD = 0;
    switch (static_cast<int32>(HardwareConfig.QualityProfile))
    {
        case static_cast<int32>(EAURACRONStreamingQualityProfile::Low):
            TargetLOD = 3; // LOD mais baixo
            break;
            
        case static_cast<int32>(EAURACRONStreamingQualityProfile::Medium):
            TargetLOD = 2;
            break;

        case static_cast<int32>(EAURACRONStreamingQualityProfile::High):
            TargetLOD = 1;
            break;

        case static_cast<int32>(EAURACRONStreamingQualityProfile::Ultra):
            TargetLOD = 0; // LOD mais alto
            break;
    }
    
    // Aplicar LOD aos componentes do ator
    TArray<UStaticMeshComponent*> MeshComponents;
    StreamingEntry.PCGElement->GetComponents<UStaticMeshComponent>(MeshComponents);
    
    for (UStaticMeshComponent* MeshComponent : MeshComponents)
    {
        if (MeshComponent)
        {
            // Forçar LOD específico se disponível
            if (MeshComponent->GetStaticMesh() && MeshComponent->GetStaticMesh()->GetNumLODs() > TargetLOD)
            {
                MeshComponent->ForcedLodModel = TargetLOD + 1; // ForcedLodModel é 1-based
            }
            
            // Ajustar distâncias de LOD baseado no perfil de qualidade
            if (HardwareConfig.LODDistanceFactors.Num() > 0)
            {
                float LODDistanceFactor = HardwareConfig.LODDistanceFactors[FMath::Min(TargetLOD, HardwareConfig.LODDistanceFactors.Num() - 1)];
                // Usar SetForcedLodModel para controlar LOD no UE 5.6
                MeshComponent->SetForcedLodModel(TargetLOD + 1);
            }
        }
    }
}

void AAURACRONPCGWorldPartitionIntegration::UpdateStreamingForAllPlayers()
{
    // Atualizar streaming para todos os jogadores
    if (UWorld* World = GetWorld())
    {
        for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
        {
            if (APlayerController* PC = Iterator->Get())
            {
                if (APawn* Pawn = PC->GetPawn())
                {
                    UpdateStreamingForPlayerLocation(Pawn->GetActorLocation());
                }
            }
        }
    }
}

// ========================================
// FUNÇÕES PÚBLICAS - OTIMIZAÇÃO DE HARDWARE
// ========================================

void AAURACRONPCGWorldPartitionIntegration::DetectHardwareCapabilities()
{
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::DetectHardwareCapabilities - Detectando capacidades de hardware");

    // UE 5.6 - Chamar implementação interna com validações
    DetectHardwareCapabilitiesInternal();
}

void AAURACRONPCGWorldPartitionIntegration::ConfigureHardwareOptimizations(bool bIsLowEndHardware)
{
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::ConfigureHardwareOptimizations - Configurando otimizações de hardware para dispositivo {DeviceType}",
              bIsLowEndHardware ? TEXT("Low-End") : TEXT("High-End"));

    // UE 5.6 - Configurar hardware baseado no parâmetro com configurações da documentação
    if (bIsLowEndHardware)
    {
        // Entry Level configuration
        HardwareConfig.QualityProfile = EAURACRONStreamingQualityProfile::Low;
        HardwareConfig.DistanceMultiplier = 0.5f;
        HardwareConfig.StreamingDistanceMultiplier = 0.4f;
        HardwareConfig.bEnableDynamicFPSAdjustment = true;
        HardwareConfig.MaxConcurrentStreamingElements = 50;
        HardwareConfig.MemoryBudgetMB = 512;

        // Configurar orçamento de partículas para Entry Level
        ParticleBudgets[EAURACRONStreamingQualityProfile::Low] = 300;
    }
    else
    {
        // High-End configuration
        HardwareConfig.QualityProfile = EAURACRONStreamingQualityProfile::High;
        HardwareConfig.DistanceMultiplier = 1.0f;
        HardwareConfig.StreamingDistanceMultiplier = 1.0f;
        HardwareConfig.bEnableDynamicFPSAdjustment = false;
        HardwareConfig.MaxConcurrentStreamingElements = 200;
        HardwareConfig.MemoryBudgetMB = 2048;

        // Configurar orçamento de partículas para High-End
        ParticleBudgets[EAURACRONStreamingQualityProfile::High] = 2000;
    }

    // UE 5.6 - Aplicar otimizações e reconfigurar orçamentos
    ApplyHardwareSpecificOptimizations();
    ConfigureParticleBudgets();

    UE_LOGFMT(LogTemp, Log, "Hardware configurado - Perfil: {Profile}, Orçamento: {Budget}MB, Elementos: {Elements}",
              static_cast<int32>(HardwareConfig.QualityProfile), HardwareConfig.MemoryBudgetMB, HardwareConfig.MaxConcurrentStreamingElements);
}



void AAURACRONPCGWorldPartitionIntegration::SetStreamingQualityProfile(EAURACRONStreamingQualityProfile NewProfile)
{
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::SetStreamingQualityProfile - Alterando perfil para {Profile}",
              static_cast<int32>(NewProfile));

    // UE 5.6 - Validar perfil de entrada
    if (NewProfile < EAURACRONStreamingQualityProfile::Low || NewProfile > EAURACRONStreamingQualityProfile::Ultra)
    {
        UE_LOGFMT(LogTemp, Warning, "Invalid quality profile {Profile}, using Medium as fallback", static_cast<int32>(NewProfile));
        NewProfile = EAURACRONStreamingQualityProfile::Medium;
    }

    // Atualizar perfil de qualidade
    EAURACRONStreamingQualityProfile OldProfile = HardwareConfig.QualityProfile;
    HardwareConfig.QualityProfile = NewProfile;

    // UE 5.6 - Configurar multiplicadores baseados na documentação AURACRON
    switch (NewProfile)
    {
        case EAURACRONStreamingQualityProfile::Low:
            HardwareConfig.DistanceMultiplier = 0.5f;
            HardwareConfig.StreamingDistanceMultiplier = 0.6f;
            break;
        case EAURACRONStreamingQualityProfile::Medium:
            HardwareConfig.DistanceMultiplier = 1.0f;
            HardwareConfig.StreamingDistanceMultiplier = 0.8f;
            break;
        case EAURACRONStreamingQualityProfile::High:
            HardwareConfig.DistanceMultiplier = 1.5f;
            HardwareConfig.StreamingDistanceMultiplier = 1.0f;
            break;
        case EAURACRONStreamingQualityProfile::Ultra:
            HardwareConfig.DistanceMultiplier = 2.0f;
            HardwareConfig.StreamingDistanceMultiplier = 1.2f;
            break;
        default:
            HardwareConfig.DistanceMultiplier = 1.0f;
            HardwareConfig.StreamingDistanceMultiplier = 1.0f;
            break;
    }

    // UE 5.6 - Aplicar otimizações e reconfigurar orçamentos
    ApplyHardwareSpecificOptimizations();
    ConfigureParticleBudgets();

    UE_LOGFMT(LogTemp, Log, "Perfil de qualidade alterado de {OldProfile} para {NewProfile}",
              static_cast<int32>(OldProfile), static_cast<int32>(NewProfile));
}

void AAURACRONPCGWorldPartitionIntegration::OptimizeStreamingForCurrentPlatform()
{
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::OptimizeStreamingForCurrentPlatform - Otimizando para plataforma atual");

    // UE 5.6 - Detectar capacidades de hardware com validações
    DetectHardwareCapabilitiesInternal();

    // UE 5.6 - Aplicar otimizações específicas da plataforma se habilitado
    if (bUsePlatformSpecificOptimizations)
    {
        ApplyPlatformSpecificOptimizations();
        UE_LOGFMT(LogTemp, VeryVerbose, "Applied platform-specific optimizations");
    }
    else
    {
        UE_LOGFMT(LogTemp, VeryVerbose, "Platform-specific optimizations disabled");
    }

    // UE 5.6 - Aplicar otimizações gerais
    ApplyHardwareSpecificOptimizations();

    // UE 5.6 - Inicializar sistemas AURACRON específicos após otimização
    if (HasAuthority())
    {
        InitializeAURACRONSystems();
    }

    UE_LOGFMT(LogTemp, Log, "Otimização de plataforma concluída - Perfil: {Profile}, Orçamento: {Budget}MB",
              static_cast<int32>(HardwareConfig.QualityProfile), HardwareConfig.MemoryBudgetMB);
}

bool AAURACRONPCGWorldPartitionIntegration::ShouldAdjustOptimizations() const
{
    // Verificar se é necessário ajustar otimizações baseado em vários fatores
    
    // Verificar se o tempo desde o último ajuste é suficiente
    if (TimeSinceLastOptimizationAdjustment < MinTimeBetweenOptimizationAdjustments)
    {
        return false;
    }
    
    // Verificar se o FPS está fora dos limites aceitáveis
    float CurrentFPS = PerformanceStats.AverageFrameTime > 0.0f ? 1.0f / PerformanceStats.AverageFrameTime : 60.0f;
    if (CurrentFPS < HardwareConfig.MinAcceptableFPS || CurrentFPS > HardwareConfig.TargetFPS * 1.5f)
    {
        return true;
    }
    
    // Verificar se houve hitches recentes
    if (PerformanceStats.HitchesDetected > 0)
    {
        return true;
    }
    
    // Verificar uso de memória
    if (PerformanceStats.CurrentMemoryUsageMB > HardwareConfig.MemoryBudgetMB * 0.9f)
    {
        return true;
    }
    
    // Por padrão, não ajustar se tudo estiver dentro dos limites aceitáveis
    return false;
}

void AAURACRONPCGWorldPartitionIntegration::OptimizeMemoryUsage()
{
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::OptimizeMemoryUsage - Otimizando uso de memória");

    // UE 5.6 - Verificar se o uso de memória está acima do orçamento com validações
    if (PerformanceStats.CurrentMemoryUsageMB <= HardwareConfig.MemoryBudgetMB)
    {
        UE_LOGFMT(LogTemp, VeryVerbose, "Memory usage within budget ({Current}MB / {Budget}MB)",
                  PerformanceStats.CurrentMemoryUsageMB, static_cast<float>(HardwareConfig.MemoryBudgetMB));
        return;
    }

    // UE 5.6 - Calcular quantos elementos precisamos descarregar para ficar dentro do orçamento
    float ExcessMemoryMB = PerformanceStats.CurrentMemoryUsageMB - (HardwareConfig.MemoryBudgetMB * 0.8f); // Alvo: 80% do orçamento
    int32 ElementsToUnload = FMath::CeilToInt(ExcessMemoryMB / 10.0f); // Estimativa: 10MB por elemento

    UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGWorldPartitionIntegration::OptimizeMemoryUsage - Uso de memória excedido ({Current}MB / {Budget}MB). Descarregando {Count} elementos",
              PerformanceStats.CurrentMemoryUsageMB, static_cast<float>(HardwareConfig.MemoryBudgetMB), ElementsToUnload);
    
    // Ordenar elementos por prioridade e distância
    TArray<TPair<FGuid, FAURACRONPCGStreamingEntry>> ElementsArray;
    for (const auto& ElementPair : RegisteredElements)
    {
        if (ElementPair.Value.bIsCurrentlyStreamed)
        {
            ElementsArray.Add(ElementPair);
        }
    }
    
    // Ordenar por prioridade (menor primeiro) e depois por última atualização (mais antiga primeiro)
    ElementsArray.Sort([](const TPair<FGuid, FAURACRONPCGStreamingEntry>& A, const TPair<FGuid, FAURACRONPCGStreamingEntry>& B) {
        // Primeiro por prioridade (menor primeiro)
        if (A.Value.StreamingConfig.StreamingPriority != B.Value.StreamingConfig.StreamingPriority)
        {
            return A.Value.StreamingConfig.StreamingPriority < B.Value.StreamingConfig.StreamingPriority;
        }
        
        // Depois por tempo desde a última atualização (mais antigo primeiro)
        return A.Value.LastUpdateTime < B.Value.LastUpdateTime;
    });
    
    // Descarregar elementos de baixa prioridade
    int32 ElementsUnloaded = 0;
    for (int32 i = 0; i < ElementsArray.Num() && ElementsUnloaded < ElementsToUnload; ++i)
    {
        auto& ElementPair = ElementsArray[i];
        if (ElementPair.Value.bIsCurrentlyStreamed)
        {
            // Não descarregar elementos de alta prioridade
            if (ElementPair.Value.StreamingConfig.StreamingPriority > 75)
            {
                continue;
            }
            
            // Descarregar elemento
            StreamOutElement(RegisteredElements[ElementPair.Key]);
            ElementsUnloaded++;
        }
    }
    
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::OptimizeMemoryUsage - Descarregados {Count} elementos para otimizar memória", ElementsUnloaded);

    // UE 5.6 - Forçar garbage collection se muitos elementos foram descarregados
    if (ElementsUnloaded > 10)
    {
        UE_LOGFMT(LogTemp, VeryVerbose, "Forcing garbage collection after unloading {Count} elements", ElementsUnloaded);
        GEngine->ForceGarbageCollection(true);
    }
}

int32 AAURACRONPCGWorldPartitionIntegration::CalculateOptimalLODLevel() const
{
    // Calcular nível de LOD ótimo baseado no hardware e desempenho atual
    
    // Base: perfil de qualidade
    int32 BaseLOD = 0;
    switch (HardwareConfig.QualityProfile)
    {
        case EAURACRONStreamingQualityProfile::Ultra:
            BaseLOD = 0;
            break;
        case EAURACRONStreamingQualityProfile::High:
            BaseLOD = 1;
            break;
        case EAURACRONStreamingQualityProfile::Medium:
            BaseLOD = 2;
            break;
        case EAURACRONStreamingQualityProfile::Low:
            BaseLOD = 3;
            break;
        default:
            BaseLOD = 2;
            break;
    }
    
    // Ajustar baseado no FPS atual
    float CurrentFPS = PerformanceStats.AverageFrameTime > 0.0f ? 1.0f / PerformanceStats.AverageFrameTime : 60.0f;
    if (CurrentFPS < HardwareConfig.MinAcceptableFPS)
    {
        // FPS muito baixo, aumentar LOD (reduzir qualidade)
        BaseLOD = FMath::Min(BaseLOD + 2, 3);
    }
    else if (CurrentFPS < HardwareConfig.TargetFPS)
    {
        // FPS abaixo do alvo, aumentar LOD levemente
        BaseLOD = FMath::Min(BaseLOD + 1, 3);
    }
    
    // Ajustar baseado no uso de memória
    if (PerformanceStats.CurrentMemoryUsageMB > HardwareConfig.MemoryBudgetMB * 0.9f)
    {
        // Uso de memória alto, aumentar LOD
        BaseLOD = FMath::Min(BaseLOD + 1, 3);
    }
    
    return BaseLOD;
}

void AAURACRONPCGWorldPartitionIntegration::ApplyStreamingConfigToElement(FAURACRONPCGStreamingEntry& Element)
{
    if (!Element.PCGElement || !IsValid(Element.PCGElement))
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGWorldPartitionIntegration::ApplyStreamingConfigToElement - Invalid PCG element");
        return;
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGWorldPartitionIntegration::ApplyStreamingConfigToElement - Aplicando configurações a {ElementName}",
              Element.PCGElement->GetName());

    // UE 5.6 - Aplicar configurações de LOD baseadas no perfil de qualidade atual
    int32 TargetLOD = CalculateOptimalLODLevel();
    
    // Aplicar LOD aos componentes do ator
    TArray<UStaticMeshComponent*> MeshComponents;
    Element.PCGElement->GetComponents<UStaticMeshComponent>(MeshComponents);
    
    for (UStaticMeshComponent* MeshComponent : MeshComponents)
    {
        if (MeshComponent)
        {
            // Forçar LOD específico se disponível
            if (MeshComponent->GetStaticMesh() && MeshComponent->GetStaticMesh()->GetNumLODs() > TargetLOD)
            {
                MeshComponent->ForcedLodModel = TargetLOD + 1; // ForcedLodModel é 1-based
            }
            
            // Ajustar distâncias de LOD baseado no perfil de qualidade
            if (HardwareConfig.LODDistanceFactors.Num() > 0)
            {
                float LODDistanceFactor = HardwareConfig.LODDistanceFactors[FMath::Min(TargetLOD, HardwareConfig.LODDistanceFactors.Num() - 1)];
                // Usar SetForcedLodModel para controlar LOD no UE 5.6
                MeshComponent->SetForcedLodModel(TargetLOD + 1);
            }
        }
    }
    
    // Aplicar configurações específicas baseadas no tipo de elemento
    if (AAURACRONPCGEnvironment* Environment = Cast<AAURACRONPCGEnvironment>(Element.PCGElement))
    {
        // Configurar densidade de elementos baseado no perfil de qualidade
        float DensityScale = 1.0f;
        switch (HardwareConfig.QualityProfile)
        {
            case EAURACRONStreamingQualityProfile::Ultra:
                DensityScale = 1.0f;
                break;
            case EAURACRONStreamingQualityProfile::High:
                DensityScale = 0.8f;
                break;
            case EAURACRONStreamingQualityProfile::Medium:
                DensityScale = 0.6f;
                break;
            case EAURACRONStreamingQualityProfile::Low:
                DensityScale = 0.4f;
                break;
        }
        
        Environment->SetDensityScale(DensityScale);
    }
    else if (AAURACRONPCGPrismalFlow* PrismalFlow = Cast<AAURACRONPCGPrismalFlow>(Element.PCGElement))
    {
        // Configurar qualidade de efeitos baseado no perfil de qualidade
        float EffectQuality = 1.0f;
        switch (HardwareConfig.QualityProfile)
        {
            case EAURACRONStreamingQualityProfile::Ultra:
                EffectQuality = 1.0f;
                break;
            case EAURACRONStreamingQualityProfile::High:
                EffectQuality = 0.75f;
                break;
            case EAURACRONStreamingQualityProfile::Medium:
                EffectQuality = 0.5f;
                break;
            case EAURACRONStreamingQualityProfile::Low:
                EffectQuality = 0.25f;
                break;
        }
        
        PrismalFlow->SetEffectQuality(EffectQuality);
    }
}

void AAURACRONPCGWorldPartitionIntegration::ApplyOptimizationToAllElements()
{
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::ApplyOptimizationToAllElements - Aplicando otimizações a todos os elementos");

    int32 ElementCount = 0;

    // UE 5.6 - Aplicar otimizações a todos os elementos registrados com validações robustas
    for (auto& ElementPair : RegisteredElements)
    {
        if (ElementPair.Value.PCGElement && IsValid(ElementPair.Value.PCGElement))
        {
            // Aplicar configurações de streaming e LOD baseadas no perfil de qualidade
            ApplyStreamingConfigToElement(ElementPair.Value);
            ElementCount++;
        }
    }

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::ApplyOptimizationToAllElements - Otimizações aplicadas a {Count} elementos", ElementCount);
    
    // Função void - não retorna valor
}

FAURACRONStreamingPerformanceStats AAURACRONPCGWorldPartitionIntegration::GetStreamingPerformanceStats() const
{
    return PerformanceStats;
}

// ========================================
// UE 5.6 - IMPLEMENTAÇÕES DE RPCs PARA MULTIPLAYER
// ========================================

// UE 5.6 - RPC para notificar clientes sobre mudanças de qualidade
void AAURACRONPCGWorldPartitionIntegration::ClientUpdateQualityProfile_Implementation(EAURACRONStreamingQualityProfile NewProfile)
{
    if (!HasAuthority())
    {
        HardwareConfig.QualityProfile = NewProfile;
        UE_LOGFMT(LogTemp, Log, "Client received quality profile update: {Profile}", static_cast<int32>(NewProfile));

        // Aplicar configurações locais baseadas no novo perfil
        ApplyHardwareSpecificOptimizations();
    }
}

// UE 5.6 - RPC para sincronizar estatísticas de performance
void AAURACRONPCGWorldPartitionIntegration::ClientUpdatePerformanceStats_Implementation(const FAURACRONStreamingPerformanceStats& NewStats)
{
    if (!HasAuthority())
    {
        PerformanceStats = NewStats;
        UE_LOGFMT(LogTemp, VeryVerbose, "Client received performance stats update - FPS: {FPS}, Memory: {Memory}MB",
                  PerformanceStats.AverageFPS, PerformanceStats.CurrentMemoryUsageMB);
    }
}

// UE 5.6 - RPC para notificar sobre mudanças de configuração de hardware
void AAURACRONPCGWorldPartitionIntegration::ClientUpdateHardwareConfig_Implementation(const FAURACRONHardwareConfig& NewConfig)
{
    if (!HasAuthority())
    {
        HardwareConfig = NewConfig;
        UE_LOGFMT(LogTemp, Log, "Client received hardware config update - Profile: {Profile}, Budget: {Budget}MB",
                  static_cast<int32>(HardwareConfig.QualityProfile), HardwareConfig.MemoryBudgetMB);

        // Reconfigurar orçamentos locais
        ConfigureParticleBudgets();
    }
}

// UE 5.6 - RPC para sincronizar estado dos trilhos
void AAURACRONPCGWorldPartitionIntegration::ClientUpdateTrailStates_Implementation(const FAURACRONTrailConfig& Solar, const FAURACRONTrailConfig& Axis, const FAURACRONTrailConfig& Lunar)
{
    if (!HasAuthority())
    {
        SolarTrailConfig = Solar;
        AxisTrailConfig = Axis;
        LunarTrailConfig = Lunar;

        UE_LOGFMT(LogTemp, VeryVerbose, "Client received trail states update");
    }
}

// UE 5.6 - RPC para sincronizar estado do Fluxo Prismal
void AAURACRONPCGWorldPartitionIntegration::ClientUpdatePrismalFlowState_Implementation(const FAURACRONPrismalFlowConfig& NewFlowConfig)
{
    if (!HasAuthority())
    {
        PrismalFlowConfig = NewFlowConfig;
        UE_LOGFMT(LogTemp, VeryVerbose, "Client received Prismal Flow state update - Width: {Width}, Speed: {Speed}",
                  PrismalFlowConfig.Width, PrismalFlowConfig.FlowSpeed);
    }
}

// UE 5.6 - RPC para sincronizar estado da Ilha Central
void AAURACRONPCGWorldPartitionIntegration::ClientUpdateCentralIslandState_Implementation(const FAURACRONCentralIslandConfig& NewIslandConfig)
{
    if (!HasAuthority())
    {
        CentralIslandConfig = NewIslandConfig;
        UE_LOGFMT(LogTemp, VeryVerbose, "Client received Central Island state update");
    }
}

// ========================================
// UE 5.6 - FUNÇÕES DE VALIDAÇÃO ANTI-CHEAT SERVER-SIDE
// ========================================

// UE 5.6 - Validação anti-cheat para posição de jogador
bool AAURACRONPCGWorldPartitionIntegration::ValidatePlayerPosition(const FVector& PlayerLocation, float MaxSpeed, float DeltaTime)
{
    if (!HasAuthority())
    {
        return true; // Clientes não fazem validação
    }

    // Verificar se a posição está dentro dos limites do mapa
    float DistanceFromCenter = FVector::Dist(PlayerLocation, FAURACRONMapDimensions::MAP_CENTER);
    if (DistanceFromCenter > FAURACRONMapDimensions::MAP_RADIUS_CM * 1.1f) // 10% de margem
    {
        UE_LOGFMT(LogTemp, Warning, "Player position validation failed - outside map bounds: {Distance} > {MaxDistance}",
                  DistanceFromCenter, FAURACRONMapDimensions::MAP_RADIUS_CM * 1.1f);
        return false;
    }

    // Verificar velocidade máxima (anti-speed hack)
    static TMap<FGuid, FVector> LastPlayerPositions;
    static TMap<FGuid, float> LastPlayerTimes;

    // Para simplificar, usar um ID baseado na posição (em implementação real, usar player ID)
    FGuid PlayerId = FGuid::NewGuid(); // Placeholder - usar ID real do jogador

    if (LastPlayerPositions.Contains(PlayerId) && LastPlayerTimes.Contains(PlayerId))
    {
        FVector LastPosition = LastPlayerPositions[PlayerId];
        float LastTime = LastPlayerTimes[PlayerId];
        float CurrentTime = GetWorld()->GetTimeSeconds();

        float TimeDiff = CurrentTime - LastTime;
        if (TimeDiff > 0.0f)
        {
            float Distance = FVector::Dist(PlayerLocation, LastPosition);
            float Speed = Distance / TimeDiff;

            if (Speed > MaxSpeed * 1.5f) // 50% de margem para lag/interpolação
            {
                UE_LOGFMT(LogTemp, Warning, "Player speed validation failed - speed {Speed} > max {MaxSpeed}",
                          Speed, MaxSpeed * 1.5f);
                return false;
            }
        }
    }

    // Atualizar posição e tempo para próxima validação
    LastPlayerPositions.Add(PlayerId, PlayerLocation);
    LastPlayerTimes.Add(PlayerId, GetWorld()->GetTimeSeconds());

    return true;
}

// UE 5.6 - Validação anti-cheat para streaming requests
bool AAURACRONPCGWorldPartitionIntegration::ValidateStreamingRequest(const FVector& RequestLocation, float RequestRadius)
{
    if (!HasAuthority())
    {
        return true;
    }

    // Verificar se o request está dentro de limites razoáveis
    if (RequestRadius > StreamingRadius * 2.0f)
    {
        UE_LOGFMT(LogTemp, Warning, "Streaming request validation failed - radius too large: {Radius} > {MaxRadius}",
                  RequestRadius, StreamingRadius * 2.0f);
        return false;
    }

    // Verificar se a localização do request é válida
    float DistanceFromCenter = FVector::Dist(RequestLocation, FAURACRONMapDimensions::MAP_CENTER);
    if (DistanceFromCenter > FAURACRONMapDimensions::MAP_RADIUS_CM * 1.2f)
    {
        UE_LOGFMT(LogTemp, Warning, "Streaming request validation failed - location outside valid area");
        return false;
    }

    return true;
}

// ========================================
// UE 5.6 - FUNÇÕES DE INTEGRAÇÃO COMPLETA COM SISTEMAS AURACRON
// ========================================

// UE 5.6 - Função para atualizar estado dos trilhos baseado na fase do jogo
void AAURACRONPCGWorldPartitionIntegration::UpdateTrailsForGamePhase(EAURACRONGamePhase CurrentPhase)
{
    if (!HasAuthority())
    {
        return;
    }

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::UpdateTrailsForGamePhase - Updating trails for phase {Phase}",
              static_cast<int32>(CurrentPhase));

    // Ajustar configurações dos trilhos baseado na fase
    switch (CurrentPhase)
    {
        case EAURACRONGamePhase::EarlyGame:
            SolarTrailConfig.Intensity = 0.6f;
            AxisTrailConfig.Intensity = 0.4f;
            LunarTrailConfig.Intensity = 0.3f;
            break;

        case EAURACRONGamePhase::MidGame:
            SolarTrailConfig.Intensity = 0.8f;
            AxisTrailConfig.Intensity = 0.7f;
            LunarTrailConfig.Intensity = 0.6f;
            break;

        case EAURACRONGamePhase::LateGame:
            SolarTrailConfig.Intensity = 1.0f;
            AxisTrailConfig.Intensity = 1.0f;
            LunarTrailConfig.Intensity = 1.0f;
            break;

        default:
            UE_LOGFMT(LogTemp, Warning, "Unknown game phase {Phase}", static_cast<int32>(CurrentPhase));
            break;
    }

    // Sincronizar com clientes
    ClientUpdateTrailStates(SolarTrailConfig, AxisTrailConfig, LunarTrailConfig);
}

// UE 5.6 - Função para otimizar baseado no número de jogadores
void AAURACRONPCGWorldPartitionIntegration::OptimizeForPlayerCount(int32 PlayerCount)
{
    if (!HasAuthority())
    {
        return;
    }

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::OptimizeForPlayerCount - Optimizing for {PlayerCount} players", PlayerCount);

    // Ajustar orçamentos baseado no número de jogadores
    float PlayerMultiplier = FMath::Clamp(PlayerCount / 100.0f, 0.5f, 2.0f); // Assumindo máximo de 100 jogadores

    // Ajustar orçamento de partículas
    int32 BaseBudget = GetParticleBudgetForQuality();
    int32 AdjustedBudget = FMath::RoundToInt(BaseBudget * PlayerMultiplier);

    CurrentParticleBudget.TrilhosBudget = AdjustedBudget * 0.4f;
    CurrentParticleBudget.FluxoPrismalBudget = AdjustedBudget * 0.3f;
    CurrentParticleBudget.AmbientalBudget = AdjustedBudget * 0.2f;
    CurrentParticleBudget.CombateBudget = AdjustedBudget * 0.1f;

    // Ajustar distância de streaming
    float DistanceMultiplier = FMath::Clamp(1.0f / FMath::Sqrt(PlayerCount / 50.0f), 0.5f, 1.5f);
    StreamingRadius = BaseStreamingRadius * DistanceMultiplier;

    UE_LOGFMT(LogTemp, Log, "Optimized for {PlayerCount} players - Budget: {Budget}, Radius: {Radius}",
              PlayerCount, AdjustedBudget, StreamingRadius);
}

// UE 5.6 - Função para cleanup e finalização
void AAURACRONPCGWorldPartitionIntegration::Cleanup()
{
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::Cleanup - Cleaning up resources");

    // Parar timer de otimização
    if (GetWorld() && GetWorld()->GetTimerManager().IsTimerActive(OptimizationTimerHandle))
    {
        GetWorld()->GetTimerManager().ClearTimer(OptimizationTimerHandle);
    }

    // Limpar StreamableManager
    if (StreamableManager.IsValid())
    {
        StreamableManager->CancelAll();
        StreamableManager.Reset();
    }

    // Descarregar todos os elementos
    for (auto& ElementPair : RegisteredElements)
    {
        if (ElementPair.Value.bIsCurrentlyStreamed)
        {
            StreamOutElement(ElementPair.Value);
        }
    }

    // Limpar containers
    RegisteredElements.Empty();
    StreamingRegions.Empty();

    // Forçar garbage collection final
    if (GEngine)
    {
        GEngine->ForceGarbageCollection(true);
    }

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGWorldPartitionIntegration::Cleanup - Cleanup completed");
}

// UE 5.6 - Função para debug e diagnóstico
void AAURACRONPCGWorldPartitionIntegration::PrintDebugInfo() const
{
    UE_LOGFMT(LogTemp, Log, "=== AURACRON PCG World Partition Integration Debug Info ===");
    UE_LOGFMT(LogTemp, Log, "Quality Profile: {Profile}", static_cast<int32>(HardwareConfig.QualityProfile));
    UE_LOGFMT(LogTemp, Log, "Memory Budget: {Budget}MB", HardwareConfig.MemoryBudgetMB);
    UE_LOGFMT(LogTemp, Log, "Current Memory Usage: {Usage}MB", PerformanceStats.CurrentMemoryUsageMB);
    UE_LOGFMT(LogTemp, Log, "Streaming Radius: {Radius}", StreamingRadius);
    UE_LOGFMT(LogTemp, Log, "Registered Elements: {Count}", RegisteredElements.Num());
    UE_LOGFMT(LogTemp, Log, "Active Elements: {Count}", PerformanceStats.ElementsCurrentlyStreamed);
    UE_LOGFMT(LogTemp, Log, "Average FPS: {FPS}", PerformanceStats.AverageFPS);
    UE_LOGFMT(LogTemp, Log, "Hitches Detected: {Count}", PerformanceStats.HitchesDetected);
    UE_LOGFMT(LogTemp, Log, "Particle Budget - Trilhos: {Trilhos}, Fluxo: {Fluxo}, Ambiental: {Ambiental}, Combate: {Combate}",
              CurrentParticleBudget.TrilhosBudget, CurrentParticleBudget.FluxoPrismalBudget,
              CurrentParticleBudget.AmbientalBudget, CurrentParticleBudget.CombateBudget);
    UE_LOGFMT(LogTemp, Log, "=== End Debug Info ===");
}
