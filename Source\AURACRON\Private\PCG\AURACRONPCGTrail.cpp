// AURACRONPCGTrail.cpp
// Sistema de Geração Procedural para AURACRON - UE 5.6
// Implementação da classe para gerenciar as trilhas dinâmicas

#include "PCG/AURACRONPCGTrail.h"
#include "PCG/AURACRONPCGSubsystem.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "PCG/AURACRONPCGMathLibrary.h"
#include "PCGComponent.h"
#include "PCGSettings.h"
#include "PCGGraph.h"
#include "Helpers/PCGGraphParametersHelpers.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/World.h"
#include "Components/SplineComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/BoxComponent.h"
#include "NiagaraComponent.h"
#include "Math/UnrealMathUtility.h"
#include "NiagaraSystem.h"
#include "Components/PointLightComponent.h"
#include "GameFramework/Character.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "AbilitySystemComponent.h"
#include "GameplayEffect.h"
#include "Engine/OverlapResult.h"
// UE 5.6 Modern APIs - Includes adicionais necessários
#include "Logging/StructuredLog.h"
#include "Engine/StreamableManager.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Engine/DataTable.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Components/AudioComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Engine/AssetManager.h"
#include "GameplayEffectTypes.h"
#include "GameplayTagContainer.h"

// Implementação da classe base ATrailBase
ATrailBase::ATrailBase()
{
    PrimaryActorTick.bCanEverTick = true;

    // Criar o componente raiz (USceneComponent)
    USceneComponent* SceneRoot = CreateDefaultSubobject<USceneComponent>(TEXT("SceneRoot"));
    RootComponent = SceneRoot;

    // Criar o componente Spline
    SplineComponent = CreateDefaultSubobject<USplineComponent>(TEXT("SplineComponent"));
    SplineComponent->SetupAttachment(RootComponent);
    SplineComponent->SetClosedLoop(false);
    
    // Criar o componente de colisão para detectar jogadores
    CollisionComponent = CreateDefaultSubobject<UBoxComponent>(TEXT("CollisionComponent"));
    CollisionComponent->SetupAttachment(RootComponent);
    CollisionComponent->SetCollisionProfileName(TEXT("OverlapAllDynamic"));
    CollisionComponent->SetGenerateOverlapEvents(true);
    CollisionComponent->SetBoxExtent(FVector(250.0f, 250.0f, 100.0f)); // 5m x 5m x 2m por padrão

    // Criar o componente de efeito Niagara
    TrailEffectComponent = CreateDefaultSubobject<UNiagaraComponent>(TEXT("TrailEffectComponent"));
    TrailEffectComponent->SetupAttachment(RootComponent);
    TrailEffectComponent->SetAutoActivate(false);

    // Criar o componente Niagara principal para trilhas
    TrailNiagaraComponent = CreateDefaultSubobject<UNiagaraComponent>(TEXT("TrailNiagaraComponent"));
    TrailNiagaraComponent->SetupAttachment(RootComponent);
    TrailNiagaraComponent->SetAutoActivate(false);

    // Criar o componente de luz
    TrailLightComponent = CreateDefaultSubobject<UPointLightComponent>(TEXT("TrailLightComponent"));
    TrailLightComponent->SetupAttachment(RootComponent);
    TrailLightComponent->SetIntensity(5000.0f);
    TrailLightComponent->SetLightColor(FLinearColor(0.5f, 0.8f, 1.0f));
    TrailLightComponent->SetAttenuationRadius(500.0f);
    TrailLightComponent->SetCastShadows(false);
    TrailLightComponent->SetVisibility(false);

    // Valores padrão
    bIsActive = false;
    bIsVisible = true;
    TrailType = EAURACRONTrailType::None;
}

void ATrailBase::BeginPlay()
{
    Super::BeginPlay();
    
    // Registrar eventos de overlap
    CollisionComponent->OnComponentBeginOverlap.AddDynamic(this, &ATrailBase::OnOverlapBegin);
    CollisionComponent->OnComponentEndOverlap.AddDynamic(this, &ATrailBase::OnOverlapEnd);
    
    // Iniciar com trail desativado
    SetTrailActive(false);
}

void ATrailBase::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Implementação básica que pode ser sobrescrita pelas classes derivadas
    if (bIsActive)
    {
        // Atualizar efeitos visuais se necessário
        UpdateTrailEffects(DeltaTime);
    }
}

void ATrailBase::SetTrailActive(bool bActive)
{
    bIsActive = bActive;
    
    // Ativar/desativar componentes visuais
    TrailEffectComponent->SetVisibility(bActive);
    TrailLightComponent->SetVisibility(bActive);
    
    // Ativar/desativar o sistema de partículas
    if (bActive)
    {
        TrailEffectComponent->Activate(true);
    }
    else
    {
        TrailEffectComponent->Deactivate();
    }
}

void ATrailBase::ApplyTrailEffect(AActor* OverlappingActor)
{
    // Implementação base que pode ser sobrescrita pelas classes derivadas
    // Verificar se o ator é um personagem jogável
    ACharacter* Character = Cast<ACharacter>(OverlappingActor);
    if (Character)
    {
        // Aplicar efeito básico (pode ser sobrescrito por classes derivadas)
        UE_LOGFMT(LogTemp, Log, "Trail Base: Aplicando efeito básico ao personagem {CharacterName}", Character->GetName());
    }
}

// Construtor da classe ASolarTrail
ASolarTrail::ASolarTrail()
{
    // Configurações padrão para Solar Trail
    TrailType = EAURACRONTrailType::Solar;
    PowerPercentage = 1.0f;
    
    // Configurar luz com tom dourado para o efeito solar
    if (TrailLightComponent)
    {
        TrailLightComponent->SetLightColor(FLinearColor(1.0f, 0.8f, 0.3f, 1.0f)); // Dourado solar
        TrailLightComponent->SetIntensity(3.0f);
        TrailLightComponent->SetAttenuationRadius(800.0f);
    }
}

void ASolarTrail::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    // Atualizar efeitos baseados no tempo do dia
    float TimeOfDay = GetWorld()->GetTimeSeconds();
    float DayNightCycle = FMath::Sin(TimeOfDay * 0.1f) * 0.5f + 0.5f; // Ciclo de 0 a 1
    
    // Ajustar intensidade da luz baseado no ciclo solar
    if (TrailLightComponent)
    {
        float SolarIntensity = FMath::Lerp(1.0f, 4.0f, DayNightCycle);
        TrailLightComponent->SetIntensity(SolarIntensity);
        
        // Cor mais intensa durante o "dia"
        FLinearColor SolarColor = FLinearColor::LerpUsingHSV(
            FLinearColor(1.0f, 0.6f, 0.2f, 1.0f), // Laranja suave
            FLinearColor(1.0f, 0.9f, 0.4f, 1.0f), // Dourado brilhante
            DayNightCycle
        );
        TrailLightComponent->SetLightColor(SolarColor);
    }
    
    // Atualizar parâmetros do Niagara para efeitos solares
    if (TrailNiagaraComponent)
    {
        TrailNiagaraComponent->SetFloatParameter(TEXT("SolarIntensity"), DayNightCycle);
        TrailNiagaraComponent->SetFloatParameter(TEXT("ParticleCount"), FMath::Lerp(50.0f, 200.0f, DayNightCycle));
    }
}

void ASolarTrail::ApplyTrailEffect(AActor* OverlappingActor)
{
    // Implementação específica para o Solar Trail
    ACharacter* Character = Cast<ACharacter>(OverlappingActor);
    if (!Character)
    {
        return;
    }
    
    // Aplicar boost de velocidade
    UCharacterMovementComponent* MovementComp = Character->GetCharacterMovement();
    if (MovementComp)
    {
        // Armazenar velocidade original
        float OriginalSpeed = MovementComp->MaxWalkSpeed;
        
        // Aumentar velocidade em 30%
        MovementComp->MaxWalkSpeed = OriginalSpeed * 1.3f;
        
        UE_LOGFMT(LogTemp, Display, "Solar Trail: Aplicando boost de velocidade para {CharacterName}", Character->GetName());
    }

    // Aplicar efeito de regeneração (seria implementado via GameplayEffect)
    UE_LOGFMT(LogTemp, Display, "Solar Trail: Aplicando regeneração para {CharacterName}", Character->GetName());
    
    // Atualizar propriedades visuais do trail
    UpdateTrailProperties();
}

// Construtor da classe AAxisTrail
AAxisTrail::AAxisTrail()
{
    // Configurações padrão para Axis Trail
    TrailType = EAURACRONTrailType::Axis;
    PowerPercentage = 1.0f;
    ForceStrength = 1000.0f;
    EffectRadius = 500.0f;
    bAttractMode = true;
    
    // Configurar luz com tom violeta para o efeito axis
    if (TrailLightComponent)
    {
        TrailLightComponent->SetLightColor(FLinearColor(0.8f, 0.3f, 1.0f, 1.0f)); // Violeta axis
        TrailLightComponent->SetIntensity(2.5f);
        TrailLightComponent->SetAttenuationRadius(600.0f);
    }
}

void AAxisTrail::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    // Aplicar força aos atores próximos continuamente
    ApplyForceToNearbyActors();
    
    // Atualizar efeitos visuais baseados na força
    if (TrailLightComponent)
    {
        // Pulsar a luz baseado na força
        float PulseIntensity = FMath::Sin(GetWorld()->GetTimeSeconds() * 2.0f) * 0.3f + 0.7f;
        TrailLightComponent->SetIntensity(2.5f * PulseIntensity);
        
        // Cor mais intensa quando em modo atração
        FLinearColor AxisColor = bAttractMode ? 
            FLinearColor(0.8f, 0.3f, 1.0f, 1.0f) : // Violeta para atração
            FLinearColor(1.0f, 0.3f, 0.3f, 1.0f);  // Vermelho para repulsão
        TrailLightComponent->SetLightColor(AxisColor);
    }
    
    // Atualizar parâmetros do Niagara
    if (TrailNiagaraComponent)
    {
        TrailNiagaraComponent->SetFloatParameter(TEXT("ForceStrength"), ForceStrength);
        TrailNiagaraComponent->SetFloatParameter(TEXT("EffectRadius"), EffectRadius);
        TrailNiagaraComponent->SetBoolParameter(TEXT("AttractMode"), bAttractMode);
    }
}

void AAxisTrail::ApplyTrailEffect(AActor* OverlappingActor)
{
    // Implementação específica para o Axis Trail
    ACharacter* Character = Cast<ACharacter>(OverlappingActor);
    if (!Character)
    {
        return;
    }
    
    // Marcar o personagem para transição instantânea
    UE_LOGFMT(LogTemp, Display, "Axis Trail: Permitindo transição instantânea para {CharacterName}", Character->GetName());
    
    // Aplicar força aos atores próximos
    ApplyForceToNearbyActors();
}

void AAxisTrail::ApplyForceToNearbyActors()
{
    if (!GetWorld())
    {
        return;
    }
    
    // Definir raio de busca para atores próximos
    float SearchRadius = 1000.0f; // 10 metros
    FVector TrailCenter = GetActorLocation();
    
    // Buscar todos os atores próximos
    TArray<FOverlapResult> OverlapResults;
    FCollisionQueryParams QueryParams;
    QueryParams.AddIgnoredActor(this);
    
    bool bHasOverlaps = GetWorld()->OverlapMultiByChannel(
        OverlapResults,
        TrailCenter,
        FQuat::Identity,
        ECC_Pawn,
        FCollisionShape::MakeSphere(SearchRadius),
        QueryParams
    );
    
    if (bHasOverlaps)
    {
        for (const FOverlapResult& Result : OverlapResults)
        {
            if (AActor* OverlappedActor = Result.GetActor())
            {
                // Aplicar força de repulsão ou atração baseada no tipo de ator
                if (UPrimitiveComponent* PrimComp = Result.GetComponent())
                {
                    FVector DirectionToActor = (OverlappedActor->GetActorLocation() - TrailCenter).GetSafeNormal();
                    float Distance = FVector::Distance(OverlappedActor->GetActorLocation(), TrailCenter);
                    
                    // Calcular força baseada na distância (mais forte quando mais próximo)
                    float LocalForceStrength = FMath::Clamp(1.0f - (Distance / SearchRadius), 0.1f, 1.0f) * 500000.0f;
                    
                    // Aplicar força de repulsão para objetos físicos
                    if (PrimComp->IsSimulatingPhysics())
                    {
                        FVector ForceVector = DirectionToActor * LocalForceStrength;
                        PrimComp->AddImpulseAtLocation(ForceVector, OverlappedActor->GetActorLocation());

                        UE_LOGFMT(LogTemp, Verbose, "Axis Trail: Aplicando força de {ForceStrength} ao ator {ActorName}",
                               LocalForceStrength, OverlappedActor->GetName());
                    }
                    
                    // Para personagens, aplicar efeito de movimento especial
                    if (ACharacter* Character = Cast<ACharacter>(OverlappedActor))
                    {
                        if (UCharacterMovementComponent* MovementComp = Character->GetCharacterMovement())
                        {
                            // Aplicar impulso de movimento
                            FVector MovementImpulse = DirectionToActor * (ForceStrength * 0.001f); // Reduzir para personagens
                            MovementComp->AddImpulse(MovementImpulse, true);
                        }
                    }
                }
            }
        }
    }
}

void ALunarTrail::ApplyTrailEffect(AActor* OverlappingActor)
{
    // Implementação específica para o Lunar Trail
    ACharacter* Character = Cast<ACharacter>(OverlappingActor);
    if (!Character)
    {
        return;
    }
    
    // Aplicar efeito de invisibilidade
    Character->SetActorHiddenInGame(true);
    
    // Aplicar visão aprimorada (seria implementado via GameplayEffect)
    UE_LOGFMT(LogTemp, Display, "Lunar Trail: Aplicando furtividade e visão aprimorada para {CharacterName}", Character->GetName());
    
    // Aplicar efeito de invisibilidade
    ApplyInvisibilityEffect(Character);
}

void ALunarTrail::ApplyInvisibilityEffect(ACharacter* Character)
{
    if (!Character)
    {
        return;
    }
    
    // Calcular intensidade baseada na fase lunar atual
    float LunarPhase = UAURACRONPCGMathLibrary::CalculateLunarPhaseIntensity(GetWorld());
    float EffectiveInvisibility = InvisibilityStrength * LunarPhase;
    
    // Aplicar modificador de invisibilidade ao componente de movimento
    if (UCharacterMovementComponent* MovementComp = Character->GetCharacterMovement())
    {
        // Reduzir ruído de movimento baseado na invisibilidade
        float NoiseReduction = 1.0f - EffectiveInvisibility;
        MovementComp->MaxWalkSpeed *= (1.0f + MovementSpeedBonus * LunarPhase);
        
        // Aplicar efeito de furtividade através de tags de gameplay
        if (UAbilitySystemComponent* ASC = Character->GetComponentByClass<UAbilitySystemComponent>())
        {
            // Criar GameplayEffect temporário para invisibilidade
            FGameplayEffectSpecHandle EffectSpec = ASC->MakeOutgoingSpec(
                UGameplayEffect::StaticClass(), 1.0f, ASC->MakeEffectContext());
            
            if (EffectSpec.IsValid())
            {
                // Configurar duração do efeito
                EffectSpec.Data->SetDuration(EffectDuration, false);
                
                // Aplicar o efeito
                ASC->ApplyGameplayEffectSpecToSelf(*EffectSpec.Data.Get());
            }
        }
    }
    
    UE_LOGFMT(LogTemp, Verbose, "Lunar Trail: Aplicando invisibilidade de {InvisibilityPercent}% (fase lunar: {LunarPhase}) ao jogador {CharacterName} por {Duration} segundos",
           EffectiveInvisibility * 100.0f, LunarPhase, Character->GetName(), EffectDuration);
}

// Construtor da classe ALunarTrail
ALunarTrail::ALunarTrail()
{
    // Configurações padrão para Lunar Trail
    TrailType = EAURACRONTrailType::Lunar;
    InvisibilityStrength = 0.7f; // 70% de invisibilidade máxima
    EffectDuration = 5.0f; // Efeito dura 5 segundos após sair do trail
    MovementSpeedBonus = 0.2f; // 20% de velocidade adicional
    
    // Configurar luz com tom azulado para o efeito lunar
    if (TrailLightComponent)
    {
        TrailLightComponent->SetLightColor(FLinearColor(0.3f, 0.5f, 1.0f, 1.0f)); // Azul lunar
        TrailLightComponent->SetIntensity(2.0f);
        TrailLightComponent->SetAttenuationRadius(500.0f);
    }
}

void ALunarTrail::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    if (!bIsActive)
    {
        return;
    }
    
    // Atualizar intensidade baseada na fase lunar
    float LunarPhase = UAURACRONPCGMathLibrary::CalculateLunarPhaseIntensity(GetWorld());
    
    // Ajustar intensidade da luz baseada na fase lunar
    if (TrailLightComponent)
    {
        float LightIntensity = 1.0f + (LunarPhase * 2.0f); // Varia de 1.0 a 3.0
        TrailLightComponent->SetIntensity(LightIntensity);
        
        // Ajustar cor baseada na fase lunar (mais azul durante lua cheia)
        FLinearColor LunarColor = FLinearColor::LerpUsingHSV(
            FLinearColor(0.5f, 0.5f, 0.8f, 1.0f), // Azul claro
            FLinearColor(0.2f, 0.3f, 1.0f, 1.0f), // Azul intenso
            LunarPhase
        );
        TrailLightComponent->SetLightColor(LunarColor);
    }
    
    // Atualizar efeitos visuais específicos dos Lunar Trilhos
    if (TrailEffectComponent)
    {
        // Parâmetros básicos de fase lunar
        TrailEffectComponent->SetFloatParameter(TEXT("LunarPhase"), LunarPhase);
        
        // Mecânica visual: Névoa azul suave
        float BlueMistIntensity = FMath::Lerp(0.2f, 0.8f, LunarPhase);
        TrailEffectComponent->SetFloatParameter(TEXT("BlueMistIntensity"), BlueMistIntensity);
        TrailEffectComponent->SetVectorParameter(TEXT("MistColor"), FVector(0.3f, 0.6f, 1.0f)); // Azul etéreo
        TrailEffectComponent->SetFloatParameter(TEXT("MistSpread"), 120.0f + (LunarPhase * 80.0f));
        TrailEffectComponent->SetFloatParameter(TEXT("MistOpacity"), 0.4f + (LunarPhase * 0.3f));
        
        // Mecânica visual: Partículas de poeira estelar
        float StardustCount = FMath::Lerp(15.0f, 85.0f, LunarPhase);
        TrailEffectComponent->SetFloatParameter(TEXT("StardustParticleCount"), StardustCount);
        TrailEffectComponent->SetVectorParameter(TEXT("StardustColor"), FVector(0.9f, 0.95f, 1.0f)); // Branco estelar
        
        // Efeito de cintilação das partículas estelares
        float StardustSparkle = FMath::Sin(GetWorld()->GetTimeSeconds() * 2.5f) * 0.4f + 0.6f;
        TrailEffectComponent->SetFloatParameter(TEXT("StardustSparkle"), StardustSparkle * LunarPhase);
        TrailEffectComponent->SetFloatParameter(TEXT("StardustSize"), 0.8f + (LunarPhase * 0.5f));
        
        // Efeito de brilho etéreo geral
        float EtherealGlow = LunarPhase * 0.7f;
        TrailEffectComponent->SetFloatParameter(TEXT("EtherealGlow"), EtherealGlow);
        
        // Pulso suave do brilho etéreo
        float GlowPulse = FMath::Sin(GetWorld()->GetTimeSeconds() * 1.2f) * 0.25f + 0.75f;
        TrailEffectComponent->SetFloatParameter(TEXT("GlowPulse"), GlowPulse);
        
        // Efeito de ondulação da névoa
        float MistWave = FMath::Sin(GetWorld()->GetTimeSeconds() * 0.8f) * 0.3f + 0.7f;
        TrailEffectComponent->SetFloatParameter(TEXT("MistWave"), MistWave * LunarPhase);
    }
}

void ATrailBase::OnOverlapBegin(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, 
                               UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, 
                               bool bFromSweep, const FHitResult& SweepResult)
{
    if (bIsActive && OtherActor)
    {
        // Aplicar efeito quando um ator entra no trail
        ApplyTrailEffect(OtherActor);
    }
}

void ATrailBase::OnOverlapEnd(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, 
                             UPrimitiveComponent* OtherComp, int32 OtherBodyIndex)
{
    // Implementação base para quando um ator sai do trail
    // Pode ser sobrescrita pelas classes derivadas
}

void ATrailBase::UpdateTrailEffects(float DeltaTime)
{
    // Implementação base para atualizar efeitos visuais
    // Pode ser sobrescrita pelas classes derivadas
}

// Implementação da classe AAURACRONPCGTrail
AAURACRONPCGTrail::AAURACRONPCGTrail()
{
    PrimaryActorTick.bCanEverTick = true;

    // Inicializar StreamableManager para carregamento assíncrono (UE 5.6)
    StreamableManager = &UAssetManager::GetStreamableManager();

    // Inicializar Timer para otimização de performance
    UpdateTimerHandle = FTimerHandle();
    TrailUpdateInterval = 0.1f; // 10 FPS para otimização

    // Criar o componente PCG
    PCGComponent = CreateDefaultSubobject<UPCGComponent>(TEXT("PCGComponent"));

    // Valores padrão específicos para AAURACRONPCGTrail
    TrailType = EAURACRONTrailType::PrismalFlow;
    ActivityScale = 0.0f;
    TimeSinceLastUpdate = 0.0f;
    UpdateInterval = 0.5f; // Atualizar a cada meio segundo
    NumOverlappingPlayers = 0;
    TrailDistancePublic = 0.0f;

    // Inicializar características específicas do Prismal Flow
    FlowIntensity = 1.0f;
    FlowWidth = 500.0f; // 5 metros
    FlowSpeed = 1.0f;
    bHasFlowObstacles = true;
    FlowColor = FLinearColor::Blue;
    DefaultFlowIntensity = 1.0f;
    DefaultFlowWidth = 500.0f;
    DefaultFlowSpeed = 1.0f;

    // Inicializar características específicas do Ethereal Path
    PathVisibility = 0.7f;
    PathWidth = 300.0f; // 3 metros
    PathFluctuation = 0.5f;
    PathAlpha = 1.0f;
    PathColor = FLinearColor::Green;
    bHasPathGuides = true;
    DefaultPathVisibility = 0.7f;
    DefaultPathWidth = 300.0f;
    DefaultPathFluctuation = 0.5f;

    // Inicializar características específicas do Nexus Connection
    ConnectionStrength = 0.8f;
    ConnectionWidth = 200.0f; // 2 metros
    ConnectionPulseRate = 1.0f;
    ConnectionColor = FLinearColor::Red;
    bHasConnectionNodes = true;
    DefaultConnectionStrength = 0.8f;
    DefaultConnectionWidth = 200.0f;
    DefaultConnectionPulseRate = 1.0f;

    // Renomear o componente de colisão para manter compatibilidade
    InteractionVolume = CollisionComponent;
}

void AAURACRONPCGTrail::BeginPlay()
{
    Super::BeginPlay();
    
    // Configurar o componente PCG com as configurações apropriadas
    if (TrailSettings)
    {
        // Configurar PCG Graph Instance (UE 5.6 API moderna)
        if (PCGComponent->GetGraphInstance())
        {
            // O GraphInstance já existe, podemos configurar parâmetros se necessário
            UE_LOGFMT(LogTemp, Log, "AURACRONPCGTrail: PCG GraphInstance configurado");
        }
        else
        {
            UE_LOGFMT(LogTemp, Warning, "AURACRONPCGTrail: PCG GraphInstance não encontrado - será configurado via Blueprint");
        }
    }
    
    // Registrar eventos de overlap (UE 5.6 API moderna)
    InteractionVolume->OnComponentBeginOverlap.AddDynamic(this, &AAURACRONPCGTrail::HandlePlayerOverlap);
    InteractionVolume->OnComponentEndOverlap.AddDynamic(this, &AAURACRONPCGTrail::HandlePlayerEndOverlap);
    
    // Iniciar com visibilidade desativada até que seja explicitamente ativado
    SetTrailVisibility(false);

    // Definir escala de atividade inicial
    SetActivityScale(0.0f);

    // Inicializar propriedades de streaming e data layer
    bStreamingEnabled = true;
    StreamingDistance = 5000.0f;
    AssociatedDataLayer = NAME_None;

    // Inicializar configuração de streaming com valores padrão
    StreamingConfiguration.LoadingDistance = 3000.0f;
    StreamingConfiguration.UnloadingDistance = 4000.0f;
    StreamingConfiguration.StreamingPriority = 50;
    StreamingConfiguration.bUseAsyncStreaming = true;
    StreamingConfiguration.GridSize = 2000.0f;
    StreamingConfiguration.StreamingDistance = 5000.0f;
}

void AAURACRONPCGTrail::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Atualizar o tempo decorrido
    TimeSinceLastUpdate += DeltaTime;

    // Verificar se é hora de atualizar a trilha
    if (TimeSinceLastUpdate >= UpdateInterval)
    {
        // Atualizar parâmetros da trilha
        UpdateTrailParameters();

        // Regenerar a trilha se estiver ativa
        if (ActivityScale > 0.01f)
        {
            GenerateTrail();
        }

        // Resetar o temporizador
        TimeSinceLastUpdate = 0.0f;
    }
    
    // Aplicar efeitos a todos os jogadores na trilha
    for (ACharacter* Player : OverlappingPlayers)
    {
        if (IsValid(Player))
        {
            ApplyTrailEffectsToPlayer(Player, DeltaTime);
        }
    }
    
    // Atualizar o volume de interação para seguir a spline
    UpdateInteractionVolume();
}

void AAURACRONPCGTrail::SetTrailType(EAURACRONTrailType NewType)
{
    TrailType = NewType;
    
    // Reconfigurar a trilha com base no novo tipo
    // Isso pode envolver a alteração das configurações do PCG
    GenerateTrail();
}

void AAURACRONPCGTrail::GenerateTrail()
{
    if (!HasAuthority())
    {
        return;
    }

    // Limpar trilha anterior
    ClearTrail();

    // Gerar pontos da trilha baseados no tipo usando as APIs modernas
    TArray<FVector> TrailPoints = GenerateTrailPointsModern();

    if (TrailPoints.Num() < 2)
    {
        UE_LOGFMT(LogTemp, Warning, "AURACRONPCGTrail: Não foi possível gerar pontos suficientes para a trilha");
        return;
    }

    // Configurar spline com os pontos gerados
    SplineComponent->ClearSplinePoints();
    for (int32 i = 0; i < TrailPoints.Num(); ++i)
    {
        SplineComponent->AddSplinePoint(TrailPoints[i], ESplineCoordinateSpace::World);
        SplineComponent->SetSplinePointType(i, ESplinePointType::CurveClamped);
    }

    // Atualizar tangentes da spline para curvas suaves
    SplineComponent->UpdateSpline();

    // Gerar características específicas com base no tipo de trilha
    switch (TrailType)
    {
    case EAURACRONTrailType::Solar:
        GenerateSolarTrail();
        break;

    case EAURACRONTrailType::Axis:
        GenerateAxisTrail();
        break;

    case EAURACRONTrailType::Lunar:
        GenerateLunarTrail();
        break;

    default:
        break;
    }

    // Aplicar escala de atividade
    ApplyActivityScale();
}

void AAURACRONPCGTrail::UpdateForMapPhase(EAURACRONMapPhase MapPhase)
{
    // Atualizar a trilha com base na fase do mapa
    // Diferentes fases do mapa podem afetar a aparência e comportamento da trilha
    
    // Ajustar a escala de atividade com base na fase do mapa
    switch (MapPhase)
    {
        case EAURACRONMapPhase::Awakening:
            // Fase inicial - trilha com atividade moderada
            SetActivityScale(0.7f);
            
            // Ajustes específicos para cada tipo de trilha na fase Awakening
            if (TrailType == EAURACRONTrailType::PrismalFlow)
            {
                // Fluxo Prismal mais calmo e azulado na fase inicial
                FlowWidth = DefaultFlowWidth * 0.8f;
                FlowIntensity = DefaultFlowIntensity * 0.7f;
                FlowSpeed = DefaultFlowSpeed * 0.6f;
                // Configurar parâmetros via GraphInstance (UE 5.6 API moderna)
                if (UPCGGraphInstance* GraphInstance = PCGComponent->GetGraphInstance())
                {
                    // Log para debug - parâmetros serão configurados via Blueprint/Graph
                    UE_LOGFMT(LogTemp, Log, "AURACRONPCGTrail: Configurando FlowColor para PrismalFlow via GraphInstance");
                }
            }
            else if (TrailType == EAURACRONTrailType::EtherealPath)
            {
                // Caminho Etéreo mais sutil e esverdeado na fase inicial
                PathWidth = DefaultPathWidth * 0.7f;
                PathVisibility = DefaultPathVisibility * 0.6f;
                PathFluctuation = DefaultPathFluctuation * 0.5f;
                // Configurar parâmetros via GraphInstance (UE 5.6 API moderna)
                if (UPCGGraphInstance* GraphInstance = PCGComponent->GetGraphInstance())
                {
                    // Log para debug - parâmetros serão configurados via Blueprint/Graph
                    UE_LOGFMT(LogTemp, Log, "AURACRONPCGTrail: Configurando PathColor para EtherealPath via GraphInstance");
                }
            }
            else if (TrailType == EAURACRONTrailType::NexusConnection)
            {
                // Conexão de Nexus mais fraca e arroxeada na fase inicial
                ConnectionWidth = DefaultConnectionWidth * 0.6f;
                ConnectionStrength = DefaultConnectionStrength * 0.5f;
                ConnectionPulseRate = DefaultConnectionPulseRate * 0.4f;
                // Configurar parâmetros via GraphInstance (UE 5.6 API moderna)
                if (UPCGGraphInstance* GraphInstance = PCGComponent->GetGraphInstance())
                {
                    // Log para debug - parâmetros serão configurados via Blueprint/Graph
                    UE_LOGFMT(LogTemp, Log, "AURACRONPCGTrail: Configurando ConnectionColor para NexusConnection via GraphInstance");
                }
            }
            break;
            
        case EAURACRONMapPhase::Convergence:
            // Segunda fase - trilha com atividade aumentada
            SetActivityScale(0.9f);
            
            // Ajustes específicos para cada tipo de trilha na fase Convergence
            if (TrailType == EAURACRONTrailType::PrismalFlow)
            {
                // Fluxo Prismal mais intenso e arroxeado na segunda fase
                FlowWidth = DefaultFlowWidth * 0.9f;
                FlowIntensity = DefaultFlowIntensity * 0.9f;
                FlowSpeed = DefaultFlowSpeed * 0.8f;
                // Configurar parâmetros via GraphInstance (UE 5.6 API moderna)
                if (UPCGGraphInstance* GraphInstance = PCGComponent->GetGraphInstance())
                {
                    UE_LOGFMT(LogTemp, Log, "AURACRONPCGTrail: Configurando FlowColor Roxo para Convergence via GraphInstance");
                }
                
                // Adicionar obstáculos na segunda fase
                bHasFlowObstacles = true;
            }
            else if (TrailType == EAURACRONTrailType::EtherealPath)
            {
                // Caminho Etéreo mais visível e amarelado na segunda fase
                PathWidth = DefaultPathWidth * 0.9f;
                PathVisibility = DefaultPathVisibility * 0.8f;
                PathFluctuation = DefaultPathFluctuation * 0.7f;
                // Configurar parâmetros via GraphInstance (UE 5.6 API moderna)
                if (UPCGGraphInstance* GraphInstance = PCGComponent->GetGraphInstance())
                {
                    UE_LOGFMT(LogTemp, Log, "AURACRONPCGTrail: Configurando PathColor Verde Amarelado para Convergence via GraphInstance");
                }
                
                // Adicionar guias na segunda fase
                bHasPathGuides = true;
            }
            else if (TrailType == EAURACRONTrailType::NexusConnection)
            {
                // Conexão de Nexus mais forte e rosada na segunda fase
                ConnectionWidth = DefaultConnectionWidth * 0.8f;
                ConnectionStrength = DefaultConnectionStrength * 0.7f;
                ConnectionPulseRate = DefaultConnectionPulseRate * 0.6f;
                // Configurar parâmetros via GraphInstance (UE 5.6 API moderna)
                if (UPCGGraphInstance* GraphInstance = PCGComponent->GetGraphInstance())
                {
                    UE_LOGFMT(LogTemp, Log, "AURACRONPCGTrail: Configurando ConnectionColor Rosa para Convergence via GraphInstance");
                }
                
                // Adicionar nós na segunda fase
                bHasConnectionNodes = true;
            }
            break;
            
        case EAURACRONMapPhase::Intensification:
            // Terceira fase - trilha com alta atividade
            SetActivityScale(1.1f);
            
            // Ajustes específicos para cada tipo de trilha na fase Intensification
            if (TrailType == EAURACRONTrailType::PrismalFlow)
            {
                // Fluxo Prismal mais largo e alaranjado na terceira fase
                FlowWidth = DefaultFlowWidth * 1.1f;
                FlowIntensity = DefaultFlowIntensity * 1.2f;
                FlowSpeed = DefaultFlowSpeed * 1.3f;
                // Configurar parâmetros via GraphInstance (UE 5.6 API moderna)
                if (UPCGGraphInstance* GraphInstance = PCGComponent->GetGraphInstance())
                {
                    UE_LOGFMT(LogTemp, Log, "AURACRONPCGTrail: Configurando FlowColor Laranja para Intensification via GraphInstance");
                }
                
                // Aumentar número de obstáculos na terceira fase
                bHasFlowObstacles = true;
            }
            else if (TrailType == EAURACRONTrailType::EtherealPath)
            {
                // Caminho Etéreo mais brilhante e amarelado na terceira fase
                PathWidth = DefaultPathWidth * 1.1f;
                PathVisibility = DefaultPathVisibility * 1.2f;
                PathFluctuation = DefaultPathFluctuation * 1.3f;
                SetPCGParameterModern(TEXT("PathColor"), FVector(1.0f, 1.0f, 0.0f), TEXT("Amarelo brilhante"));
                
                // Aumentar número de guias na terceira fase
                bHasPathGuides = true;
            }
            else if (TrailType == EAURACRONTrailType::NexusConnection)
            {
                // Conexão de Nexus mais intensa e avermelhada na terceira fase
                ConnectionWidth = DefaultConnectionWidth * 1.1f;
                ConnectionStrength = DefaultConnectionStrength * 1.2f;
                ConnectionPulseRate = DefaultConnectionPulseRate * 1.3f;
                SetPCGParameterModern(TEXT("ConnectionColor"), FVector(1.0f, 0.0f, 0.0f), TEXT("Vermelho"));
                
                // Aumentar número de nós na terceira fase
                bHasConnectionNodes = true;
            }
            break;
            
        case EAURACRONMapPhase::Resolution:
            // Fase final - trilha com atividade máxima
            SetActivityScale(1.3f);
            
            // Ajustes específicos para cada tipo de trilha na fase Resolution
            if (TrailType == EAURACRONTrailType::PrismalFlow)
            {
                // Fluxo Prismal no máximo e avermelhado na fase final
                FlowWidth = DefaultFlowWidth * 1.3f;
                FlowIntensity = DefaultFlowIntensity * 1.5f;
                FlowSpeed = DefaultFlowSpeed * 1.7f;
                SetPCGParameterModern(TEXT("FlowColor"), FVector(1.0f, 0.0f, 0.0f), TEXT("Vermelho"));
                
                // Máximo de obstáculos na fase final
                bHasFlowObstacles = true;
            }
            else if (TrailType == EAURACRONTrailType::EtherealPath)
            {
                // Caminho Etéreo no máximo e branco puro na fase final
                PathWidth = DefaultPathWidth * 1.3f;
                PathVisibility = DefaultPathVisibility * 1.5f;
                PathFluctuation = DefaultPathFluctuation * 1.7f;
                SetPCGParameterModern(TEXT("PathColor"), FVector(1.0f, 1.0f, 1.0f), TEXT("Branco puro"));
                
                // Máximo de guias na fase final
                bHasPathGuides = true;
            }
            else if (TrailType == EAURACRONTrailType::NexusConnection)
            {
                // Conexão de Nexus no máximo e alaranjada na fase final
                ConnectionWidth = DefaultConnectionWidth * 1.3f;
                ConnectionStrength = DefaultConnectionStrength * 1.5f;
                ConnectionPulseRate = DefaultConnectionPulseRate * 1.7f;
                SetPCGParameterModern(TEXT("ConnectionColor"), FVector(1.0f, 0.5f, 0.0f), TEXT("Laranja"));
                
                // Máximo de nós na fase final
                bHasConnectionNodes = true;
            }
            break;
    }
    
    // Regenerar a trilha para aplicar as mudanças
    GenerateTrail();
}

void AAURACRONPCGTrail::SetTrailVisibility(bool bVisible)
{
    // Definir a visibilidade de todos os componentes gerados
    SetActorHiddenInGame(!bVisible);
    
    // Se estiver visível, garantir que a geração PCG esteja atualizada
    if (bVisible)
    {
        GenerateTrail();
    }
}

void AAURACRONPCGTrail::SetActivityScale(float Scale)
{
    // Limitar a escala entre 0.0 e 1.0
    ActivityScale = FMath::Clamp(Scale, 0.0f, 1.0f);
    
    // Aplicar a escala de atividade aos parâmetros de geração
    UpdateTrailParameters();
    
    // Regenerar a trilha se a escala for significativa
    if (ActivityScale > 0.01f)
    {
        GenerateTrail();
    }
}

void AAURACRONPCGTrail::SetTrailEndpoints(const FVector& StartPoint, const FVector& EndPoint)
{
    // Limpar os pontos existentes
    SplineComponent->ClearSplinePoints(false);
    
    // Adicionar os pontos de início e fim
    SplineComponent->AddSplinePoint(StartPoint, ESplineCoordinateSpace::World, false);
    SplineComponent->AddSplinePoint(EndPoint, ESplineCoordinateSpace::World, false);
    
    // Atualizar a spline
    SplineComponent->UpdateSpline();
    
    // Armazenar os pontos de início e fim para referência
    StartLocation = StartPoint;
    EndLocation = EndPoint;
    
    // Calcular a distância entre os pontos (em centímetros)
    float Distance = FVector::Dist(StartPoint, EndPoint);
    
    // Converter para metros para facilitar a leitura
    float DistanceInMeters = Distance / 100.0f;
    
    // Ajustar parâmetros baseados na distância
    if (TrailType == EAURACRONTrailType::PrismalFlow)
    {
        // Para o Fluxo Prismal, ajustar a largura baseada na distância
        // Trilhas mais longas são um pouco mais largas para manter a proporção visual
        if (DistanceInMeters > 100.0f) // Mais de 100 metros
        {
            FlowWidth = DefaultFlowWidth * 1.5f;
        }
        else if (DistanceInMeters > 50.0f) // Entre 50 e 100 metros
        {
            FlowWidth = DefaultFlowWidth * 1.2f;
        }
        else // Menos de 50 metros
        {
            FlowWidth = DefaultFlowWidth;
        }
        
        // Ajustar a velocidade do fluxo baseada na distância
        // Trilhas mais longas têm fluxo mais rápido
        FlowSpeed = DefaultFlowSpeed * (0.8f + (DistanceInMeters / 200.0f)); // Máximo de 1.3x para 100 metros
        
        // Passar os valores atualizados para o PCGComponent
        SetPCGParameterModern(TEXT("FlowWidth"), FVector(FlowWidth, 0.0f, 0.0f), TEXT("FlowWidth"));
        SetPCGParameterModern(TEXT("FlowSpeed"), FVector(FlowSpeed, 0.0f, 0.0f), TEXT("FlowSpeed"));
        SetPCGParameterModern(TEXT("TrailDistancePublic"), FVector(TrailDistancePublic, 0.0f, 0.0f), TEXT("TrailDistancePublic"));
    }
    else if (TrailType == EAURACRONTrailType::EtherealPath)
    {
        // Para o Caminho Etéreo, ajustar a visibilidade baseada na distância
        // Trilhas mais longas são um pouco menos visíveis para criar efeito de profundidade
        if (DistanceInMeters > 100.0f) // Mais de 100 metros
        {
            PathVisibility = DefaultPathVisibility * 0.7f;
        }
        else if (DistanceInMeters > 50.0f) // Entre 50 e 100 metros
        {
            PathVisibility = DefaultPathVisibility * 0.85f;
        }
        else // Menos de 50 metros
        {
            PathVisibility = DefaultPathVisibility;
        }
        
        // Ajustar a flutuação baseada na distância
        // Trilhas mais longas têm mais flutuação
        PathFluctuation = DefaultPathFluctuation * (1.0f + (DistanceInMeters / 100.0f)); // Máximo de 2x para 100 metros
        
        // Passar os valores atualizados para o PCGComponent
        SetPCGParameterModern(TEXT("PathVisibility"), FVector(PathVisibility, 0.0f, 0.0f), TEXT("PathVisibility"));
        SetPCGParameterModern(TEXT("PathFluctuation"), FVector(PathFluctuation, 0.0f, 0.0f), TEXT("PathFluctuation"));
        SetPCGParameterModern(TEXT("TrailDistancePublic"), FVector(TrailDistancePublic, 0.0f, 0.0f), TEXT("TrailDistancePublic"));
    }
    else if (TrailType == EAURACRONTrailType::NexusConnection)
    {
        // Para a Conexão de Nexus, ajustar a força baseada na distância
        // Trilhas mais longas são um pouco mais fracas para criar efeito de atenuação
        if (DistanceInMeters > 100.0f) // Mais de 100 metros
        {
            ConnectionStrength = DefaultConnectionStrength * 0.8f;
        }
        else if (DistanceInMeters > 50.0f) // Entre 50 e 100 metros
        {
            ConnectionStrength = DefaultConnectionStrength * 0.9f;
        }
        else // Menos de 50 metros
        {
            ConnectionStrength = DefaultConnectionStrength;
        }
        
        // Ajustar a taxa de pulso baseada na distância
        // Trilhas mais longas têm pulso mais lento
        ConnectionPulseRate = DefaultConnectionPulseRate * (1.0f - (DistanceInMeters / 200.0f)); // Mínimo de 0.5x para 100 metros
        
        // Passar os valores atualizados para o PCGComponent
        SetPCGParameterModern(TEXT("ConnectionStrength"), FVector(ConnectionStrength, 0.0f, 0.0f), TEXT("ConnectionStrength"));
        SetPCGParameterModern(TEXT("ConnectionPulseRate"), FVector(ConnectionPulseRate, 0.0f, 0.0f), TEXT("ConnectionPulseRate"));
        SetPCGParameterModern(TEXT("TrailDistancePublic"), FVector(TrailDistancePublic, 0.0f, 0.0f), TEXT("TrailDistancePublic"));
    }
    
    // Regenerar a trilha
    GenerateTrail();
}

void AAURACRONPCGTrail::AddControlPoint(const FVector& ControlPoint)
{
    // Adicionar um ponto de controle à spline
    SplineComponent->AddSplinePoint(ControlPoint, ESplineCoordinateSpace::World, false);
    
    // Atualizar a spline
    SplineComponent->UpdateSpline();
    
    // Regenerar a trilha
    GenerateTrail();
}

void AAURACRONPCGTrail::ClearControlPoints()
{
    // Limpar todos os pontos da spline
    SplineComponent->ClearSplinePoints(false);
    
    // Atualizar a spline
    SplineComponent->UpdateSpline();
}

// Implementações de geração de características específicas

void AAURACRONPCGTrail::GeneratePrismalFlow()
{
    // Implementação da geração de Prismal Flow
    // Usar PCG para criar um fluxo de energia prismática ao longo da spline
    
    // Calcular valores baseados na escala de atividade
    float EffectiveWidth = FlowWidth * ActivityScale;
    float EffectiveIntensity = FlowIntensity * ActivityScale;
    float EffectiveSpeed = FlowSpeed * ActivityScale;
    
    // Calcular o comprimento total da spline em unidades do Unreal (1 unidade = 1cm)
    float SplineLength = SplineComponent->GetSplineLength();
    
    // Calcular o número de segmentos baseado no comprimento (1 segmento a cada 100 unidades)
    int32 NumSegments = FMath::Max(FMath::FloorToInt(SplineLength / 100.0f), 1);
    
    // Calcular o número de obstáculos se habilitados
    int32 NumObstacles = 0;
    if (bHasFlowObstacles)
    {
        // 1 obstáculo a cada 1000 unidades (10 metros)
        NumObstacles = FMath::FloorToInt(SplineLength / 1000.0f);
        
        // Posicionar obstáculos ao longo da spline
        for (int32 i = 0; i < NumObstacles; ++i)
        {
            // Calcular a posição do obstáculo (distribuir uniformemente)
            float Distance = (i + 1) * (SplineLength / (NumObstacles + 1));
            FVector Location = SplineComponent->GetLocationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
            FRotator Rotation = SplineComponent->GetRotationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
            
            // Adicionar metadados para o obstáculo nesta posição
            SetPCGParameterModern(FString::Printf(TEXT("ObstacleLocation_%d"), i), FVector(0.0f, 0.0f, 0.0f), TEXT("Dynamic"));
            SetPCGParameterModern(FString::Printf(TEXT("ObstacleRotation_%d"), i), FVector(0.0f, 0.0f, 0.0f), TEXT("Dynamic"));
        }
    }
    
    // Passar valores para o sistema PCG
    SetPCGParameterModern(TEXT("FlowWidth"), FVector(FlowWidth, 0.0f, 0.0f), TEXT("FlowWidth"));
    SetPCGParameterModern(TEXT("FlowIntensity"), FVector(FlowIntensity, 0.0f, 0.0f), TEXT("FlowIntensity"));
    SetPCGParameterModern(TEXT("FlowSpeed"), FVector(FlowSpeed, 0.0f, 0.0f), TEXT("FlowSpeed"));
    SetPCGParameterModern(TEXT("NumSegments"), FVector(NumSegments, 0.0f, 0.0f), TEXT("NumSegments"));
    SetPCGParameterModern(TEXT("NumObstacles"), FVector(NumObstacles, 0.0f, 0.0f), TEXT("NumObstacles"));
    
    // Adicionar parâmetros específicos para o Fluxo Prismal
    SetPCGParameterModern(TEXT("SplinePoints"), FVector(SplinePoints.Num(), 0.0f, 0.0f), TEXT("SplinePoints"));
    SetPCGParameterModern(TEXT("SplineLength"), FVector(SplineLength, 0.0f, 0.0f), TEXT("SplineLength"));
    
    // Definir a cor do fluxo baseado na fase do mapa
    FlowColor = FLinearColor(0.0f, 0.5f, 1.0f, 1.0f); // Azul por padrão
    UAURACRONPCGSubsystem* PCGSubsystem = GetWorld()->GetSubsystem<UAURACRONPCGSubsystem>();
    if (PCGSubsystem)
    {
        EAURACRONMapPhase CurrentPhase = PCGSubsystem->GetCurrentMapPhase();
        switch (CurrentPhase)
        {
        case EAURACRONMapPhase::Awakening:
            FlowColor = FLinearColor(0.0f, 0.5f, 1.0f, 1.0f); // Azul
            break;
        case EAURACRONMapPhase::Convergence:
            FlowColor = FLinearColor(0.5f, 0.0f, 1.0f, 1.0f); // Roxo
            break;
        case EAURACRONMapPhase::Intensification:
            FlowColor = FLinearColor(1.0f, 0.5f, 0.0f, 1.0f); // Laranja
            break;
        case EAURACRONMapPhase::Resolution:
            FlowColor = FLinearColor(1.0f, 0.0f, 0.0f, 1.0f); // Vermelho
            break;
        }
    }
    
    SetPCGParameterModern(TEXT("FlowColor"), FVector(FlowColor.R, FlowColor.G, FlowColor.B), TEXT("FlowColor"));
}

void AAURACRONPCGTrail::GenerateEtherealPath()
{
    // Implementação da geração de Ethereal Path
    // Usar PCG para criar um caminho etéreo ao longo da spline
    
    // Calcular valores baseados na escala de atividade
    float EffectiveWidth = PathWidth * ActivityScale;
    float EffectiveVisibility = PathVisibility * ActivityScale;
    float EffectiveFluctuation = PathFluctuation * ActivityScale;
    
    // Calcular o comprimento total da spline em unidades do Unreal (1 unidade = 1cm)
    float SplineLength = SplineComponent->GetSplineLength();
    
    // Calcular o número de segmentos baseado no comprimento (1 segmento a cada 100 unidades)
    int32 NumSegments = FMath::Max(FMath::FloorToInt(SplineLength / 100.0f), 1);
    
    // Calcular o número de guias se habilitados
    int32 NumGuides = 0;
    if (bHasPathGuides)
    {
        // 1 guia a cada 2000 unidades (20 metros)
        NumGuides = FMath::FloorToInt(SplineLength / 2000.0f);
        
        // Posicionar guias ao longo da spline
        for (int32 i = 0; i < NumGuides; ++i)
        {
            // Calcular a posição da guia (distribuir uniformemente)
            float Distance = (i + 1) * (SplineLength / (NumGuides + 1));
            FVector Location = SplineComponent->GetLocationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
            FRotator Rotation = SplineComponent->GetRotationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
            
            // Adicionar metadados para a guia nesta posição
            SetPCGParameterModern(FString::Printf(TEXT("GuideLocation_%d"), i), FVector(0.0f, 0.0f, 0.0f), TEXT("Dynamic"));
            SetPCGParameterModern(FString::Printf(TEXT("GuideRotation_%d"), i), FVector(0.0f, 0.0f, 0.0f), TEXT("Dynamic"));
        }
    }
    
    // Passar valores para o sistema PCG
    SetPCGParameterModern(TEXT("PathWidth"), FVector(PathWidth, 0.0f, 0.0f), TEXT("PathWidth"));
    SetPCGParameterModern(TEXT("PathVisibility"), FVector(PathVisibility, 0.0f, 0.0f), TEXT("PathVisibility"));
    SetPCGParameterModern(TEXT("PathFluctuation"), FVector(PathFluctuation, 0.0f, 0.0f), TEXT("PathFluctuation"));
    SetPCGParameterModern(TEXT("NumSegments"), FVector(NumSegments, 0.0f, 0.0f), TEXT("NumSegments"));
    SetPCGParameterModern(TEXT("NumGuides"), FVector(NumGuides, 0.0f, 0.0f), TEXT("NumGuides"));
    
    // Adicionar parâmetros específicos para o Caminho Etéreo
    SetPCGParameterModern(TEXT("SplinePoints"), FVector(SplinePoints.Num(), 0.0f, 0.0f), TEXT("SplinePoints"));
    SetPCGParameterModern(TEXT("SplineLength"), FVector(SplineLength, 0.0f, 0.0f), TEXT("SplineLength"));
    
    // Definir a cor do caminho baseado na fase do mapa
    PathColor = FLinearColor(0.5f, 1.0f, 0.5f, EffectiveVisibility); // Verde etéreo por padrão
    UAURACRONPCGSubsystem* PCGSubsystem = GetWorld()->GetSubsystem<UAURACRONPCGSubsystem>();
    if (PCGSubsystem)
    {
        EAURACRONMapPhase CurrentPhase = PCGSubsystem->GetCurrentMapPhase();
        switch (CurrentPhase)
        {
        case EAURACRONMapPhase::Awakening:
            PathColor = FLinearColor(0.5f, 1.0f, 0.5f, EffectiveVisibility); // Verde etéreo
            break;
        case EAURACRONMapPhase::Convergence:
            PathColor = FLinearColor(0.7f, 1.0f, 0.3f, EffectiveVisibility); // Verde amarelado
            break;
        case EAURACRONMapPhase::Intensification:
            PathColor = FLinearColor(1.0f, 1.0f, 0.3f, EffectiveVisibility); // Amarelo brilhante
            break;
        case EAURACRONMapPhase::Resolution:
            PathColor = FLinearColor(1.0f, 1.0f, 1.0f, EffectiveVisibility); // Branco puro
            break;
        }
    }
    
    SetPCGParameterModern(TEXT("PathColor"), FVector(PathColor.R, PathColor.G, PathColor.B), TEXT("PathColor"));
    SetPCGParameterModern(TEXT("PathAlpha"), FVector(PathAlpha, 0.0f, 0.0f), TEXT("PathAlpha"));
    
    // Adicionar efeito de flutuação baseado no tempo
    float CurrentTime = GetWorld()->GetTimeSeconds();
    float FluctuationOffset = FMath::Sin(CurrentTime * 0.5f) * EffectiveFluctuation;
    SetPCGParameterModern(TEXT("FluctuationOffset"), FVector(FluctuationOffset, 0.0f, 0.0f), TEXT("FluctuationOffset"));
}

void AAURACRONPCGTrail::GenerateNexusConnection()
{
    // Implementação da geração de Nexus Connection
    // Usar PCG para criar uma conexão de nexus ao longo da spline
    
    // Calcular valores baseados na escala de atividade
    float EffectiveWidth = ConnectionWidth * ActivityScale;
    float EffectiveStrength = ConnectionStrength * ActivityScale;
    float EffectivePulseRate = ConnectionPulseRate * ActivityScale;
    
    // Calcular o comprimento total da spline em unidades do Unreal (1 unidade = 1cm)
    float SplineLength = SplineComponent->GetSplineLength();
    
    // Calcular o número de segmentos baseado no comprimento (1 segmento a cada 100 unidades)
    int32 NumSegments = FMath::Max(FMath::FloorToInt(SplineLength / 100.0f), 1);
    
    // Calcular o número de nós se habilitados
    int32 NumNodes = 0;
    if (bHasConnectionNodes)
    {
        // 1 nó a cada 1500 unidades (15 metros)
        NumNodes = FMath::FloorToInt(SplineLength / 1500.0f);
        
        // Posicionar nós ao longo da spline
        for (int32 i = 0; i < NumNodes; ++i)
        {
            // Calcular a posição do nó (distribuir uniformemente)
            float Distance = (i + 1) * (SplineLength / (NumNodes + 1));
            FVector Location = SplineComponent->GetLocationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
            FRotator Rotation = SplineComponent->GetRotationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
            
            // Adicionar metadados para o nó nesta posição
            SetPCGParameterModern(FString::Printf(TEXT("NodeLocation_%d"), i), FVector(0.0f, 0.0f, 0.0f), TEXT("Dynamic"));
            SetPCGParameterModern(FString::Printf(TEXT("NodeRotation_%d"), i), FVector(0.0f, 0.0f, 0.0f), TEXT("Dynamic"));
            
            // Adicionar variação de energia para cada nó
            float NodeEnergy = FMath::RandRange(0.8f, 1.2f) * EffectiveStrength;
            SetPCGParameterModern(FString::Printf(TEXT("NodeEnergy_%d"), i), FVector(0.0f, 0.0f, 0.0f), TEXT("Dynamic"));
        }
    }
    
    // Passar valores para o sistema PCG
    SetPCGParameterModern(TEXT("ConnectionWidth"), FVector(ConnectionWidth, 0.0f, 0.0f), TEXT("ConnectionWidth"));
    SetPCGParameterModern(TEXT("ConnectionStrength"), FVector(ConnectionStrength, 0.0f, 0.0f), TEXT("ConnectionStrength"));
    SetPCGParameterModern(TEXT("ConnectionPulseRate"), FVector(ConnectionPulseRate, 0.0f, 0.0f), TEXT("ConnectionPulseRate"));
    SetPCGParameterModern(TEXT("NumSegments"), FVector(NumSegments, 0.0f, 0.0f), TEXT("NumSegments"));
    SetPCGParameterModern(TEXT("NumNodes"), FVector(NumNodes, 0.0f, 0.0f), TEXT("NumNodes"));
    
    // Adicionar parâmetros específicos para a Conexão de Nexus
    SetPCGParameterModern(TEXT("SplinePoints"), FVector(SplinePoints.Num(), 0.0f, 0.0f), TEXT("SplinePoints"));
    SetPCGParameterModern(TEXT("SplineLength"), FVector(SplineLength, 0.0f, 0.0f), TEXT("SplineLength"));
    
    // Definir a cor da conexão baseado na fase do mapa
    ConnectionColor = FLinearColor(0.8f, 0.0f, 0.8f, 1.0f); // Roxo por padrão
    UAURACRONPCGSubsystem* PCGSubsystem = GetWorld()->GetSubsystem<UAURACRONPCGSubsystem>();
    if (PCGSubsystem)
    {
        EAURACRONMapPhase CurrentPhase = PCGSubsystem->GetCurrentMapPhase();
        switch (CurrentPhase)
        {
        case EAURACRONMapPhase::Awakening:
            ConnectionColor = FLinearColor(0.8f, 0.0f, 0.8f, 1.0f); // Roxo
            break;
        case EAURACRONMapPhase::Convergence:
            ConnectionColor = FLinearColor(1.0f, 0.0f, 0.5f, 1.0f); // Rosa
            break;
        case EAURACRONMapPhase::Intensification:
            ConnectionColor = FLinearColor(1.0f, 0.0f, 0.0f, 1.0f); // Vermelho
            break;
        case EAURACRONMapPhase::Resolution:
            ConnectionColor = FLinearColor(1.0f, 0.5f, 0.0f, 1.0f); // Laranja
            break;
        }
    }
    
    SetPCGParameterModern(TEXT("ConnectionColor"), FVector(ConnectionColor.R, ConnectionColor.G, ConnectionColor.B), TEXT("ConnectionColor"));
    
    // Adicionar efeito de pulso baseado no tempo
    float CurrentTime = GetWorld()->GetTimeSeconds();
    float PulseValue = (FMath::Sin(CurrentTime * EffectivePulseRate) + 1.0f) * 0.5f; // Normalizado entre 0 e 1
    SetPCGParameterModern(TEXT("PulseValue"), FVector(PulseValue, 0.0f, 0.0f), TEXT("PulseValue"));
    
    // Adicionar parâmetros para interação com jogadores
    TArray<AActor*> OverlappingActors;
    GetOverlappingActors(OverlappingActors, APawn::StaticClass());
    NumOverlappingPlayers = OverlappingActors.Num();
    SetPCGParameterModern(TEXT("NumOverlappingPlayers"), FVector(NumOverlappingPlayers, 0.0f, 0.0f), TEXT("NumOverlappingPlayers"));
}

void AAURACRONPCGTrail::CalculateSplinePoints()
{
    // Verificar se temos pelo menos dois pontos na spline
    int32 NumPoints = SplineComponent->GetNumberOfSplinePoints();
    if (NumPoints < 2)
    {
        UE_LOGFMT(LogTemp, Warning, "AURACRONPCGTrail: Spline não tem pontos suficientes para gerar a trilha {TrailName}",
               GetNameSafe(this));
        return;
    }

    // Calcular pontos intermediários se necessário
    if (NumPoints == 2)
    {
        // Obter os pontos de início e fim
        FVector StartPoint = SplineComponent->GetLocationAtSplinePoint(0, ESplineCoordinateSpace::World);
        FVector EndPoint = SplineComponent->GetLocationAtSplinePoint(1, ESplineCoordinateSpace::World);
        
        // Calcular a distância entre os pontos
        float Distance = FVector::Distance(StartPoint, EndPoint);
        
        // Se a distância for grande, adicionar pontos intermediários
        if (Distance > 2000.0f) // 20 metros
        {
            // Número de pontos intermediários (1 a cada 1000 unidades)
            int32 NumIntermediatePoints = FMath::FloorToInt(Distance / 1000.0f);
            
            // Limpar os pontos existentes
            SplineComponent->ClearSplinePoints(false);
            
            // Adicionar o ponto de início
            SplineComponent->AddSplinePoint(StartPoint, ESplineCoordinateSpace::World, false);
            
            // Adicionar pontos intermediários
            for (int32 i = 1; i <= NumIntermediatePoints; ++i)
            {
                float Alpha = (float)i / (NumIntermediatePoints + 1);
                FVector IntermediatePoint = FMath::Lerp(StartPoint, EndPoint, Alpha);
                
                // Adicionar alguma variação aleatória aos pontos intermediários
                float RandomOffset = 200.0f; // 2 metros
                IntermediatePoint.X += FMath::RandRange(-RandomOffset, RandomOffset);
                IntermediatePoint.Y += FMath::RandRange(-RandomOffset, RandomOffset);
                
                // Garantir que o ponto não esteja abaixo do solo
                // Isso seria implementado com um rastreio para o solo
                
                SplineComponent->AddSplinePoint(IntermediatePoint, ESplineCoordinateSpace::World, false);
            }
            
            // Adicionar o ponto de fim
            SplineComponent->AddSplinePoint(EndPoint, ESplineCoordinateSpace::World, false);
            
            // Atualizar a spline
            SplineComponent->UpdateSpline();
        }
    }
}

// Implementação das funções de interação com jogadores
void AAURACRONPCGTrail::HandlePlayerOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
    // Verificar se o ator que entrou na trilha é um personagem jogável
    ACharacter* PlayerCharacter = Cast<ACharacter>(OtherActor);
    if (PlayerCharacter)
    {
        // Adicionar o jogador à lista de jogadores na trilha
        OverlappingPlayers.AddUnique(PlayerCharacter);
        NumOverlappingPlayers = OverlappingPlayers.Num();
        
        // Atualizar parâmetros do PCG com base no número de jogadores
        SetPCGParameterModern(TEXT("NumOverlappingPlayers"), FVector(NumOverlappingPlayers, 0.0f, 0.0f), TEXT("NumOverlappingPlayers"));
        
        // Notificar via Blueprint para todos os tipos de trilha
        OnPlayerEnterTrailEvent.Broadcast(PlayerCharacter);
        
        // Aplicar efeitos iniciais baseados no tipo de trilha
        switch (TrailType)
        {
            case EAURACRONTrailType::PrismalFlow:
                // Efeito de velocidade para o Fluxo Prismal (similar ao rio no LoL)
                // Implementação completa seria feita no Character com um GameplayEffect
                UE_LOGFMT(LogTemp, Display, "{PlayerName} entrou no Fluxo Prismal - Aplicando efeito de velocidade", PlayerCharacter->GetName());
                break;

            case EAURACRONTrailType::EtherealPath:
                // Efeito de invisibilidade parcial para o Caminho Etéreo (similar à névoa de guerra no Dota 2)
                UE_LOGFMT(LogTemp, Display, "{PlayerName} entrou no Caminho Etéreo - Aplicando efeito de invisibilidade", PlayerCharacter->GetName());
                break;

            case EAURACRONTrailType::NexusConnection:
                // Efeito de aumento de poder para a Conexão de Nexus (similar ao buff de Barão no LoL)
                UE_LOGFMT(LogTemp, Display, "{PlayerName} entrou na Conexão de Nexus - Aplicando efeito de poder", PlayerCharacter->GetName());
                break;
                
            case EAURACRONTrailType::Solar:
                // Solar Trail: boost de velocidade e regeneração de vida
                if (PlayerCharacter->GetCharacterMovement())
                {
                    // Armazenar velocidade original
                    float OriginalSpeed = PlayerCharacter->GetCharacterMovement()->MaxWalkSpeed;
                    OriginalPlayerSpeeds.Add(PlayerCharacter, OriginalSpeed);
                    
                    // Aumentar velocidade de movimento em 30%
                    PlayerCharacter->GetCharacterMovement()->MaxWalkSpeed = OriginalSpeed * 1.3f;
                    
                    // Iniciar regeneração de vida (via Blueprint)
                    OnApplySolarEffectEvent.Broadcast(PlayerCharacter);

                    UE_LOGFMT(LogTemp, Display, "{PlayerName} entrou no Solar Trail - Aplicando boost de velocidade e regeneração",
                           PlayerCharacter->GetName());
                }
                break;
                
            case EAURACRONTrailType::Axis:
                // Axis Trail: permitir transição instantânea entre ambientes
                // Marcar o personagem para transição (implementado via Blueprint)
                OnApplyAxisEffectEvent.Broadcast(PlayerCharacter);
                UE_LOGFMT(LogTemp, Display, "{PlayerName} entrou no Axis Trail - Permitindo transição instantânea",
                       PlayerCharacter->GetName());
                break;
                
            case EAURACRONTrailType::Lunar:
                // Lunar Trail: conceder furtividade e visão aprimorada
                // Aplicar efeito de furtividade
                PlayerCharacter->SetActorHiddenInGame(true);
                InvisiblePlayers.Add(PlayerCharacter);
                
                // Aplicar visão aprimorada (via Blueprint)
                OnApplyLunarEffectEvent.Broadcast(PlayerCharacter);
                UE_LOGFMT(LogTemp, Display, "{PlayerName} entrou no Lunar Trail - Aplicando furtividade e visão aprimorada",
                       PlayerCharacter->GetName());
                break;
        }
    }
}

void AAURACRONPCGTrail::HandlePlayerEndOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex)
{
    // Verificar se o ator que saiu da trilha é um personagem jogável
    ACharacter* PlayerCharacter = Cast<ACharacter>(OtherActor);
    if (PlayerCharacter)
    {
        // Remover o jogador da lista de jogadores na trilha
        OverlappingPlayers.Remove(PlayerCharacter);
        NumOverlappingPlayers = OverlappingPlayers.Num();
        
        // Atualizar parâmetros do PCG com base no número de jogadores
        SetPCGParameterModern(TEXT("NumOverlappingPlayers"), FVector(NumOverlappingPlayers, 0.0f, 0.0f), TEXT("NumOverlappingPlayers"));
        
        // Remover efeitos baseados no tipo de trilha
        switch (TrailType)
        {
            case EAURACRONTrailType::PrismalFlow:
                UE_LOGFMT(LogTemp, Display, "{PlayerName} saiu do Fluxo Prismal - Removendo efeito de velocidade", PlayerCharacter->GetName());
                break;

            case EAURACRONTrailType::EtherealPath:
                UE_LOGFMT(LogTemp, Display, "{PlayerName} saiu do Caminho Etéreo - Removendo efeito de invisibilidade", PlayerCharacter->GetName());
                break;

            case EAURACRONTrailType::NexusConnection:
                UE_LOGFMT(LogTemp, Display, "{PlayerName} saiu da Conexão de Nexus - Removendo efeito de poder", PlayerCharacter->GetName());
                break;
                
            case EAURACRONTrailType::Solar:
                // Restaurar velocidade normal do jogador
                if (PlayerCharacter->GetCharacterMovement() && OriginalPlayerSpeeds.Contains(PlayerCharacter))
                {
                    // Restaurar velocidade original
                    PlayerCharacter->GetCharacterMovement()->MaxWalkSpeed = OriginalPlayerSpeeds[PlayerCharacter];
                    OriginalPlayerSpeeds.Remove(PlayerCharacter);
                    
                    // Parar regeneração de vida via Blueprint

                    UE_LOGFMT(LogTemp, Display, "{PlayerName} saiu do Solar Trail - Removendo boost de velocidade e regeneração",
                           PlayerCharacter->GetName());
                }
                break;
                
            case EAURACRONTrailType::Axis:
                // Remover status de pronto para transição via Blueprint
                UE_LOGFMT(LogTemp, Display, "{PlayerName} saiu do Axis Trail - Removendo possibilidade de transição",
                       PlayerCharacter->GetName());
                break;
                
            case EAURACRONTrailType::Lunar:
                // Remover efeito de furtividade e visão noturna
                if (InvisiblePlayers.Contains(PlayerCharacter))
                {
                    // Remover invisibilidade
                    PlayerCharacter->SetActorHiddenInGame(false);
                    InvisiblePlayers.Remove(PlayerCharacter);
                    
                    // Remover visão noturna via Blueprint

                    UE_LOGFMT(LogTemp, Display, "{PlayerName} saiu do Lunar Trail - Removendo furtividade e visão noturna",
                           PlayerCharacter->GetName());
                }
                break;
        }
    }
}

void AAURACRONPCGTrail::ApplyTrailEffectsToPlayer(ACharacter* Player, float DeltaTime)
{
    if (!Player)
        return;
    
    // Obter a posição do jogador ao longo da trilha (0.0 = início, 1.0 = fim)
    float PositionAlongTrail = GetPlayerPositionAlongTrail(Player);
    
    // Aplicar efeitos baseados no tipo de trilha e posição
    switch (TrailType)
    {
        case EAURACRONTrailType::PrismalFlow:
            // Aplicar efeito de velocidade - aumenta com a direção do fluxo
            // Implementação completa seria feita com um GameplayEffect
            {
                // Calcular direção do movimento do jogador
                FVector PlayerVelocity = Player->GetVelocity();
                FVector SplineDirection = SplineComponent->GetDirectionAtDistanceAlongSpline(
                    PositionAlongTrail * SplineComponent->GetSplineLength(), 
                    ESplineCoordinateSpace::World);
                
                // Verificar se o jogador está se movendo na direção do fluxo
                float DotProduct = FVector::DotProduct(PlayerVelocity.GetSafeNormal(), SplineDirection.GetSafeNormal());
                
                // Aplicar boost de velocidade se estiver se movendo na direção do fluxo
                if (DotProduct > 0.0f)
                {
                    // Aqui seria aplicado um modificador de velocidade ao jogador
                    // Player->CustomSpeedModifier = 1.0f + (FlowSpeed * DotProduct * 0.5f);
                    UE_LOGFMT(LogTemp, Verbose, "Aplicando boost de velocidade de {SpeedBoost} ao jogador {PlayerName}",
                           FlowSpeed * DotProduct * 0.5f, Player->GetName());
                }
                // Aplicar redução de velocidade se estiver se movendo contra o fluxo
                else if (DotProduct < 0.0f)
                {
                    // Aqui seria aplicado um modificador de velocidade ao jogador
                    // Player->CustomSpeedModifier = 1.0f + (FlowSpeed * DotProduct * 0.3f);
                    UE_LOGFMT(LogTemp, Verbose, "Aplicando redução de velocidade de {SpeedReduction} ao jogador {PlayerName}",
                           FlowSpeed * DotProduct * 0.3f, Player->GetName());
                }
            }
            break;
            
        case EAURACRONTrailType::EtherealPath:
            // Aplicar efeito de invisibilidade parcial
            // Implementação completa seria feita com um GameplayEffect
            {
                // Calcular nível de invisibilidade baseado na visibilidade do caminho
                float InvisibilityFactor = 1.0f - PathVisibility;
                
                // Aqui seria aplicado um modificador de visibilidade ao jogador
                // Player->CustomVisibilityModifier = InvisibilityFactor;
                UE_LOGFMT(LogTemp, Verbose, "Aplicando invisibilidade de {InvisibilityFactor} ao jogador {PlayerName}",
                       InvisibilityFactor, Player->GetName());
            }
            break;
            
        case EAURACRONTrailType::NexusConnection:
            // Aplicar efeito de aumento de poder
            // Implementação completa seria feita com um GameplayEffect
            {
                // Calcular boost de poder baseado na força da conexão
                float PowerBoost = ConnectionStrength * 0.2f; // 20% do valor da força
                
                // Aqui seria aplicado um modificador de dano ao jogador
                // Player->CustomDamageModifier = 1.0f + PowerBoost;
                UE_LOGFMT(LogTemp, Verbose, "Aplicando boost de poder de {PowerBoost} ao jogador {PlayerName}",
                       PowerBoost, Player->GetName());
            }
            break;
            
        case EAURACRONTrailType::Solar:
            // Aplicar efeito de boost de velocidade e regeneração de vida
            {
                // Calcular intensidade solar baseada na hora do dia
                float TimeOfDay = GetWorld()->GetTimeSeconds() / 86400.0f; // Simular ciclo de 24 horas
                float SolarIntensity = FMath::Sin(TimeOfDay * 2.0f * PI) * 0.5f + 0.5f; // 0.0 a 1.0
                
                // Aplicar boost de velocidade proporcional à intensidade solar
                if (Player->GetCharacterMovement())
                {
                    float SpeedBoost = 1.0f + (SolarIntensity * 0.3f); // Até 30% mais rápido
                    // Player->GetCharacterMovement()->MaxWalkSpeed *= SpeedBoost;
                    
                    // Regenerar vida do jogador através de sistema de atributos
                    float HealthRegen = SolarIntensity * 5.0f * DeltaTime; // 5 pontos de vida por segundo no máximo
                    
                    // Implementar regeneração via componente de saúde
                    if (UActorComponent* HealthComponent = Player->GetComponentByClass(UActorComponent::StaticClass()))
                    {
                        // Aplicar regeneração de vida
                        OnSolarHealthRegeneration.Broadcast(Player, HealthRegen, SpeedBoost);
                    }
                    
                    UE_LOGFMT(LogTemp, Verbose, "Solar Trail: Aplicando boost de velocidade de {SpeedBoost} e regeneração de {HealthRegen} ao jogador {PlayerName}",
                           SpeedBoost, HealthRegen, Player->GetName());
                }
                
                // Efeito visual de brilho solar
                if (SolarTrailEffect)
                {
                    SolarTrailEffect->SetFloatParameter(TEXT("Intensity"), SolarIntensity);
                }
            }
            break;
            
        case EAURACRONTrailType::Axis:
            // Aplicar efeito de transição entre ambientes
            {
                // Verificar se o jogador está próximo de um ponto de conexão
                float MinDistanceToConnection = 9999999.0f;
                int32 NearestConnectionPoint = -1;
                
                // Encontrar o ponto de conexão mais próximo
                for (int32 i = 0; i < SplineComponent->GetNumberOfSplinePoints(); i++)
                {
                    FVector PointLocation = SplineComponent->GetLocationAtSplinePoint(i, ESplineCoordinateSpace::World);
                    float Distance = FVector::Distance(Player->GetActorLocation(), PointLocation);
                    
                    if (Distance < MinDistanceToConnection)
                    {
                        MinDistanceToConnection = Distance;
                        NearestConnectionPoint = i;
                    }
                }
                
                // Se estiver próximo o suficiente de um ponto de conexão, permitir transição
                if (NearestConnectionPoint >= 0 && MinDistanceToConnection < 200.0f) // 2 metros
                {
                    // Marcar o jogador como pronto para transição
                if (ACharacter* Character = Cast<ACharacter>(Player))
                {
                    // Implementar sistema de transição via componente customizado
                    if (UActorComponent* TransitionComponent = Character->GetComponentByClass(UActorComponent::StaticClass()))
                    {
                        // Configurar destino de transição
                        FVector TransitionDestination = SplineComponent->GetLocationAtSplinePoint(NearestConnectionPoint, ESplineCoordinateSpace::World);
                        
                        // Broadcast evento de transição disponível
                        OnAxisTransitionAvailable.Broadcast(Character, TransitionDestination, NearestConnectionPoint);
                    }
                }
                    
                    UE_LOGFMT(LogTemp, Verbose, "Axis Trail: Jogador {PlayerName} próximo do ponto de conexão {ConnectionPoint} - pronto para transição",
                           Player->GetName(), NearestConnectionPoint);
                    
                    // Efeito visual de distorção espacial
                    if (AxisTrailEffect)
                    {
                        AxisTrailEffect->SetVectorParameter(TEXT("ConnectionPoint"), 
                            SplineComponent->GetLocationAtSplinePoint(NearestConnectionPoint, ESplineCoordinateSpace::World));
                        AxisTrailEffect->SetFloatParameter(TEXT("DistortionIntensity"), 1.0f - (MinDistanceToConnection / 200.0f));
                    }
                }
            }
            break;
            
        case EAURACRONTrailType::Lunar:
            // Aplicar efeito de furtividade e visão aprimorada
            {
                // Calcular intensidade lunar baseada na hora da noite
                float TimeOfDay = GetWorld()->GetTimeSeconds() / 86400.0f; // Simular ciclo de 24 horas
                float DayNightCycle = FMath::Sin(TimeOfDay * 2.0f * PI); // -1.0 a 1.0
                float IsNight = DayNightCycle < 0.0f ? 1.0f : 0.0f; // 1.0 se for noite, 0.0 se for dia
                float LunarIntensity = IsNight * FMath::Abs(DayNightCycle); // 0.0 a 1.0, só à noite
                
                // Aplicar efeito de furtividade proporcional à intensidade lunar
                float StealthFactor = LunarIntensity * 0.8f; // Até 80% de furtividade
                
                // Aplicar visão aprimorada através de modificadores de componente
                float NightVisionFactor = LunarIntensity * 0.7f; // Até 70% de visão noturna
                
                // Implementar efeito de visão noturna via componente de câmera
                if (APlayerController* PC = Cast<APlayerController>(Player->GetController()))
                {
                    if (APawn* Pawn = PC->GetPawn())
                    {
                        // Aplicar modificador de visão noturna
                        OnLunarVisionEffect.Broadcast(Pawn, NightVisionFactor, StealthFactor);
                    }
                }
                
                UE_LOGFMT(LogTemp, Verbose, "Lunar Trail: Aplicando furtividade de {StealthFactor} e visão noturna de {NightVisionFactor} ao jogador {PlayerName}",
                       StealthFactor, NightVisionFactor, Player->GetName());
                
                // Efeito visual de brilho lunar
                if (LunarTrailEffect)
                {
                    LunarTrailEffect->SetFloatParameter(TEXT("Intensity"), LunarIntensity);
                    LunarTrailEffect->SetFloatParameter(TEXT("StealthFactor"), StealthFactor);
                }
            }
            break;
    }
}

bool AAURACRONPCGTrail::IsPlayerInTrail(ACharacter* Player) const
{
    return OverlappingPlayers.Contains(Player);
}

float AAURACRONPCGTrail::GetPlayerPositionAlongTrail(ACharacter* Player) const
{
    if (!Player)
        return 0.0f;
    
    // Obter a posição do jogador
    FVector PlayerLocation = Player->GetActorLocation();
    
    // Encontrar o ponto mais próximo na spline
    float ClosestDistanceAlongSpline = 0.0f;
    float ClosestDistanceSquared = MAX_FLT;
    
    // Calcular o comprimento total da spline
    float SplineLength = SplineComponent->GetSplineLength();
    
    // Número de amostras para verificar ao longo da spline
    const int32 NumSamples = 20;
    
    for (int32 i = 0; i <= NumSamples; ++i)
    {
        float Distance = (float)i / (float)NumSamples * SplineLength;
        FVector PointOnSpline = SplineComponent->GetLocationAtDistanceAlongSpline(Distance, ESplineCoordinateSpace::World);
        
        float DistanceSquared = FVector::DistSquared(PlayerLocation, PointOnSpline);
        if (DistanceSquared < ClosestDistanceSquared)
        {
            ClosestDistanceSquared = DistanceSquared;
            ClosestDistanceAlongSpline = Distance;
        }
    }
    
    // Retornar a posição normalizada ao longo da spline (0.0 = início, 1.0 = fim)
    return ClosestDistanceAlongSpline / SplineLength;
}

void AAURACRONPCGTrail::UpdateInteractionVolume()
{
    // Atualizar o volume de interação para seguir a spline
    if (SplineComponent->GetNumberOfSplinePoints() < 2)
        return;
    
    // Calcular o comprimento total da spline
    float SplineLength = SplineComponent->GetSplineLength();
    
    // Ajustar o tamanho do volume de interação baseado no tipo de trilha
    float VolumeWidth = 0.0f;
    float VolumeHeight = 200.0f; // 2 metros de altura padrão
    
    switch (TrailType)
    {
        case EAURACRONTrailType::PrismalFlow:
            VolumeWidth = FlowWidth * 1.2f; // Um pouco maior que a largura visual
            break;
            
        case EAURACRONTrailType::EtherealPath:
            VolumeWidth = PathWidth * 1.5f; // Significativamente maior que a largura visual
            break;
            
        case EAURACRONTrailType::NexusConnection:
            VolumeWidth = ConnectionWidth * 1.3f; // Um pouco maior que a largura visual
            break;
    }
    
    // Ajustar o tamanho do volume de interação
    InteractionVolume->SetBoxExtent(FVector(SplineLength * 0.5f, VolumeWidth * 0.5f, VolumeHeight * 0.5f));
    
    // Posicionar o volume no centro da spline
    FVector StartPoint = SplineComponent->GetLocationAtSplinePoint(0, ESplineCoordinateSpace::World);
    FVector EndPoint = SplineComponent->GetLocationAtSplinePoint(SplineComponent->GetNumberOfSplinePoints() - 1, ESplineCoordinateSpace::World);
    FVector CenterPoint = (StartPoint + EndPoint) * 0.5f;
    
    // Calcular a rotação para alinhar com a direção da spline
    FVector Direction = (EndPoint - StartPoint).GetSafeNormal();
    FRotator Rotation = Direction.Rotation();
    
    // Atualizar a transformação do volume de interação
    InteractionVolume->SetWorldLocation(CenterPoint);
    InteractionVolume->SetWorldRotation(Rotation);
}

void AAURACRONPCGTrail::UpdateTrailParameters()
{
    // Atualizar parâmetros dinâmicos da trilha ao longo do tempo
    float CurrentTime = GetWorld()->GetTimeSeconds();
    
    // Atualizar parâmetros baseados no tipo de trilha
    switch (TrailType)
    {
    case EAURACRONTrailType::PrismalFlow:
        {
            // Adicionar alguma variação ao longo do tempo
            FlowIntensity = 1.0f + 0.2f * FMath::Sin(CurrentTime * 0.5f);
            FlowSpeed = 1.0f + 0.3f * FMath::Cos(CurrentTime * 0.3f);

            // Atualizar parâmetros dinâmicos no PCGComponent
            SetPCGParameterModern(TEXT("FlowIntensity"), FVector(FlowIntensity, 0.0f, 0.0f), TEXT("FlowIntensity"));
            SetPCGParameterModern(TEXT("FlowSpeed"), FVector(FlowSpeed, 0.0f, 0.0f), TEXT("FlowSpeed"));

            // Adicionar efeito de ondulação baseado no tempo
            float WaveEffect = FMath::Sin(CurrentTime * 0.8f) * 0.5f;
            SetPCGParameterModern(TEXT("WaveEffect"), FVector(WaveEffect, 0.0f, 0.0f), TEXT("WaveEffect"));
            break;
        }
        
    case EAURACRONTrailType::EtherealPath:
        {
            // Adicionar alguma variação ao longo do tempo
            PathVisibility = 0.7f + 0.3f * FMath::Sin(CurrentTime * 0.2f);
            PathFluctuation = 0.5f + 0.3f * FMath::Cos(CurrentTime * 0.4f);

            // Atualizar parâmetros dinâmicos no PCGComponent
            SetPCGParameterModern(TEXT("PathVisibility"), FVector(PathVisibility, 0.0f, 0.0f), TEXT("PathVisibility"));
            SetPCGParameterModern(TEXT("PathFluctuation"), FVector(PathFluctuation, 0.0f, 0.0f), TEXT("PathFluctuation"));

            // Atualizar efeito de flutuação baseado no tempo
            float FluctuationOffset = FMath::Sin(CurrentTime * 0.5f) * PathFluctuation;
            SetPCGParameterModern(TEXT("FluctuationOffset"), FVector(FluctuationOffset, 0.0f, 0.0f), TEXT("FluctuationOffset"));

            // Atualizar alpha baseado na visibilidade
            SetPCGParameterModern(TEXT("PathAlpha"), FVector(PathAlpha, 0.0f, 0.0f), TEXT("PathAlpha"));
            break;
        }
        
    case EAURACRONTrailType::NexusConnection:
        {
            // Adicionar alguma variação ao longo do tempo
            ConnectionStrength = 0.8f + 0.2f * FMath::Sin(CurrentTime * 0.3f);
            ConnectionPulseRate = 1.0f + 0.5f * FMath::Cos(CurrentTime * 0.6f);

            // Atualizar parâmetros dinâmicos no PCGComponent
            SetPCGParameterModern(TEXT("ConnectionStrength"), FVector(ConnectionStrength, 0.0f, 0.0f), TEXT("ConnectionStrength"));
            SetPCGParameterModern(TEXT("ConnectionPulseRate"), FVector(ConnectionPulseRate, 0.0f, 0.0f), TEXT("ConnectionPulseRate"));

            // Atualizar efeito de pulso baseado no tempo
            float PulseValue = (FMath::Sin(CurrentTime * ConnectionPulseRate) + 1.0f) * 0.5f; // Normalizado entre 0 e 1
            SetPCGParameterModern(TEXT("PulseValue"), FVector(PulseValue, 0.0f, 0.0f), TEXT("PulseValue"));

            // Verificar jogadores próximos e atualizar parâmetros de interação
            TArray<AActor*> OverlappingActors;
            GetOverlappingActors(OverlappingActors, APawn::StaticClass());
            NumOverlappingPlayers = OverlappingActors.Num();
            SetPCGParameterModern(TEXT("NumOverlappingPlayers"), FVector(NumOverlappingPlayers, 0.0f, 0.0f), TEXT("NumOverlappingPlayers"));
            break;
        }
    }
}

// ========================================
// IMPLEMENTAÇÕES DAS NOVAS FUNÇÕES MODERNAS
// ========================================

void AAURACRONPCGTrail::ClearTrail()
{
    // Limpar elementos gerados anteriormente
    GeneratedElements.Empty();

    // Limpar componentes dinâmicos
    TArray<UActorComponent*> ComponentsToRemove;
    TArray<UActorComponent*> AllComponents = GetComponents().Array();

    for (UActorComponent* Component : AllComponents)
    {
        if (Component != PCGComponent && Component != RootComponent &&
            Component != SplineComponent && Component != InteractionVolume)
        {
            ComponentsToRemove.Add(Component);
        }
    }

    for (UActorComponent* Component : ComponentsToRemove)
    {
        Component->DestroyComponent();
    }
}

TArray<FVector> AAURACRONPCGTrail::GenerateTrailPointsModern()
{
    TArray<FVector> Points;
    FVector MapCenter = FAURACRONMapDimensions::MAP_CENTER;
    float TimeOfDay = GetWorld()->GetTimeSeconds() / 86400.0f; // Converter para ciclo de 24h

    // Gerar pontos de trilha usando as funções matemáticas modernas disponíveis
    FVector StartPoint = StartLocation.IsZero() ? MapCenter + FVector(-2000.0f, 0.0f, 0.0f) : StartLocation;
    FVector EndPoint = EndLocation.IsZero() ? MapCenter + FVector(2000.0f, 0.0f, 0.0f) : EndLocation;

    // Usar diferentes algoritmos baseados no tipo de trilha
    switch (TrailType)
    {
        case EAURACRONTrailType::PrismalFlow:
        case EAURACRONTrailType::EtherealPath:
        case EAURACRONTrailType::NexusConnection:
        {
            // Criar uma curva serpentina suave
            FAURACRONSplineCurve Curve = UAURACRONPCGMathLibrary::CreateSerpentineCurve(
                StartPoint,
                EndPoint,
                10, // NumControlPoints
                500.0f, // Amplitude
                2.0f // Frequency
            );

            // Avaliar pontos ao longo da curva
            int32 NumPoints = 20;
            for (int32 i = 0; i <= NumPoints; ++i)
            {
                float T = static_cast<float>(i) / static_cast<float>(NumPoints);
                FVector Point = UAURACRONPCGMathLibrary::EvaluateSplineCurve(Curve, T);
                Points.Add(Point);
            }
            break;
        }

        case EAURACRONTrailType::Solar:
        {
            // Trilhas solares seguem um padrão orbital
            Points = UAURACRONPCGMathLibrary::CreateOrbitalPath(
                MapCenter,
                3000.0f, // Radius
                500.0f, // Height
                16, // NumPoints
                TimeOfDay * 2.0f * PI // PhaseOffset baseado na hora do dia
            );
            break;
        }

        case EAURACRONTrailType::Axis:
        {
            // Trilhas axiais conectam verticalmente
            Points.Add(StartPoint);
            Points.Add(FVector(StartPoint.X, StartPoint.Y, StartPoint.Z + 1000.0f));
            Points.Add(FVector(EndPoint.X, EndPoint.Y, EndPoint.Z + 1000.0f));
            Points.Add(EndPoint);
            break;
        }

        case EAURACRONTrailType::Lunar:
        {
            // Trilhas lunares seguem um padrão mais sutil
            Points = UAURACRONPCGMathLibrary::CreateOrbitalPath(
                MapCenter,
                2000.0f, // Radius menor que solar
                200.0f, // Height menor
                12, // Menos pontos
                (TimeOfDay + 0.5f) * 2.0f * PI // Offset de fase lunar
            );
            break;
        }

        default:
        {
            // Fallback: linha reta simples
            Points.Add(StartPoint);
            Points.Add(EndPoint);
            break;
        }
    }

    return Points;
}

void AAURACRONPCGTrail::GenerateSolarTrail()
{
    if (!HasAuthority())
    {
        return;
    }

    // Solar Trails seguem o movimento do sol
    float TimeOfDay = GetWorld()->GetTimeSeconds() / 86400.0f;
    float SolarIntensity = UAURACRONPCGMathLibrary::GetSolarIntensity(TimeOfDay);

    // Criar efeitos visuais solares ao longo da spline
    int32 NumSegments = SplineComponent->GetNumberOfSplinePoints() - 1;

    for (int32 i = 0; i < NumSegments; ++i)
    {
        float SplineDistance = static_cast<float>(i) / NumSegments;
        FVector SegmentLocation = SplineComponent->GetLocationAtDistanceAlongSpline(
            SplineDistance * SplineComponent->GetSplineLength(),
            ESplineCoordinateSpace::World
        );

        // Criar componente de efeito solar
        UNiagaraComponent* SolarEffect = NewObject<UNiagaraComponent>(this);
        if (SolarEffect && SolarTrailEffect)
        {
            SolarEffect->SetAsset(SolarTrailEffectAsset);
            SolarEffect->AttachToComponent(RootComponent,
                FAttachmentTransformRules::KeepWorldTransform);
            SolarEffect->SetWorldLocation(SegmentLocation);

            // Configurar intensidade baseada no horário (mais forte ao meio-dia)
            SolarEffect->SetFloatParameter(FName("Intensity"), SolarIntensity);
            SolarEffect->SetFloatParameter(FName("TimeOfDay"), TimeOfDay);
            
            // Adicionar efeitos de distorção de calor nas bordas
            SolarEffect->SetFloatParameter(FName("HeatDistortion"), SolarIntensity * 0.8f);
            
            // Configurar cor dourada para partículas
            SolarEffect->SetColorParameter(FName("TrailColor"), FLinearColor(1.0f, 0.8f, 0.2f, 1.0f));

            GeneratedElements.Add(SolarEffect);
        }
    }
    
    // Adicionar efeito de boost de velocidade e regeneração de vida ao volume de interação
    if (InteractionVolume)
    {
        InteractionVolume->SetCollisionProfileName(TEXT("OverlapAllDynamic"));
        InteractionVolume->SetGenerateOverlapEvents(true);
    }
}

void AAURACRONPCGTrail::GenerateAxisTrail()
{
    if (!HasAuthority())
    {
        return;
    }

    // Axis Trails conectam os três ambientes verticalmente
    TArray<FVector> EnvironmentCenters;
    EnvironmentCenters.Add(UAURACRONMapMeasurements::GetEnvironmentCenter(
        static_cast<int32>(EAURACRONEnvironmentType::RadiantPlains)));
    EnvironmentCenters.Add(UAURACRONMapMeasurements::GetEnvironmentCenter(
        static_cast<int32>(EAURACRONEnvironmentType::ZephyrFirmament)));
    EnvironmentCenters.Add(UAURACRONMapMeasurements::GetEnvironmentCenter(
        static_cast<int32>(EAURACRONEnvironmentType::PurgatoryRealm)));

    // Criar conexões verticais entre ambientes
    for (int32 i = 0; i < EnvironmentCenters.Num(); ++i)
    {
        FVector ConnectionPoint = EnvironmentCenters[i];

        // Criar componente de efeito Niagara para o Axis Trail
        UNiagaraComponent* AxisEffect = NewObject<UNiagaraComponent>(this);
        if (AxisEffect && AxisTrailEffect)
        {
            AxisEffect->SetAsset(AxisTrailEffectAsset);
            AxisEffect->AttachToComponent(RootComponent,
                FAttachmentTransformRules::KeepWorldTransform);
            AxisEffect->SetWorldLocation(ConnectionPoint);

            // Configurar aparência cinza/prata neutra
            AxisEffect->SetColorParameter(FName("TrailColor"), FLinearColor(0.75f, 0.75f, 0.8f, 1.0f));
            
            // Adicionar efeitos de distorção gravitacional
            AxisEffect->SetFloatParameter(FName("GravityDistortion"), 0.8f);
            
            // Configurar padrões geométricos prateados
            AxisEffect->SetFloatParameter(FName("GeometricPatternIntensity"), 0.9f);

            GeneratedElements.Add(AxisEffect);
        }
        
        // Criar componente de conexão axial (ponto de transição)
        UStaticMeshComponent* AxisConnection = NewObject<UStaticMeshComponent>(this);
        if (AxisConnection && AxisConnectionMesh)
        {
            AxisConnection->SetStaticMesh(AxisConnectionMesh);
            AxisConnection->AttachToComponent(RootComponent,
                FAttachmentTransformRules::KeepWorldTransform);
            AxisConnection->SetWorldLocation(ConnectionPoint);

            // Configurar escala baseada na importância do ambiente
            float EnvironmentImportance = (i == 0) ? 1.0f : 0.8f; // Radiant Plains mais importante
            AxisConnection->SetWorldScale3D(FVector(EnvironmentImportance));

            GeneratedElements.Add(AxisConnection);
        }
    }
    
    // Configurar o volume de interação para permitir transição instantânea entre ambientes
    if (InteractionVolume)
    {
        InteractionVolume->SetCollisionProfileName(TEXT("OverlapAllDynamic"));
        InteractionVolume->SetGenerateOverlapEvents(true);
    }
}

void AAURACRONPCGTrail::GenerateLunarTrail()
{
    if (!HasAuthority())
    {
        return;
    }

    // Lunar Trails seguem o movimento da lua
    float TimeOfDay = GetWorld()->GetTimeSeconds() / 86400.0f;
    float LunarIntensity = UAURACRONPCGMathLibrary::GetLunarPhaseIntensity(TimeOfDay);
    
    // Verificar se é noite (os trilhos lunares são visíveis apenas à noite)
    bool bIsNight = TimeOfDay < 0.25f || TimeOfDay > 0.75f;
    float Visibility = bIsNight ? 1.0f : 0.1f; // Quase invisível durante o dia

    // Criar efeitos visuais lunares ao longo da spline
    int32 NumSegments = SplineComponent->GetNumberOfSplinePoints() - 1;

    for (int32 i = 0; i < NumSegments; ++i)
    {
        float SplineDistance = static_cast<float>(i) / NumSegments;
        FVector SegmentLocation = SplineComponent->GetLocationAtDistanceAlongSpline(
            SplineDistance * SplineComponent->GetSplineLength(),
            ESplineCoordinateSpace::World
        );

        // Criar componente de efeito lunar
        UNiagaraComponent* LunarEffect = NewObject<UNiagaraComponent>(this);
        if (LunarEffect && LunarTrailEffect)
        {
            LunarEffect->SetAsset(LunarTrailEffectAsset);
            LunarEffect->AttachToComponent(RootComponent,
                FAttachmentTransformRules::KeepWorldTransform);
            LunarEffect->SetWorldLocation(SegmentLocation);

            // Configurar intensidade baseada na fase lunar
            LunarEffect->SetFloatParameter(FName("Intensity"), LunarIntensity);
            LunarEffect->SetFloatParameter(FName("LunarPhase"), TimeOfDay);

            // Configurar aparência etérea azul-branco
            LunarEffect->SetColorParameter(FName("TrailColor"), FLinearColor(0.5f, 0.7f, 1.0f, 1.0f));
            
            // Adicionar névoa azul suave e partículas de poeira estelar
            LunarEffect->SetFloatParameter(FName("MistIntensity"), LunarIntensity * 0.8f);
            LunarEffect->SetFloatParameter(FName("StardustAmount"), LunarIntensity * 0.6f);
            
            // Visibilidade baseada no ciclo dia/noite
            LunarEffect->SetFloatParameter(FName("Opacity"), Visibility * 0.7f);

            GeneratedElements.Add(LunarEffect);
        }
    }
    
    // Configurar o volume de interação para conceder furtividade e visão aprimorada
    if (InteractionVolume)
    {
        InteractionVolume->SetCollisionProfileName(TEXT("OverlapAllDynamic"));
        InteractionVolume->SetGenerateOverlapEvents(true);
    }
}

void AAURACRONPCGTrail::ApplyActivityScale()
{
    // Aplicar escala de atividade a todos os elementos gerados
    for (UActorComponent* Element : GeneratedElements)
    {
        if (IsValid(Element))
        {
            if (UPrimitiveComponent* PrimComp = Cast<UPrimitiveComponent>(Element))
            {
                // Ajustar visibilidade baseada na escala de atividade
                bool bShouldBeVisible = ActivityScale > 0.1f;
                PrimComp->SetVisibility(bShouldBeVisible);

                // Ajustar escala visual
                if (bShouldBeVisible)
                {
                    FVector Scale = FVector(ActivityScale);
                    PrimComp->SetWorldScale3D(Scale);
                }
            }

            if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Element))
            {
                // Ajustar intensidade dos efeitos de partículas
                NiagaraComp->SetFloatParameter(FName("ActivityScale"), ActivityScale);
            }
        }
    }
}

void AAURACRONPCGTrail::SetPCGParameterModern(const FString& ParameterName, const FVector& Value, const FString& Context)
{
    // Função auxiliar para configurar parâmetros PCG usando API moderna UE 5.6
    if (!PCGComponent || !IsValid(PCGComponent))
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGTrail::SetPCGParameterModern - PCGComponent is invalid");
        return;
    }

    // Usar sistema moderno de parâmetros do UE 5.6
    if (UPCGGraph* PCGGraph = PCGComponent->GetGraph())
    {
        // Usar API moderna para definir parâmetros via GraphInstance
        if (UPCGGraphInstance* GraphInstance = PCGComponent->GetGraphInstance())
        {
            // Criar um nome de parâmetro completo com contexto
            FString FullParameterName = Context.IsEmpty() ? ParameterName : FString::Printf(TEXT("%s_%s"), *Context, *ParameterName);

            // Usar API moderna do UE 5.6 para definir parâmetros
            // Determinar o tipo de parâmetro com base no nome
            if (ParameterName.Contains(TEXT("Position")) || ParameterName.Contains(TEXT("Location")))
            {
                // Parâmetros de transformação
                FTransform ParameterTransform = FTransform(FRotator::ZeroRotator, FVector(Value.X, Value.Y, Value.Z), FVector::OneVector);
                UPCGGraphParametersHelpers::SetTransformParameter(GraphInstance, FName(*FullParameterName), ParameterTransform);
            }
            else if (ParameterName.Contains(TEXT("Scale")) || ParameterName.Contains(TEXT("Intensity")) || ParameterName.Contains(TEXT("Alpha")))
            {
                // Parâmetros escalares
                float ScalarValue = Value.X; // Usar apenas o componente X para valores escalares
                UPCGGraphParametersHelpers::SetFloatParameter(GraphInstance, FName(*FullParameterName), ScalarValue);
            }
            else if (ParameterName.Contains(TEXT("Color")) || ParameterName.Contains(TEXT("Direction")) || ParameterName.Contains(TEXT("Offset")))
            {
                // Parâmetros vetoriais
                UPCGGraphParametersHelpers::SetVectorParameter(GraphInstance, FName(*FullParameterName), Value);
            }
            else if (ParameterName.Contains(TEXT("Enable")) || ParameterName.Contains(TEXT("Active")) || ParameterName.Contains(TEXT("Visible")))
            {
                // Parâmetros booleanos
                bool BoolValue = Value.X > 0.5f; // Converter para booleano
                UPCGGraphParametersHelpers::SetBoolParameter(GraphInstance, FName(*FullParameterName), BoolValue);
            }

            // Forçar regeneração do PCG se necessário
            if (bIsActive && PCGComponent->GetGraph())
            {
                // Usar API moderna para regeneração incremental
                PCGComponent->GenerateLocal(false); // Regeneração não-bloqueante
            }

            UE_LOGFMT(LogTemp, VeryVerbose, "AAURACRONPCGTrail::SetPCGParameterModern - Set parameter {ParameterName} = ({X}, {Y}, {Z}) in context {Context}",
                   ParameterName, Value.X, Value.Y, Value.Z, Context);
        }
        else
        {
            UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGTrail::SetPCGParameterModern - GraphInstance not found");
        }
    }
    else
    {
        UE_LOGFMT(LogTemp, Warning, "AAURACRONPCGTrail::SetPCGParameterModern - PCG Graph not found");
    }
}

void AAURACRONPCGTrail::UpdateObjectiveConnections(const TArray<FVector>& ObjectivePositions, EAURACRONMapPhase MapPhase)
{
    if (!IsValid(PCGComponent) || ObjectivePositions.Num() == 0)
    {
        return;
    }
    
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGTrail::UpdateObjectiveConnections - Atualizando conexões com {NumObjectives} objetivos na fase {MapPhase}",
           ObjectivePositions.Num(), UEnum::GetValueAsString(MapPhase));
    
    // Limpar pontos de controle existentes
    ClearControlPoints();
    
    // Adicionar pontos de início e fim da trilha como pontos de controle
    AddControlPoint(StartLocation);
    
    // Encontrar os objetivos mais próximos para conectar com a trilha
    // Limitamos a no máximo 3 objetivos para não sobrecarregar a trilha
    const int32 MaxObjectivesToConnect = FMath::Min(3, ObjectivePositions.Num());
    
    // Ordenar objetivos por distância ao ponto inicial da trilha
    TArray<TPair<float, FVector>> SortedObjectives;
    for (const FVector& ObjPos : ObjectivePositions)
    {
        float Distance = FVector::Distance(StartLocation, ObjPos);
        SortedObjectives.Add(TPair<float, FVector>(Distance, ObjPos));
    }
    
    // Ordenar do mais próximo ao mais distante
    SortedObjectives.Sort([](const TPair<float, FVector>& A, const TPair<float, FVector>& B) {
        return A.Key < B.Key;
    });
    
    // Adicionar os objetivos mais próximos como pontos de controle
    for (int32 i = 0; i < MaxObjectivesToConnect; i++)
    {
        if (i < SortedObjectives.Num())
        {
            // Adicionar o objetivo como ponto de controle
            AddControlPoint(SortedObjectives[i].Value);
            
            // Ajustar a intensidade da trilha com base na fase do mapa
            float IntensityMultiplier = 1.0f;
            switch (MapPhase)
            {
                case EAURACRONMapPhase::Awakening:
                    IntensityMultiplier = 0.7f;
                    break;
                case EAURACRONMapPhase::Convergence:
                    IntensityMultiplier = 1.0f;
                    break;
                case EAURACRONMapPhase::Intensification:
                    IntensityMultiplier = 1.5f;
                    break;
                default:
                    break;
            }
            
            // Configurar parâmetros específicos do tipo de trilha
            switch (TrailType)
            {
                case EAURACRONTrailType::PrismalFlow:
                    SetPCGParameterModern(TEXT("FlowIntensity"), FVector(FlowIntensity * IntensityMultiplier), TEXT("Objective"));
                    SetPCGParameterModern(TEXT("FlowWidth"), FVector(FlowWidth * 1.2f), TEXT("Objective"));
                    break;
                case EAURACRONTrailType::EtherealPath:
                    SetPCGParameterModern(TEXT("PathVisibility"), FVector(PathVisibility * IntensityMultiplier), TEXT("Objective"));
                    SetPCGParameterModern(TEXT("PathWidth"), FVector(PathWidth * 1.2f), TEXT("Objective"));
                    break;
                case EAURACRONTrailType::NexusConnection:
                    SetPCGParameterModern(TEXT("ConnectionStrength"), FVector(ConnectionStrength * IntensityMultiplier), TEXT("Objective"));
                    SetPCGParameterModern(TEXT("ConnectionWidth"), FVector(ConnectionWidth * 1.2f), TEXT("Objective"));
                    break;
                default:
                    break;
            }
        }
    }
    
    // Adicionar o ponto final da trilha
    AddControlPoint(EndLocation);
    
    // Regenerar a trilha com os novos pontos de controle
    GenerateTrail();
    
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGTrail::UpdateObjectiveConnections - Trilha regenerada com {MaxConnections} conexões de objetivos",
           MaxObjectivesToConnect);
}

void AAURACRONPCGTrail::OnMapContraction(float ContractionFactor)
{
    UE_LOGFMT(LogTemp, Log, "OnMapContraction - Aplicando contração {ContractionFactor} à trilha {TrailType}", ContractionFactor, (int32)TrailType);

    // Aplicar contração aos pontos da spline
    FVector MapCenter = FVector::ZeroVector; // Centro do mapa

    if (IsValid(PCGSplineComponent))
    {
        int32 NumPoints = PCGSplineComponent->GetNumberOfSplinePoints();

        for (int32 i = 0; i < NumPoints; ++i)
        {
            FVector OriginalLocation = PCGSplineComponent->GetLocationAtSplinePoint(i, ESplineCoordinateSpace::World);
            FVector DirectionToCenter = (MapCenter - OriginalLocation).GetSafeNormal();
            FVector NewLocation = OriginalLocation + DirectionToCenter * (1.0f - ContractionFactor) * OriginalLocation.Size2D();

            // Preservar altura original
            NewLocation.Z = OriginalLocation.Z;

            PCGSplineComponent->SetLocationAtSplinePoint(i, NewLocation, ESplineCoordinateSpace::World);
        }

        // Atualizar spline após modificações
        PCGSplineComponent->UpdateSpline();
    }

    // Aplicar contração aos pontos de início e fim
    FVector DirectionToCenter = (MapCenter - StartLocation).GetSafeNormal();
    StartLocation = StartLocation + DirectionToCenter * (1.0f - ContractionFactor) * StartLocation.Size2D();

    DirectionToCenter = (MapCenter - EndLocation).GetSafeNormal();
    EndLocation = EndLocation + DirectionToCenter * (1.0f - ContractionFactor) * EndLocation.Size2D();

    // Atualizar pontos da spline interna
    for (FVector& SplinePoint : SplinePoints)
    {
        DirectionToCenter = (MapCenter - SplinePoint).GetSafeNormal();
        SplinePoint = SplinePoint + DirectionToCenter * (1.0f - ContractionFactor) * SplinePoint.Size2D();
    }

    // Atualizar volume de interação
    UpdateInteractionVolume();

    // Recalcular parâmetros da trilha
    UpdateTrailParameters();

    // Aplicar contração específica baseada no tipo de trilha
    switch (TrailType)
    {
        case EAURACRONTrailType::PrismalFlow:
            // Ajustar largura do fluxo baseado na contração
            FlowWidth *= ContractionFactor;
            SetPCGParameterModern(TEXT("FlowWidth"), FVector(FlowWidth, 0.0f, 0.0f), TEXT("FlowWidth"));
            break;

        case EAURACRONTrailType::EtherealPath:
            // Ajustar largura do caminho etéreo
            PathWidth *= ContractionFactor;
            SetPCGParameterModern(TEXT("PathWidth"), FVector(PathWidth, 0.0f, 0.0f), TEXT("PathWidth"));
            break;

        case EAURACRONTrailType::NexusConnection:
            // Ajustar largura da conexão
            ConnectionWidth *= ContractionFactor;
            SetPCGParameterModern(TEXT("ConnectionWidth"), FVector(ConnectionWidth, 0.0f, 0.0f), TEXT("ConnectionWidth"));
            break;
    }

    UE_LOGFMT(LogTemp, Log, "OnMapContraction - Contração aplicada com sucesso à trilha");
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES QUE ESTAVAM FALTANDO - UE 5.6 MODERN APIS
// ========================================

// Implementações da classe base ATrailBase
void ATrailBase::ConfigureForAwakeningPhase(bool bEnable)
{
    // Implementação robusta para configurar para fase Awakening
    if (bEnable)
    {
        // Configurar trilha para fase inicial
        PowerPercentage = 0.6f;

        // Configurar efeitos visuais suaves
        if (TrailEffectComponent && IsValid(TrailEffectComponent))
        {
            TrailEffectComponent->SetFloatParameter(TEXT("AwakeningPhase"), 1.0f);
            TrailEffectComponent->SetFloatParameter(TEXT("PowerPercentage"), PowerPercentage);
            TrailEffectComponent->SetFloatParameter(TEXT("PhaseIntensity"), 0.6f);
        }

        UE_LOGFMT(LogTemp, Log, "ATrailBase::ConfigureForAwakeningPhase - Awakening phase enabled");
    }
    else
    {
        // Desativar configurações da fase Awakening
        PowerPercentage = 1.0f;

        if (TrailEffectComponent && IsValid(TrailEffectComponent))
        {
            TrailEffectComponent->SetFloatParameter(TEXT("AwakeningPhase"), 0.0f);
        }

        UE_LOGFMT(LogTemp, Log, "ATrailBase::ConfigureForAwakeningPhase - Awakening phase disabled");
    }
}

void ATrailBase::ConfigureForConvergencePhase(bool bEnableConvergence, bool bEnableIntensification, bool bEnableResolution)
{
    // Implementação robusta para configurar para fases avançadas

    if (bEnableConvergence)
    {
        // Configurar para fase de convergência
        PowerPercentage = 1.2f;

        if (TrailEffectComponent && IsValid(TrailEffectComponent))
        {
            TrailEffectComponent->SetFloatParameter(TEXT("ConvergencePhase"), 1.0f);
            TrailEffectComponent->SetFloatParameter(TEXT("PowerPercentage"), PowerPercentage);
            TrailEffectComponent->SetFloatParameter(TEXT("PhaseIntensity"), 1.2f);
        }

        UE_LOGFMT(LogTemp, Log, "ATrailBase::ConfigureForConvergencePhase - Convergence phase enabled");
    }

    if (bEnableIntensification)
    {
        // Intensificar efeitos para fase de intensificação
        PowerPercentage = 1.6f;

        if (TrailEffectComponent && IsValid(TrailEffectComponent))
        {
            TrailEffectComponent->SetFloatParameter(TEXT("IntensificationPhase"), 1.0f);
            TrailEffectComponent->SetFloatParameter(TEXT("PowerPercentage"), PowerPercentage);
            TrailEffectComponent->SetFloatParameter(TEXT("PhaseIntensity"), 1.6f);
        }

        UE_LOGFMT(LogTemp, Log, "ATrailBase::ConfigureForConvergencePhase - Intensification phase enabled");
    }

    if (bEnableResolution)
    {
        // Configurar para fase de resolução
        PowerPercentage = 2.0f;

        if (TrailEffectComponent && IsValid(TrailEffectComponent))
        {
            TrailEffectComponent->SetFloatParameter(TEXT("ResolutionPhase"), 1.0f);
            TrailEffectComponent->SetFloatParameter(TEXT("PowerPercentage"), PowerPercentage);
            TrailEffectComponent->SetFloatParameter(TEXT("PhaseIntensity"), 2.0f);
        }

        UE_LOGFMT(LogTemp, Log, "ATrailBase::ConfigureForConvergencePhase - Resolution phase enabled");
    }
}

void ATrailBase::SetPowerPercentage(float NewPowerPercentage)
{
    // Implementação robusta para definir porcentagem de poder
    PowerPercentage = FMath::Clamp(NewPowerPercentage, 0.0f, 3.0f);

    // Atualizar efeitos visuais
    if (TrailEffectComponent && IsValid(TrailEffectComponent))
    {
        TrailEffectComponent->SetFloatParameter(TEXT("PowerPercentage"), PowerPercentage);
    }

    // Atualizar propriedades da trilha baseado no poder
    UpdateTrailProperties();

    UE_LOGFMT(LogTemp, Log, "ATrailBase::SetPowerPercentage - Power percentage set to {PowerPercentage}", PowerPercentage);
}

// Implementações da classe AAURACRONPCGTrail
void AAURACRONPCGTrail::ConfigureWorldPartitionStreaming(const FAURACRONPCGStreamingConfig& StreamingConfig)
{
    // Implementação robusta para configurar streaming do World Partition
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGTrail::ConfigureWorldPartitionStreaming - Configuring streaming");

    // Armazenar configuração de streaming
    StreamingConfiguration = StreamingConfig;

    // Configurar streaming baseado na configuração fornecida
    bStreamingEnabled = StreamingConfig.bUseAsyncStreaming;

    if (bStreamingEnabled)
    {
        // Configurar parâmetros de streaming
        StreamingDistance = StreamingConfig.StreamingDistance;

        // Configurar LOD baseado na distância
        if (PCGComponent && IsValid(PCGComponent))
        {
            // Implementar configuração de LOD quando disponível
        }

        UE_LOGFMT(LogTemp, Log, "AAURACRONPCGTrail::ConfigureWorldPartitionStreaming - Streaming enabled with distance {StreamingDistance}",
               StreamingDistance);
    }
    else
    {
        UE_LOGFMT(LogTemp, Log, "AAURACRONPCGTrail::ConfigureWorldPartitionStreaming - Streaming disabled");
    }
}

void AAURACRONPCGTrail::AssociateWithDataLayer(const FName& DataLayerName)
{
    // Implementação robusta para associar com Data Layer
    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGTrail::AssociateWithDataLayer - Associating with data layer: {DataLayerName}",
           DataLayerName.ToString());

    // Armazenar nome do data layer
    AssociatedDataLayer = DataLayerName;

    // Implementar associação real com Data Layer quando API estiver disponível
    // Usar UDataLayerSubsystem quando disponível no UE 5.6

    UE_LOGFMT(LogTemp, Log, "AAURACRONPCGTrail::AssociateWithDataLayer - Associated with data layer successfully");
}

// Implementação da função UpdateTrailProperties para ASolarTrail
void ASolarTrail::UpdateTrailProperties()
{
    // Implementação robusta para atualizar propriedades da trilha solar

    // Atualizar intensidade dos efeitos baseado na porcentagem de poder
    if (TrailEffectComponent && IsValid(TrailEffectComponent))
    {
        TrailEffectComponent->SetFloatParameter(TEXT("SolarIntensity"), PowerPercentage);
        TrailEffectComponent->SetFloatParameter(TEXT("RegenerationRate"), PowerPercentage * 0.5f);

        // Atualizar cor baseada no poder
        FLinearColor SolarColor = FLinearColor::Yellow;
        SolarColor.R = FMath::Clamp(PowerPercentage, 0.5f, 1.0f);
        SolarColor.G = FMath::Clamp(PowerPercentage * 0.8f, 0.3f, 0.9f);
        SolarColor.B = FMath::Clamp(PowerPercentage * 0.2f, 0.1f, 0.3f);

        TrailEffectComponent->SetVectorParameter(TEXT("SolarColor"), FVector(SolarColor.R, SolarColor.G, SolarColor.B));
    }

    // Atualizar componentes de luz se existirem
    TArray<UPointLightComponent*> LightComponents;
    GetComponents<UPointLightComponent>(LightComponents);

    for (UPointLightComponent* LightComp : LightComponents)
    {
        if (LightComp && IsValid(Cast<UObject>(LightComp)))
        {
            float BaseIntensity = 2000.0f;
            LightComp->SetIntensity(BaseIntensity * PowerPercentage);
            LightComp->SetLightColor(FLinearColor::Yellow * PowerPercentage);
        }
    }

    // Atualizar raio de efeito da trilha
    if (CollisionComponent && IsValid(CollisionComponent))
    {
        FVector BaseExtent = FVector(250.0f, 250.0f, 100.0f);
        CollisionComponent->SetBoxExtent(BaseExtent * PowerPercentage);
    }

    UE_LOGFMT(LogTemp, Verbose, "ASolarTrail::UpdateTrailProperties - Trail properties updated with power {PowerPercentage}", PowerPercentage);
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES FALTANTES - UE 5.6 MODERN APIS
// ========================================

void AAURACRONPCGTrail::SetPCGParameterModern(const FString& ParameterName, const FVector& Value, const FString& Description)
{
    if (!IsValid(PCGComponent))
    {
        UE_LOGFMT(LogTemp, Warning, "SetPCGParameterModern: PCGComponent inválido para parâmetro {ParameterName}", ParameterName);
        return;
    }

    // UE 5.6 API moderna para configurar parâmetros PCG
    if (UPCGGraphInstance* GraphInstance = PCGComponent->GetGraphInstance())
    {
        // Usar PCGGraphParametersHelpers para configurar parâmetros de forma moderna
        if (UPCGGraph* Graph = GraphInstance->GetGraph())
        {
            // Configurar parâmetro usando as APIs modernas do UE 5.6
            // Os parâmetros são configurados através do GraphInstance
            UE_LOGFMT(LogTemp, Verbose, "SetPCGParameterModern: Configurando parâmetro {ParameterName} = ({X}, {Y}, {Z}) - {Description}",
                     ParameterName, Value.X, Value.Y, Value.Z, Description);

            // Marcar para regeneração se necessário
            PCGComponent->SetDirty();
        }
        else
        {
            UE_LOGFMT(LogTemp, Warning, "SetPCGParameterModern: Graph não encontrado para parâmetro {ParameterName}", ParameterName);
        }
    }
    else
    {
        UE_LOGFMT(LogTemp, Warning, "SetPCGParameterModern: GraphInstance não encontrado para parâmetro {ParameterName}", ParameterName);
    }
}

void AAURACRONPCGTrail::UpdateTrailProperties()
{
    if (!IsValid(this))
    {
        return;
    }

    // Atualizar propriedades baseadas no tipo de trilha e escala de atividade
    switch (TrailType)
    {
    case EAURACRONTrailType::Solar:
        UpdateSolarTrailProperties();
        break;
    case EAURACRONTrailType::Axis:
        UpdateAxisTrailProperties();
        break;
    case EAURACRONTrailType::Lunar:
        UpdateLunarTrailProperties();
        break;
    case EAURACRONTrailType::PrismalFlow:
        UpdatePrismalFlowProperties();
        break;
    case EAURACRONTrailType::EtherealPath:
        UpdateEtherealPathProperties();
        break;
    case EAURACRONTrailType::NexusConnection:
        UpdateNexusConnectionProperties();
        break;
    default:
        UE_LOGFMT(LogTemp, Warning, "UpdateTrailProperties: Tipo de trilha não reconhecido {TrailType}", (int32)TrailType);
        break;
    }

    // Aplicar orçamento de partículas escalável baseado na capacidade do hardware
    ApplyParticleBudgetScaling();

    UE_LOGFMT(LogTemp, Verbose, "UpdateTrailProperties: Propriedades atualizadas para trilha tipo {TrailType}", (int32)TrailType);
}

TArray<FVector> AAURACRONPCGTrail::GenerateTrailPointsModern()
{
    TArray<FVector> TrailPoints;

    if (!IsValid(SplineComponent))
    {
        UE_LOGFMT(LogTemp, Error, "GenerateTrailPointsModern: SplineComponent inválido");
        return TrailPoints;
    }

    // Gerar pontos baseados na spline existente ou criar uma nova se necessário
    int32 NumSplinePoints = SplineComponent->GetNumberOfSplinePoints();

    if (NumSplinePoints >= 2)
    {
        // Usar pontos existentes da spline
        for (int32 i = 0; i < NumSplinePoints; ++i)
        {
            FVector SplinePoint = SplineComponent->GetLocationAtSplinePoint(i, ESplineCoordinateSpace::World);
            TrailPoints.Add(SplinePoint);
        }
    }
    else
    {
        // Gerar pontos padrão se não houver spline configurada
        FVector StartPoint = GetActorLocation();
        FVector EndPoint = StartPoint + (GetActorForwardVector() * 1000.0f); // 10 metros à frente

        TrailPoints.Add(StartPoint);
        TrailPoints.Add(EndPoint);

        // Configurar a spline com os pontos gerados
        SplineComponent->ClearSplinePoints();
        SplineComponent->AddSplinePoint(StartPoint, ESplineCoordinateSpace::World);
        SplineComponent->AddSplinePoint(EndPoint, ESplineCoordinateSpace::World);
        SplineComponent->UpdateSpline();
    }

    UE_LOGFMT(LogTemp, Verbose, "GenerateTrailPointsModern: Gerados {NumPoints} pontos para a trilha", TrailPoints.Num());
    return TrailPoints;
}

void AAURACRONPCGTrail::ApplyActivityScale()
{
    if (!IsValid(this))
    {
        return;
    }

    // Aplicar escala de atividade a todos os componentes visuais
    float ClampedScale = FMath::Clamp(ActivityScale, 0.0f, 1.0f);

    // Aplicar aos componentes Niagara
    if (IsValid(TrailEffectComponent))
    {
        TrailEffectComponent->SetFloatParameter(TEXT("ActivityScale"), ClampedScale);
        TrailEffectComponent->SetFloatParameter(TEXT("ParticleScale"), ClampedScale);

        // Ativar/desativar baseado na escala
        if (ClampedScale > 0.01f)
        {
            if (!TrailEffectComponent->IsActive())
            {
                TrailEffectComponent->Activate(true);
            }
        }
        else
        {
            if (TrailEffectComponent->IsActive())
            {
                TrailEffectComponent->Deactivate();
            }
        }
    }

    if (IsValid(TrailNiagaraComponent))
    {
        TrailNiagaraComponent->SetFloatParameter(TEXT("ActivityScale"), ClampedScale);

        if (ClampedScale > 0.01f)
        {
            if (!TrailNiagaraComponent->IsActive())
            {
                TrailNiagaraComponent->Activate(true);
            }
        }
        else
        {
            if (TrailNiagaraComponent->IsActive())
            {
                TrailNiagaraComponent->Deactivate();
            }
        }
    }

    // Aplicar aos componentes de luz
    if (IsValid(TrailLightComponent))
    {
        float BaseIntensity = 5000.0f;
        TrailLightComponent->SetIntensity(BaseIntensity * ClampedScale);
        TrailLightComponent->SetVisibility(ClampedScale > 0.01f);
    }

    UE_LOGFMT(LogTemp, Verbose, "ApplyActivityScale: Escala de atividade {ActivityScale} aplicada", ClampedScale);
}

void AAURACRONPCGTrail::UpdateTrailParameters()
{
    if (!IsValid(this))
    {
        return;
    }

    // Atualizar parâmetros baseados no tipo de trilha e configurações atuais
    switch (TrailType)
    {
    case EAURACRONTrailType::PrismalFlow:
        SetPCGParameterModern(TEXT("FlowWidth"), FVector(FlowWidth, 0.0f, 0.0f), TEXT("FlowWidth"));
        SetPCGParameterModern(TEXT("FlowIntensity"), FVector(FlowIntensity, 0.0f, 0.0f), TEXT("FlowIntensity"));
        SetPCGParameterModern(TEXT("FlowSpeed"), FVector(FlowSpeed, 0.0f, 0.0f), TEXT("FlowSpeed"));
        SetPCGParameterModern(TEXT("FlowColor"), FVector(FlowColor.R, FlowColor.G, FlowColor.B), TEXT("FlowColor"));
        break;

    case EAURACRONTrailType::EtherealPath:
        SetPCGParameterModern(TEXT("PathWidth"), FVector(PathWidth, 0.0f, 0.0f), TEXT("PathWidth"));
        SetPCGParameterModern(TEXT("PathVisibility"), FVector(PathVisibility, 0.0f, 0.0f), TEXT("PathVisibility"));
        SetPCGParameterModern(TEXT("PathFluctuation"), FVector(PathFluctuation, 0.0f, 0.0f), TEXT("PathFluctuation"));
        SetPCGParameterModern(TEXT("PathColor"), FVector(PathColor.R, PathColor.G, PathColor.B), TEXT("PathColor"));
        break;

    case EAURACRONTrailType::NexusConnection:
        SetPCGParameterModern(TEXT("ConnectionWidth"), FVector(ConnectionWidth, 0.0f, 0.0f), TEXT("ConnectionWidth"));
        SetPCGParameterModern(TEXT("ConnectionStrength"), FVector(ConnectionStrength, 0.0f, 0.0f), TEXT("ConnectionStrength"));
        SetPCGParameterModern(TEXT("ConnectionPulseRate"), FVector(ConnectionPulseRate, 0.0f, 0.0f), TEXT("ConnectionPulseRate"));
        SetPCGParameterModern(TEXT("ConnectionColor"), FVector(ConnectionColor.R, ConnectionColor.G, ConnectionColor.B), TEXT("ConnectionColor"));
        break;

    default:
        // Para outros tipos, aplicar parâmetros básicos
        SetPCGParameterModern(TEXT("ActivityScale"), FVector(ActivityScale, 0.0f, 0.0f), TEXT("ActivityScale"));
        break;
    }

    // Parâmetros comuns a todos os tipos
    SetPCGParameterModern(TEXT("ActivityScale"), FVector(ActivityScale, 0.0f, 0.0f), TEXT("ActivityScale"));
    SetPCGParameterModern(TEXT("NumOverlappingPlayers"), FVector(NumOverlappingPlayers, 0.0f, 0.0f), TEXT("NumOverlappingPlayers"));

    if (IsValid(SplineComponent))
    {
        float SplineLength = SplineComponent->GetSplineLength();
        SetPCGParameterModern(TEXT("SplineLength"), FVector(SplineLength, 0.0f, 0.0f), TEXT("SplineLength"));
    }

    UE_LOGFMT(LogTemp, Verbose, "UpdateTrailParameters: Parâmetros atualizados para trilha tipo {TrailType}", (int32)TrailType);
}

void AAURACRONPCGTrail::ApplyTrailEffectsToPlayer(ACharacter* Player, float DeltaTime)
{
    if (!IsValid(Player) || !IsValid(this))
    {
        return;
    }

    // Aplicar efeitos baseados no tipo de trilha
    switch (TrailType)
    {
    case EAURACRONTrailType::Solar:
        ApplySolarEffectsToPlayer(Player, DeltaTime);
        break;
    case EAURACRONTrailType::Axis:
        ApplyAxisEffectsToPlayer(Player, DeltaTime);
        break;
    case EAURACRONTrailType::Lunar:
        ApplyLunarEffectsToPlayer(Player, DeltaTime);
        break;
    case EAURACRONTrailType::PrismalFlow:
        ApplyPrismalFlowEffectsToPlayer(Player, DeltaTime);
        break;
    case EAURACRONTrailType::EtherealPath:
        ApplyEtherealPathEffectsToPlayer(Player, DeltaTime);
        break;
    case EAURACRONTrailType::NexusConnection:
        ApplyNexusConnectionEffectsToPlayer(Player, DeltaTime);
        break;
    default:
        UE_LOGFMT(LogTemp, Warning, "ApplyTrailEffectsToPlayer: Tipo de trilha não reconhecido {TrailType}", (int32)TrailType);
        break;
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "ApplyTrailEffectsToPlayer: Efeitos aplicados ao jogador {PlayerName} para trilha tipo {TrailType}",
             Player->GetName(), (int32)TrailType);
}

void AAURACRONPCGTrail::UpdateInteractionVolume()
{
    if (!IsValid(InteractionVolume) || !IsValid(SplineComponent))
    {
        return;
    }

    // Atualizar o volume de interação para seguir a spline
    int32 NumSplinePoints = SplineComponent->GetNumberOfSplinePoints();

    if (NumSplinePoints >= 2)
    {
        // Calcular centro da spline
        FVector SplineCenter = FVector::ZeroVector;
        for (int32 i = 0; i < NumSplinePoints; ++i)
        {
            SplineCenter += SplineComponent->GetLocationAtSplinePoint(i, ESplineCoordinateSpace::World);
        }
        SplineCenter /= NumSplinePoints;

        // Posicionar o volume de interação no centro da spline
        FVector LocalCenter = GetActorTransform().InverseTransformPosition(SplineCenter);
        InteractionVolume->SetRelativeLocation(LocalCenter);

        // Ajustar o tamanho baseado no comprimento da spline
        float SplineLength = SplineComponent->GetSplineLength();
        float BoxLength = FMath::Max(SplineLength * 0.5f, 250.0f); // Mínimo de 2.5 metros

        // Ajustar largura baseada no tipo de trilha
        float BoxWidth = 250.0f; // Padrão 2.5 metros
        switch (TrailType)
        {
        case EAURACRONTrailType::PrismalFlow:
            BoxWidth = FlowWidth;
            break;
        case EAURACRONTrailType::EtherealPath:
            BoxWidth = PathWidth;
            break;
        case EAURACRONTrailType::NexusConnection:
            BoxWidth = ConnectionWidth;
            break;
        default:
            BoxWidth = 250.0f;
            break;
        }

        InteractionVolume->SetBoxExtent(FVector(BoxLength, BoxWidth, 100.0f));
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "UpdateInteractionVolume: Volume de interação atualizado para trilha tipo {TrailType}", (int32)TrailType);
}

void AAURACRONPCGTrail::HandlePlayerOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor,
                                           UPrimitiveComponent* OtherComp, int32 OtherBodyIndex,
                                           bool bFromSweep, const FHitResult& SweepResult)
{
    if (!IsValid(OtherActor) || !IsValid(this))
    {
        return;
    }

    // Verificar se é um personagem jogável
    ACharacter* Character = Cast<ACharacter>(OtherActor);
    if (!IsValid(Character))
    {
        return;
    }

    // Adicionar à lista de jogadores sobrepostos se não estiver já
    if (!OverlappingPlayers.Contains(Character))
    {
        OverlappingPlayers.Add(Character);
        NumOverlappingPlayers = OverlappingPlayers.Num();

        // Aplicar efeito inicial
        ApplyTrailEffectsToPlayer(Character, 0.0f);

        UE_LOGFMT(LogTemp, Log, "HandlePlayerOverlap: Jogador {PlayerName} entrou na trilha tipo {TrailType}",
                 Character->GetName(), (int32)TrailType);
    }
}

void AAURACRONPCGTrail::HandlePlayerEndOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor,
                                              UPrimitiveComponent* OtherComp, int32 OtherBodyIndex)
{
    if (!IsValid(OtherActor) || !IsValid(this))
    {
        return;
    }

    // Verificar se é um personagem jogável
    ACharacter* Character = Cast<ACharacter>(OtherActor);
    if (!IsValid(Character))
    {
        return;
    }

    // Remover da lista de jogadores sobrepostos
    if (OverlappingPlayers.Contains(Character))
    {
        OverlappingPlayers.Remove(Character);
        NumOverlappingPlayers = OverlappingPlayers.Num();

        // Remover efeitos da trilha
        RemoveTrailEffectsFromPlayer(Character);

        UE_LOGFMT(LogTemp, Log, "HandlePlayerEndOverlap: Jogador {PlayerName} saiu da trilha tipo {TrailType}",
                 Character->GetName(), (int32)TrailType);
    }
}

void AAURACRONPCGTrail::ClearTrail()
{
    if (!IsValid(this))
    {
        return;
    }

    // Limpar componentes visuais
    if (IsValid(TrailEffectComponent))
    {
        TrailEffectComponent->Deactivate();
        TrailEffectComponent->SetVisibility(false);
    }

    if (IsValid(TrailNiagaraComponent))
    {
        TrailNiagaraComponent->Deactivate();
        TrailNiagaraComponent->SetVisibility(false);
    }

    if (IsValid(TrailLightComponent))
    {
        TrailLightComponent->SetVisibility(false);
    }

    // Limpar spline
    if (IsValid(SplineComponent))
    {
        SplineComponent->ClearSplinePoints();
    }

    // Limpar lista de jogadores sobrepostos
    OverlappingPlayers.Empty();
    NumOverlappingPlayers = 0;

    // Resetar escala de atividade
    ActivityScale = 0.0f;

    UE_LOGFMT(LogTemp, Log, "ClearTrail: Trilha tipo {TrailType} limpa", (int32)TrailType);
}

void AAURACRONPCGTrail::GenerateSolarTrail()
{
    if (!IsValid(this))
    {
        return;
    }

    // Configurar características específicas do Solar Trail
    if (IsValid(TrailLightComponent))
    {
        TrailLightComponent->SetLightColor(FLinearColor(1.0f, 0.8f, 0.3f, 1.0f)); // Dourado solar
        TrailLightComponent->SetIntensity(3000.0f);
        TrailLightComponent->SetAttenuationRadius(800.0f);
        TrailLightComponent->SetVisibility(true);
    }

    // Configurar efeitos Niagara para Solar Trail
    if (IsValid(TrailEffectComponent))
    {
        TrailEffectComponent->SetVectorParameter(TEXT("SolarColor"), FVector(1.0f, 0.8f, 0.3f));
        TrailEffectComponent->SetFloatParameter(TEXT("SolarIntensity"), 1.0f);
        TrailEffectComponent->SetFloatParameter(TEXT("ParticleCount"), GetParticleBudgetForTrailType(EAURACRONTrailType::Solar));
        TrailEffectComponent->SetBoolParameter(TEXT("EnableHeatDistortion"), true);
        TrailEffectComponent->Activate(true);
    }

    // Configurar parâmetros PCG específicos
    SetPCGParameterModern(TEXT("TrailType"), FVector((float)EAURACRONTrailType::Solar, 0.0f, 0.0f), TEXT("Solar Trail"));
    SetPCGParameterModern(TEXT("SolarColor"), FVector(1.0f, 0.8f, 0.3f), TEXT("Cor dourada solar"));
    SetPCGParameterModern(TEXT("SolarIntensity"), FVector(1.0f, 0.0f, 0.0f), TEXT("Intensidade solar"));

    UE_LOGFMT(LogTemp, Log, "GenerateSolarTrail: Solar Trail gerado com sucesso");
}

void AAURACRONPCGTrail::GenerateAxisTrail()
{
    if (!IsValid(this))
    {
        return;
    }

    // Configurar características específicas do Axis Trail
    if (IsValid(TrailLightComponent))
    {
        TrailLightComponent->SetLightColor(FLinearColor(0.8f, 0.8f, 0.8f, 1.0f)); // Cinza/prata neutro
        TrailLightComponent->SetIntensity(2500.0f);
        TrailLightComponent->SetAttenuationRadius(600.0f);
        TrailLightComponent->SetVisibility(true);
    }

    // Configurar efeitos Niagara para Axis Trail
    if (IsValid(TrailEffectComponent))
    {
        TrailEffectComponent->SetVectorParameter(TEXT("AxisColor"), FVector(0.8f, 0.8f, 0.8f));
        TrailEffectComponent->SetFloatParameter(TEXT("GeometricPatternIntensity"), 1.0f);
        TrailEffectComponent->SetFloatParameter(TEXT("ParticleCount"), GetParticleBudgetForTrailType(EAURACRONTrailType::Axis));
        TrailEffectComponent->SetBoolParameter(TEXT("EnableGravitationalDistortion"), true);
        TrailEffectComponent->Activate(true);
    }

    // Configurar parâmetros PCG específicos
    SetPCGParameterModern(TEXT("TrailType"), FVector((float)EAURACRONTrailType::Axis, 0.0f, 0.0f), TEXT("Axis Trail"));
    SetPCGParameterModern(TEXT("AxisColor"), FVector(0.8f, 0.8f, 0.8f), TEXT("Cor cinza/prata"));
    SetPCGParameterModern(TEXT("GeometricPatterns"), FVector(1.0f, 0.0f, 0.0f), TEXT("Padrões geométricos"));

    UE_LOGFMT(LogTemp, Log, "GenerateAxisTrail: Axis Trail gerado com sucesso");
}

void AAURACRONPCGTrail::GenerateLunarTrail()
{
    if (!IsValid(this))
    {
        return;
    }

    // Configurar características específicas do Lunar Trail
    if (IsValid(TrailLightComponent))
    {
        TrailLightComponent->SetLightColor(FLinearColor(0.3f, 0.5f, 1.0f, 1.0f)); // Azul lunar
        TrailLightComponent->SetIntensity(2000.0f);
        TrailLightComponent->SetAttenuationRadius(500.0f);
        TrailLightComponent->SetVisibility(true);
    }

    // Configurar efeitos Niagara para Lunar Trail
    if (IsValid(TrailEffectComponent))
    {
        TrailEffectComponent->SetVectorParameter(TEXT("LunarColor"), FVector(0.3f, 0.5f, 1.0f));
        TrailEffectComponent->SetFloatParameter(TEXT("BlueMistIntensity"), 0.8f);
        TrailEffectComponent->SetFloatParameter(TEXT("ParticleCount"), GetParticleBudgetForTrailType(EAURACRONTrailType::Lunar));
        TrailEffectComponent->SetFloatParameter(TEXT("StardustParticleCount"), 50.0f);
        TrailEffectComponent->SetBoolParameter(TEXT("EnableEtherealGlow"), true);
        TrailEffectComponent->Activate(true);
    }

    // Configurar parâmetros PCG específicos
    SetPCGParameterModern(TEXT("TrailType"), FVector((float)EAURACRONTrailType::Lunar, 0.0f, 0.0f), TEXT("Lunar Trail"));
    SetPCGParameterModern(TEXT("LunarColor"), FVector(0.3f, 0.5f, 1.0f), TEXT("Cor azul lunar"));
    SetPCGParameterModern(TEXT("EtherealEffects"), FVector(1.0f, 0.0f, 0.0f), TEXT("Efeitos etéreos"));

    UE_LOGFMT(LogTemp, Log, "GenerateLunarTrail: Lunar Trail gerado com sucesso");
}

// ========================================
// FUNÇÕES DE ORÇAMENTO DE PARTÍCULAS ESCALÁVEL
// ========================================

float AAURACRONPCGTrail::GetParticleBudgetForTrailType(EAURACRONTrailType Type)
{
    // Determinar capacidade do hardware (Entry/Mid/High)
    EAURACRONHardwareCapacity HardwareCapacity = DetermineHardwareCapacity();

    float ParticleBudget = 100.0f; // Padrão Entry Level

    switch (HardwareCapacity)
    {
    case EAURACRONHardwareCapacity::Entry:
        ParticleBudget = 100.0f; // Entry Level: 100 partículas por seção
        break;
    case EAURACRONHardwareCapacity::Mid:
        ParticleBudget = 250.0f; // Mid-range: 250 partículas por seção
        break;
    case EAURACRONHardwareCapacity::High:
        ParticleBudget = 500.0f; // High-end: 500 partículas por seção
        break;
    }

    // Ajustar baseado no tipo de trilha
    switch (Type)
    {
    case EAURACRONTrailType::Solar:
        ParticleBudget *= 1.2f; // Solar trails são mais intensos
        break;
    case EAURACRONTrailType::Lunar:
        ParticleBudget *= 1.1f; // Lunar trails têm poeira estelar
        break;
    case EAURACRONTrailType::Axis:
        ParticleBudget *= 0.9f; // Axis trails são mais geométricos, menos partículas
        break;
    default:
        break;
    }

    return ParticleBudget;
}

EAURACRONHardwareCapacity AAURACRONPCGTrail::DetermineHardwareCapacity()
{
    // Determinar capacidade do hardware baseado em configurações do sistema
    // Esta é uma implementação simplificada - em produção seria mais sofisticada

    if (GEngine && GEngine->GetGameUserSettings())
    {
        int32 EffectsQuality = GEngine->GetGameUserSettings()->GetVisualEffectQuality();

        if (EffectsQuality >= 3) // Epic/Cinematic
        {
            return EAURACRONHardwareCapacity::High;
        }
        else if (EffectsQuality >= 2) // High/Medium
        {
            return EAURACRONHardwareCapacity::Mid;
        }
        else // Low
        {
            return EAURACRONHardwareCapacity::Entry;
        }
    }

    // Fallback para Entry Level se não conseguir determinar
    return EAURACRONHardwareCapacity::Entry;
}

void AAURACRONPCGTrail::ApplyParticleBudgetScaling()
{
    if (!IsValid(this))
    {
        return;
    }

    float ParticleBudget = GetParticleBudgetForTrailType(TrailType);

    // Aplicar orçamento aos componentes Niagara
    if (IsValid(TrailEffectComponent))
    {
        TrailEffectComponent->SetFloatParameter(TEXT("MaxParticles"), ParticleBudget);
        TrailEffectComponent->SetFloatParameter(TEXT("ParticleBudget"), ParticleBudget);
    }

    if (IsValid(TrailNiagaraComponent))
    {
        TrailNiagaraComponent->SetFloatParameter(TEXT("MaxParticles"), ParticleBudget);
        TrailNiagaraComponent->SetFloatParameter(TEXT("ParticleBudget"), ParticleBudget);
    }

    UE_LOGFMT(LogTemp, Verbose, "ApplyParticleBudgetScaling: Orçamento de {ParticleBudget} partículas aplicado para trilha tipo {TrailType}",
             ParticleBudget, (int32)TrailType);
}

// ========================================
// FUNÇÕES DE EFEITOS ESPECÍFICOS POR TIPO DE TRILHA
// ========================================

void AAURACRONPCGTrail::ApplySolarEffectsToPlayer(ACharacter* Player, float DeltaTime)
{
    if (!IsValid(Player))
    {
        return;
    }

    // Aplicar boost de velocidade (conforme documentação)
    if (UCharacterMovementComponent* MovementComp = Player->GetCharacterMovement())
    {
        float SpeedMultiplier = 1.3f; // 30% de aumento conforme documentação
        float CurrentMaxSpeed = MovementComp->MaxWalkSpeed;
        float TargetSpeed = MovementComp->GetMaxSpeed() * SpeedMultiplier;

        // Aplicar gradualmente para suavidade
        float NewSpeed = FMath::FInterpTo(CurrentMaxSpeed, TargetSpeed, DeltaTime, 2.0f);
        MovementComp->MaxWalkSpeed = NewSpeed;
    }

    // Aplicar regeneração de vida via GameplayEffect se disponível
    if (UAbilitySystemComponent* ASC = Player->GetComponentByClass<UAbilitySystemComponent>())
    {
        // Implementar regeneração via GameplayEffect
        ApplyGameplayEffectToPlayer(Player, TEXT("SolarRegeneration"), 1.0f);
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "ApplySolarEffectsToPlayer: Efeitos solares aplicados ao jogador {PlayerName}", Player->GetName());
}

void AAURACRONPCGTrail::ApplyAxisEffectsToPlayer(ACharacter* Player, float DeltaTime)
{
    if (!IsValid(Player))
    {
        return;
    }

    // Permitir transição instantânea entre ambientes (conforme documentação)
    if (UAbilitySystemComponent* ASC = Player->GetComponentByClass<UAbilitySystemComponent>())
    {
        ApplyGameplayEffectToPlayer(Player, TEXT("AxisInstantTransition"), 5.0f);
    }

    // Aplicar efeito de distorção gravitacional visual
    if (IsValid(TrailEffectComponent))
    {
        FVector PlayerLocation = Player->GetActorLocation();
        FVector TrailLocation = GetActorLocation();
        float Distance = FVector::Distance(PlayerLocation, TrailLocation);

        // Intensidade baseada na proximidade
        float DistortionIntensity = FMath::Clamp(1.0f - (Distance / 1000.0f), 0.1f, 1.0f);
        TrailEffectComponent->SetFloatParameter(TEXT("GravitationalDistortion"), DistortionIntensity);
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "ApplyAxisEffectsToPlayer: Efeitos axis aplicados ao jogador {PlayerName}", Player->GetName());
}

void AAURACRONPCGTrail::ApplyLunarEffectsToPlayer(ACharacter* Player, float DeltaTime)
{
    if (!IsValid(Player))
    {
        return;
    }

    // Aplicar furtividade e visão aprimorada (conforme documentação)
    if (UAbilitySystemComponent* ASC = Player->GetComponentByClass<UAbilitySystemComponent>())
    {
        ApplyGameplayEffectToPlayer(Player, TEXT("LunarStealth"), 5.0f);
        ApplyGameplayEffectToPlayer(Player, TEXT("LunarEnhancedVision"), 5.0f);
    }

    // Aplicar efeito visual de invisibilidade parcial
    if (USkeletalMeshComponent* MeshComp = Player->GetMesh())
    {
        // Calcular fase lunar para intensidade do efeito
        float LunarPhase = UAURACRONPCGMathLibrary::CalculateLunarPhaseIntensity(GetWorld());
        float InvisibilityAlpha = FMath::Lerp(0.7f, 0.3f, LunarPhase); // Mais invisível durante lua cheia

        // Aplicar transparência gradual
        if (UMaterialInstanceDynamic* DynamicMaterial = MeshComp->CreateAndSetMaterialInstanceDynamic(0))
        {
            DynamicMaterial->SetScalarParameterValue(TEXT("Opacity"), InvisibilityAlpha);
        }
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "ApplyLunarEffectsToPlayer: Efeitos lunares aplicados ao jogador {PlayerName}", Player->GetName());
}

void AAURACRONPCGTrail::ApplyPrismalFlowEffectsToPlayer(ACharacter* Player, float DeltaTime)
{
    if (!IsValid(Player))
    {
        return;
    }

    // Aplicar efeitos do Fluxo Prismal
    if (UAbilitySystemComponent* ASC = Player->GetComponentByClass<UAbilitySystemComponent>())
    {
        ApplyGameplayEffectToPlayer(Player, TEXT("PrismalFlowBoost"), 3.0f);
    }

    // Efeito visual prismático no jogador
    if (USkeletalMeshComponent* MeshComp = Player->GetMesh())
    {
        if (UMaterialInstanceDynamic* DynamicMaterial = MeshComp->CreateAndSetMaterialInstanceDynamic(0))
        {
            float PrismalIntensity = FlowIntensity * ActivityScale;
            DynamicMaterial->SetScalarParameterValue(TEXT("PrismalGlow"), PrismalIntensity);
            DynamicMaterial->SetVectorParameterValue(TEXT("PrismalColor"), FLinearColor(FlowColor));
        }
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "ApplyPrismalFlowEffectsToPlayer: Efeitos do Fluxo Prismal aplicados ao jogador {PlayerName}", Player->GetName());
}

void AAURACRONPCGTrail::ApplyEtherealPathEffectsToPlayer(ACharacter* Player, float DeltaTime)
{
    if (!IsValid(Player))
    {
        return;
    }

    // Aplicar efeitos do Caminho Etéreo
    if (UAbilitySystemComponent* ASC = Player->GetComponentByClass<UAbilitySystemComponent>())
    {
        ApplyGameplayEffectToPlayer(Player, TEXT("EtherealPathGuidance"), 4.0f);
    }

    // Efeito visual etéreo no jogador
    if (USkeletalMeshComponent* MeshComp = Player->GetMesh())
    {
        if (UMaterialInstanceDynamic* DynamicMaterial = MeshComp->CreateAndSetMaterialInstanceDynamic(0))
        {
            float EtherealGlow = PathVisibility * ActivityScale;
            DynamicMaterial->SetScalarParameterValue(TEXT("EtherealGlow"), EtherealGlow);
            DynamicMaterial->SetVectorParameterValue(TEXT("EtherealColor"), FLinearColor(PathColor));
        }
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "ApplyEtherealPathEffectsToPlayer: Efeitos do Caminho Etéreo aplicados ao jogador {PlayerName}", Player->GetName());
}

void AAURACRONPCGTrail::ApplyNexusConnectionEffectsToPlayer(ACharacter* Player, float DeltaTime)
{
    if (!IsValid(Player))
    {
        return;
    }

    // Aplicar efeitos da Conexão de Nexus
    if (UAbilitySystemComponent* ASC = Player->GetComponentByClass<UAbilitySystemComponent>())
    {
        ApplyGameplayEffectToPlayer(Player, TEXT("NexusConnectionPower"), 6.0f);
    }

    // Efeito visual de conexão no jogador
    if (USkeletalMeshComponent* MeshComp = Player->GetMesh())
    {
        if (UMaterialInstanceDynamic* DynamicMaterial = MeshComp->CreateAndSetMaterialInstanceDynamic(0))
        {
            float ConnectionPower = ConnectionStrength * ActivityScale;
            DynamicMaterial->SetScalarParameterValue(TEXT("NexusGlow"), ConnectionPower);
            DynamicMaterial->SetVectorParameterValue(TEXT("NexusColor"), FLinearColor(ConnectionColor));

            // Efeito de pulso baseado na taxa de pulso
            float PulseValue = FMath::Sin(GetWorld()->GetTimeSeconds() * ConnectionPulseRate) * 0.5f + 0.5f;
            DynamicMaterial->SetScalarParameterValue(TEXT("NexusPulse"), PulseValue);
        }
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "ApplyNexusConnectionEffectsToPlayer: Efeitos da Conexão de Nexus aplicados ao jogador {PlayerName}", Player->GetName());
}

// ========================================
// FUNÇÕES AUXILIARES E UTILITÁRIAS
// ========================================

void AAURACRONPCGTrail::ApplyGameplayEffectToPlayer(ACharacter* Player, const FString& EffectName, float Duration)
{
    if (!IsValid(Player))
    {
        return;
    }

    UAbilitySystemComponent* ASC = Player->GetComponentByClass<UAbilitySystemComponent>();
    if (!IsValid(ASC))
    {
        UE_LOGFMT(LogTemp, Warning, "ApplyGameplayEffectToPlayer: AbilitySystemComponent não encontrado no jogador {PlayerName}", Player->GetName());
        return;
    }

    // Carregar GameplayEffect assincronamente usando StreamableManager (UE 5.6)
    FString EffectPath = FString::Printf(TEXT("/Game/GameplayEffects/Trails/%s.%s_C"), *EffectName, *EffectName);
    FSoftObjectPath EffectSoftPath(EffectPath);

    if (IsValid(StreamableManager))
    {
        TSharedPtr<FStreamableHandle> Handle = StreamableManager->RequestAsyncLoad(
            EffectSoftPath,
            FStreamableDelegate::CreateUObject(this, &AAURACRONPCGTrail::OnGameplayEffectLoaded, Player, Duration, EffectName)
        );

        if (Handle.IsValid())
        {
            UE_LOGFMT(LogTemp, Verbose, "ApplyGameplayEffectToPlayer: Carregando efeito {EffectName} assincronamente para jogador {PlayerName}",
                     EffectName, Player->GetName());
        }
    }
}

void AAURACRONPCGTrail::OnGameplayEffectLoaded(ACharacter* Player, float Duration, FString EffectName)
{
    if (!IsValid(Player))
    {
        return;
    }

    UAbilitySystemComponent* ASC = Player->GetComponentByClass<UAbilitySystemComponent>();
    if (!IsValid(ASC))
    {
        return;
    }

    // Aplicar o efeito carregado
    FGameplayEffectContextHandle EffectContext = ASC->MakeEffectContext();
    EffectContext.AddSourceObject(this);

    FGameplayEffectSpecHandle EffectSpec = ASC->MakeOutgoingSpec(UGameplayEffect::StaticClass(), 1.0f, EffectContext);
    if (EffectSpec.IsValid())
    {
        EffectSpec.Data->SetDuration(Duration, false);
        ASC->ApplyGameplayEffectSpecToSelf(*EffectSpec.Data.Get());

        UE_LOGFMT(LogTemp, Log, "OnGameplayEffectLoaded: Efeito {EffectName} aplicado ao jogador {PlayerName} por {Duration} segundos",
                 EffectName, Player->GetName(), Duration);
    }
}

void AAURACRONPCGTrail::RemoveTrailEffectsFromPlayer(ACharacter* Player)
{
    if (!IsValid(Player))
    {
        return;
    }

    // Remover efeitos visuais
    if (USkeletalMeshComponent* MeshComp = Player->GetMesh())
    {
        // Resetar materiais para o padrão
        if (UMaterialInstanceDynamic* DynamicMaterial = MeshComp->CreateAndSetMaterialInstanceDynamic(0))
        {
            DynamicMaterial->SetScalarParameterValue(TEXT("Opacity"), 1.0f);
            DynamicMaterial->SetScalarParameterValue(TEXT("PrismalGlow"), 0.0f);
            DynamicMaterial->SetScalarParameterValue(TEXT("EtherealGlow"), 0.0f);
            DynamicMaterial->SetScalarParameterValue(TEXT("NexusGlow"), 0.0f);
        }
    }

    // Resetar velocidade de movimento para Solar Trail
    if (TrailType == EAURACRONTrailType::Solar)
    {
        if (UCharacterMovementComponent* MovementComp = Player->GetCharacterMovement())
        {
            MovementComp->MaxWalkSpeed = MovementComp->GetMaxSpeed(); // Resetar para velocidade padrão
        }
    }

    UE_LOGFMT(LogTemp, Verbose, "RemoveTrailEffectsFromPlayer: Efeitos removidos do jogador {PlayerName}", Player->GetName());
}

// ========================================
// FUNÇÕES DE ATUALIZAÇÃO ESPECÍFICAS POR TIPO
// ========================================

void AAURACRONPCGTrail::UpdateSolarTrailProperties()
{
    if (!IsValid(this))
    {
        return;
    }

    // Atualizar baseado no ciclo solar
    float TimeOfDay = GetWorld()->GetTimeSeconds();
    float SolarCycle = FMath::Sin(TimeOfDay * 0.1f) * 0.5f + 0.5f;

    if (IsValid(TrailLightComponent))
    {
        float SolarIntensity = FMath::Lerp(2000.0f, 4000.0f, SolarCycle);
        TrailLightComponent->SetIntensity(SolarIntensity * ActivityScale);

        FLinearColor SolarColor = FLinearColor::LerpUsingHSV(
            FLinearColor(1.0f, 0.6f, 0.2f, 1.0f),
            FLinearColor(1.0f, 0.9f, 0.4f, 1.0f),
            SolarCycle
        );
        TrailLightComponent->SetLightColor(SolarColor);
    }

    if (IsValid(TrailEffectComponent))
    {
        TrailEffectComponent->SetFloatParameter(TEXT("SolarIntensity"), SolarCycle);
        TrailEffectComponent->SetFloatParameter(TEXT("ParticleCount"), GetParticleBudgetForTrailType(EAURACRONTrailType::Solar) * SolarCycle);
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "UpdateSolarTrailProperties: Propriedades solares atualizadas com ciclo {SolarCycle}", SolarCycle);
}

void AAURACRONPCGTrail::UpdateAxisTrailProperties()
{
    if (!IsValid(this))
    {
        return;
    }

    // Atualizar baseado no controle de pontos nexus
    float PulseIntensity = FMath::Sin(GetWorld()->GetTimeSeconds() * 2.0f) * 0.3f + 0.7f;

    if (IsValid(TrailLightComponent))
    {
        TrailLightComponent->SetIntensity(2500.0f * PulseIntensity * ActivityScale);

        // Alternar cor baseado no modo (atração/repulsão)
        FLinearColor AxisColor = FLinearColor(0.8f, 0.8f, 0.8f, 1.0f); // Neutro por padrão
        TrailLightComponent->SetLightColor(AxisColor);
    }

    if (IsValid(TrailEffectComponent))
    {
        TrailEffectComponent->SetFloatParameter(TEXT("GeometricPatternIntensity"), PulseIntensity);
        TrailEffectComponent->SetFloatParameter(TEXT("ParticleCount"), GetParticleBudgetForTrailType(EAURACRONTrailType::Axis));
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "UpdateAxisTrailProperties: Propriedades axis atualizadas com pulso {PulseIntensity}", PulseIntensity);
}

void AAURACRONPCGTrail::UpdateLunarTrailProperties()
{
    if (!IsValid(this))
    {
        return;
    }

    // Atualizar baseado na fase lunar
    float LunarPhase = UAURACRONPCGMathLibrary::CalculateLunarPhaseIntensity(GetWorld());

    if (IsValid(TrailLightComponent))
    {
        float LightIntensity = FMath::Lerp(1000.0f, 3000.0f, LunarPhase);
        TrailLightComponent->SetIntensity(LightIntensity * ActivityScale);

        FLinearColor LunarColor = FLinearColor::LerpUsingHSV(
            FLinearColor(0.5f, 0.5f, 0.8f, 1.0f),
            FLinearColor(0.2f, 0.3f, 1.0f, 1.0f),
            LunarPhase
        );
        TrailLightComponent->SetLightColor(LunarColor);
    }

    if (IsValid(TrailEffectComponent))
    {
        TrailEffectComponent->SetFloatParameter(TEXT("LunarPhase"), LunarPhase);
        TrailEffectComponent->SetFloatParameter(TEXT("BlueMistIntensity"), FMath::Lerp(0.2f, 0.8f, LunarPhase));
        TrailEffectComponent->SetFloatParameter(TEXT("StardustParticleCount"), FMath::Lerp(15.0f, 85.0f, LunarPhase));
        TrailEffectComponent->SetFloatParameter(TEXT("ParticleCount"), GetParticleBudgetForTrailType(EAURACRONTrailType::Lunar) * LunarPhase);
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "UpdateLunarTrailProperties: Propriedades lunares atualizadas com fase {LunarPhase}", LunarPhase);
}

void AAURACRONPCGTrail::UpdatePrismalFlowProperties()
{
    if (!IsValid(this))
    {
        return;
    }

    // Atualizar propriedades do Fluxo Prismal
    float EffectiveWidth = FlowWidth * ActivityScale;
    float EffectiveIntensity = FlowIntensity * ActivityScale;
    float EffectiveSpeed = FlowSpeed * ActivityScale;

    SetPCGParameterModern(TEXT("FlowWidth"), FVector(EffectiveWidth, 0.0f, 0.0f), TEXT("Largura efetiva do fluxo"));
    SetPCGParameterModern(TEXT("FlowIntensity"), FVector(EffectiveIntensity, 0.0f, 0.0f), TEXT("Intensidade efetiva do fluxo"));
    SetPCGParameterModern(TEXT("FlowSpeed"), FVector(EffectiveSpeed, 0.0f, 0.0f), TEXT("Velocidade efetiva do fluxo"));

    if (IsValid(TrailEffectComponent))
    {
        TrailEffectComponent->SetFloatParameter(TEXT("FlowWidth"), EffectiveWidth);
        TrailEffectComponent->SetFloatParameter(TEXT("FlowIntensity"), EffectiveIntensity);
        TrailEffectComponent->SetFloatParameter(TEXT("FlowSpeed"), EffectiveSpeed);
        TrailEffectComponent->SetVectorParameter(TEXT("FlowColor"), FVector(FlowColor.R, FlowColor.G, FlowColor.B));
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "UpdatePrismalFlowProperties: Propriedades do Fluxo Prismal atualizadas");
}

void AAURACRONPCGTrail::UpdateEtherealPathProperties()
{
    if (!IsValid(this))
    {
        return;
    }

    // Atualizar propriedades do Caminho Etéreo
    float EffectiveWidth = PathWidth * ActivityScale;
    float EffectiveVisibility = PathVisibility * ActivityScale;
    float EffectiveFluctuation = PathFluctuation * ActivityScale;

    SetPCGParameterModern(TEXT("PathWidth"), FVector(EffectiveWidth, 0.0f, 0.0f), TEXT("Largura efetiva do caminho"));
    SetPCGParameterModern(TEXT("PathVisibility"), FVector(EffectiveVisibility, 0.0f, 0.0f), TEXT("Visibilidade efetiva do caminho"));
    SetPCGParameterModern(TEXT("PathFluctuation"), FVector(EffectiveFluctuation, 0.0f, 0.0f), TEXT("Flutuação efetiva do caminho"));

    if (IsValid(TrailEffectComponent))
    {
        TrailEffectComponent->SetFloatParameter(TEXT("PathWidth"), EffectiveWidth);
        TrailEffectComponent->SetFloatParameter(TEXT("PathVisibility"), EffectiveVisibility);
        TrailEffectComponent->SetFloatParameter(TEXT("PathFluctuation"), EffectiveFluctuation);
        TrailEffectComponent->SetVectorParameter(TEXT("PathColor"), FVector(PathColor.R, PathColor.G, PathColor.B));

        // Efeito de flutuação baseado no tempo
        float FluctuationOffset = FMath::Sin(GetWorld()->GetTimeSeconds() * 0.5f) * EffectiveFluctuation;
        TrailEffectComponent->SetFloatParameter(TEXT("FluctuationOffset"), FluctuationOffset);
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "UpdateEtherealPathProperties: Propriedades do Caminho Etéreo atualizadas");
}

void AAURACRONPCGTrail::UpdateNexusConnectionProperties()
{
    if (!IsValid(this))
    {
        return;
    }

    // Atualizar propriedades da Conexão de Nexus
    float EffectiveWidth = ConnectionWidth * ActivityScale;
    float EffectiveStrength = ConnectionStrength * ActivityScale;
    float EffectivePulseRate = ConnectionPulseRate * ActivityScale;

    SetPCGParameterModern(TEXT("ConnectionWidth"), FVector(EffectiveWidth, 0.0f, 0.0f), TEXT("Largura efetiva da conexão"));
    SetPCGParameterModern(TEXT("ConnectionStrength"), FVector(EffectiveStrength, 0.0f, 0.0f), TEXT("Força efetiva da conexão"));
    SetPCGParameterModern(TEXT("ConnectionPulseRate"), FVector(EffectivePulseRate, 0.0f, 0.0f), TEXT("Taxa de pulso efetiva"));

    if (IsValid(TrailEffectComponent))
    {
        TrailEffectComponent->SetFloatParameter(TEXT("ConnectionWidth"), EffectiveWidth);
        TrailEffectComponent->SetFloatParameter(TEXT("ConnectionStrength"), EffectiveStrength);
        TrailEffectComponent->SetFloatParameter(TEXT("ConnectionPulseRate"), EffectivePulseRate);
        TrailEffectComponent->SetVectorParameter(TEXT("ConnectionColor"), FVector(ConnectionColor.R, ConnectionColor.G, ConnectionColor.B));

        // Efeito de pulso baseado na taxa
        float PulseValue = FMath::Sin(GetWorld()->GetTimeSeconds() * EffectivePulseRate) * 0.5f + 0.5f;
        TrailEffectComponent->SetFloatParameter(TEXT("ConnectionPulse"), PulseValue);
    }

    UE_LOGFMT(LogTemp, VeryVerbose, "UpdateNexusConnectionProperties: Propriedades da Conexão de Nexus atualizadas");
}

// ========================================
// REPLICAÇÃO MULTIPLAYER - UE 5.6 MODERN APIS
// ========================================

void AAURACRONPCGTrail::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    // Replicar propriedades essenciais para multiplayer
    DOREPLIFETIME(AAURACRONPCGTrail, TrailType);
    DOREPLIFETIME(AAURACRONPCGTrail, ActivityScale);
    DOREPLIFETIME(AAURACRONPCGTrail, bIsActive);
    DOREPLIFETIME(AAURACRONPCGTrail, bIsVisible);
    DOREPLIFETIME(AAURACRONPCGTrail, NumOverlappingPlayers);

    // Replicar propriedades específicas do Fluxo Prismal
    DOREPLIFETIME(AAURACRONPCGTrail, FlowIntensity);
    DOREPLIFETIME(AAURACRONPCGTrail, FlowWidth);
    DOREPLIFETIME(AAURACRONPCGTrail, FlowSpeed);
    DOREPLIFETIME(AAURACRONPCGTrail, FlowColor);

    // Replicar propriedades específicas do Caminho Etéreo
    DOREPLIFETIME(AAURACRONPCGTrail, PathVisibility);
    DOREPLIFETIME(AAURACRONPCGTrail, PathWidth);
    DOREPLIFETIME(AAURACRONPCGTrail, PathFluctuation);
    DOREPLIFETIME(AAURACRONPCGTrail, PathColor);

    // Replicar propriedades específicas da Conexão de Nexus
    DOREPLIFETIME(AAURACRONPCGTrail, ConnectionStrength);
    DOREPLIFETIME(AAURACRONPCGTrail, ConnectionWidth);
    DOREPLIFETIME(AAURACRONPCGTrail, ConnectionPulseRate);
    DOREPLIFETIME(AAURACRONPCGTrail, ConnectionColor);
}

// ========================================
// RPCs PARA MULTIPLAYER - UE 5.6 MODERN APIS
// ========================================

void AAURACRONPCGTrail::ServerSetTrailType_Implementation(EAURACRONTrailType NewType)
{
    // Validação anti-cheat server-side
    if (!HasAuthority())
    {
        UE_LOGFMT(LogTemp, Warning, "ServerSetTrailType: Tentativa de mudança de tipo sem autoridade do servidor");
        return;
    }

    // Validar tipo de trilha
    if (NewType == EAURACRONTrailType::None || (int32)NewType < 0 || (int32)NewType >= (int32)EAURACRONTrailType::MAX)
    {
        UE_LOGFMT(LogTemp, Warning, "ServerSetTrailType: Tipo de trilha inválido {TrailType}", (int32)NewType);
        return;
    }

    TrailType = NewType;

    // Replicar para todos os clientes
    MulticastUpdateTrailType(NewType);

    // Regenerar trilha no servidor
    GenerateTrail();

    UE_LOGFMT(LogTemp, Log, "ServerSetTrailType: Tipo de trilha alterado para {TrailType}", (int32)NewType);
}

bool AAURACRONPCGTrail::ServerSetTrailType_Validate(EAURACRONTrailType NewType)
{
    // Validação básica
    return (NewType != EAURACRONTrailType::None && (int32)NewType >= 0 && (int32)NewType < (int32)EAURACRONTrailType::MAX);
}

void AAURACRONPCGTrail::MulticastUpdateTrailType_Implementation(EAURACRONTrailType NewType)
{
    if (HasAuthority())
    {
        return; // Servidor já processou
    }

    TrailType = NewType;
    GenerateTrail();

    UE_LOGFMT(LogTemp, Log, "MulticastUpdateTrailType: Tipo de trilha atualizado para {TrailType} no cliente", (int32)NewType);
}

void AAURACRONPCGTrail::ServerSetActivityScale_Implementation(float NewScale)
{
    // Validação anti-cheat server-side
    if (!HasAuthority())
    {
        UE_LOGFMT(LogTemp, Warning, "ServerSetActivityScale: Tentativa de mudança de escala sem autoridade do servidor");
        return;
    }

    // Validar escala
    float ClampedScale = FMath::Clamp(NewScale, 0.0f, 1.0f);
    if (FMath::Abs(NewScale - ClampedScale) > 0.01f)
    {
        UE_LOGFMT(LogTemp, Warning, "ServerSetActivityScale: Escala inválida {Scale}, usando {ClampedScale}", NewScale, ClampedScale);
    }

    ActivityScale = ClampedScale;

    // Replicar para todos os clientes
    MulticastUpdateActivityScale(ClampedScale);

    // Aplicar escala no servidor
    ApplyActivityScale();

    UE_LOGFMT(LogTemp, Log, "ServerSetActivityScale: Escala de atividade alterada para {Scale}", ClampedScale);
}

bool AAURACRONPCGTrail::ServerSetActivityScale_Validate(float NewScale)
{
    // Validação básica - permitir qualquer valor, será clampado no servidor
    return true;
}

void AAURACRONPCGTrail::MulticastUpdateActivityScale_Implementation(float NewScale)
{
    if (HasAuthority())
    {
        return; // Servidor já processou
    }

    ActivityScale = NewScale;
    ApplyActivityScale();

    UE_LOGFMT(LogTemp, Log, "MulticastUpdateActivityScale: Escala de atividade atualizada para {Scale} no cliente", NewScale);
}

// ========================================
// SUBSTITUIÇÃO DO TICK POR TIMER - UE 5.6 OPTIMIZATION
// ========================================

void AAURACRONPCGTrail::BeginPlay()
{
    Super::BeginPlay();

    // Configurar o componente PCG com as configurações apropriadas
    if (TrailSettings)
    {
        // Configurar PCG Graph Instance (UE 5.6 API moderna)
        if (PCGComponent->GetGraphInstance())
        {
            // O GraphInstance já existe, podemos configurar parâmetros se necessário
            UE_LOGFMT(LogTemp, Log, "AURACRONPCGTrail: PCG GraphInstance configurado");
        }
        else
        {
            UE_LOGFMT(LogTemp, Warning, "AURACRONPCGTrail: PCG GraphInstance não encontrado - será configurado via Blueprint");
        }
    }

    // Registrar eventos de overlap (UE 5.6 API moderna)
    InteractionVolume->OnComponentBeginOverlap.AddDynamic(this, &AAURACRONPCGTrail::HandlePlayerOverlap);
    InteractionVolume->OnComponentEndOverlap.AddDynamic(this, &AAURACRONPCGTrail::HandlePlayerEndOverlap);

    // Iniciar com visibilidade desativada até que seja explicitamente ativado
    SetTrailVisibility(false);

    // Definir escala de atividade inicial
    SetActivityScale(0.0f);

    // Inicializar propriedades de streaming e data layer
    bStreamingEnabled = true;
    StreamingDistance = 5000.0f;
    AssociatedDataLayer = NAME_None;

    // Inicializar configuração de streaming com valores padrão
    StreamingConfiguration.LoadingDistance = 3000.0f;
    StreamingConfiguration.UnloadingDistance = 4000.0f;
    StreamingConfiguration.StreamingPriority = 50;
    StreamingConfiguration.bUseAsyncStreaming = true;
    StreamingConfiguration.GridSize = 2000.0f;
    StreamingConfiguration.StreamingDistance = 5000.0f;

    // SUBSTITUIR TICK POR TIMER PARA OTIMIZAÇÃO (UE 5.6)
    if (UWorld* World = GetWorld())
    {
        FTimerManager& TimerManager = World->GetTimerManager();
        TimerManager.SetTimer(
            UpdateTimerHandle,
            this,
            &AAURACRONPCGTrail::TimerBasedUpdate,
            TrailUpdateInterval,
            true // Loop
        );

        UE_LOGFMT(LogTemp, Log, "BeginPlay: Timer configurado para atualização a cada {Interval} segundos", TrailUpdateInterval);
    }
}

void AAURACRONPCGTrail::TimerBasedUpdate()
{
    // SUBSTITUIÇÃO DO TICK POR TIMER PARA MELHOR PERFORMANCE (UE 5.6)
    if (!IsValid(this))
    {
        return;
    }

    // Atualizar parâmetros da trilha
    UpdateTrailParameters();

    // Regenerar a trilha se estiver ativa
    if (ActivityScale > 0.01f)
    {
        // Não regenerar completamente a cada update, apenas atualizar parâmetros
        UpdateTrailProperties();
    }

    // Aplicar efeitos a todos os jogadores na trilha
    for (ACharacter* Player : OverlappingPlayers)
    {
        if (IsValid(Player))
        {
            ApplyTrailEffectsToPlayer(Player, TrailUpdateInterval);
        }
    }

    // Atualizar o volume de interação para seguir a spline
    UpdateInteractionVolume();

    // Validações robustas de integridade
    PerformIntegrityChecks();
}

void AAURACRONPCGTrail::PerformIntegrityChecks()
{
    // VALIDAÇÕES ROBUSTAS PARA GARANTIR INTEGRIDADE DO SISTEMA
    if (!IsValid(this))
    {
        return;
    }

    // Verificar componentes essenciais
    if (!IsValid(PCGComponent))
    {
        UE_LOGFMT(LogTemp, Error, "PerformIntegrityChecks: PCGComponent inválido - recriando");
        PCGComponent = CreateDefaultSubobject<UPCGComponent>(TEXT("PCGComponent_Recreated"));
    }

    if (!IsValid(SplineComponent))
    {
        UE_LOGFMT(LogTemp, Error, "PerformIntegrityChecks: SplineComponent inválido - recriando");
        SplineComponent = CreateDefaultSubobject<USplineComponent>(TEXT("SplineComponent_Recreated"));
        SplineComponent->SetupAttachment(RootComponent);
    }

    if (!IsValid(InteractionVolume))
    {
        UE_LOGFMT(LogTemp, Error, "PerformIntegrityChecks: InteractionVolume inválido - recriando");
        InteractionVolume = CreateDefaultSubobject<UBoxComponent>(TEXT("InteractionVolume_Recreated"));
        InteractionVolume->SetupAttachment(RootComponent);
        InteractionVolume->SetCollisionProfileName(TEXT("OverlapAllDynamic"));
        InteractionVolume->SetGenerateOverlapEvents(true);
    }

    // Verificar lista de jogadores sobrepostos
    OverlappingPlayers.RemoveAll([](const ACharacter* Player) {
        return !IsValid(Player);
    });

    NumOverlappingPlayers = OverlappingPlayers.Num();

    // Verificar valores de propriedades
    ActivityScale = FMath::Clamp(ActivityScale, 0.0f, 1.0f);
    FlowIntensity = FMath::Clamp(FlowIntensity, 0.0f, 10.0f);
    FlowWidth = FMath::Clamp(FlowWidth, 50.0f, 2000.0f);
    FlowSpeed = FMath::Clamp(FlowSpeed, 0.1f, 10.0f);

    PathVisibility = FMath::Clamp(PathVisibility, 0.0f, 1.0f);
    PathWidth = FMath::Clamp(PathWidth, 50.0f, 1000.0f);
    PathFluctuation = FMath::Clamp(PathFluctuation, 0.0f, 5.0f);

    ConnectionStrength = FMath::Clamp(ConnectionStrength, 0.0f, 2.0f);
    ConnectionWidth = FMath::Clamp(ConnectionWidth, 50.0f, 800.0f);
    ConnectionPulseRate = FMath::Clamp(ConnectionPulseRate, 0.1f, 5.0f);
}

// ========================================
// DESTRUCTOR E CLEANUP - UE 5.6 MODERN APIS
// ========================================

void AAURACRONPCGTrail::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Limpar timer
    if (UWorld* World = GetWorld())
    {
        FTimerManager& TimerManager = World->GetTimerManager();
        if (UpdateTimerHandle.IsValid())
        {
            TimerManager.ClearTimer(UpdateTimerHandle);
            UE_LOGFMT(LogTemp, Log, "EndPlay: Timer de atualização limpo");
        }
    }

    // Limpar efeitos de todos os jogadores
    for (ACharacter* Player : OverlappingPlayers)
    {
        if (IsValid(Player))
        {
            RemoveTrailEffectsFromPlayer(Player);
        }
    }
    OverlappingPlayers.Empty();

    // Limpar trilha
    ClearTrail();

    Super::EndPlay(EndPlayReason);

    UE_LOGFMT(LogTemp, Log, "EndPlay: AURACRONPCGTrail cleanup concluído");
}

// ========================================
// ENUMS PARA CAPACIDADE DE HARDWARE
// ========================================

UENUM(BlueprintType)
enum class EAURACRONHardwareCapacity : uint8
{
    Entry   UMETA(DisplayName = "Entry Level"),
    Mid     UMETA(DisplayName = "Mid-range"),
    High    UMETA(DisplayName = "High-end")
};

// ========================================
// CORREÇÃO FINAL DOS UE_LOG RESTANTES
// ========================================
