// AURACRONPCGIsland.cpp
// Sistema de Geração Procedural para AURACRON - UE 5.6
// Implementação da classe para gerenciar as ilhas flutuantes

#include "PCG/AURACRONPCGIsland.h"
#include "PCG/AURACRONPCGSubsystem.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "PCG/AURACRONPCGMathLibrary.h"
#include "PCGComponent.h"
#include "PCGSettings.h"
#include "PCGGraph.h"
#include "Helpers/PCGGraphParametersHelpers.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/World.h"
#include "Components/LightComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SphereComponent.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "Materials/MaterialInterface.h"
#include "Net/UnrealNetwork.h"

// ========================================
// INCLUDES MODERNOS UE 5.6 - APIS ROBUSTAS
// ========================================
#include "Engine/StreamableManager.h"
#include "TimerManager.h"
#include "Logging/StructuredLog.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Components/AudioComponent.h"
#include "Components/PointLightComponent.h"
#include "Engine/DataTable.h"
#include "Engine/AssetManager.h"
#include "NiagaraFunctionLibrary.h"
#include "Components/SceneComponent.h"
#include "Engine/Engine.h"
#include "GameFramework/GameModeBase.h"

AAURACRONPCGIsland::AAURACRONPCGIsland()
    : IslandType(EAURACRONIslandType::Sanctuary)
    , ActivityScale(0.0f)
    , CurrentMapPhase(EAURACRONMapPhase::Awakening)
    , CurrentBoundaryBlurStrength(0.0f)
    , AccumulatedTime(0.0f)
    , TimeSinceLastUpdate(0.0f)
    , UpdateInterval(0.1f) // Otimizado para performance - Timer ao invés de Tick
    , StreamableManager(nullptr)
    , UpdateTimerHandle()
    , bHasSolarTrilho(false)
    , bHasAxisTrilho(false)
    , bHasLunarTrilho(false)
    , bHasFluxoPrismal(false)
    , bHasIlhaCentralAuracron(false)
    , SolarTrilhoIntensity(0.5f)
    , AxisTrilhoIntensity(0.5f)
    , LunarTrilhoIntensity(0.5f)
    , FluxoPrismalIntensity(1.0f)
{
    PrimaryActorTick.bCanEverTick = false; // Desabilitado - usando Timer para performance

    // Configurar replicação para multiplayer usando APIs modernas UE 5.6
    bReplicates = true;
    SetReplicateMovement(false); // Ilhas não se movem (exceto flutuação)

    // Criar o componente raiz (USceneComponent)
    USceneComponent* SceneRoot = CreateDefaultSubobject<USceneComponent>(TEXT("SceneRoot"));
    RootComponent = SceneRoot;

    // Criar o componente PCG usando APIs modernas UE 5.6
    PCGComponent = CreateDefaultSubobject<UPCGComponent>(TEXT("PCGComponent"));
    if (PCGComponent)
    {
        PCGComponent->SetupAttachment(RootComponent);
    }

    // Criar componente de colisão base com validações robustas
    CollisionComponent = CreateDefaultSubobject<USphereComponent>(TEXT("CollisionComponent"));
    if (CollisionComponent)
    {
        CollisionComponent->SetupAttachment(RootComponent);
        CollisionComponent->SetCollisionProfileName(TEXT("BlockAll"));
        CollisionComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
        CollisionComponent->SetCollisionObjectType(ECollisionChannel::ECC_WorldStatic);
    }

    // Criar componente de mesh principal com validações robustas
    IslandMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("IslandMesh"));
    if (IslandMesh)
    {
        IslandMesh->SetupAttachment(RootComponent);
        IslandMesh->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
        IslandMesh->SetCollisionObjectType(ECollisionChannel::ECC_WorldStatic);
    }

    // Criar componentes de áudio para efeitos sonoros usando APIs modernas UE 5.6
    AudioComponent = CreateDefaultSubobject<UAudioComponent>(TEXT("AudioComponent"));
    if (AudioComponent)
    {
        AudioComponent->SetupAttachment(RootComponent);
        AudioComponent->bAutoActivate = false;
    }

    // Criar componente de luz para iluminação dinâmica usando APIs modernas UE 5.6
    PointLightComponent = CreateDefaultSubobject<UPointLightComponent>(TEXT("PointLightComponent"));
    if (PointLightComponent)
    {
        PointLightComponent->SetupAttachment(RootComponent);
        PointLightComponent->SetIntensity(1000.0f);
        PointLightComponent->SetLightColor(FLinearColor::White);
        PointLightComponent->SetAttenuationRadius(2000.0f);
    }

    // Valores padrão baseados nas medidas do sistema com validações robustas
    IslandSize = FMath::Max(FAURACRONMapDimensions::SANCTUARY_ISLAND_RADIUS_CM * 2.0f, 1000.0f); // Diâmetro mínimo
    IslandHeight = FMath::Max(FAURACRONMapDimensions::ISLAND_HEIGHT_CM, 100.0f); // Altura mínima

    // Inicializar características específicas do Sanctuary
    bHasHealingFountain = true;
    bHasProtectiveBarrier = true;
    bHasAncientTree = true;
    bHasResourceNodes = true;

    // Inicializar características específicas do Nexus
    bHasCentralSpire = true;
    bHasEnergyVortex = true;
    bHasAncientRunes = true;
    bHasPortalGateways = true;

    // Inicializar características específicas do Arsenal/Chaos
    bHasStrategicPoints = true;
    bHasDestructibleElements = true;
    bHasElevationChanges = true;
    bHasHazardZones = true;

    // Inicializar estado de emergência
    bIsFullyEmerged = false;

    // Inicializar StreamableManager para carregamento assíncrono usando APIs modernas UE 5.6
    if (UAssetManager* AssetManager = UAssetManager::GetIfValid())
    {
        StreamableManager = &AssetManager->GetStreamableManager();
    }
}

void AAURACRONPCGIsland::BeginPlay()
{
    Super::BeginPlay();

    // Validações robustas usando APIs modernas UE 5.6
    if (!IsValid(this))
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGIsland::BeginPlay - Invalid island instance"));
        return;
    }

    // Configurar o componente PCG com as configurações apropriadas usando APIs modernas UE 5.6
    if (PCGComponent && IsValid(PCGComponent))
    {
        // Configurar PCG Graph Instance (UE 5.6 API moderna)
        if (PCGComponent->GetGraphInstance())
        {
            // O GraphInstance já existe, podemos configurar parâmetros se necessário
            UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::BeginPlay - PCG GraphInstance configurado para ilha %s"), *GetName());

            // Configurar parâmetros iniciais usando APIs modernas
            if (UPCGGraph* PCGGraph = PCGComponent->GetGraph())
            {
                UPCGGraphParametersHelpers::SetFloatParameter(PCGGraph, FName("IslandSize"), IslandSize);
                UPCGGraphParametersHelpers::SetFloatParameter(PCGGraph, FName("IslandHeight"), IslandHeight);
                UPCGGraphParametersHelpers::SetFloatParameter(PCGGraph, FName("ActivityScale"), ActivityScale);
            }
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGIsland::BeginPlay - PCG GraphInstance não encontrado - será configurado via Blueprint"));
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGIsland::BeginPlay - PCGComponent inválido"));
    }

    // Inicializar StreamableManager se não foi inicializado no construtor
    if (!StreamableManager)
    {
        if (UAssetManager* AssetManager = UAssetManager::GetIfValid())
        {
            StreamableManager = &AssetManager->GetStreamableManager();
            UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::BeginPlay - StreamableManager inicializado"));
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGIsland::BeginPlay - Falha ao inicializar StreamableManager"));
        }
    }

    // Configurar Timer para atualizações otimizadas ao invés de Tick
    if (UWorld* World = GetWorld())
    {
        if (FTimerManager& TimerManager = World->GetTimerManager())
        {
            TimerManager.SetTimer(UpdateTimerHandle, this, &AAURACRONPCGIsland::UpdateIslandTimer, UpdateInterval, true);
            UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::BeginPlay - Timer configurado com intervalo %.2f"), UpdateInterval);
        }
    }

    // Inicializar sistemas da documentação - Trilhos dinâmicos
    InitializeTrilhosSystems();

    // Inicializar Fluxo Prismal
    InitializeFluxoPrismal();

    // Inicializar Ilha Central Auracron se for do tipo Nexus
    if (IslandType == EAURACRONIslandType::Nexus)
    {
        InitializeIlhaCentralAuracron();
    }

    // Iniciar com visibilidade desativada até que seja explicitamente ativado
    SetIslandVisibility(false);

    // Definir escala de atividade inicial baseada na fase
    SetActivityScale(CurrentMapPhase == EAURACRONMapPhase::Awakening ? 1.0f : 0.0f);

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::BeginPlay - Ilha %s inicializada com sucesso"), *GetName());
}

void AAURACRONPCGIsland::Tick(float DeltaTime)
{
    // TICK DESABILITADO - Usando Timer para performance otimizada
    // Esta função é mantida para compatibilidade mas não é chamada
    Super::Tick(DeltaTime);
}

// ========================================
// FUNÇÃO DE TIMER OTIMIZADA - UE 5.6 MODERN APIS
// ========================================
void AAURACRONPCGIsland::UpdateIslandTimer()
{
    // Função de timer otimizada para substituir Tick com validações robustas
    if (!IsValid(this))
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGIsland::UpdateIslandTimer - Invalid island instance"));
        return;
    }

    // Atualizar parâmetros da ilha usando APIs modernas
    UpdateIslandParameters();

    // Atualizar sistemas de Trilhos dinâmicos
    UpdateTrilhosSystems();

    // Atualizar Fluxo Prismal
    UpdateFluxoPrismal();

    // Regenerar a ilha se estiver ativa com validações robustas
    if (ActivityScale > 0.01f && PCGComponent && IsValid(PCGComponent))
    {
        // Usar regeneração não-bloqueante para performance
        PCGComponent->GenerateLocal(false);
    }

    // Atualizar efeitos visuais baseados na fase atual
    UpdatePhaseVisualEffects();

    // Atualizar áudio ambiente
    UpdateAmbientAudio();

    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGIsland::UpdateIslandTimer - Timer update completed for island %s"), *GetName());
}

void AAURACRONPCGIsland::SetIslandType(EAURACRONIslandType NewType)
{
    IslandType = NewType;
    
    // Reconfigurar a ilha com base no novo tipo
    // Isso pode envolver a alteração das configurações do PCG
    GenerateIsland();
}

void AAURACRONPCGIsland::GenerateIsland()
{
    if (!HasAuthority())
    {
        return;
    }

    // Limpar elementos anteriores
    ClearGeneratedElements();

    // Configurar dimensões baseadas no tipo de ilha
    ConfigureIslandDimensions();

    // Posicionar ilha baseada no tipo
    PositionIsland();

    // Gerar características específicas baseadas no tipo
    switch (IslandType)
    {
    case EAURACRONIslandType::Nexus:
        GenerateNexusIsland();
        break;

    case EAURACRONIslandType::Sanctuary:
        GenerateSanctuaryIsland();
        break;

    case EAURACRONIslandType::Arsenal:
        GenerateArsenalIsland();
        break;

    case EAURACRONIslandType::Chaos:
        GenerateChaosIsland();
        break;

    default:
        break;
    }

    // Aplicar efeitos da fase atual do mapa
    ApplyMapPhaseEffects();

    // Aplicar escala de atividade
    ApplyActivityScale();
    CalculateIslandParameters();

    // Gerar características específicas com base no tipo de ilha
    switch (IslandType)
    {
    case EAURACRONIslandType::Sanctuary:
        GenerateSanctuary();
        break;
        
    case EAURACRONIslandType::Nexus:
        GenerateNexus();
        break;
        
    case EAURACRONIslandType::Battlefield:
        GenerateBattlefield();
        break;
    }

    // Executar a geração PCG
    PCGComponent->Generate();
}

void AAURACRONPCGIsland::UpdateForMapPhase(EAURACRONMapPhase MapPhase)
{
    // Ajustar a ilha com base na fase do mapa
    // Isso pode envolver a alteração de parâmetros de geração ou a ativação/desativação de características
    if (CurrentMapPhase == MapPhase)
    {
        return; // Já está na fase correta
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::UpdateForMapPhase - Updating island %s to phase %d"),
           *GetName(), (int32)MapPhase);
    
    // Atualizar a fase atual
    CurrentMapPhase = MapPhase;
    
    // Chamar a função moderna de atualização de fase
    UpdateIslandPhase(MapPhase);
    
    // Exemplo: Ativar características específicas durante a fase de Prismal Flow
    if (MapPhase == EAURACRONMapPhase::Intensification)
    {
        // Ativar características específicas com base no tipo de ilha
        switch (IslandType)
        {
        case EAURACRONIslandType::Sanctuary:
            bHasProtectiveBarrier = true;
            break;
            
        case EAURACRONIslandType::Nexus:
            bHasEnergyVortex = true;
            break;
            
        case EAURACRONIslandType::Battlefield:
            bHasHazardZones = true;
            break;
        }
    }
    
    // Regenerar a ilha com as novas configurações
    GenerateIsland();
}

void AAURACRONPCGIsland::SetIslandVisibility(bool bVisible)
{
    // Definir a visibilidade de todos os componentes gerados
    SetActorHiddenInGame(!bVisible);
    
    // Se estiver visível, garantir que a geração PCG esteja atualizada
    if (bVisible)
    {
        GenerateIsland();
    }
}

void AAURACRONPCGIsland::SetActivityScale(float Scale)
{
    // Limitar a escala entre 0.0 e 1.0
    ActivityScale = FMath::Clamp(Scale, 0.0f, 1.0f);
    
    // Aplicar a escala de atividade aos parâmetros de geração
    UpdateIslandParameters();
    
    // Regenerar a ilha se a escala for significativa
    if (ActivityScale > 0.01f)
    {
        GenerateIsland();
    }
}

void AAURACRONPCGIsland::SetIslandPosition(const FVector& Position)
{
    // Definir a posição da ilha
    SetActorLocation(Position);
    
    // Regenerar a ilha na nova posição
    GenerateIsland();
}

void AAURACRONPCGIsland::SetIslandSize(float Size)
{
    // Definir o tamanho da ilha
    IslandSize = FMath::Max(Size, 1000.0f); // Mínimo de 10 metros

    // Regenerar a ilha com o novo tamanho
    GenerateIsland();
}

float AAURACRONPCGIsland::GetIslandSize() const
{
    return IslandSize;
}

void AAURACRONPCGIsland::SetIslandHeight(float Height)
{
    // Definir a altura da ilha
    IslandHeight = FMath::Max(Height, 100.0f); // Mínimo de 1 metro
    
    // Regenerar a ilha com a nova altura
    GenerateIsland();
}

// Implementações de geração de características específicas

void AAURACRONPCGIsland::GenerateSanctuary()
{
    // Implementação da geração de Sanctuary
    // Usar PCG para criar uma ilha de santuário com características específicas
    
    // Calcular o raio da ilha em unidades do Unreal (1 unidade = 1cm)
    float IslandRadius = IslandSize / 2.0f;
    
    // Calcular posições para características específicas
    if (bHasHealingFountain)
    {
        // Posicionar a fonte de cura no centro da ilha
        FVector FountainLocation = FVector::ZeroVector; // Relativo ao centro da ilha
        
        // Implementar metadados para a fonte usando APIs modernas UE 5.6
        if (PCGComponent && IsValid(PCGComponent))
        {
            if (UPCGGraph* PCGGraph = PCGComponent->GetGraph())
            {
                UPCGGraphParametersHelpers::SetVectorParameter(PCGGraph, FName("FountainLocation"), FountainLocation);
                UPCGGraphParametersHelpers::SetFloatParameter(PCGGraph, FName("FountainActive"), ActivityScale);
                UPCGGraphParametersHelpers::SetFloatParameter(PCGGraph, FName("FountainIntensity"), 1.5f);
            }
        }
    }
    
    if (bHasProtectiveBarrier)
    {
        // Criar uma barreira protetora ao redor da ilha
        // O raio da barreira seria um pouco maior que o raio da ilha
        float BarrierRadius = IslandRadius * 1.1f;
        
        // Implementar metadados para a barreira usando APIs modernas UE 5.6
        if (PCGComponent && IsValid(PCGComponent))
        {
            if (UPCGGraph* PCGGraph = PCGComponent->GetGraph())
            {
                UPCGGraphParametersHelpers::SetFloatParameter(PCGGraph, FName("BarrierRadius"), BarrierRadius);
                UPCGGraphParametersHelpers::SetFloatParameter(PCGGraph, FName("BarrierStrength"), ActivityScale);
                UPCGGraphParametersHelpers::SetFloatParameter(PCGGraph, FName("BarrierProtection"), 2.0f);
            }
        }
    }
    
    if (bHasAncientTree)
    {
        // Posicionar a árvore antiga próxima ao centro, mas não exatamente no centro
        FVector TreeLocation = FVector(IslandRadius * 0.2f, 0.0f, 0.0f); // Deslocado do centro
        
        // Implementar metadados para a árvore usando APIs modernas UE 5.6
        if (PCGComponent && IsValid(PCGComponent))
        {
            if (UPCGGraph* PCGGraph = PCGComponent->GetGraph())
            {
                UPCGGraphParametersHelpers::SetVectorParameter(PCGGraph, FName("TreeLocation"), TreeLocation);
                UPCGGraphParametersHelpers::SetFloatParameter(PCGGraph, FName("TreeSize"), IslandHeight * 0.8f);
                UPCGGraphParametersHelpers::SetFloatParameter(PCGGraph, FName("TreeWisdom"), 3.0f);
            }
        }
    }
    
    if (bHasResourceNodes)
    {
        // Calcular o número de nós de recursos com base no tamanho da ilha
        int32 NumResourceNodes = FMath::FloorToInt(IslandRadius / 500.0f); // 1 nó a cada 5 metros de raio
        
        // Posicionar nós de recursos ao redor da ilha
        for (int32 i = 0; i < NumResourceNodes; ++i)
        {
            // Distribuir uniformemente em um círculo
            float Angle = (float)i / NumResourceNodes * 2.0f * PI;
            float Distance = IslandRadius * 0.7f; // 70% do raio da ilha
            
            FVector NodeLocation;
            NodeLocation.X = Distance * FMath::Cos(Angle);
            NodeLocation.Y = Distance * FMath::Sin(Angle);
            NodeLocation.Z = 0.0f;
            
            // Implementar metadados para o nó usando APIs modernas UE 5.6
            if (PCGComponent && IsValid(PCGComponent))
            {
                if (UPCGGraph* PCGGraph = PCGComponent->GetGraph())
                {
                    FName NodeLocationParam = FName(*FString::Printf(TEXT("ResourceNodeLocation_%d"), i));
                    UPCGGraphParametersHelpers::SetVectorParameter(PCGGraph, NodeLocationParam, NodeLocation);
                    UPCGGraphParametersHelpers::SetFloatParameter(PCGGraph, FName("ResourceNodeValue"), 1.0f + (i * 0.2f));
                }
            }
        }

        // Implementar parâmetro de contagem usando APIs modernas UE 5.6
        if (PCGComponent && IsValid(PCGComponent))
        {
            if (UPCGGraph* PCGGraph = PCGComponent->GetGraph())
            {
                UPCGGraphParametersHelpers::SetFloatParameter(PCGGraph, FName("NumResourceNodes"), static_cast<float>(NumResourceNodes));
            }
        }
    }
    
    // Implementar parâmetros gerais usando APIs modernas UE 5.6
    if (PCGComponent && IsValid(PCGComponent))
    {
        if (UPCGGraph* PCGGraph = PCGComponent->GetGraph())
        {
            UPCGGraphParametersHelpers::SetFloatParameter(PCGGraph, FName("IslandRadius"), IslandRadius);
            UPCGGraphParametersHelpers::SetFloatParameter(PCGGraph, FName("IslandHeight"), IslandHeight);
            UPCGGraphParametersHelpers::SetFloatParameter(PCGGraph, FName("ActivityScale"), ActivityScale);
            UPCGGraphParametersHelpers::SetFloatParameter(PCGGraph, FName("SanctuaryType"), 1.0f);
        }
    }
}

void AAURACRONPCGIsland::GenerateNexus()
{
    // Implementação da geração de Nexus
    // Usar PCG para criar uma ilha de nexus com características específicas
    
    // Calcular o raio da ilha em unidades do Unreal (1 unidade = 1cm)
    float IslandRadius = IslandSize / 2.0f;
    
    // Calcular posições para características específicas
    if (bHasCentralSpire)
    {
        // Posicionar a espiral central no centro da ilha
        FVector SpireLocation = FVector::ZeroVector; // Relativo ao centro da ilha
        
        // Altura da espiral baseada na altura da ilha
        float SpireHeight = IslandHeight * 2.0f;
        
        // Adicionar metadados para a espiral nesta posição
        // PCGComponent->SetDynamicParameter("SpireLocation", SpireLocation);
        // PCGComponent->SetDynamicParameter("SpireHeight", SpireHeight);
    }
    
    if (bHasEnergyVortex)
    {
        // Criar um vórtice de energia ao redor da espiral central
        float VortexRadius = IslandRadius * 0.3f;
        
        // Adicionar metadados para o vórtice
        // PCGComponent->SetDynamicParameter("VortexRadius", VortexRadius);
        // PCGComponent->SetDynamicParameter("VortexIntensity", ActivityScale);
    }
    
    if (bHasAncientRunes)
    {
        // Calcular o número de runas com base no tamanho da ilha
        int32 NumRunes = FMath::FloorToInt(IslandRadius / 400.0f); // 1 runa a cada 4 metros de raio
        
        // Posicionar runas em um padrão circular ao redor do centro
        for (int32 i = 0; i < NumRunes; ++i)
        {
            // Distribuir uniformemente em um círculo
            float Angle = (float)i / NumRunes * 2.0f * PI;
            float Distance = IslandRadius * 0.5f; // 50% do raio da ilha
            
            FVector RuneLocation;
            RuneLocation.X = Distance * FMath::Cos(Angle);
            RuneLocation.Y = Distance * FMath::Sin(Angle);
            RuneLocation.Z = 0.0f;
            
            // Adicionar metadados para a runa nesta posição
            // PCGComponent->SetDynamicParameter(FString::Printf(TEXT("RuneLocation_%d"), i), RuneLocation);
        }
        
        // PCGComponent->SetDynamicParameter("NumRunes", (float)NumRunes);
    }
    
    if (bHasPortalGateways)
    {
        // Calcular o número de portais com base no tamanho da ilha
        int32 NumPortals = FMath::Min(FMath::FloorToInt(IslandRadius / 1000.0f), 4); // Máximo de 4 portais
        
        // Posicionar portais nas bordas da ilha
        for (int32 i = 0; i < NumPortals; ++i)
        {
            // Distribuir uniformemente em um círculo
            float Angle = (float)i / NumPortals * 2.0f * PI;
            float Distance = IslandRadius * 0.9f; // 90% do raio da ilha
            
            FVector PortalLocation;
            PortalLocation.X = Distance * FMath::Cos(Angle);
            PortalLocation.Y = Distance * FMath::Sin(Angle);
            PortalLocation.Z = 0.0f;
            
            // Adicionar metadados para o portal nesta posição
            // PCGComponent->SetDynamicParameter(FString::Printf(TEXT("PortalLocation_%d"), i), PortalLocation);
        }
        
        // PCGComponent->SetDynamicParameter("NumPortals", (float)NumPortals);
    }
    
    // Estes valores seriam passados para o sistema PCG
    // PCGComponent->SetDynamicParameter("IslandRadius", IslandRadius);
    // PCGComponent->SetDynamicParameter("IslandHeight", IslandHeight);
    // PCGComponent->SetDynamicParameter("ActivityScale", ActivityScale);
}

void AAURACRONPCGIsland::GenerateBattlefield()
{
    // Implementação da geração de Battlefield
    // Usar PCG para criar uma ilha de campo de batalha com características específicas
    
    // Calcular o raio da ilha em unidades do Unreal (1 unidade = 1cm)
    float IslandRadius = IslandSize / 2.0f;
    
    // Calcular posições para características específicas
    if (bHasStrategicPoints)
    {
        // Calcular o número de pontos estratégicos com base no tamanho da ilha
        int32 NumStrategicPoints = FMath::FloorToInt(IslandRadius / 800.0f); // 1 ponto a cada 8 metros de raio
        
        // Posicionar pontos estratégicos em locais importantes da ilha
        for (int32 i = 0; i < NumStrategicPoints; ++i)
        {
            // Distribuir em um padrão específico para criar áreas de interesse
            float Angle = (float)i / NumStrategicPoints * 2.0f * PI;
            float Distance = IslandRadius * (0.3f + 0.5f * (i % 2)); // Alternar entre 30% e 80% do raio
            
            FVector PointLocation;
            PointLocation.X = Distance * FMath::Cos(Angle);
            PointLocation.Y = Distance * FMath::Sin(Angle);
            PointLocation.Z = 0.0f;
            
            // Adicionar metadados para o ponto nesta posição
            // PCGComponent->SetDynamicParameter(FString::Printf(TEXT("StrategicPointLocation_%d"), i), PointLocation);
        }
        
        // PCGComponent->SetDynamicParameter("NumStrategicPoints", (float)NumStrategicPoints);
    }
    
    if (bHasDestructibleElements)
    {
        // Calcular o número de elementos destrutíveis com base no tamanho da ilha
        int32 NumDestructibles = FMath::FloorToInt(IslandRadius / 300.0f); // 1 elemento a cada 3 metros de raio
        
        // Posicionar elementos destrutíveis ao redor da ilha
        for (int32 i = 0; i < NumDestructibles; ++i)
        {
            // Distribuir aleatoriamente dentro da ilha
            float Angle = FMath::RandRange(0.0f, 2.0f * PI);
            float Distance = FMath::RandRange(0.2f, 0.9f) * IslandRadius;
            
            FVector ElementLocation;
            ElementLocation.X = Distance * FMath::Cos(Angle);
            ElementLocation.Y = Distance * FMath::Sin(Angle);
            ElementLocation.Z = 0.0f;
            
            // Adicionar metadados para o elemento nesta posição
            // PCGComponent->SetDynamicParameter(FString::Printf(TEXT("DestructibleLocation_%d"), i), ElementLocation);
        }
        
        // PCGComponent->SetDynamicParameter("NumDestructibles", (float)NumDestructibles);
    }
    
    if (bHasElevationChanges)
    {
        // Criar mudanças de elevação na ilha
        int32 NumElevationPoints = FMath::FloorToInt(IslandRadius / 500.0f); // 1 ponto a cada 5 metros de raio
        
        // Posicionar pontos de elevação ao redor da ilha
        for (int32 i = 0; i < NumElevationPoints; ++i)
        {
            // Distribuir em um padrão específico
            float Angle = (float)i / NumElevationPoints * 2.0f * PI;
            float Distance = IslandRadius * 0.6f; // 60% do raio da ilha
            
            FVector ElevationLocation;
            ElevationLocation.X = Distance * FMath::Cos(Angle);
            ElevationLocation.Y = Distance * FMath::Sin(Angle);
            ElevationLocation.Z = 0.0f;
            
            // Altura da elevação (variando entre 10% e 40% da altura da ilha)
            float ElevationHeight = IslandHeight * FMath::RandRange(0.1f, 0.4f);
            
            // Adicionar metadados para a elevação nesta posição
            // PCGComponent->SetDynamicParameter(FString::Printf(TEXT("ElevationLocation_%d"), i), ElevationLocation);
            // PCGComponent->SetDynamicParameter(FString::Printf(TEXT("ElevationHeight_%d"), i), ElevationHeight);
        }
        
        // PCGComponent->SetDynamicParameter("NumElevationPoints", (float)NumElevationPoints);
    }
    
    if (bHasHazardZones)
    {
        // Calcular o número de zonas de perigo com base no tamanho da ilha
        int32 NumHazardZones = FMath::FloorToInt(IslandRadius / 1000.0f); // 1 zona a cada 10 metros de raio
        
        // Posicionar zonas de perigo em locais estratégicos
        for (int32 i = 0; i < NumHazardZones; ++i)
        {
            // Distribuir em locais específicos para criar áreas de risco
            float Angle = (float)i / NumHazardZones * 2.0f * PI;
            float Distance = IslandRadius * 0.7f; // 70% do raio da ilha
            
            FVector HazardLocation;
            HazardLocation.X = Distance * FMath::Cos(Angle);
            HazardLocation.Y = Distance * FMath::Sin(Angle);
            HazardLocation.Z = 0.0f;
            
            // Raio da zona de perigo
            float HazardRadius = IslandRadius * 0.2f;
            
            // Adicionar metadados para a zona nesta posição
            // PCGComponent->SetDynamicParameter(FString::Printf(TEXT("HazardLocation_%d"), i), HazardLocation);
            // PCGComponent->SetDynamicParameter(FString::Printf(TEXT("HazardRadius_%d"), i), HazardRadius);
        }
        
        // PCGComponent->SetDynamicParameter("NumHazardZones", (float)NumHazardZones);
    }
    
    // Estes valores seriam passados para o sistema PCG
    // PCGComponent->SetDynamicParameter("IslandRadius", IslandRadius);
    // PCGComponent->SetDynamicParameter("IslandHeight", IslandHeight);
    // PCGComponent->SetDynamicParameter("ActivityScale", ActivityScale);
}

void AAURACRONPCGIsland::CalculateIslandParameters()
{
    // Calcular parâmetros básicos da ilha
    float IslandRadius = IslandSize / 2.0f;
    
    // Calcular a forma da ilha com base no tipo
    float IslandRoughness = 0.0f;
    float IslandDetailLevel = 0.0f;
    
    switch (IslandType)
    {
    case EAURACRONIslandType::Sanctuary:
        // Ilha de santuário: mais suave e regular
        IslandRoughness = 0.2f;
        IslandDetailLevel = 0.5f;
        break;
        
    case EAURACRONIslandType::Nexus:
        // Ilha de nexus: formas geométricas e regulares
        IslandRoughness = 0.1f;
        IslandDetailLevel = 0.8f;
        break;
        
    case EAURACRONIslandType::Battlefield:
        // Ilha de campo de batalha: mais irregular e acidentada
        IslandRoughness = 0.4f;
        IslandDetailLevel = 0.7f;
        break;
    }
    
    // Estes valores seriam passados para o sistema PCG
    // PCGComponent->SetDynamicParameter("IslandRadius", IslandRadius);
    // PCGComponent->SetDynamicParameter("IslandHeight", IslandHeight);
    // PCGComponent->SetDynamicParameter("IslandRoughness", IslandRoughness);
    // PCGComponent->SetDynamicParameter("IslandDetailLevel", IslandDetailLevel);
}

void AAURACRONPCGIsland::UpdateIslandParameters()
{
    // Atualizar parâmetros específicos com base no tipo de ilha
    switch (IslandType)
    {
    case EAURACRONIslandType::Sanctuary:
        // Adicionar alguma variação ao longo do tempo para a ilha de santuário
        // Por exemplo, a intensidade da fonte de cura pode pulsar
        if (bHasHealingFountain)
        {
            float FountainIntensity = 0.5f + 0.5f * FMath::Sin(GetWorld()->GetTimeSeconds() * 0.5f);
            // PCGComponent->SetDynamicParameter("FountainIntensity", FountainIntensity);
        }
        break;
        
    case EAURACRONIslandType::Nexus:
        // Adicionar alguma variação ao longo do tempo para a ilha de nexus
        // Por exemplo, o vórtice de energia pode girar mais rápido ou mais devagar
        if (bHasEnergyVortex)
        {
            float VortexSpeed = 1.0f + 0.5f * FMath::Sin(GetWorld()->GetTimeSeconds() * 0.3f);
            // PCGComponent->SetDynamicParameter("VortexSpeed", VortexSpeed);
        }
        break;
        
    case EAURACRONIslandType::Battlefield:
        // Adicionar alguma variação ao longo do tempo para a ilha de campo de batalha
        // Por exemplo, as zonas de perigo podem expandir e contrair
        if (bHasHazardZones)
        {
            float HazardIntensity = 0.7f + 0.3f * FMath::Sin(GetWorld()->GetTimeSeconds() * 0.2f);
            // PCGComponent->SetDynamicParameter("HazardIntensity", HazardIntensity);
        }
        break;
    }
}

// ========================================
// IMPLEMENTAÇÕES DAS NOVAS FUNÇÕES MODERNAS
// ========================================

void AAURACRONPCGIsland::ClearGeneratedElements()
{
    // Limpar componentes gerados
    for (UActorComponent* Component : GeneratedComponents)
    {
        if (IsValid(Component))
        {
            Component->DestroyComponent();
        }
    }

    GeneratedComponents.Empty();
}

void AAURACRONPCGIsland::ConfigureIslandDimensions()
{
    // Configurar dimensões baseadas no tipo de ilha
    switch (IslandType)
    {
    case EAURACRONIslandType::Nexus:
        IslandSize = FAURACRONMapDimensions::NEXUS_ISLAND_RADIUS_CM * 2.0f;
        break;

    case EAURACRONIslandType::Sanctuary:
        IslandSize = FAURACRONMapDimensions::SANCTUARY_ISLAND_RADIUS_CM * 2.0f;
        break;

    case EAURACRONIslandType::Arsenal:
        IslandSize = FAURACRONMapDimensions::ARSENAL_ISLAND_RADIUS_CM * 2.0f;
        break;

    case EAURACRONIslandType::Chaos:
        IslandSize = FAURACRONMapDimensions::CHAOS_ISLAND_RADIUS_CM * 2.0f;
        break;
    }

    // Configurar componente de colisão
    if (CollisionComponent)
    {
        CollisionComponent->SetSphereRadius(IslandSize * 0.5f);
    }

    // Configurar escala do mesh
    if (IslandMesh)
    {
        float Scale = IslandSize / 1000.0f; // Normalizar para escala padrão
        IslandMesh->SetWorldScale3D(FVector(Scale));
    }
}

void AAURACRONPCGIsland::PositionIsland()
{
    // Obter posição baseada no tipo de ilha
    TArray<FVector> IslandPositions = UAURACRONMapMeasurements::GetIslandPositions(
        static_cast<int32>(IslandType),
        TArray<FVector>() // Sem pontos de referência específicos
    );

    if (IslandPositions.Num() > 0)
    {
        // Usar a primeira posição disponível
        FVector TargetPosition = IslandPositions[0];

        // Adicionar altura de flutuação
        TargetPosition.Z += IslandHeight;

        SetActorLocation(TargetPosition);
    }
}

void AAURACRONPCGIsland::GenerateNexusIsland()
{
    // Gerar ilha nexus usando APIs modernas do UE 5.6
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::GenerateNexusIsland - Generating nexus island with modern UE 5.6 APIs"));

    // Configurar propriedades específicas do nexus
    bHasCentralSpire = true;
    bHasEnergyVortex = true;
    bHasAncientRunes = true;
    bHasPortalGateways = true;

    // Gerar spire central
    if (bHasCentralSpire)
    {
        UStaticMeshComponent* CentralSpire = CreateDefaultSubobject<UStaticMeshComponent>(
            *FString::Printf(TEXT("CentralSpire_%d"), FMath::RandRange(1000, 9999))
        );
        if (CentralSpire)
        {
            CentralSpire->SetupAttachment(RootComponent);
            CentralSpire->SetRelativeLocation(FVector(0.0f, 0.0f, IslandHeight * 0.5f));
            CentralSpire->SetRelativeScale3D(FVector(1.0f, 1.0f, 2.0f)); // Spire alta

            GeneratedComponents.Add(CentralSpire);
        }
    }

    // Gerar vórtice de energia
    if (bHasEnergyVortex)
    {
        UNiagaraComponent* EnergyVortex = CreateDefaultSubobject<UNiagaraComponent>(
            *FString::Printf(TEXT("EnergyVortex_%d"), FMath::RandRange(1000, 9999))
        );
        if (EnergyVortex)
        {
            EnergyVortex->SetupAttachment(RootComponent);
            EnergyVortex->SetRelativeLocation(FVector(0.0f, 0.0f, IslandHeight * 0.8f));

            // Configurar parâmetros do vórtice
            EnergyVortex->SetFloatParameter(FName("VortexRadius"), IslandSize * 0.3f);
            EnergyVortex->SetFloatParameter(FName("VortexSpeed"), 2.0f);
            EnergyVortex->SetColorParameter(FName("VortexColor"), FLinearColor::Blue);

            GeneratedComponents.Add(EnergyVortex);
        }
    }

    // Gerar runas antigas
    if (bHasAncientRunes)
    {
        int32 RuneCount = 8;
        float RuneRadius = IslandSize * 0.4f;

        for (int32 i = 0; i < RuneCount; ++i)
        {
            float Angle = 2.0f * PI * i / RuneCount;
            FVector RunePosition = FVector(
                RuneRadius * FMath::Cos(Angle),
                RuneRadius * FMath::Sin(Angle),
                IslandHeight * 0.1f
            );

            UStaticMeshComponent* Rune = CreateDefaultSubobject<UStaticMeshComponent>(
                *FString::Printf(TEXT("AncientRune_%d_%d"), i, FMath::RandRange(1000, 9999))
            );
            if (Rune)
            {
                Rune->SetupAttachment(RootComponent);
                Rune->SetRelativeLocation(RunePosition);
                Rune->SetRelativeScale3D(FVector(1.0f, 1.0f, 1.0f));

                // Configurar material de runa usando carregamento assíncrono UE 5.6
                LoadMaterialAsync(TEXT("/Engine/BasicShapes/BasicShapeMaterial"),
                    FStreamableDelegate::CreateUObject(this, &AAURACRONPCGIsland::OnRuneMaterialLoaded, Rune, i));

                GeneratedComponents.Add(Rune);
            }
        }
    }

    // Gerar portais
    if (bHasPortalGateways)
    {
        int32 PortalCount = 4;
        float PortalRadius = IslandSize * 0.45f;

        for (int32 i = 0; i < PortalCount; ++i)
        {
            float Angle = 2.0f * PI * i / PortalCount;
            FVector PortalPosition = FVector(
                PortalRadius * FMath::Cos(Angle),
                PortalRadius * FMath::Sin(Angle),
                IslandHeight * 0.3f
            );

            UNiagaraComponent* Portal = CreateDefaultSubobject<UNiagaraComponent>(
                *FString::Printf(TEXT("Portal_%d_%d"), i, FMath::RandRange(1000, 9999))
            );
            if (Portal)
            {
                Portal->SetupAttachment(RootComponent);
                Portal->SetRelativeLocation(PortalPosition);

                // Configurar efeito de portal
                Portal->SetFloatParameter(FName("PortalSize"), 200.0f);
                Portal->SetColorParameter(FName("PortalColor"), FLinearColor::Blue);

                GeneratedComponents.Add(Portal);
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::GenerateNexusIsland - Nexus island generation completed with %d components"), GeneratedComponents.Num());
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES FALTANTES - UE 5.6 APIS MODERNAS
// ========================================

void AAURACRONPCGIsland::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    // Replicar propriedades usando APIs modernas do UE 5.6
    DOREPLIFETIME(AAURACRONPCGIsland, IslandType);
    DOREPLIFETIME(AAURACRONPCGIsland, ActivityScale);
    DOREPLIFETIME(AAURACRONPCGIsland, CurrentMapPhase);
    DOREPLIFETIME(AAURACRONPCGIsland, IslandSize);
    DOREPLIFETIME(AAURACRONPCGIsland, IslandHeight);
    DOREPLIFETIME(AAURACRONPCGIsland, bHasHealingFountain);
    DOREPLIFETIME(AAURACRONPCGIsland, bHasProtectiveBarrier);
    DOREPLIFETIME(AAURACRONPCGIsland, bHasAncientTree);
    DOREPLIFETIME(AAURACRONPCGIsland, bHasResourceNodes);
    DOREPLIFETIME(AAURACRONPCGIsland, bHasCentralSpire);
    DOREPLIFETIME(AAURACRONPCGIsland, bHasEnergyVortex);
    DOREPLIFETIME(AAURACRONPCGIsland, bHasAncientRunes);
    DOREPLIFETIME(AAURACRONPCGIsland, bHasPortalGateways);
    DOREPLIFETIME(AAURACRONPCGIsland, bHasStrategicPoints);
    DOREPLIFETIME(AAURACRONPCGIsland, bHasDestructibleElements);
    DOREPLIFETIME(AAURACRONPCGIsland, bHasElevationChanges);
    DOREPLIFETIME(AAURACRONPCGIsland, bHasHazardZones);
}

void AAURACRONPCGIsland::OnIslandActivated()
{
    // Callback quando ilha é ativada usando APIs modernas do UE 5.6
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::OnIslandActivated - Island %s activated"), *GetName());

    // Ativar componentes da ilha
    if (PCGComponent && IsValid(PCGComponent))
    {
        PCGComponent->SetComponentTickEnabled(true);

        // Regenerar conteúdo PCG se necessário
        if (ActivityScale > 0.0f)
        {
            PCGComponent->GenerateLocal(false); // Regeneração não-bloqueante
        }
    }

    // Ativar mesh da ilha
    if (IslandMesh && IsValid(IslandMesh))
    {
        IslandMesh->SetVisibility(true);
        IslandMesh->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    }

    // Ativar colisão
    if (CollisionComponent && IsValid(CollisionComponent))
    {
        CollisionComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    }

    // Ativar componentes gerados
    for (UActorComponent* Component : GeneratedComponents)
    {
        if (Component && IsValid(Component))
        {
            Component->SetComponentTickEnabled(true);

            if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
            {
                MeshComp->SetVisibility(true);
            }
            else if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Component))
            {
                NiagaraComp->SetVisibility(true);
                NiagaraComp->Activate();
            }
        }
    }

    // Ativar tick do ator
    SetActorTickEnabled(true);
    SetActorHiddenInGame(false);

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::OnIslandActivated - Island activation completed"));
}

void AAURACRONPCGIsland::OnIslandDeactivated()
{
    // Callback quando ilha é desativada usando APIs modernas do UE 5.6
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::OnIslandDeactivated - Island %s deactivated"), *GetName());

    // Desativar componentes da ilha
    if (PCGComponent && IsValid(PCGComponent))
    {
        PCGComponent->SetComponentTickEnabled(false);
    }

    // Desativar mesh da ilha
    if (IslandMesh && IsValid(IslandMesh))
    {
        IslandMesh->SetVisibility(false);
        IslandMesh->SetCollisionEnabled(ECollisionEnabled::NoCollision);
    }

    // Desativar colisão
    if (CollisionComponent && IsValid(CollisionComponent))
    {
        CollisionComponent->SetCollisionEnabled(ECollisionEnabled::NoCollision);
    }

    // Desativar componentes gerados
    for (UActorComponent* Component : GeneratedComponents)
    {
        if (Component && IsValid(Component))
        {
            Component->SetComponentTickEnabled(false);

            if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
            {
                MeshComp->SetVisibility(false);
            }
            else if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Component))
            {
                NiagaraComp->SetVisibility(false);
                NiagaraComp->Deactivate();
            }
        }
    }

    // Desativar tick do ator
    SetActorTickEnabled(false);
    SetActorHiddenInGame(true);

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::OnIslandDeactivated - Island deactivation completed"));
}

void AAURACRONPCGIsland::UpdateIslandPhase(EAURACRONMapPhase NewPhase)
{
    // Atualizar ilha para nova fase do mapa usando APIs modernas do UE 5.6
    if (CurrentMapPhase == NewPhase)
    {
        return; // Já está na fase correta
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::UpdateIslandPhase - Updating island %s from phase %d to phase %d"),
           *GetName(), (int32)CurrentMapPhase, (int32)NewPhase);

    EAURACRONMapPhase PreviousPhase = CurrentMapPhase;
    CurrentMapPhase = NewPhase;

    // Aplicar mudanças baseadas na nova fase
    switch (NewPhase)
    {
        case EAURACRONMapPhase::Awakening:
        {
            // Fase 1: Despertar (0-15 minutos) - Conforme documentação
            // Todas as ilhas totalmente emergidas
            SetActivityScale(1.0f); // Totalmente ativo conforme documentação

            // Configurar efeitos visuais para fase inicial
            // Trilhos a 50% de poder, efeitos visuais adaptados ao hardware
            for (UActorComponent* Component : GeneratedComponents)
            {
                if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Component))
                {
                    NiagaraComp->SetFloatParameter(FName("PhaseIntensity"), 0.6f);
                    NiagaraComp->SetColorParameter(FName("PhaseColor"), FLinearColor(1.0f, 0.8f, 0.6f)); // Dourado suave
                }
            }
            break;
        }

        case EAURACRONMapPhase::Convergence:
        {
            // Fase de convergência: atividade aumentada
            SetActivityScale(0.8f);

            // Configurar efeitos visuais para convergência
            for (UActorComponent* Component : GeneratedComponents)
            {
                if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Component))
                {
                    NiagaraComp->SetFloatParameter(FName("PhaseIntensity"), 0.8f);
                    NiagaraComp->SetColorParameter(FName("PhaseColor"), FLinearColor(1.0f, 1.0f, 0.9f)); // Branco brilhante
                }
            }
            break;
        }

        case EAURACRONMapPhase::Intensification:
        {
            // Fase de intensificação: alta atividade
            SetActivityScale(1.0f);

            // Configurar efeitos visuais para intensificação
            for (UActorComponent* Component : GeneratedComponents)
            {
                if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Component))
                {
                    NiagaraComp->SetFloatParameter(FName("PhaseIntensity"), 1.0f);
                    NiagaraComp->SetColorParameter(FName("PhaseColor"), FLinearColor(1.0f, 0.6f, 0.4f)); // Laranja intenso
                }
            }
            break;
        }

        case EAURACRONMapPhase::Resolution:
        {
            // Fase final: máxima atividade
            SetActivityScale(1.2f); // Além do normal

            // Configurar efeitos visuais para resolução
            for (UActorComponent* Component : GeneratedComponents)
            {
                if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Component))
                {
                    NiagaraComp->SetFloatParameter(FName("PhaseIntensity"), 1.2f);
                    NiagaraComp->SetColorParameter(FName("PhaseColor"), FLinearColor(0.6f, 0.7f, 1.0f)); // Azul místico
                }
            }
            break;
        }
    }

    // Aplicar efeitos específicos do tipo de ilha
    switch (IslandType)
    {
        case EAURACRONIslandType::Sanctuary:
            // Santuários respondem mais suavemente às mudanças de fase
            if (bHasHealingFountain)
            {
                // Aumentar poder de cura baseado na fase
                float HealingMultiplier = 1.0f + (static_cast<int32>(NewPhase) * 0.2f);
                // Aplicar multiplicador aos efeitos de cura
            }
            break;

        case EAURACRONIslandType::Nexus:
            // Nexus amplifica os efeitos da fase
            if (bHasEnergyVortex)
            {
                // Aumentar intensidade do vórtice baseado na fase
                float VortexIntensity = 0.5f + (static_cast<int32>(NewPhase) * 0.3f);
                // Aplicar intensidade aos efeitos do vórtice
            }
            break;

        case EAURACRONIslandType::Battlefield:
            // Campos de batalha se tornam mais perigosos
            if (bHasHazardZones)
            {
                // Aumentar perigo das zonas baseado na fase
                float HazardLevel = 0.3f + (static_cast<int32>(NewPhase) * 0.4f);
                // Aplicar nível de perigo às zonas
            }
            break;

        case EAURACRONIslandType::Arsenal:
            // Arsenal aumenta disponibilidade de recursos
            // Aumentar spawn rate de itens baseado na fase
            break;

        case EAURACRONIslandType::Chaos:
            // Chaos Islands se tornam mais imprevisíveis
            // Aumentar aleatoriedade dos efeitos baseado na fase
            break;
    }

    // Regenerar conteúdo PCG se necessário
    if (PCGComponent && IsValid(PCGComponent) && ActivityScale > 0.0f)
    {
        PCGComponent->GenerateLocal(false); // Regeneração não-bloqueante
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::UpdateIslandPhase - Island phase update completed"));
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES FALTANTES - UE 5.6 APIS MODERNAS
// ========================================

void AAURACRONPCGIsland::GenerateSanctuaryIsland()
{
    // Gerar ilha santuário usando APIs modernas do UE 5.6
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::GenerateSanctuaryIsland - Generating sanctuary island with modern UE 5.6 APIs"));

    // Configurar propriedades específicas do santuário
    bHasHealingFountain = true;
    bHasProtectiveBarrier = true;
    bHasAncientTree = true;
    bHasResourceNodes = true;

    // Criar fonte de cura central usando APIs modernas
    if (bHasHealingFountain)
    {
        UStaticMeshComponent* HealingFountain = CreateDefaultSubobject<UStaticMeshComponent>(
            *FString::Printf(TEXT("HealingFountain_%d"), FMath::RandRange(1000, 9999))
        );
        if (HealingFountain)
        {
            HealingFountain->SetupAttachment(RootComponent);
            HealingFountain->SetRelativeLocation(FVector(0.0f, 0.0f, 100.0f));
            HealingFountain->SetRelativeScale3D(FVector(2.0f, 2.0f, 3.0f));

            // Configurar material dinâmico para efeitos de cura
            if (UMaterialInterface* BaseMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial")))
            {
                UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, this);
                if (DynamicMaterial)
                {
                    // Configurar parâmetros de cura usando APIs modernas
                    DynamicMaterial->SetScalarParameterValue(FName("HealingIntensity"), 1.5f);
                    DynamicMaterial->SetVectorParameterValue(FName("HealingColor"), FLinearColor(0.2f, 1.0f, 0.3f, 1.0f));
                    HealingFountain->SetMaterial(0, DynamicMaterial);
                }
            }

            GeneratedComponents.Add(HealingFountain);
        }
    }

    // Criar árvore ancestral usando APIs modernas
    if (bHasAncientTree)
    {
        UStaticMeshComponent* AncientTree = CreateDefaultSubobject<UStaticMeshComponent>(
            *FString::Printf(TEXT("AncientTree_%d"), FMath::RandRange(1000, 9999))
        );
        if (AncientTree)
        {
            AncientTree->SetupAttachment(RootComponent);
            AncientTree->SetRelativeLocation(FVector(500.0f, 0.0f, 0.0f));
            AncientTree->SetRelativeScale3D(FVector(3.0f, 3.0f, 5.0f));

            // Configurar colisão para interação
            AncientTree->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
            AncientTree->SetCollisionObjectType(ECollisionChannel::ECC_WorldStatic);

            GeneratedComponents.Add(AncientTree);
        }
    }

    // Criar barreira protetora usando APIs modernas
    if (bHasProtectiveBarrier)
    {
        for (int32 i = 0; i < 8; ++i)
        {
            float Angle = (i / 8.0f) * 2.0f * PI;
            FVector BarrierLocation = FVector(
                FMath::Cos(Angle) * (IslandSize * 0.8f),
                FMath::Sin(Angle) * (IslandSize * 0.8f),
                200.0f
            );

            UStaticMeshComponent* BarrierPillar = CreateDefaultSubobject<UStaticMeshComponent>(
                *FString::Printf(TEXT("BarrierPillar_%d_%d"), i, FMath::RandRange(1000, 9999))
            );
            if (BarrierPillar)
            {
                BarrierPillar->SetupAttachment(RootComponent);
                BarrierPillar->SetRelativeLocation(BarrierLocation);
                BarrierPillar->SetRelativeScale3D(FVector(1.0f, 1.0f, 4.0f));

                // Configurar material de barreira mágica
                if (UMaterialInterface* BaseMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial")))
                {
                    UMaterialInstanceDynamic* BarrierMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, this);
                    if (BarrierMaterial)
                    {
                        BarrierMaterial->SetScalarParameterValue(FName("ProtectionStrength"), 2.0f);
                        BarrierMaterial->SetVectorParameterValue(FName("BarrierColor"), FLinearColor(0.3f, 0.7f, 1.0f, 0.8f));
                        BarrierPillar->SetMaterial(0, BarrierMaterial);
                    }
                }

                GeneratedComponents.Add(BarrierPillar);
            }
        }
    }

    // Criar nós de recursos usando APIs modernas
    if (bHasResourceNodes)
    {
        for (int32 i = 0; i < 6; ++i)
        {
            float Angle = (i / 6.0f) * 2.0f * PI;
            FVector ResourceLocation = FVector(
                FMath::Cos(Angle) * (IslandSize * 0.5f),
                FMath::Sin(Angle) * (IslandSize * 0.5f),
                50.0f
            );

            UStaticMeshComponent* ResourceNode = CreateDefaultSubobject<UStaticMeshComponent>(
                *FString::Printf(TEXT("ResourceNode_%d_%d"), i, FMath::RandRange(1000, 9999))
            );
            if (ResourceNode)
            {
                ResourceNode->SetupAttachment(RootComponent);
                ResourceNode->SetRelativeLocation(ResourceLocation);
                ResourceNode->SetRelativeScale3D(FVector(1.5f, 1.5f, 1.5f));

                // Configurar interação com recursos
                ResourceNode->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
                ResourceNode->SetCollisionObjectType(ECollisionChannel::ECC_WorldDynamic);

                GeneratedComponents.Add(ResourceNode);
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::GenerateSanctuaryIsland - Sanctuary island generation completed with %d components"), GeneratedComponents.Num());
}

void AAURACRONPCGIsland::GenerateArsenalIsland()
{
    // Gerar ilha arsenal usando APIs modernas do UE 5.6
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::GenerateArsenalIsland - Generating arsenal island with modern UE 5.6 APIs"));

    // Configurar propriedades específicas do arsenal
    bHasResourceNodes = true;
    bHasStrategicPoints = true;
    bHasDestructibleElements = true;
    bHasElevationChanges = true;

    // Criar torre central de arsenal usando APIs modernas
    UStaticMeshComponent* ArsenalTower = CreateDefaultSubobject<UStaticMeshComponent>(
        *FString::Printf(TEXT("ArsenalTower_%d"), FMath::RandRange(1000, 9999))
    );
    if (ArsenalTower)
    {
        ArsenalTower->SetupAttachment(RootComponent);
        ArsenalTower->SetRelativeLocation(FVector(0.0f, 0.0f, 300.0f));
        ArsenalTower->SetRelativeScale3D(FVector(2.5f, 2.5f, 6.0f));

        // Configurar material de arsenal com efeitos metálicos
        if (UMaterialInterface* BaseMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial")))
        {
            UMaterialInstanceDynamic* ArsenalMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, this);
            if (ArsenalMaterial)
            {
                ArsenalMaterial->SetScalarParameterValue(FName("Metallic"), 0.9f);
                ArsenalMaterial->SetScalarParameterValue(FName("Roughness"), 0.3f);
                ArsenalMaterial->SetVectorParameterValue(FName("BaseColor"), FLinearColor(0.7f, 0.7f, 0.8f, 1.0f));
                ArsenalTower->SetMaterial(0, ArsenalMaterial);
            }
        }

        // Configurar colisão estratégica
        ArsenalTower->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
        ArsenalTower->SetCollisionObjectType(ECollisionChannel::ECC_WorldStatic);

        GeneratedComponents.Add(ArsenalTower);
    }

    // Criar plataformas de armas usando APIs modernas
    if (bHasStrategicPoints)
    {
        for (int32 i = 0; i < 12; ++i)
        {
            float Angle = (i / 12.0f) * 2.0f * PI;
            float Distance = IslandSize * (0.4f + (i % 3) * 0.2f); // Múltiplos anéis
            FVector PlatformLocation = FVector(
                FMath::Cos(Angle) * Distance,
                FMath::Sin(Angle) * Distance,
                100.0f + (i % 3) * 150.0f // Diferentes alturas
            );

            UStaticMeshComponent* WeaponPlatform = CreateDefaultSubobject<UStaticMeshComponent>(
                *FString::Printf(TEXT("WeaponPlatform_%d_%d"), i, FMath::RandRange(1000, 9999))
            );
            if (WeaponPlatform)
            {
                WeaponPlatform->SetupAttachment(RootComponent);
                WeaponPlatform->SetRelativeLocation(PlatformLocation);
                WeaponPlatform->SetRelativeScale3D(FVector(2.0f, 2.0f, 0.5f));

                // Configurar material de plataforma tática
                if (UMaterialInterface* BaseMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial")))
                {
                    UMaterialInstanceDynamic* PlatformMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, this);
                    if (PlatformMaterial)
                    {
                        PlatformMaterial->SetScalarParameterValue(FName("TacticalLevel"), static_cast<float>(i % 3 + 1));
                        PlatformMaterial->SetVectorParameterValue(FName("PlatformColor"), FLinearColor(0.6f, 0.4f, 0.2f, 1.0f));
                        WeaponPlatform->SetMaterial(0, PlatformMaterial);
                    }
                }

                GeneratedComponents.Add(WeaponPlatform);
            }
        }
    }

    // Criar depósitos de munição usando APIs modernas
    if (bHasResourceNodes)
    {
        for (int32 i = 0; i < 8; ++i)
        {
            float Angle = (i / 8.0f) * 2.0f * PI;
            FVector DepotLocation = FVector(
                FMath::Cos(Angle) * (IslandSize * 0.6f),
                FMath::Sin(Angle) * (IslandSize * 0.6f),
                75.0f
            );

            UStaticMeshComponent* AmmoDepot = CreateDefaultSubobject<UStaticMeshComponent>(
                *FString::Printf(TEXT("AmmoDepot_%d_%d"), i, FMath::RandRange(1000, 9999))
            );
            if (AmmoDepot)
            {
                AmmoDepot->SetupAttachment(RootComponent);
                AmmoDepot->SetRelativeLocation(DepotLocation);
                AmmoDepot->SetRelativeScale3D(FVector(1.8f, 1.8f, 2.5f));

                // Configurar como elemento destrutível
                AmmoDepot->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
                AmmoDepot->SetCollisionObjectType(ECollisionChannel::ECC_Destructible);

                // Configurar material explosivo
                if (UMaterialInterface* BaseMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial")))
                {
                    UMaterialInstanceDynamic* ExplosiveMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, this);
                    if (ExplosiveMaterial)
                    {
                        ExplosiveMaterial->SetScalarParameterValue(FName("ExplosivePotential"), 2.5f);
                        ExplosiveMaterial->SetVectorParameterValue(FName("DangerColor"), FLinearColor(1.0f, 0.3f, 0.1f, 1.0f));
                        AmmoDepot->SetMaterial(0, ExplosiveMaterial);
                    }
                }

                GeneratedComponents.Add(AmmoDepot);
            }
        }
    }

    // Criar rampas e elevações usando APIs modernas
    if (bHasElevationChanges)
    {
        for (int32 i = 0; i < 6; ++i)
        {
            float Angle = (i / 6.0f) * 2.0f * PI;
            FVector RampLocation = FVector(
                FMath::Cos(Angle) * (IslandSize * 0.3f),
                FMath::Sin(Angle) * (IslandSize * 0.3f),
                0.0f
            );

            UStaticMeshComponent* TacticalRamp = CreateDefaultSubobject<UStaticMeshComponent>(
                *FString::Printf(TEXT("TacticalRamp_%d_%d"), i, FMath::RandRange(1000, 9999))
            );
            if (TacticalRamp)
            {
                TacticalRamp->SetupAttachment(RootComponent);
                TacticalRamp->SetRelativeLocation(RampLocation);
                TacticalRamp->SetRelativeRotation(FRotator(15.0f, Angle * 180.0f / PI, 0.0f));
                TacticalRamp->SetRelativeScale3D(FVector(4.0f, 1.0f, 0.2f));

                // Configurar superfície tática
                TacticalRamp->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
                TacticalRamp->SetCollisionObjectType(ECollisionChannel::ECC_WorldStatic);

                GeneratedComponents.Add(TacticalRamp);
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::GenerateArsenalIsland - Arsenal island generation completed with %d components"), GeneratedComponents.Num());
}

void AAURACRONPCGIsland::GenerateChaosIsland()
{
    // Gerar ilha do caos usando APIs modernas do UE 5.6
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::GenerateChaosIsland - Generating chaos island with modern UE 5.6 APIs"));

    // Configurar propriedades específicas do caos
    bHasCentralSpire = true;
    bHasEnergyVortex = true;
    bHasAncientRunes = true;
    bHasPortalGateways = true;
    bHasHazardZones = true;

    // Criar espiral central do caos usando APIs modernas
    if (bHasCentralSpire)
    {
        UStaticMeshComponent* ChaosSpire = CreateDefaultSubobject<UStaticMeshComponent>(
            *FString::Printf(TEXT("ChaosSpire_%d"), FMath::RandRange(1000, 9999))
        );
        if (ChaosSpire)
        {
            ChaosSpire->SetupAttachment(RootComponent);
            ChaosSpire->SetRelativeLocation(FVector(0.0f, 0.0f, 400.0f));
            ChaosSpire->SetRelativeScale3D(FVector(1.5f, 1.5f, 8.0f));

            // Configurar material caótico com efeitos dinâmicos
            if (UMaterialInterface* BaseMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial")))
            {
                UMaterialInstanceDynamic* ChaosMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, this);
                if (ChaosMaterial)
                {
                    ChaosMaterial->SetScalarParameterValue(FName("ChaosIntensity"), 3.0f);
                    ChaosMaterial->SetScalarParameterValue(FName("EnergyFlux"), 2.5f);
                    ChaosMaterial->SetVectorParameterValue(FName("ChaosColor"), FLinearColor(0.8f, 0.2f, 0.9f, 1.0f));
                    ChaosSpire->SetMaterial(0, ChaosMaterial);
                }
            }

            GeneratedComponents.Add(ChaosSpire);
        }
    }

    // Criar vórtices de energia usando APIs modernas
    if (bHasEnergyVortex)
    {
        for (int32 i = 0; i < 4; ++i)
        {
            float Angle = (i / 4.0f) * 2.0f * PI;
            FVector VortexLocation = FVector(
                FMath::Cos(Angle) * (IslandSize * 0.7f),
                FMath::Sin(Angle) * (IslandSize * 0.7f),
                250.0f
            );

            UStaticMeshComponent* EnergyVortex = CreateDefaultSubobject<UStaticMeshComponent>(
                *FString::Printf(TEXT("EnergyVortex_%d_%d"), i, FMath::RandRange(1000, 9999))
            );
            if (EnergyVortex)
            {
                EnergyVortex->SetupAttachment(RootComponent);
                EnergyVortex->SetRelativeLocation(VortexLocation);
                EnergyVortex->SetRelativeScale3D(FVector(2.0f, 2.0f, 4.0f));

                // Configurar rotação contínua para efeito de vórtice
                EnergyVortex->SetRelativeRotation(FRotator(0.0f, i * 90.0f, 0.0f));

                // Configurar material de energia caótica
                if (UMaterialInterface* BaseMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial")))
                {
                    UMaterialInstanceDynamic* VortexMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, this);
                    if (VortexMaterial)
                    {
                        VortexMaterial->SetScalarParameterValue(FName("VortexSpeed"), 4.0f);
                        VortexMaterial->SetScalarParameterValue(FName("EnergyLevel"), 2.8f);
                        VortexMaterial->SetVectorParameterValue(FName("VortexColor"), FLinearColor(0.2f, 0.8f, 1.0f, 0.9f));
                        EnergyVortex->SetMaterial(0, VortexMaterial);
                    }
                }

                GeneratedComponents.Add(EnergyVortex);
            }
        }
    }

    // Criar runas ancestrais usando APIs modernas
    if (bHasAncientRunes)
    {
        for (int32 i = 0; i < 16; ++i)
        {
            float Angle = (i / 16.0f) * 2.0f * PI;
            float Distance = IslandSize * (0.3f + (i % 4) * 0.15f);
            FVector RuneLocation = FVector(
                FMath::Cos(Angle) * Distance,
                FMath::Sin(Angle) * Distance,
                25.0f + FMath::Sin(Angle * 3.0f) * 50.0f // Altura variável
            );

            UStaticMeshComponent* AncientRune = CreateDefaultSubobject<UStaticMeshComponent>(
                *FString::Printf(TEXT("AncientRune_%d_%d"), i, FMath::RandRange(1000, 9999))
            );
            if (AncientRune)
            {
                AncientRune->SetupAttachment(RootComponent);
                AncientRune->SetRelativeLocation(RuneLocation);
                AncientRune->SetRelativeRotation(FRotator(90.0f, Angle * 180.0f / PI, 0.0f));
                AncientRune->SetRelativeScale3D(FVector(1.0f, 1.0f, 0.1f));

                // Configurar material rúnico com brilho místico
                if (UMaterialInterface* BaseMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial")))
                {
                    UMaterialInstanceDynamic* RuneMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, this);
                    if (RuneMaterial)
                    {
                        RuneMaterial->SetScalarParameterValue(FName("RunePower"), 1.5f + (i % 3) * 0.5f);
                        RuneMaterial->SetScalarParameterValue(FName("MysticGlow"), 2.0f);
                        RuneMaterial->SetVectorParameterValue(FName("RuneColor"), FLinearColor(1.0f, 0.8f, 0.2f, 1.0f));
                        AncientRune->SetMaterial(0, RuneMaterial);
                    }
                }

                GeneratedComponents.Add(AncientRune);
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::GenerateChaosIsland - Chaos island generation completed with %d components"), GeneratedComponents.Num());
}

void AAURACRONPCGIsland::ApplyMapPhaseEffects()
{
    // Aplicar efeitos da fase do mapa usando APIs modernas do UE 5.6
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::ApplyMapPhaseEffects - Applying map phase effects for phase %d"), (int32)CurrentMapPhase);

    // Aplicar efeitos baseados na fase atual
    switch (CurrentMapPhase)
    {
        case EAURACRONMapPhase::Awakening:
        {
            // Fase 1: Despertar (0-15 minutos) - Conforme documentação
            // Todas as ilhas totalmente emergidas
            SetIslandVisibility(true);
            SetActivityScale(1.0f); // Totalmente ativo conforme documentação
            
            // Aplicar efeitos visuais para fase inicial
            // Trilhos a 50% de poder, efeitos visuais adaptados ao hardware
            for (UActorComponent* Component : GeneratedComponents)
            {
                if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
                {
                    // Aplicar material dinâmico com efeitos de despertar
                    if (UMaterialInstanceDynamic* DynMaterial = Cast<UMaterialInstanceDynamic>(MeshComp->GetMaterial(0)))
                    {
                        DynMaterial->SetScalarParameterValue(FName("PhaseIntensity"), 0.5f); // 50% de poder
                        DynMaterial->SetScalarParameterValue(FName("AwakeningGlow"), 1.0f);
                        DynMaterial->SetVectorParameterValue(FName("PhaseColor"), FLinearColor(1.0f, 0.9f, 0.7f, 1.0f));
                    }
                }
            }
            break;
        }

        case EAURACRONMapPhase::Convergence:
        {
            // Fase da convergência: efeitos de energia crescente
            for (UActorComponent* Component : GeneratedComponents)
            {
                if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
                {
                    if (UMaterialInstanceDynamic* DynMaterial = Cast<UMaterialInstanceDynamic>(MeshComp->GetMaterial(0)))
                    {
                        DynMaterial->SetScalarParameterValue(FName("PhaseIntensity"), 0.8f);
                        DynMaterial->SetScalarParameterValue(FName("ConvergenceEnergy"), 1.8f);
                        DynMaterial->SetVectorParameterValue(FName("PhaseColor"), FLinearColor(0.8f, 1.0f, 0.9f, 1.0f));
                    }
                }
            }
            break;
        }

        case EAURACRONMapPhase::Intensification:
        {
            // Fase da intensificação: efeitos dramáticos
            for (UActorComponent* Component : GeneratedComponents)
            {
                if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
                {
                    if (UMaterialInstanceDynamic* DynMaterial = Cast<UMaterialInstanceDynamic>(MeshComp->GetMaterial(0)))
                    {
                        DynMaterial->SetScalarParameterValue(FName("PhaseIntensity"), 1.0f);
                        DynMaterial->SetScalarParameterValue(FName("IntensificationPower"), 2.5f);
                        DynMaterial->SetVectorParameterValue(FName("PhaseColor"), FLinearColor(1.0f, 0.6f, 0.3f, 1.0f));
                    }
                }
            }
            break;
        }

        case EAURACRONMapPhase::Resolution:
        {
            // Fase da resolução: efeitos épicos máximos
            for (UActorComponent* Component : GeneratedComponents)
            {
                if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
                {
                    if (UMaterialInstanceDynamic* DynMaterial = Cast<UMaterialInstanceDynamic>(MeshComp->GetMaterial(0)))
                    {
                        DynMaterial->SetScalarParameterValue(FName("PhaseIntensity"), 1.5f);
                        DynMaterial->SetScalarParameterValue(FName("ResolutionMajesty"), 3.0f);
                        DynMaterial->SetVectorParameterValue(FName("PhaseColor"), FLinearColor(0.7f, 0.4f, 1.0f, 1.0f));
                    }
                }
            }
            break;
        }
    }

    // Aplicar efeitos específicos do tipo de ilha
    switch (IslandType)
    {
        case EAURACRONIslandType::Sanctuary:
            // Santuários amplificam efeitos de cura baseados na fase
            if (bHasHealingFountain)
            {
                float HealingMultiplier = 1.0f + (static_cast<int32>(CurrentMapPhase) * 0.3f);
                // Aplicar multiplicador aos componentes de cura
                for (UActorComponent* Component : GeneratedComponents)
                {
                    if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
                    {
                        if (MeshComp->GetName().Contains(TEXT("HealingFountain")))
                        {
                            if (UMaterialInstanceDynamic* DynMaterial = Cast<UMaterialInstanceDynamic>(MeshComp->GetMaterial(0)))
                            {
                                DynMaterial->SetScalarParameterValue(FName("HealingIntensity"), 1.5f * HealingMultiplier);
                            }
                        }
                    }
                }
            }
            break;

        case EAURACRONIslandType::Arsenal:
            // Arsenal aumenta potência tática baseada na fase
            if (bHasStrategicPoints)
            {
                float TacticalMultiplier = 1.0f + (static_cast<int32>(CurrentMapPhase) * 0.4f);
                for (UActorComponent* Component : GeneratedComponents)
                {
                    if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
                    {
                        if (MeshComp->GetName().Contains(TEXT("WeaponPlatform")))
                        {
                            if (UMaterialInstanceDynamic* DynMaterial = Cast<UMaterialInstanceDynamic>(MeshComp->GetMaterial(0)))
                            {
                                DynMaterial->SetScalarParameterValue(FName("TacticalLevel"), TacticalMultiplier);
                            }
                        }
                    }
                }
            }
            break;

        case EAURACRONIslandType::Chaos:
            // Chaos Islands se tornam mais imprevisíveis
            if (bHasEnergyVortex)
            {
                float ChaosMultiplier = 1.0f + (static_cast<int32>(CurrentMapPhase) * 0.5f);
                for (UActorComponent* Component : GeneratedComponents)
                {
                    if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
                    {
                        if (MeshComp->GetName().Contains(TEXT("EnergyVortex")))
                        {
                            if (UMaterialInstanceDynamic* DynMaterial = Cast<UMaterialInstanceDynamic>(MeshComp->GetMaterial(0)))
                            {
                                DynMaterial->SetScalarParameterValue(FName("ChaosIntensity"), 3.0f * ChaosMultiplier);
                                DynMaterial->SetScalarParameterValue(FName("VortexSpeed"), 4.0f * ChaosMultiplier);
                            }
                        }
                    }
                }
            }
            break;
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::ApplyMapPhaseEffects - Map phase effects applied successfully"));
}

void AAURACRONPCGIsland::ApplyActivityScale()
{
    // Aplicar escala de atividade usando APIs modernas do UE 5.6
    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGIsland::ApplyActivityScale - Applying activity scale %.2f"), ActivityScale);

    // Aplicar escala de atividade a todos os componentes gerados
    for (UActorComponent* Component : GeneratedComponents)
    {
        if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
        {
            // Controlar visibilidade baseada na escala de atividade
            bool bShouldBeVisible = ActivityScale > 0.1f;
            MeshComp->SetVisibility(bShouldBeVisible);

            // Controlar colisão baseada na escala de atividade
            if (ActivityScale > 0.5f)
            {
                MeshComp->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
            }
            else if (ActivityScale > 0.2f)
            {
                MeshComp->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
            }
            else
            {
                MeshComp->SetCollisionEnabled(ECollisionEnabled::NoCollision);
            }

            // Aplicar escala visual baseada na atividade
            FVector BaseScale = FVector(1.0f, 1.0f, 1.0f);
            if (MeshComp->GetName().Contains(TEXT("HealingFountain")))
            {
                BaseScale = FVector(2.0f, 2.0f, 3.0f);
            }
            else if (MeshComp->GetName().Contains(TEXT("ArsenalTower")))
            {
                BaseScale = FVector(2.5f, 2.5f, 6.0f);
            }
            else if (MeshComp->GetName().Contains(TEXT("ChaosSpire")))
            {
                BaseScale = FVector(1.5f, 1.5f, 8.0f);
            }

            FVector ScaledSize = BaseScale * FMath::Lerp(0.5f, 1.2f, ActivityScale);
            MeshComp->SetRelativeScale3D(ScaledSize);

            // Aplicar efeitos de material baseados na atividade
            if (UMaterialInstanceDynamic* DynMaterial = Cast<UMaterialInstanceDynamic>(MeshComp->GetMaterial(0)))
            {
                DynMaterial->SetScalarParameterValue(FName("ActivityLevel"), ActivityScale);
                DynMaterial->SetScalarParameterValue(FName("OpacityMultiplier"), FMath::Clamp(ActivityScale, 0.3f, 1.0f));

                // Aplicar efeitos específicos baseados no tipo de componente
                if (MeshComp->GetName().Contains(TEXT("HealingFountain")))
                {
                    DynMaterial->SetScalarParameterValue(FName("HealingIntensity"), 1.5f * ActivityScale);
                }
                else if (MeshComp->GetName().Contains(TEXT("WeaponPlatform")))
                {
                    DynMaterial->SetScalarParameterValue(FName("TacticalLevel"), ActivityScale * 3.0f);
                }
                else if (MeshComp->GetName().Contains(TEXT("EnergyVortex")))
                {
                    DynMaterial->SetScalarParameterValue(FName("VortexSpeed"), 4.0f * ActivityScale);
                    DynMaterial->SetScalarParameterValue(FName("EnergyLevel"), 2.8f * ActivityScale);
                }
                else if (MeshComp->GetName().Contains(TEXT("AncientRune")))
                {
                    DynMaterial->SetScalarParameterValue(FName("RunePower"), (1.5f + FMath::RandRange(0.0f, 1.5f)) * ActivityScale);
                    DynMaterial->SetScalarParameterValue(FName("MysticGlow"), 2.0f * ActivityScale);
                }
            }
        }
    }

    // Aplicar escala de atividade ao componente PCG
    if (PCGComponent && IsValid(PCGComponent))
    {
        // Controlar geração PCG baseada na atividade
        if (ActivityScale > 0.3f)
        {
            PCGComponent->SetComponentTickEnabled(true);

            // Regenerar se a atividade mudou significativamente
            static float LastActivityScale = -1.0f;
            if (FMath::Abs(ActivityScale - LastActivityScale) > 0.2f)
            {
                PCGComponent->GenerateLocal(false); // Regeneração não-bloqueante
                LastActivityScale = ActivityScale;
            }
        }
        else
        {
            PCGComponent->SetComponentTickEnabled(false);
        }
    }

    // Aplicar escala de atividade ao mesh principal da ilha
    if (IslandMesh && IsValid(IslandMesh))
    {
        IslandMesh->SetVisibility(ActivityScale > 0.1f);

        // Aplicar LOD baseado na atividade
        if (ActivityScale > 0.8f)
        {
            IslandMesh->SetForcedLodModel(0); // LOD mais alto
        }
        else if (ActivityScale > 0.5f)
        {
            IslandMesh->SetForcedLodModel(1); // LOD médio
        }
        else if (ActivityScale > 0.2f)
        {
            IslandMesh->SetForcedLodModel(2); // LOD baixo
        }
        else
        {
            IslandMesh->SetForcedLodModel(3); // LOD mínimo
        }
    }

    // Controlar tick do ator baseado na atividade
    SetActorTickEnabled(ActivityScale > 0.1f);

    // Controlar visibilidade geral do ator
    SetActorHiddenInGame(ActivityScale <= 0.05f);

    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGIsland::ApplyActivityScale - Activity scale applied to %d components"), GeneratedComponents.Num());
}

void AAURACRONPCGIsland::OnMapContraction(float ContractionFactor)
{
    UE_LOG(LogTemp, Log, TEXT("OnMapContraction - Aplicando contração %.2f à ilha %d"), ContractionFactor, (int32)IslandType);

    // Aplicar contração à posição da ilha
    FVector MapCenter = FVector::ZeroVector; // Centro do mapa
    FVector CurrentLocation = GetActorLocation();
    FVector DirectionToCenter = (MapCenter - CurrentLocation).GetSafeNormal();
    FVector NewLocation = CurrentLocation + DirectionToCenter * (1.0f - ContractionFactor) * CurrentLocation.Size2D();

    // Preservar altura original
    NewLocation.Z = CurrentLocation.Z;

    SetActorLocation(NewLocation);

    // Aplicar contração ao tamanho da ilha
    IslandSize *= ContractionFactor;

    // Atualizar escala dos componentes baseado na contração
    FVector NewScale = GetActorScale3D() * ContractionFactor;
    SetActorScale3D(NewScale);

    // Atualizar componente de colisão
    if (IsValid(CollisionComponent))
    {
        float NewRadius = CollisionComponent->GetScaledSphereRadius() * ContractionFactor;
        CollisionComponent->SetSphereRadius(NewRadius);
    }

    // Aplicar contração específica baseada no tipo de ilha
    switch (IslandType)
    {
        case EAURACRONIslandType::Sanctuary:
            // Para santuários, manter funcionalidade de cura mas reduzir alcance
            if (IsValid(PCGComponent))
            {
                // Reduzir raio de efeito de cura
                float HealingRadius = 1000.0f * ContractionFactor;
                // Usar API moderna do UE 5.6 para definir parâmetros PCG
                if (UPCGGraph* PCGGraph = PCGComponent->GetGraph())
                {
                    // Implementação usando APIs modernas do PCG no UE 5.6 - UPCGGraph herda de UPCGGraphInterface
                    UPCGGraphParametersHelpers::SetFloatParameter(PCGGraph, FName("HealingRadius"), HealingRadius);
                }
            }
            break;

        case EAURACRONIslandType::Nexus:
            // Para nexus, reduzir alcance de conexões
            if (IsValid(PCGComponent))
            {
                float ConnectionRadius = 1500.0f * ContractionFactor;
                if (UPCGGraph* PCGGraph = PCGComponent->GetGraph())
                {
                    UPCGGraphParametersHelpers::SetFloatParameter(PCGGraph, FName("ConnectionRadius"), ConnectionRadius);
                }
            }
            break;

        case EAURACRONIslandType::Battlefield:
            // Para campos de batalha, reduzir área de combate
            if (IsValid(PCGComponent))
            {
                float BattleRadius = 800.0f * ContractionFactor;
                if (UPCGGraph* PCGGraph = PCGComponent->GetGraph())
                {
                    UPCGGraphParametersHelpers::SetFloatParameter(PCGGraph, FName("BattleRadius"), BattleRadius);
                }
            }
            break;
    }

    // Recalcular parâmetros da ilha
    CalculateIslandParameters();

    UE_LOG(LogTemp, Log, TEXT("OnMapContraction - Contração aplicada com sucesso à ilha"));
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES DE BOUNDARY EFFECTS - UE 5.6 MODERN APIS
// ========================================

void AAURACRONPCGIsland::SetBoundaryBlurStrength(float BlurStrength)
{
    // Implementação robusta usando APIs modernas do UE 5.6
    BlurStrength = FMath::Clamp(BlurStrength, 0.0f, 10.0f);
    CurrentBoundaryBlurStrength = BlurStrength;

    // Aplicar blur aos componentes de mesh da ilha
    TArray<UStaticMeshComponent*> MeshComponents;
    GetComponents<UStaticMeshComponent>(MeshComponents);

    for (UStaticMeshComponent* MeshComp : MeshComponents)
    {
        if (MeshComp && IsValid(MeshComp))
        {
            // Usar material parameters para controlar blur
            if (UMaterialInterface* Material = MeshComp->GetMaterial(0))
            {
                // Criar dynamic material instance para controlar blur
                UMaterialInstanceDynamic* DynamicMaterial = MeshComp->CreateAndSetMaterialInstanceDynamic(0);
                if (DynamicMaterial)
                {
                    DynamicMaterial->SetScalarParameterValue(TEXT("BlurStrength"), BlurStrength);
                    DynamicMaterial->SetScalarParameterValue(TEXT("BoundaryEffect"), BlurStrength > 0.0f ? 1.0f : 0.0f);
                }
            }
        }
    }

    // Aplicar blur aos efeitos de partículas Niagara
    TArray<UNiagaraComponent*> NiagaraComponents;
    GetComponents<UNiagaraComponent>(NiagaraComponents);

    for (UNiagaraComponent* NiagaraComp : NiagaraComponents)
    {
        if (NiagaraComp && IsValid(NiagaraComp))
        {
            NiagaraComp->SetFloatParameter(TEXT("BlurStrength"), BlurStrength);
            NiagaraComp->SetFloatParameter(TEXT("BoundaryIntensity"), BlurStrength * 0.5f);
        }
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGIsland::SetBoundaryBlurStrength - Blur strength set to %.2f"), BlurStrength);
}

void AAURACRONPCGIsland::UpdateBoundaryEffects()
{
    // Implementação robusta para atualizar todos os efeitos de boundary

    // Atualizar efeitos baseados no tipo de ilha
    switch (IslandType)
    {
        case EAURACRONIslandType::Sanctuary:
            // Efeitos dourados e curativos para santuários
            SetBoundaryBlurStrength(2.0f);
            break;

        case EAURACRONIslandType::Nexus:
            // Efeitos azuis e energéticos para nexus
            SetBoundaryBlurStrength(4.0f);
            break;

        case EAURACRONIslandType::Battlefield:
            // Efeitos vermelhos e intensos para campos de batalha
            SetBoundaryBlurStrength(6.0f);
            break;

        default:
            SetBoundaryBlurStrength(1.0f);
            break;
    }

    // Atualizar efeitos baseados na fase do mapa
    float PhaseMultiplier = 1.0f;
    switch (CurrentMapPhase)
    {
        case EAURACRONMapPhase::Awakening:
            PhaseMultiplier = 0.5f;
            break;
        case EAURACRONMapPhase::Convergence:
            PhaseMultiplier = 1.0f;
            break;
        case EAURACRONMapPhase::Intensification:
            PhaseMultiplier = 1.5f;
            break;
        case EAURACRONMapPhase::Resolution:
            PhaseMultiplier = 2.0f;
            break;
    }

    // Aplicar multiplicador de fase
    SetBoundaryBlurStrength(GetBoundaryBlurStrength() * PhaseMultiplier);

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGIsland::UpdateBoundaryEffects - Boundary effects updated for island type %d and phase %d"),
           (int32)IslandType, (int32)CurrentMapPhase);
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES DE MAP PHASE - UE 5.6 MODERN APIS
// ========================================

void AAURACRONPCGIsland::SetCurrentMapPhase(EAURACRONMapPhase NewPhase)
{
    // Implementação robusta para definir a fase atual do mapa
    if (CurrentMapPhase != NewPhase)
    {
        EAURACRONMapPhase OldPhase = CurrentMapPhase;
        CurrentMapPhase = NewPhase;

        // Aplicar configuração automática para a nova fase
        ApplyPhaseConfiguration(NewPhase);

        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::SetCurrentMapPhase - Phase changed from %d to %d"),
               (int32)OldPhase, (int32)NewPhase);
    }
}

void AAURACRONPCGIsland::ApplyPhaseConfiguration(EAURACRONMapPhase Phase)
{
    // Implementação robusta para aplicar configuração específica da fase
    switch (Phase)
    {
        case EAURACRONMapPhase::Awakening:
            // Configuração para fase de despertar
            ActivityScale = 0.3f;
            SetBoundaryBlurStrength(1.0f);

            // Configurar PCG para modo exploração
            if (PCGComponent && IsValid(PCGComponent))
            {
                // Usar APIs modernas do UE 5.6 para PCG
                PCGComponent->GenerateLocal(true);
            }
            break;

        case EAURACRONMapPhase::Convergence:
            // Configuração para fase de convergência
            ActivityScale = 0.6f;
            SetBoundaryBlurStrength(2.5f);

            // Intensificar efeitos visuais
            if (PCGComponent && IsValid(PCGComponent))
            {
                PCGComponent->GenerateLocal(true);
            }
            break;

        case EAURACRONMapPhase::Intensification:
            // Configuração para fase de intensificação
            ActivityScale = 0.9f;
            SetBoundaryBlurStrength(4.0f);

            // Máxima intensidade visual
            if (PCGComponent && IsValid(PCGComponent))
            {
                PCGComponent->GenerateLocal(true);
            }
            break;

        case EAURACRONMapPhase::Resolution:
            // Configuração para fase de resolução
            ActivityScale = 1.0f;
            SetBoundaryBlurStrength(6.0f);

            // Efeitos épicos para resolução
            if (PCGComponent && IsValid(PCGComponent))
            {
                PCGComponent->GenerateLocal(true);
            }
            break;
    }

    // Atualizar todos os efeitos de boundary
    UpdateBoundaryEffects();

    // Forçar atualização dos componentes visuais
    MarkComponentsRenderStateDirty();

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::ApplyPhaseConfiguration - Configuration applied for phase %d"), (int32)Phase);
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES QUE ESTAVAM FALTANDO - UE 5.6 MODERN APIS
// ========================================

void AAURACRONPCGIsland::ConfigureForAwakeningPhase(bool bEnable)
{
    // Implementação robusta para configurar para fase Awakening
    if (bEnable)
    {
        // Configurar ilha para fase inicial
        SetIslandScale(0.8f);
        SetEmergenceLevel(0.3f);
        SetActivityLevel(0.4f);

        // Ativar efeitos visuais suaves
        TArray<UNiagaraComponent*> NiagaraComponents;
        GetComponents<UNiagaraComponent>(NiagaraComponents);

        for (UNiagaraComponent* NiagaraComp : NiagaraComponents)
        {
            if (NiagaraComp && IsValid(NiagaraComp))
            {
                NiagaraComp->SetFloatParameter(TEXT("AwakeningPhase"), 1.0f);
                NiagaraComp->SetFloatParameter(TEXT("PhaseIntensity"), 0.4f);
            }
        }

        // Configurar iluminação suave
        TArray<ULightComponent*> LightComponents;
        GetComponents<ULightComponent>(LightComponents);

        for (ULightComponent* LightComp : LightComponents)
        {
            if (LightComp && IsValid(Cast<UObject>(LightComp)))
            {
                float CurrentIntensity = LightComp->Intensity;
                LightComp->SetIntensity(CurrentIntensity * 0.6f);
            }
        }
    }
    else
    {
        // Desativar configurações da fase Awakening
        SetIslandScale(1.0f);
        SetEmergenceLevel(0.0f);
        SetActivityLevel(0.0f);
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::ConfigureForAwakeningPhase - Awakening phase %s"), bEnable ? TEXT("enabled") : TEXT("disabled"));
}

void AAURACRONPCGIsland::ConfigureForConvergencePhase(bool bEnableConvergence, bool bEnableIntensification, bool bEnableResolution)
{
    // Implementação robusta para configurar para fase Convergence

    if (bEnableConvergence)
    {
        // Configurar ilha para fase de convergência
        SetIslandScale(1.2f);
        SetEmergenceLevel(0.6f);
        SetActivityLevel(0.7f);

        // Ativar efeitos visuais de convergência
        TArray<UNiagaraComponent*> NiagaraComponents;
        GetComponents<UNiagaraComponent>(NiagaraComponents);

        for (UNiagaraComponent* NiagaraComp : NiagaraComponents)
        {
            if (NiagaraComp && IsValid(NiagaraComp))
            {
                NiagaraComp->SetFloatParameter(TEXT("ConvergencePhase"), 1.0f);
                NiagaraComp->SetFloatParameter(TEXT("PhaseIntensity"), 0.7f);
            }
        }
    }

    if (bEnableIntensification)
    {
        // Intensificar efeitos para fase de intensificação
        SetIslandScale(1.5f);
        SetEmergenceLevel(0.8f);
        SetActivityLevel(0.9f);

        TArray<UNiagaraComponent*> NiagaraComponents;
        GetComponents<UNiagaraComponent>(NiagaraComponents);

        for (UNiagaraComponent* NiagaraComp : NiagaraComponents)
        {
            if (NiagaraComp && IsValid(NiagaraComp))
            {
                NiagaraComp->SetFloatParameter(TEXT("IntensificationPhase"), 1.0f);
                NiagaraComp->SetFloatParameter(TEXT("PhaseIntensity"), 0.9f);
            }
        }
    }

    if (bEnableResolution)
    {
        // Configurar para fase de resolução
        SetIslandScale(2.0f);
        SetEmergenceLevel(1.0f);
        SetActivityLevel(1.0f);
        SetFullyEmerged(true);

        TArray<UNiagaraComponent*> NiagaraComponents;
        GetComponents<UNiagaraComponent>(NiagaraComponents);

        for (UNiagaraComponent* NiagaraComp : NiagaraComponents)
        {
            if (NiagaraComp && IsValid(NiagaraComp))
            {
                NiagaraComp->SetFloatParameter(TEXT("ResolutionPhase"), 1.0f);
                NiagaraComp->SetFloatParameter(TEXT("PhaseIntensity"), 1.0f);
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::ConfigureForConvergencePhase - Convergence: %s, Intensification: %s, Resolution: %s"),
           bEnableConvergence ? TEXT("true") : TEXT("false"),
           bEnableIntensification ? TEXT("true") : TEXT("false"),
           bEnableResolution ? TEXT("true") : TEXT("false"));
}

void AAURACRONPCGIsland::SetFullyEmerged(bool bFullyEmerged)
{
    // Implementação robusta para definir ilha como totalmente emergida
    bIsFullyEmerged = bFullyEmerged;

    if (bFullyEmerged)
    {
        // Configurar ilha como totalmente emergida
        SetEmergenceLevel(1.0f);
        SetActivityLevel(1.0f);
        SetIslandScale(2.0f);

        // Ativar todos os efeitos visuais
        TArray<UNiagaraComponent*> NiagaraComponents;
        GetComponents<UNiagaraComponent>(NiagaraComponents);

        for (UNiagaraComponent* NiagaraComp : NiagaraComponents)
        {
            if (NiagaraComp && IsValid(NiagaraComp))
            {
                NiagaraComp->SetFloatParameter(TEXT("FullyEmerged"), 1.0f);
                NiagaraComp->SetFloatParameter(TEXT("EmergenceLevel"), 1.0f);
            }
        }

        // Maximizar iluminação
        TArray<ULightComponent*> LightComponents;
        GetComponents<ULightComponent>(LightComponents);

        for (ULightComponent* LightComp : LightComponents)
        {
            if (LightComp && IsValid(Cast<UObject>(LightComp)))
            {
                float CurrentIntensity = LightComp->Intensity;
                LightComp->SetIntensity(CurrentIntensity * 2.0f);
            }
        }
    }
    else
    {
        // Reduzir emergência
        SetEmergenceLevel(0.5f);
        SetActivityLevel(0.5f);
        SetIslandScale(1.0f);
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::SetFullyEmerged - Island fully emerged: %s"), bFullyEmerged ? TEXT("true") : TEXT("false"));
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES AUXILIARES QUE ESTAVAM FALTANDO - UE 5.6 MODERN APIS
// ========================================

void AAURACRONPCGIsland::SetIslandScale(float NewScale)
{
    // Implementação robusta para definir escala da ilha
    NewScale = FMath::Clamp(NewScale, 0.1f, 5.0f);

    // Aplicar escala ao componente raiz
    if (GetRootComponent())
    {
        GetRootComponent()->SetWorldScale3D(FVector(NewScale));
    }

    // Aplicar escala ao mesh principal
    if (IslandMesh && IsValid(IslandMesh))
    {
        IslandMesh->SetWorldScale3D(FVector(NewScale));
    }

    // Aplicar escala aos componentes de colisão
    if (CollisionComponent && IsValid(CollisionComponent))
    {
        CollisionComponent->SetSphereRadius(CollisionComponent->GetScaledSphereRadius() * NewScale);
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGIsland::SetIslandScale - Scale set to %.2f"), NewScale);
}

void AAURACRONPCGIsland::SetEmergenceLevel(float NewLevel)
{
    // Implementação robusta para definir nível de emergência
    NewLevel = FMath::Clamp(NewLevel, 0.0f, 1.0f);

    // Atualizar posição Z baseada no nível de emergência
    FVector CurrentLocation = this->GetActorLocation();
    float BaseHeight = -500.0f; // Altura base submersa
    float MaxHeight = 500.0f;   // Altura máxima emergida

    CurrentLocation.Z = FMath::Lerp(BaseHeight, MaxHeight, NewLevel);
    this->SetActorLocation(CurrentLocation);

    // Atualizar efeitos visuais baseados na emergência
    TArray<UNiagaraComponent*> NiagaraComponents;
    this->GetComponents<UNiagaraComponent>(NiagaraComponents);

    for (UNiagaraComponent* NiagaraComp : NiagaraComponents)
    {
        if (NiagaraComp && IsValid(NiagaraComp))
        {
            NiagaraComp->SetFloatParameter(TEXT("EmergenceLevel"), NewLevel);
        }
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGIsland::SetEmergenceLevel - Emergence level set to %.2f"), NewLevel);
}

void AAURACRONPCGIsland::SetActivityLevel(float NewLevel)
{
    // Implementação robusta para definir nível de atividade
    NewLevel = FMath::Clamp(NewLevel, 0.0f, 1.0f);
    ActivityScale = NewLevel;

    // Atualizar efeitos visuais baseados na atividade
    TArray<UNiagaraComponent*> NiagaraComponents;
    this->GetComponents<UNiagaraComponent>(NiagaraComponents);

    for (UNiagaraComponent* NiagaraComp : NiagaraComponents)
    {
        if (NiagaraComp && IsValid(NiagaraComp))
        {
            NiagaraComp->SetFloatParameter(TEXT("ActivityLevel"), NewLevel);
            NiagaraComp->SetFloatParameter(TEXT("ActivityScale"), NewLevel);
        }
    }

    // Atualizar iluminação baseada na atividade
    TArray<ULightComponent*> LightComponents;
    this->GetComponents<ULightComponent>(LightComponents);

    for (ULightComponent* LightComp : LightComponents)
    {
        if (LightComp && IsValid(Cast<UObject>(LightComp)))
        {
            float BaseIntensity = 1000.0f; // Intensidade base
            LightComp->SetIntensity(BaseIntensity * NewLevel);
        }
    }

    // Regenerar PCG se necessário
    if (PCGComponent && IsValid(PCGComponent) && NewLevel > 0.0f)
    {
        PCGComponent->GenerateLocal(false);
    }

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGIsland::SetActivityLevel - Activity level set to %.2f"), NewLevel);
}

// ========================================
// IMPLEMENTAÇÕES DOS SISTEMAS DA DOCUMENTAÇÃO - UE 5.6 MODERN APIS
// ========================================

void AAURACRONPCGIsland::InitializeTrilhosSystems()
{
    // Implementação robusta dos Trilhos dinâmicos conforme documentação
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::InitializeTrilhosSystems - Inicializando sistemas de Trilhos dinâmicos"));

    // Inicializar Solar Trilhos (energia dourada)
    if (!bHasSolarTrilho)
    {
        bHasSolarTrilho = true;
        SolarTrilhoIntensity = 0.5f; // 50% de poder conforme documentação fase Despertar

        // Criar componente Niagara para Solar Trilho
        USolarTrilhoComponent = CreateDefaultSubobject<UNiagaraComponent>(
            *FString::Printf(TEXT("SolarTrilho_%d"), FMath::RandRange(1000, 9999))
        );
        if (USolarTrilhoComponent)
        {
            USolarTrilhoComponent->SetupAttachment(RootComponent);
            USolarTrilhoComponent->SetRelativeLocation(FVector(0.0f, 0.0f, 100.0f));

            // Configurar parâmetros do Solar Trilho
            USolarTrilhoComponent->SetFloatParameter(FName("TrilhoIntensity"), SolarTrilhoIntensity);
            USolarTrilhoComponent->SetColorParameter(FName("TrilhoColor"), FLinearColor(1.0f, 0.8f, 0.2f, 1.0f)); // Dourado
            USolarTrilhoComponent->SetFloatParameter(FName("MovementBoost"), 1.5f);
            USolarTrilhoComponent->SetFloatParameter(FName("HealthRegen"), 2.0f);

            GeneratedComponents.Add(USolarTrilhoComponent);
        }
    }

    // Inicializar Axis Trilhos (energia prata neutra)
    if (!bHasAxisTrilho)
    {
        bHasAxisTrilho = true;
        AxisTrilhoIntensity = 0.5f;

        // Criar componente Niagara para Axis Trilho
        UAxisTrilhoComponent = CreateDefaultSubobject<UNiagaraComponent>(
            *FString::Printf(TEXT("AxisTrilho_%d"), FMath::RandRange(1000, 9999))
        );
        if (UAxisTrilhoComponent)
        {
            UAxisTrilhoComponent->SetupAttachment(RootComponent);
            UAxisTrilhoComponent->SetRelativeLocation(FVector(200.0f, 0.0f, 100.0f));

            // Configurar parâmetros do Axis Trilho
            UAxisTrilhoComponent->SetFloatParameter(FName("TrilhoIntensity"), AxisTrilhoIntensity);
            UAxisTrilhoComponent->SetColorParameter(FName("TrilhoColor"), FLinearColor(0.7f, 0.7f, 0.8f, 1.0f)); // Prata
            UAxisTrilhoComponent->SetFloatParameter(FName("TransitionSpeed"), 2.0f);
            UAxisTrilhoComponent->SetFloatParameter(FName("GravityDistortion"), 1.2f);

            GeneratedComponents.Add(UAxisTrilhoComponent);
        }
    }

    // Inicializar Lunar Trilhos (energia azul etérea)
    if (!bHasLunarTrilho)
    {
        bHasLunarTrilho = true;
        LunarTrilhoIntensity = 0.5f;

        // Criar componente Niagara para Lunar Trilho
        ULunarTrilhoComponent = CreateDefaultSubobject<UNiagaraComponent>(
            *FString::Printf(TEXT("LunarTrilho_%d"), FMath::RandRange(1000, 9999))
        );
        if (ULunarTrilhoComponent)
        {
            ULunarTrilhoComponent->SetupAttachment(RootComponent);
            ULunarTrilhoComponent->SetRelativeLocation(FVector(-200.0f, 0.0f, 100.0f));

            // Configurar parâmetros do Lunar Trilho
            ULunarTrilhoComponent->SetFloatParameter(FName("TrilhoIntensity"), LunarTrilhoIntensity);
            ULunarTrilhoComponent->SetColorParameter(FName("TrilhoColor"), FLinearColor(0.3f, 0.6f, 1.0f, 0.8f)); // Azul etéreo
            ULunarTrilhoComponent->SetFloatParameter(FName("StealthBonus"), 1.8f);
            ULunarTrilhoComponent->SetFloatParameter(FName("VisionRange"), 2.5f);

            GeneratedComponents.Add(ULunarTrilhoComponent);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::InitializeTrilhosSystems - Sistemas de Trilhos inicializados com sucesso"));
}

void AAURACRONPCGIsland::UpdateTrilhosSystems()
{
    // Atualização robusta dos sistemas de Trilhos baseada no ciclo dia/noite e fases
    if (!IsValid(this)) return;

    // Atualizar Solar Trilho baseado na posição do sol
    if (USolarTrilhoComponent && IsValid(USolarTrilhoComponent))
    {
        // Simular ciclo solar - mais forte ao meio-dia
        float TimeOfDay = FMath::Fmod(GetWorld()->GetTimeSeconds() / 60.0f, 24.0f); // Ciclo de 24 minutos = 24 horas
        float SolarStrength = FMath::Max(0.2f, FMath::Sin((TimeOfDay - 6.0f) * PI / 12.0f)); // Pico ao meio-dia

        SolarTrilhoIntensity = SolarStrength * (0.5f + static_cast<int32>(CurrentMapPhase) * 0.2f);
        USolarTrilhoComponent->SetFloatParameter(FName("TrilhoIntensity"), SolarTrilhoIntensity);
        USolarTrilhoComponent->SetFloatParameter(FName("SolarCycle"), SolarStrength);
    }

    // Atualizar Axis Trilho baseado no controle de equipe
    if (UAxisTrilhoComponent && IsValid(UAxisTrilhoComponent))
    {
        // Ativar baseado no controle de pontos nexus (simulado)
        float TeamControlFactor = ActivityScale; // Usar ActivityScale como proxy
        AxisTrilhoIntensity = TeamControlFactor * (0.5f + static_cast<int32>(CurrentMapPhase) * 0.15f);
        UAxisTrilhoComponent->SetFloatParameter(FName("TrilhoIntensity"), AxisTrilhoIntensity);
        UAxisTrilhoComponent->SetFloatParameter(FName("TeamControl"), TeamControlFactor);
    }

    // Atualizar Lunar Trilho baseado nas fases lunares
    if (ULunarTrilhoComponent && IsValid(ULunarTrilhoComponent))
    {
        // Simular fases lunares - mais forte à noite
        float TimeOfDay = FMath::Fmod(GetWorld()->GetTimeSeconds() / 60.0f, 24.0f);
        float LunarStrength = (TimeOfDay < 6.0f || TimeOfDay > 18.0f) ? 1.0f : 0.3f; // Forte à noite

        LunarTrilhoIntensity = LunarStrength * (0.5f + static_cast<int32>(CurrentMapPhase) * 0.25f);
        ULunarTrilhoComponent->SetFloatParameter(FName("TrilhoIntensity"), LunarTrilhoIntensity);
        ULunarTrilhoComponent->SetFloatParameter(FName("LunarPhase"), LunarStrength);

        // Controlar visibilidade - apenas visível à noite
        ULunarTrilhoComponent->SetVisibility(LunarStrength > 0.5f);
    }
}

void AAURACRONPCGIsland::InitializeFluxoPrismal()
{
    // Implementação robusta do Fluxo Prismal serpentino conforme documentação
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::InitializeFluxoPrismal - Inicializando Fluxo Prismal serpentino"));

    if (!bHasFluxoPrismal)
    {
        bHasFluxoPrismal = true;
        FluxoPrismalIntensity = 1.0f;

        // Criar componente Niagara para Fluxo Prismal
        UFluxoPrismalComponent = CreateDefaultSubobject<UNiagaraComponent>(
            *FString::Printf(TEXT("FluxoPrismal_%d"), FMath::RandRange(1000, 9999))
        );
        if (UFluxoPrismalComponent)
        {
            UFluxoPrismalComponent->SetupAttachment(RootComponent);
            UFluxoPrismalComponent->SetRelativeLocation(FVector(0.0f, 0.0f, 50.0f));

            // Configurar parâmetros do Fluxo Prismal serpentino
            UFluxoPrismalComponent->SetFloatParameter(FName("FluxoIntensity"), FluxoPrismalIntensity);
            UFluxoPrismalComponent->SetColorParameter(FName("FluxoColor"), FLinearColor::White); // Neutro inicialmente
            UFluxoPrismalComponent->SetFloatParameter(FName("SerpentinePattern"), 1.0f);
            UFluxoPrismalComponent->SetFloatParameter(FName("FlowSpeed"), 2.0f);
            UFluxoPrismalComponent->SetFloatParameter(FName("Width"), 30.0f); // 20-50 unidades conforme documentação
            UFluxoPrismalComponent->SetFloatParameter(FName("PrismaticEffect"), 2.5f);

            GeneratedComponents.Add(UFluxoPrismalComponent);
        }

        // Criar componente de áudio para o Fluxo Prismal
        UFluxoPrismalAudioComponent = CreateDefaultSubobject<UAudioComponent>(
            *FString::Printf(TEXT("FluxoPrismalAudio_%d"), FMath::RandRange(1000, 9999))
        );
        if (UFluxoPrismalAudioComponent)
        {
            UFluxoPrismalAudioComponent->SetupAttachment(RootComponent);
            UFluxoPrismalAudioComponent->SetRelativeLocation(FVector(0.0f, 0.0f, 50.0f));
            UFluxoPrismalAudioComponent->bAutoActivate = true;
            UFluxoPrismalAudioComponent->SetVolumeMultiplier(0.7f);

            GeneratedComponents.Add(UFluxoPrismalAudioComponent);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::InitializeFluxoPrismal - Fluxo Prismal inicializado com sucesso"));
}

void AAURACRONPCGIsland::UpdateFluxoPrismal()
{
    // Atualização robusta do Fluxo Prismal baseada no controle de equipe e fases
    if (!UFluxoPrismalComponent || !IsValid(UFluxoPrismalComponent)) return;

    // Atualizar padrão serpentino - muda a cada 10 minutos conforme documentação
    float GameTime = GetWorld()->GetTimeSeconds();
    float PatternCycle = FMath::Fmod(GameTime / 600.0f, 1.0f); // 10 minutos = 600 segundos
    float SerpentineOffset = FMath::Sin(PatternCycle * 2.0f * PI) * 500.0f;

    UFluxoPrismalComponent->SetFloatParameter(FName("SerpentineOffset"), SerpentineOffset);
    UFluxoPrismalComponent->SetFloatParameter(FName("PatternCycle"), PatternCycle);

    // Atualizar cor baseada no controle de equipe (simulado)
    FLinearColor FluxoColor = FLinearColor::White; // Neutro por padrão
    if (ActivityScale > 0.7f)
    {
        // Equipe A controlando (azul)
        FluxoColor = FLinearColor(0.2f, 0.5f, 1.0f, 1.0f);
    }
    else if (ActivityScale < 0.3f)
    {
        // Equipe B controlando (vermelho)
        FluxoColor = FLinearColor(1.0f, 0.3f, 0.2f, 1.0f);
    }

    UFluxoPrismalComponent->SetColorParameter(FName("FluxoColor"), FluxoColor);

    // Atualizar intensidade baseada na fase do mapa
    float PhaseMultiplier = 1.0f + static_cast<int32>(CurrentMapPhase) * 0.3f;
    FluxoPrismalIntensity = PhaseMultiplier;
    UFluxoPrismalComponent->SetFloatParameter(FName("FluxoIntensity"), FluxoPrismalIntensity);

    // Atualizar velocidade do fluxo baseada na volatilidade
    float FlowSpeed = 2.0f + (static_cast<int32>(CurrentMapPhase) * 0.5f);
    UFluxoPrismalComponent->SetFloatParameter(FName("FlowSpeed"), FlowSpeed);

    // Atualizar largura baseada nos pontos de estrangulamento
    float Width = FMath::Lerp(20.0f, 50.0f, ActivityScale); // 20-50 unidades conforme documentação
    UFluxoPrismalComponent->SetFloatParameter(FName("Width"), Width);

    // Atualizar áudio ambiente
    if (UFluxoPrismalAudioComponent && IsValid(UFluxoPrismalAudioComponent))
    {
        float AudioIntensity = FluxoPrismalIntensity * 0.8f;
        UFluxoPrismalAudioComponent->SetVolumeMultiplier(AudioIntensity);
        UFluxoPrismalAudioComponent->SetPitchMultiplier(1.0f + (FlowSpeed - 2.0f) * 0.2f);
    }
}

void AAURACRONPCGIsland::InitializeIlhaCentralAuracron()
{
    // Implementação robusta da Ilha Central Auracron conforme documentação
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::InitializeIlhaCentralAuracron - Inicializando Ilha Central Auracron"));

    if (!bHasIlhaCentralAuracron && IslandType == EAURACRONIslandType::Nexus)
    {
        bHasIlhaCentralAuracron = true;

        // Criar setores da Ilha Central Auracron conforme documentação

        // Setor Nexus - Geradores de recursos e manipulação do Fluxo
        USetorNexusComponent = CreateDefaultSubobject<UStaticMeshComponent>(
            *FString::Printf(TEXT("SetorNexus_%d"), FMath::RandRange(1000, 9999))
        );
        if (USetorNexusComponent)
        {
            USetorNexusComponent->SetupAttachment(RootComponent);
            USetorNexusComponent->SetRelativeLocation(FVector(300.0f, 0.0f, 200.0f));
            USetorNexusComponent->SetRelativeScale3D(FVector(2.0f, 2.0f, 3.0f));
            GeneratedComponents.Add(USetorNexusComponent);
        }

        // Setor Santuário - Fontes de cura e amplificadores de visão
        USetorSanctuaryComponent = CreateDefaultSubobject<UStaticMeshComponent>(
            *FString::Printf(TEXT("SetorSanctuary_%d"), FMath::RandRange(1000, 9999))
        );
        if (USetorSanctuaryComponent)
        {
            USetorSanctuaryComponent->SetupAttachment(RootComponent);
            USetorSanctuaryComponent->SetRelativeLocation(FVector(0.0f, 300.0f, 200.0f));
            USetorSanctuaryComponent->SetRelativeScale3D(FVector(2.0f, 2.0f, 2.5f));
            GeneratedComponents.Add(USetorSanctuaryComponent);
        }

        // Setor Arsenal - Upgrades de armas e potencializadores de habilidades
        USetorArsenalComponent = CreateDefaultSubobject<UStaticMeshComponent>(
            *FString::Printf(TEXT("SetorArsenal_%d"), FMath::RandRange(1000, 9999))
        );
        if (USetorArsenalComponent)
        {
            USetorArsenalComponent->SetupAttachment(RootComponent);
            USetorArsenalComponent->SetRelativeLocation(FVector(-300.0f, 0.0f, 200.0f));
            USetorArsenalComponent->SetRelativeScale3D(FVector(2.0f, 2.0f, 2.8f));
            GeneratedComponents.Add(USetorArsenalComponent);
        }

        // Setor Caos - Perigos ambientais com recompensas de alto risco
        USetorChaosComponent = CreateDefaultSubobject<UStaticMeshComponent>(
            *FString::Printf(TEXT("SetorChaos_%d"), FMath::RandRange(1000, 9999))
        );
        if (USetorChaosComponent)
        {
            USetorChaosComponent->SetupAttachment(RootComponent);
            USetorChaosComponent->SetRelativeLocation(FVector(0.0f, -300.0f, 200.0f));
            USetorChaosComponent->SetRelativeScale3D(FVector(2.0f, 2.0f, 3.2f));
            GeneratedComponents.Add(USetorChaosComponent);
        }

        // Torre de controle central
        UTorreControleCentralComponent = CreateDefaultSubobject<UStaticMeshComponent>(
            *FString::Printf(TEXT("TorreControleCentral_%d"), FMath::RandRange(1000, 9999))
        );
        if (UTorreControleCentralComponent)
        {
            UTorreControleCentralComponent->SetupAttachment(RootComponent);
            UTorreControleCentralComponent->SetRelativeLocation(FVector(0.0f, 0.0f, 400.0f));
            UTorreControleCentralComponent->SetRelativeScale3D(FVector(3.0f, 3.0f, 8.0f));
            GeneratedComponents.Add(UTorreControleCentralComponent);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::InitializeIlhaCentralAuracron - Ilha Central Auracron inicializada com sucesso"));
}

// ========================================
// FUNÇÕES AUXILIARES MODERNAS - UE 5.6 APIS
// ========================================

void AAURACRONPCGIsland::LoadMaterialAsync(const FString& MaterialPath, FStreamableDelegate OnLoadedDelegate)
{
    // Implementação robusta de carregamento assíncrono de materiais usando APIs modernas UE 5.6
    if (!StreamableManager)
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGIsland::LoadMaterialAsync - StreamableManager não inicializado"));
        return;
    }

    FSoftObjectPath MaterialSoftPath(MaterialPath);
    if (!MaterialSoftPath.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGIsland::LoadMaterialAsync - Caminho de material inválido: %s"), *MaterialPath);
        return;
    }

    // Carregar material de forma assíncrona
    StreamableManager->RequestAsyncLoad(MaterialSoftPath, OnLoadedDelegate, FStreamableManager::AsyncLoadHighPriority);

    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGIsland::LoadMaterialAsync - Carregamento assíncrono iniciado para: %s"), *MaterialPath);
}

void AAURACRONPCGIsland::OnRuneMaterialLoaded(UStaticMeshComponent* RuneComponent, int32 RuneIndex)
{
    // Callback para material de runa carregado usando APIs modernas UE 5.6
    if (!RuneComponent || !IsValid(RuneComponent))
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGIsland::OnRuneMaterialLoaded - Componente de runa inválido"));
        return;
    }

    // Buscar material carregado
    FSoftObjectPath MaterialPath(TEXT("/Engine/BasicShapes/BasicShapeMaterial"));
    if (UMaterialInterface* LoadedMaterial = Cast<UMaterialInterface>(StreamableManager->GetStreamableAsset(MaterialPath)))
    {
        // Criar material dinâmico
        UMaterialInstanceDynamic* RuneMaterial = UMaterialInstanceDynamic::Create(LoadedMaterial, this);
        if (RuneMaterial)
        {
            // Configurar parâmetros da runa
            RuneMaterial->SetScalarParameterValue(FName("RunePower"), 1.5f + (RuneIndex * 0.1f));
            RuneMaterial->SetVectorParameterValue(FName("RuneColor"), FLinearColor(0.1f, 0.5f, 0.9f, 1.0f));
            RuneMaterial->SetScalarParameterValue(FName("MysticGlow"), 2.0f);

            // Aplicar material ao componente
            RuneComponent->SetMaterial(0, RuneMaterial);

            UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGIsland::OnRuneMaterialLoaded - Material de runa %d aplicado com sucesso"), RuneIndex);
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGIsland::OnRuneMaterialLoaded - Falha ao carregar material de runa"));
    }
}

void AAURACRONPCGIsland::UpdatePhaseVisualEffects()
{
    // Atualização robusta dos efeitos visuais baseados na fase atual
    if (!IsValid(this)) return;

    // Atualizar efeitos dos Trilhos baseados na fase
    float PhaseIntensityMultiplier = 0.5f + (static_cast<int32>(CurrentMapPhase) * 0.2f);

    if (USolarTrilhoComponent && IsValid(USolarTrilhoComponent))
    {
        USolarTrilhoComponent->SetFloatParameter(FName("PhaseIntensity"), PhaseIntensityMultiplier);

        // Cores específicas por fase
        FLinearColor PhaseColor = FLinearColor(1.0f, 0.8f, 0.2f, 1.0f); // Dourado padrão
        switch (CurrentMapPhase)
        {
            case EAURACRONMapPhase::Awakening:
                PhaseColor = FLinearColor(1.0f, 0.9f, 0.7f, 1.0f); // Dourado suave
                break;
            case EAURACRONMapPhase::Convergence:
                PhaseColor = FLinearColor(1.0f, 1.0f, 0.9f, 1.0f); // Branco brilhante
                break;
            case EAURACRONMapPhase::Intensification:
                PhaseColor = FLinearColor(1.0f, 0.6f, 0.4f, 1.0f); // Laranja intenso
                break;
            case EAURACRONMapPhase::Resolution:
                PhaseColor = FLinearColor(0.6f, 0.7f, 1.0f, 1.0f); // Azul místico
                break;
        }
        USolarTrilhoComponent->SetColorParameter(FName("PhaseColor"), PhaseColor);
    }

    // Atualizar iluminação baseada na fase
    if (PointLightComponent && IsValid(PointLightComponent))
    {
        float BaseIntensity = 1000.0f;
        float PhaseIntensity = BaseIntensity * PhaseIntensityMultiplier;
        PointLightComponent->SetIntensity(PhaseIntensity);

        // Cor da luz baseada na fase
        FLinearColor LightColor = FLinearColor::White;
        switch (CurrentMapPhase)
        {
            case EAURACRONMapPhase::Awakening:
                LightColor = FLinearColor(1.0f, 0.9f, 0.8f, 1.0f);
                break;
            case EAURACRONMapPhase::Convergence:
                LightColor = FLinearColor(0.9f, 1.0f, 1.0f, 1.0f);
                break;
            case EAURACRONMapPhase::Intensification:
                LightColor = FLinearColor(1.0f, 0.7f, 0.5f, 1.0f);
                break;
            case EAURACRONMapPhase::Resolution:
                LightColor = FLinearColor(0.7f, 0.8f, 1.0f, 1.0f);
                break;
        }
        PointLightComponent->SetLightColor(LightColor);
    }
}

void AAURACRONPCGIsland::UpdateAmbientAudio()
{
    // Atualização robusta do áudio ambiente baseado na fase e atividade
    if (!AudioComponent || !IsValid(AudioComponent)) return;

    // Ajustar volume baseado na atividade da ilha
    float VolumeMultiplier = FMath::Lerp(0.3f, 1.0f, ActivityScale);
    AudioComponent->SetVolumeMultiplier(VolumeMultiplier);

    // Ajustar pitch baseado na fase do mapa
    float PitchMultiplier = 1.0f + (static_cast<int32>(CurrentMapPhase) * 0.1f);
    AudioComponent->SetPitchMultiplier(PitchMultiplier);

    // Ativar/desativar baseado na atividade
    if (ActivityScale > 0.1f && !AudioComponent->IsPlaying())
    {
        AudioComponent->Play();
    }
    else if (ActivityScale <= 0.1f && AudioComponent->IsPlaying())
    {
        AudioComponent->Stop();
    }
}

float AAURACRONPCGIsland::GetBoundaryBlurStrength() const
{
    // Getter robusto para força do blur de boundary
    return CurrentBoundaryBlurStrength;
}