// AURACRONPCGPortal.h
// Sistema de Portais de Posicionamento Tático - UE 5.6

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "Data/AURACRONEnums.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SphereComponent.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "Engine/StreamableManager.h"
#include "TimerManager.h"
#include "AURACRONPCGPortal.generated.h"

// Forward declarations
struct FStreamableManager;

/**
 * Enumeração para os tipos de portais de posicionamento tático
 */
UENUM(BlueprintType)
enum class EAURACRONPortalType : uint8
{
    RadiantPlains UMETA(DisplayName = "Portal Radiante"),  // Energia dourada - Planície Radiante
    ZephyrFirmament  UMETA(DisplayName = "Portal Zephyr"),    // Energia prateada - Firmamento Zephyr
    PurgatoryRealm  UMETA(DisplayName = "Portal Umbral")     // Energia violeta - Reino Purgatório
};

/**
 * Estrutura para configurações de portal de posicionamento tático
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONPortalSettings
{
    GENERATED_BODY()

    /** Tipo do portal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    EAURACRONPortalType PortalType;
    
    /** Tipo do ambiente atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    EAURACRONEnvironmentType CurrentEnvironment;
    
    /** Localização de destino para teletransporte */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FVector DestinationLocation;
    
    /** Rotação de destino para teletransporte */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FRotator DestinationRotation;
    
    /** Escala do portal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float PortalScale;
    
    /** Cor do portal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FLinearColor PortalColor;
    
    /** Intensidade do efeito */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float EffectIntensity;
    
    /** Raio de ativação */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float ActivationRadius;
    
    /** Se o portal está ativo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bIsActive;
    
    /** Se o portal está visível */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bIsVisible;

    /** Fase necessária para ativar o portal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    EAURACRONMapPhase RequiredPhase;

    /** ID único do portal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int32 PortalID;
    
    FAURACRONPortalSettings()
        : PortalType(EAURACRONPortalType::RadiantPlains)
        , CurrentEnvironment(EAURACRONEnvironmentType::RadiantPlains)
        , DestinationLocation(FVector::ZeroVector)
        , DestinationRotation(FRotator::ZeroRotator)
        , PortalScale(1.0f)
        , PortalColor(FLinearColor(1.0f, 0.84f, 0.0f)) // Dourado por padrão (Portal Radiante)
        , EffectIntensity(1.0f)
        , ActivationRadius(300.0f)
        , bIsActive(true)
        , bIsVisible(true)
        , RequiredPhase(EAURACRONMapPhase::Awakening)
        , PortalID(0)
    {
    }
    
    /** Construtor com tipo de portal específico */
    FAURACRONPortalSettings(EAURACRONPortalType InPortalType)
        : PortalType(InPortalType)
        , CurrentEnvironment(EAURACRONEnvironmentType::RadiantPlains)
        , DestinationLocation(FVector::ZeroVector)
        , DestinationRotation(FRotator::ZeroRotator)
        , PortalScale(1.0f)
        , EffectIntensity(1.0f)
        , ActivationRadius(300.0f)
        , bIsActive(true)
        , bIsVisible(true)
        , RequiredPhase(EAURACRONMapPhase::Awakening)
        , PortalID(0)
    {
        // Configurar cor baseada no tipo de portal
        switch (InPortalType)
        {
        case EAURACRONPortalType::RadiantPlains:
            PortalColor = FLinearColor(1.0f, 0.84f, 0.0f); // Dourado
            CurrentEnvironment = EAURACRONEnvironmentType::RadiantPlains;
            break;
        case EAURACRONPortalType::ZephyrFirmament:
            PortalColor = FLinearColor(0.75f, 0.75f, 0.75f); // Prateado
            CurrentEnvironment = EAURACRONEnvironmentType::ZephyrFirmament;
            break;
        case EAURACRONPortalType::PurgatoryRealm:
            PortalColor = FLinearColor(0.5f, 0.0f, 0.5f); // Violeta
            CurrentEnvironment = EAURACRONEnvironmentType::PurgatoryRealm;
            break;
        }
    }
};

/**
 * Classe para portais de posicionamento tático
 */
UCLASS()
class AURACRON_API AAURACRONPCGPortal : public AActor
{
    GENERATED_BODY()

public:
    AAURACRONPCGPortal();

    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

    // ========================================
    // FUNÇÕES PÚBLICAS
    // ========================================
    
    /** Inicializar portal */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Portal")
    void InitializePortal(const FAURACRONPortalSettings& Settings);
    
    /** Inicializar portal com tipo específico */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Portal")
    void InitializePortalWithType(EAURACRONPortalType PortalType, FVector DestLocation, FRotator DestRotation);
    
    /** Criar portal radiante (energia dourada) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Portal")
    void CreateRadiantPortal(FVector DestLocation, FRotator DestRotation);
    
    /** Criar portal zephyr (energia prateada) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Portal")
    void CreateZephyrPortal(FVector DestLocation, FRotator DestRotation);
    
    /** Criar portal umbral (energia violeta) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Portal")
    void CreateUmbralPortal(FVector DestLocation, FRotator DestRotation);
    
    /** Ativar portal */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Portal")
    void ActivatePortal();
    
    /** Desativar portal */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Portal")
    void DeactivatePortal();
    
    /** Definir visibilidade do portal */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Portal")
    void SetPortalVisibility(bool bVisible);
    
    /** Atualizar para fase do mapa */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Portal")
    void UpdateForMapPhase(EAURACRONMapPhase MapPhase);
    
    /** Aplicar efeito de teletransporte */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Portal")
    void ApplyTeleportEffect(float EffectAlpha, bool bFadeIn);
    
    /** Teletransportar jogador para destino com efeito de Fade Out/In */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Portal")
    void TeleportPlayerToDestination(class ACharacter* PlayerCharacter);
    
    /** Iniciar efeito de Fade Out antes do teletransporte */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Portal")
    void StartFadeOutEffect(class ACharacter* PlayerCharacter);
    
    /** Iniciar efeito de Fade In após o teletransporte */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Portal")
    void StartFadeInEffect(class ACharacter* PlayerCharacter);
    
    /** Obter tipo do portal */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Portal")
    EAURACRONPortalType GetPortalType() const { return PortalSettings.PortalType; }
    
    /** Obter ambiente atual */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Portal")
    EAURACRONEnvironmentType GetCurrentEnvironment() const { return PortalSettings.CurrentEnvironment; }
    
    /** Obter localização de destino */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Portal")
    FVector GetDestinationLocation() const { return PortalSettings.DestinationLocation; }
    
    /** Obter rotação de destino */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Portal")
    FRotator GetDestinationRotation() const { return PortalSettings.DestinationRotation; }
    
    /** Verificar se o portal está ativo */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Portal")
    bool IsPortalActive() const { return PortalSettings.bIsActive; }
    
    /** Verificar se o portal está visível */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Portal")
    bool IsPortalVisible() const { return PortalSettings.bIsVisible; }
    
    /** Verificar se é um portal radiante */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Portal")
    bool IsRadiantPortal() const { return PortalSettings.PortalType == EAURACRONPortalType::RadiantPlains; }
    
    /** Verificar se é um portal zephyr */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Portal")
    bool IsZephyrPortal() const { return PortalSettings.PortalType == EAURACRONPortalType::ZephyrFirmament; }
    
    /** Verificar se é um portal umbral */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Portal")
    bool IsUmbralPortal() const { return PortalSettings.PortalType == EAURACRONPortalType::PurgatoryRealm; }

    /** Obter raio de ativação */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Portal")
    float GetActivationRange() const { return ActivationSphere ? ActivationSphere->GetScaledSphereRadius() : 100.0f; }

    /** Definir raio de ativação */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|Portal")
    void SetActivationRange(float NewRange) { if (ActivationSphere) ActivationSphere->SetSphereRadius(NewRange); }

    /** Obter cor do portal */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Portal")
    FLinearColor GetPortalColor() const { return PortalSettings.PortalColor; }

    /** Obter raio de ativação (alias) */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Portal")
    float GetActivationRadius() const { return GetActivationRange(); }

    /** Verificar se está ativo (alias) */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Portal")
    bool IsActive() const { return IsPortalActive(); }

    /** Verificar se está visível (alias) */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Portal")
    bool IsVisible() const { return IsPortalVisible(); }

    /** Obter fase do portal */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Portal")
    EAURACRONMapPhase GetPortalPhase() const { return PortalSettings.RequiredPhase; }

    /** Obter ID do portal */
    UFUNCTION(BlueprintPure, Category = "AURACRON|Portal")
    int32 GetPortalID() const { return PortalSettings.PortalID; }

protected:
    // ========================================
    // COMPONENTES
    // ========================================
    
    /** Componente de malha estática */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|Portal")
    UStaticMeshComponent* PortalMesh;
    
    /** Componente de colisão */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|Portal")
    USphereComponent* ActivationSphere;
    
    /** Componente de efeito de partículas */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|Portal")
    UNiagaraComponent* PortalEffect;
    
    /** Sistema de partículas para o portal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|Portal")
    UNiagaraSystem* PortalParticleSystem;
    
    /** Efeito Niagara para teletransporte */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|Portal")
    UNiagaraSystem* TeleportEffect;
    
    /** Som de teletransporte */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|Portal")
    USoundBase* TeleportSound;
    
    /** Configurações do portal */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AURACRON|Portal")
    FAURACRONPortalSettings PortalSettings;

    /** Fase atual do mapa */
    UPROPERTY(BlueprintReadOnly, Replicated, Category = "AURACRON|Portal")
    EAURACRONMapPhase CurrentMapPhase;

private:
    // ========================================
    // ESTADO INTERNO
    // ========================================
    
    /** Material dinâmico para o portal */
    UPROPERTY()
    UMaterialInstanceDynamic* PortalDynamicMaterial;
    
    /** Tempo de vida do portal */
    UPROPERTY(Replicated, ReplicatedUsing = OnRep_PortalLifetime)
    float PortalLifetime;
    
    /** Velocidade de rotação */
    UPROPERTY()
    float RotationSpeed;
    
    /** Intensidade de pulsação */
    UPROPERTY()
    float PulsateIntensity;
    
    /** Frequência de pulsação */
    UPROPERTY()
    float PulsateFrequency;
    
    /** Duração do efeito de Fade Out/In em segundos */
    UPROPERTY()
    float FadeTransitionDuration;
    
    /** Temporizador para controlar o efeito de Fade */
    UPROPERTY()
    FTimerHandle FadeTimerHandle;
    
    /** Personagem atualmente em processo de teletransporte */
    UPROPERTY(Replicated)
    ACharacter* TeleportingCharacter;
    
    /** Valor atual do efeito de Fade (0.0 a 1.0) */
    UPROPERTY()
    float CurrentFadeValue;

    /** Componente de áudio para sons do portal */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|Portal", meta = (AllowPrivateAccess = "true"))
    class UAudioComponent* AudioComponent;

    /** Componente de luz do portal */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|Portal", meta = (AllowPrivateAccess = "true"))
    class UPointLightComponent* PortalLight;

    /** Gerenciador de carregamento assíncrono - UE 5.6: usar TSharedPtr */
    TSharedPtr<FStreamableManager> StreamableManager;

    /** Flag indicando se assets estão sendo carregados */
    UPROPERTY()
    bool bIsLoadingAssets;

    /** Timer para atualização de efeitos visuais */
    UPROPERTY()
    FTimerHandle VisualEffectsTimerHandle;
    
    // ========================================
    // FUNÇÕES DE NETWORKING E CARREGAMENTO ASSÍNCRONO
    // ========================================

    /** Inicializar carregamento assíncrono de assets */
    void InitializeAsyncAssetLoading();

    /** Callback quando assets são carregados */
    void OnAssetsLoaded();

    /** Configurar efeitos específicos por tipo de portal */
    void ConfigurePortalTypeEffects();

    /** Atualizar efeitos visuais via Timer (ao invés de Tick) */
    void UpdateVisualEffectsTimer();

    /** Replicação do tempo de vida do portal */
    UFUNCTION()
    void OnRep_PortalLifetime();

    /** RPC do servidor para solicitar teletransporte */
    UFUNCTION(Server, Reliable)
    void ServerRequestTeleport(ACharacter* PlayerCharacter);

    /** RPC multicast para notificar ativação do portal */
    UFUNCTION(NetMulticast, Reliable)
    void ClientNotifyPortalActivation(ACharacter* PlayerCharacter);

    // ========================================
    // FUNÇÕES INTERNAS
    // ========================================
    
    /** Atualizar efeitos visuais */
    void UpdateVisualEffects(float DeltaTime);
    
    /** Atualizar parâmetros de material */
    void UpdateMaterialParameters();
    
    /** Atualizar parâmetros de partículas */
    void UpdateParticleParameters();
    
    /** Executar o teletransporte após o Fade Out */
    void ExecuteTeleport();
    
    /** Atualizar o efeito de Fade */
    void UpdateFadeEffect();
    
    /** Callback para sobreposição com ator */
    UFUNCTION()
    void OnOverlapBegin(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, 
                        UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, 
                        bool bFromSweep, const FHitResult& SweepResult);
};